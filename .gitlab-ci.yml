stages:
  - build
  - deploy
  - alarm
variables:
  TZ: "Asia/Shanghai"
  BUILD_PREFIX_INFO: "$CI_COMMIT_BRANCH:$CI_COMMIT_SHORT_SHA 构建开始\n构建创建者：$GITLAB_USER_NAME\n分支提交者：$CI_COMMIT_AUTHOR\n $CI_COMMIT_MESSAGE"
  IMAGE_TAG: $NEXUS_HOST/wnkx
build-image-dev:
  only:
    - main
  tags:
    - node
  stage: build
  script:
    - yarn --registry=http://mirrors.tencent.com/npm/
    - yarn build:prod
    - echo "Build Success"
    - mv dist ci/
    - docker login -u $NEXUS_USER_NAME -p $NEXUS_USER_PASSWORD http://$NEXUS_HOST
    - docker build -t $IMAGE_TAG/wnkx-front:latest ci
    - docker push $IMAGE_TAG/wnkx-front:latest
    - echo "Package Success"
build-image-test:
  only:
    - test
  tags:
    - node
  stage: build
  script:
    - yarn --registry=http://mirrors.tencent.com/npm/
    - yarn build:test
    - echo "Build Success"
    - mv dist ci/
    - docker login -u $NEXUS_USER_NAME -p $NEXUS_USER_PASSWORD http://$NEXUS_HOST
    - docker build -f ci/Dockerfile-test -t $IMAGE_TAG/wnkx-front:latest-test ci
    - docker push $IMAGE_TAG/wnkx-front:latest-test
    - echo "Package Success"
build-image-daily:
  only:
    - daily
  tags:
    - node
  stage: build
  script:
    - yarn --registry=http://mirrors.tencent.com/npm/
    - yarn build:daily
    - echo "Build Success"
    - mv dist ci/
    - docker login -u $NEXUS_USER_NAME -p $NEXUS_USER_PASSWORD http://$NEXUS_HOST
    - docker build -f ci/Dockerfile-daily -t $IMAGE_TAG/wnkx-front:latest-daily ci
    - docker push $IMAGE_TAG/wnkx-front:latest-daily
    - echo "Package Success"
build-image-uat:
  only:
    - uat
  tags:
    - node
  stage: build
  script:
    - yarn config set strict-ssl false
    - yarn --registry=http://mirrors.tencent.com/npm/
    - yarn build:uat
    - echo "Build Success"
    - mv dist ci/
    - docker login -u $NEXUS_USER_NAME -p $NEXUS_USER_PASSWORD http://$NEXUS_HOST
    - docker build -f ci/Dockerfile-uat -t $IMAGE_TAG/wnkx-front:latest-uat ci
    - docker push $IMAGE_TAG/wnkx-front:latest-uat
    - echo "Package Success"

build-image-prod:
  only:
    - prod
  tags:
    - node
  stage: build
  script:
    - yarn config set strict-ssl false
    - yarn --registry=http://mirrors.tencent.com/npm/
    - yarn build:prod
    - echo "Build Success"
    - mv dist ci/
    - docker login -u $NEXUS_USER_NAME -p $NEXUS_USER_PASSWORD http://$NEXUS_HOST
    - docker build -f ci/Dockerfile-prod -t $IMAGE_TAG/wnkx-front:latest-prod ci
    - docker push $IMAGE_TAG/wnkx-front:latest-prod
    - echo "Package Success"
update-dev-container:
  stage: deploy
  only:
    - main
  tags:
    - node
  image: docker:26.1.0-dind
  script:
    - echo "开始更新容器"
    - docker compose -f ci/compose/docker-compose.yml pull
    - docker compose -f ci/compose/docker-compose.yml up -d
  needs:
    - build-image-dev
  environment:
    name: dev
    url: http://$DEV_HOST
update-test-container:
  stage: deploy
  only:
    - test
  tags:
    - node
  image: docker:26.1.0-dind
  script:
    - echo "开始更新容器"
    - docker compose -f ci/compose/docker-compose-test.yml pull
    - docker compose -f ci/compose/docker-compose-test.yml up -d
  needs:
    - build-image-test
  environment:
    name: test
    url: http://$UAT_HOST
update-daily-container:
  stage: deploy
  only:
    - daily
  tags:
    - node
  image: docker:26.1.0-dind
  script:
    - echo "开始更新容器"
    - docker compose -f ci/compose/docker-compose-daily.yml pull
    - docker compose -f ci/compose/docker-compose-daily.yml up -d
  needs:
    - build-image-daily
  environment:
    name: daily
    url: http://$DAILY_HOST
update-uat-container:
  stage: deploy
  only:
    - uat
  tags:
    - node
  image: docker:26.1.0-dind
  script:
    - echo "开始更新容器"
    - docker compose -f ci/compose/docker-compose-uat.yml pull
    - docker compose -f ci/compose/docker-compose-uat.yml up -d
  needs:
    - build-image-uat
  environment:
    name: uat
    url: http://$UAT_HOST
update-prod-container:
  tags:
    - prod
  stage: deploy
  image: docker:26.1.0-dind
  only:
    - prod
  script:
    - echo "开始更新容器"
    - docker stack deploy -c ci/compose/docker-compose-prod.yml prod
  needs:
    - build-image-prod
  environment:
    name: prod
    url: https://run.woniu.video
  when: manual

alarm:
  stage: alarm
  script:
    - echo "构建失败"
    - curl --location --request POST "$ALARM_HOST" --header 'Content-Type:application/json' --data-raw "$ALARM_TEXT"
    - echo 'curl --location --request POST "$ALARM_HOST" --header 'Content-Type:application/json' --data-raw "$ALARM_TEXT"'
  when: on_failure
