import Cookies from 'js-cookie'

const TokenKey = 'Admin-Token'
const LocalTokenKey = 'WNKJAT'

const ExpiresInKey = 'Admin-Expires-In'

export function getToken() {
  let token = Cookies.get(TokenKey)
  if(!token) {
    token = localStorage.getItem(LocalTokenKey)
  }
  return token
}

export function setToken(token) {
  localStorage.setItem(LocalTokenKey, token)
  return Cookies.set(TokenKey, token)
}

export function removeToken() {
  localStorage.removeItem(LocalTokenKey)
  return Cookies.remove(TokenKey)
}

export function getExpiresIn() {
  return Cookies.get(ExpiresInKey) || -1
}

export function setExpiresIn(time) {
  return Cookies.set(ExpiresInKey, time)
}

export function removeExpiresIn() {
  return Cookies.remove(ExpiresInKey)
}
