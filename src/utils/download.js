/**
 * 下载文件
 * @param url
 * @param fileName
 */
export function downUrlFile(url, fileName) {
  // 创建一个Image对象
  const image = new Image()
  // 设置图片的src属性为要下载的图片地址
  image.src = url
  image.setAttribute('crossOrigin', 'anonymous')

  // 加载图片资源
  image.onload = function () {
    // 创建Canvas元素
    let canvas = document.createElement('canvas')
    canvas.width = image.width
    canvas.height = image.height

    // 绘制图片到Canvas
    let ctx = canvas.getContext('2d')
    ctx?.drawImage(image, 0, 0)

    let src = canvas.toDataURL('image/png')
    let a = document.createElement('a')
    a.href = src
    a.download = fileName
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
  }
}
export function downUrlImageV1(reqUrl,filename) {
  fetch(reqUrl).then(res => {
    res.blob().then(blob => {
      let a = document.createElement('a');
      let url = window.URL.createObjectURL(blob);
      // 直接获取文件类型
      const fileExtension = reqUrl.split('.').pop().toLowerCase();
      a.href = url;
      //给图片处理后缀
      a.download = filename + '.' + fileExtension;
      a.click();
      window.URL.revokeObjectURL(url);
    });
  });
}