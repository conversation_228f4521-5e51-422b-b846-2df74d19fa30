import useDictStore from '@/store/modules/dict'
import { getDicts } from '@/api/system/dict/data'

const sessionStorageStr = 'dict-'
const res = ref({})

/**
 * 获取字典数据
 */
function useDict(...args) {
  return (() => {
    args.forEach((dictType, index) => {
      if (!res.value[dictType]) {
        sessionStorage.removeItem(sessionStorageStr + dictType)
        res.value[dictType] = []
      }
      const dicts = useDictStore().getDict(dictType)
      if (dicts) {
        res.value[dictType] = dicts
      } else {
        let s = sessionStorage.getItem(sessionStorageStr + dictType)
        if (!s || (s && new Date().getTime() - Number(s) > 1000 * 60 * 5)) {
          sessionStorage.setItem(sessionStorageStr + dictType, new Date().getTime() + '')
          getDicts(dictType)
            .then(resp => {
              res.value[dictType] = resp.data.map(p => ({
                label: p.dictLabel,
                value: p.dictValue,
                elTagType: p.listClass,
                elTagClass: p.cssClass,
              }))
              useDictStore().setDict(dictType, res.value[dictType])
            })
            .finally(() => {
              sessionStorage.removeItem(sessionStorageStr + dictType)
            })
        }
      }
    })
    return toRefs(res.value)
  })()
}
/**
 * 支付方式
 */
// const bizPayTypeList = [
//   { label: '微信', value: 1 },
//   { label: '支付宝支付', value: 2 },
//   { label: '云闪付/银联', value: 3 },
//   { label: '数字人民币', value: 4 },
//   { label: '银行卡转账', value: 5 },
//   { label: '对公转账', value: 6 },
//   { label: '余额支付', value: 10 },
//   { label: '微信支付+余额支付', value: 11 },
//   { label: '支付宝支付+余额支付', value: 12 },
//   { label: '云闪付/银联+余额支付', value: 13 },
//   { label: '数字人民币+余额支付', value: 14 },
//   { label: '银行卡转账+余额支付', value: 15 },
//   { label: '对公转账+余额支付', value: 16 },
// ]
const bizPayTypeList = [
  { label: '微信', value: 1 },
  { label: '支付宝', value: 2 },
  { label: '云闪付/银联', value: 3 },
  { label: '数字人民币', value: 4 },
  { label: '银行卡', value: 5 },
  { label: '对公', value: 6 },
  { label: '全币种', value: 7 },
  { label: '余额', value: 10 },
  { label: '微信+余额', value: 11 },
  { label: '支付宝+余额', value: 12 },
  { label: '云闪付/银联+余额', value: 13 },
  { label: '数字人民币+余额', value: 14 },
  { label: '银行卡+余额', value: 15 },
  { label: '对公+余额', value: 16 },
  { label: '全币种+余额', value: 17 },
]
const bizPayTypeListNew = [
  { label: '微信', value: 1 },
  { label: '支付宝', value: 2 },
  { label: '云闪付/银联', value: 3 },
  { label: '数字人民币', value: 4 },
  { label: '银行卡', value: 5 },
  { label: '对公', value: 6 },
  { label: '全币种', value: 7 },
  { label: '余额', value: 10 },
  { label: '微信+余额', value: 11 },
  { label: '支付宝+余额', value: 12 },
  { label: '云闪付/银联+余额', value: 13 },
  { label: '数字人民币+余额', value: 14 },
  { label: '银行卡+余额', value: 15 },
  { label: '对公+余额', value: 16 },
  { label: '全币种+余额', value: 17 },
  { label: '其他', value: 99 },
]

/**
 * 支付方式
 */
const payTypeMap = {}
bizPayTypeList.forEach(item => {
  payTypeMap[item.label] = item.value
  payTypeMap[item.value] = item.label
})
/**
 * 支付方式
 */
const payTypeMapNew = {}
bizPayTypeListNew.forEach(item => {
  payTypeMapNew[item.label] = item.value
  payTypeMapNew[item.value] = item.label
})
/**
 * 下拉使用的支付方式
 */
const payTypeSelectList = [
  { label: '微信', value: 1 },
  { label: '支付宝', value: 2 },
  { label: '银行卡', value: 5 },
  { label: '对公', value: 6 },
  { label: '全币种', value: 7 },
  { label: '余额支付', value: 10 },
  // { label: '其他', value: 99 }
]

/**
 * 钱币单位
 */
const bizCommissionUnit = [
  { label: '美金', value: 'USD' },
  { label: '加币', value: 'CAD' },
  { label: '英镑', value: 'GBP' },
  { label: '欧元', value: 'EUR' },
]

/**
 * 模特状态
 */
const bizModelStatus = [
  { label: '正常合作', value: 0 },
  { label: '暂停合作', value: 1 },
  { label: '行程中', value: 2 },
  { label: '取消合作', value: 3 },
]

const logisticsStatus = [
  { label: '查询不到', value: 1 },
  { label: '收到信息', value: 2 },
  { label: '运输途中', value: 3 },
  { label: '运输过久', value: 4 },
  { label: '到达待取', value: 5 },
  { label: '派送途中', value: 6 },
  { label: '投递失败', value: 7 },
  { label: '成功签收', value: 8 },
  { label: '可能异常', value: 9 },
]

//地址信息
const addressInfoList = [
  {
    recipient: 'Recipient Name',
    detailAddress: 'House Number and Street Name',
    localArea: 'Locality',
    city: 'City or Town',
    zipCode: 'Postal Code',
    nation: 'Country',
    value: 1,
  },
  {
    recipient: 'Name',
    detailAddress: 'Street Address',
    city: 'City',
    province: 'Province',
    zipCode: 'Postal Code',
    nation: 'Country',
    value: 2,
  },
  {
    recipient: 'Empfängername',
    detailAddress: 'Straße und Hausnummer',
    city: 'Ort',
    zipCode: 'Postleitzahl',
    province: 'Bundesländer',
    nation: 'Land',
    value: 3,
  },
  {
    recipient: 'Nom',
    detailAddress: 'Street Address',
    province: 'Département',
    city: 'Ville',
    zipCode: 'Code postal',
    nation: 'Pays',
    value: 4,
  },
  {
    recipient: 'Nome del destinatario',
    detailAddress: 'Via e numero civico',
    city: 'Città',
    province: 'Provincia',
    zipCode: 'CAP',
    nation: 'Paese',
    value: 5,
  },
  {
    recipient: 'Nombre del destinatario',
    detailAddress: 'Dirección (calle y número)',
    city: 'Ciudad',
    province: 'Provincias',
    zipCode: 'Código postal',
    nation: 'País',
    value: 6,
  },
  {
    recipient: 'Name',
    detailAddress: 'Street Address',
    city: 'City',
    state: 'State',
    zipCode: 'Postal code',
    nation: 'Country',
    value: 7,
  },
]

const videoFormatOptions = [
  { label: '横屏拍摄16:9', value: 1 },
  { label: '竖屏拍摄9:16', value: 2 },
]

const payTypeList = [
  { label: '微信', value: 1 },
  { label: '支付宝', value: 2 },
  { label: '银行卡转账', value: 5 },
  { label: '对公转账', value: 6 },
  { label: '全币种', value: 7 },
  { label: '其他', value: 99 },
]

export {
  useDict,
  bizPayTypeList,
  payTypeMap,
  payTypeMapNew,
  payTypeSelectList,
  bizCommissionUnit,
  bizModelStatus,
  logisticsStatus,
  addressInfoList,
  videoFormatOptions,
  payTypeList,
}
