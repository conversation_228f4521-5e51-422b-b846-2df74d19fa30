import { uploadCloudFile } from '@/api/index'
import { ElMessage, ElLoading } from "element-plus"

let timer
/**
 * 防抖
 * @param fn 执行函数
 * @param delay 防抖时间
 */
export const debounce = (fn, delay = 500) => {
  if (timer) {
    clearTimeout(timer)
  }
  timer = setTimeout(fn, delay)
}
/**
 * 节流
 * @param fn 执行函数
 * @param delay 节流时间
 * @returns {Function}
 */
export const throttle = (fn, delay = 500) => {
  let flag = true
  return function () {
    if (!flag) {
      return
    }
    flag = false
    setTimeout(() => {
      fn.call(this)
      flag = true
    }, delay)
  }
}
/**
 * 处理粘贴文件上传
 * @param event paste 事件参数
 * @param options.autoUpload 是否自动上传 默认：false
 * @param options.isBeforeUpload 是否对粘贴文件进行校验 默认：true
 * @param options.fileType 校验文件后缀 默认：['png', 'jpg', 'jpeg']
 * @param options.size 校验文件大小 默认：5 (MB)
 * @returns File[] || 自动上传返回接口参数
 */
export const handlePasteFile = (event, options) => {
  // console.log(event);
  return new Promise((resolve, reject) => {
    const items = event.clipboardData?.items || []
    let pasteFiles = []
    for (let index in items) {
      const item = items[index]
      if (item.kind === 'file') {
        const file = item.getAsFile()
        if (file !== null) {
          pasteFiles.push(file)
        }
      }
    }
    if (!(options?.isBeforeUpload === false) && !beforeUpload(pasteFiles, options)) {
      return reject('error beforeUpload')
    }
    // 是否直接上传
    if (options?.autoUpload) {
      const el_loading = ElLoading.service({
        lock: true,
        text: '正在上传中，请稍后',
        background: 'rgba(0, 0, 0, 0.7)',
      })
      let requests = []
      pasteFiles.forEach(file => {
        const fromData = new FormData()
        fromData.append('file', file)
        requests.push(uploadCloudFile(fromData))
      })
      Promise.all(requests)
        .then(res => {
          ElMessage.success('上传成功')
          let data = []
          res.forEach(item => {
            data.push(item.data)
          })
          resolve(data)
        })
        .finally(() => el_loading.close())
    } else {
      resolve(pasteFiles)
    }
  })
}
export function beforeUpload(files, options) {
  if (!files || !files.length) {
    return false
  }
  const limit = options?.limit || 0
  if (limit && files.length > limit) {
    ElMessage.warning(`最多可上传${limit}个文件`)
    return false
  }
  let check = true
  let fileType = options?.fileType || ['png', 'jpg', 'jpeg']
  for (let i = 0; i < files.length; i++) {
    const fileSuffix = files[i].name.substring(files[i].name.lastIndexOf(".") + 1).toLowerCase()
    if (!fileType.includes(fileSuffix)) {
      ElMessage.warning(`请上传${fileType.join('/')}格式的文件`)
      check = false
      break
    }
    const size = (options?.size || 20) * 1024 * 1024
    if (files[i].size > size) {
      ElMessage.warning(`要上传的文件不能超过${(options?.size || 20)}MB`)
      check = false
      break
    }
  }
  return check
}