import { ElMessage, ElMessageBox, ElNotification, ElLoading } from 'element-plus'
import IconHint from '@/components/SvgIcon/hint.vue'
import { markRaw } from 'vue'

let loadingInstance;

export default {
  // 消息提示
  msg(content) {
    ElMessage.info(content)
  },
  // 错误消息
  msgError(content) {
    ElMessage.error(content)
  },
  // 成功消息
  msgSuccess(content) {
    ElMessage.success(content)
  },
  // 警告消息
  msgWarning(content) {
    ElMessage.warning(content)
  },
  // 弹出提示
  alert(content, title = "系统提示", options = {}) {
    if(!options.autofocus) {
      options.autofocus = false
    }
    return ElMessageBox.alert(content, title, options)
  },
  // 错误提示
  alertError(content, title = "系统提示") {
    ElMessageBox.alert(content, title, { type: 'error', autofocus: false })
  },
  // 成功提示
  alertSuccess(content, title = "系统提示") {
    ElMessageBox.alert(content, title, { type: 'success', autofocus: false })
  },
  // 警告提示
  alertWarning(content, title = "系统提示") {
    ElMessageBox.alert(content, title, { type: 'warning', autofocus: false })
  },
  // 通知提示
  notify(content) {
    ElNotification.info(content)
  },
  // 错误通知
  notifyError(content) {
    ElNotification.error(content);
  },
  // 成功通知
  notifySuccess(content) {
    ElNotification.success(content)
  },
  // 警告通知
  notifyWarning(content) {
    ElNotification.warning(content)
  },
  // 确认窗体
  confirm(content, title = "系统提示", options) {
    if(!options){
      options = {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: "warning",
      }
    }
    if(!options.autofocus) {
      options.autofocus = false
    }
    return ElMessageBox.confirm(content, title, options)
  },
  // 自定义样式确认窗体
  customConfirm(content, title = "系统提示", options) {
    return ElMessageBox.confirm(content, title, {
      cancelButtonText: '取消',
      confirmButtonText: '确定',
      icon: markRaw(IconHint),
      dangerouslyUseHTMLString: true,
      autofocus: false,
      ...options,
      customClass: 'custom-message-box',
    })
  },
  // 提交内容
  prompt(content, title = "系统提示", options) {
    if(!options){
      options = {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: "warning",
      }
    }
    if(!options.autofocus) {
      options.autofocus = false
    }
    return ElMessageBox.prompt(content, title, options)
  },
  // 打开遮罩层
  loading(content = "正在加载中") {
    loadingInstance = ElLoading.service({
      lock: true,
      text: content,
      background: "rgba(0, 0, 0, 0.7)",
    })
  },
  // 关闭遮罩层
  closeLoading() {
    loadingInstance.close();
  }
}
