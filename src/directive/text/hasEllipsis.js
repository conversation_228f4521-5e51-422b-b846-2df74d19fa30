/**
* v-has-ellipsis:xxx="obj" || v-has-ellipsis="(boolean) => void"
* 判断div css text-overflow: ellipsis;是否显示省略号
*/

export default {
  created(el, binding, vnode) {
    if (!el.style.paddingRight) {
      // 处理文字长度计算为小数可能出现省略号的情况，元素已有padding-right属性时需要外部设置样式+0.5px
      el.style.paddingRight = '0.5px'
    }
    if (typeof binding.value === 'function') {
      el.__private__handle = (el, binding) => {
        if (el.scrollHeight > el.clientHeight || el.scrollWidth > el.clientWidth) {
          binding.value(true)
          return
        }
        binding.value(false)
      }
    } else if (binding.arg) {
      el.__private__handle = (el, binding) => {
        if (el.scrollHeight > el.clientHeight || el.scrollWidth > el.clientWidth) {
          binding.value[binding.arg] = true
          return
        }
        binding.value[binding.arg] = false
      }
    }
  },
  mounted(el, binding, vnode) {
    setTimeout(() => {
      el.__private__handle && el.__private__handle(el, binding)
    })
  },
  beforeUnmount(el, binding, vnode) {
    el.__private__handle = null
  }
}