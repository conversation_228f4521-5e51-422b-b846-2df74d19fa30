import { useTooltips } from '@/hooks/useTooltips'

const { tooltipKey, tooltipsTriggerRef, tooltipVisible, showTooltips } = useTooltips()

/**
* v-ellipsis-tooltips="string"
* 判断div css text-overflow: ellipsis;是否显示省略号
* 显示省略号时鼠标移入显示tooltips
*/
export default {
  mounted(el, binding, vnode) {
    if (binding.value) {
      el._binding_value = binding.value
      el._customMouseEnterHandler = (e) => {
        if (e.target?.scrollHeight > e.target?.clientHeight || e.target?.scrollWidth > e.target?.clientWidth) {
          showTooltips(e, `<div style="max-width: 500px; word-wrap: break-word;">${e.target._binding_value}</div>`)
        } else if (tooltipsTriggerRef.value && !tooltipVisible.value) {
          tooltipsTriggerRef.value = null
          tooltipKey.value++
        }
      }
      el.addEventListener('mouseenter', el._customMouseEnterHandler)
    }
  },
  updated(el, binding, vnode) {
    if (el._customMouseEnterHandler) {
      el._binding_value = binding.value || ''
    }
  },
  beforeUnmount(el, binding, vnode) {
    if (el._customMouseEnterHandler) {
      el.removeEventListener('mouseenter', el._customMouseEnterHandler)
    }
  }
}