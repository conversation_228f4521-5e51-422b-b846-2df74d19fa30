import hasRole from './permission/hasRole'
import hasPermi from './permission/hasPermi'
import copyText from './common/copyText'
import btn from './button/btn.js'
import ellipsis from './text/hasEllipsis.js'
import ellipsisTooltips from './text/ellipsisTooltips.js'

export default function directive(app){
  app.directive('hasRole', hasRole)
  app.directive('hasPermi', hasPermi)
  app.directive('copyText', copyText)
  app.directive('btn', btn)
  app.directive('hasEllipsis', ellipsis)
  app.directive('ellipsisTooltips', ellipsisTooltips)
}