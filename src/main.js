import { createApp } from 'vue'

import Cookies from 'js-cookie'

import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import locale from 'element-plus/es/locale/lang/zh-cn'

import '@/assets/styles/index.scss' // global css

import App from './App'
import store from './store'
import router from './router'
import directive from './directive' // directive

// 注册指令
import plugins from './plugins' // plugins
import { download } from '@/utils/request'

// svg图标
import 'virtual:svg-icons-register'
import SvgIcon from '@/components/SvgIcon'
import elementIcons from '@/components/SvgIcon/svgicon'

import './permission' // permission control

import { useDict } from '@/utils/dict'
import {
  parseTime,
  resetForm,
  addDateRange,
  handleTree,
  selectDictLabel,
  selectDictLabels,
} from '@/utils/ruoyi'
import aegis from '@/utils/aegis'

// 分页组件
import Pagination from '@/components/Pagination'
import PaginationFloatBar from '@/components/Pagination/PaginationFloatBar.vue'
// 自定义表格工具组件
import RightToolbar from '@/components/RightToolbar'
// 富文本组件
import Editor from '@/components/Editor'
// 自定义表单组件
// 文件上传组件
import FileUpload from '@/components/FileUpload'
import DragUploadDialog from '@/components/FileUpload/dragUploadDialog'
// 图片上传组件
import ImageUpload from '@/components/ImageUpload'
// 图片预览组件
import ImagePreview from '@/components/ImagePreview'
// 自定义树选择组件
import TreeSelect from '@/components/TreeSelect'
// 字典标签组件
import DictTag from '@/components/DictTag'
import bizModelType from '@/components/DictTag/bizModelType'
import bizModelTypeNew from '@/components/DictTag/bizModelTypeNew.vue'
import bizModelPlatform from '@/components/DictTag/bizModelPlatform'
import bizModelCooperation from '@/components/DictTag/bizModelCooperation'
import bizModelAgeGroup from '@/components/DictTag/bizModelAgeGroup'
import bizNation from '@/components/DictTag/bizNation'
import bizModelStatus from '@/components/DictTag/bizModelStatus'
import bizModelFamilyType from '@/components/DictTag/bizModelFamilyType'
import modelScore from '@/components/DictTag/modelScore'

const app = createApp(App)

// 全局方法挂载
app.config.globalProperties.useDict = useDict
app.config.globalProperties.download = download
app.config.globalProperties.parseTime = parseTime
app.config.globalProperties.resetForm = resetForm
app.config.globalProperties.handleTree = handleTree
app.config.globalProperties.addDateRange = addDateRange
app.config.globalProperties.selectDictLabel = selectDictLabel
app.config.globalProperties.selectDictLabels = selectDictLabels
app.config.globalProperties.$picUrl = import.meta.env.VITE_APP_FILE_HTTP_PATH

// 全局组件挂载
app.component('DictTag', DictTag)
app.component('BizModelType', bizModelType)
app.component('BizModelTypeNew', bizModelTypeNew)
app.component('BizModelPlatform', bizModelPlatform)
app.component('BizModelCooperation', bizModelCooperation)
app.component('BizModelAgeGroup', bizModelAgeGroup)
app.component('BizNation', bizNation)
app.component('BizModelStatus', bizModelStatus)
app.component('BizModelFamilyType', bizModelFamilyType)
app.component('ModelScore', modelScore)
app.component('Pagination', Pagination)
app.component('PaginationFloatBar', PaginationFloatBar)
app.component('TreeSelect', TreeSelect)
app.component('FileUpload', FileUpload)
app.component('DragUploadDialog', DragUploadDialog)
app.component('ImageUpload', ImageUpload)
app.component('ImagePreview', ImagePreview)
app.component('RightToolbar', RightToolbar)
app.component('Editor', Editor)

app.use(router)
app.use(store)
app.use(plugins)
app.use(elementIcons)
app.component('svg-icon', SvgIcon)

directive(app)

app.config.errorHandler = function (err, vm, info) {
  if (import.meta.env.VITE_APP_ENV === 'development') {
    console.error(err)
    console.log(`Error: ${err}\nInfo: ${info}`)
    return
  }
  if (err instanceof Error) {
    aegis.error(`Error: ${err.toString()}\nStack: ${err.stack}\nInfo: ${info}`)
  } else {
    aegis.error(`Error: ${err}\nInfo: ${info}`)
  }
}

// 使用element-plus 并且设置全局的大小
app.use(ElementPlus, {
  locale: locale,
  // 支持 large、default、small
  size: Cookies.get('size') || 'default',
})

app.mount('#app')
