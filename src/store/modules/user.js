import { login, logout, getInfo } from '@/api/login'
import { getToken, setToken, removeToken } from '@/utils/auth'
import defAva from '@/assets/images/profile.jpg'
import watermark from '@/hooks/watermark'
import { useWebsocket } from '@/hooks/useWebsocket'
import { fingerprint, waitFingerprint } from '@/utils/fingerprintjs2'
const { initWebSocket, closeConnect } = useWebsocket()
import { useRemindBox } from '@/hooks/useRemindBox'
const { remindVisible, remindUrl } = useRemindBox()

const useUserStore = defineStore('user', {
  state: () => ({
    token: getToken(),
    id: '',
    name: '',
    avatar: '',
    roles: [],
    permissions: [],
    isAdmin: false,
    worker: null,
    workbenchRoleType: 0,
  }),
  actions: {
    // 登录
    login(userInfo, type = 0) {
      const code = userInfo.code
      if (type === 0) {
        // 账号密码登录
        const username = userInfo.username.trim()
        const password = userInfo.password
        const uuid = userInfo.uuid
        return new Promise((resolve, reject) => {
          login(username, password, code, uuid)
            .then(res => {
              let data = res.data
              setToken(data.access_token)
              this.token = data.access_token
              resolve()
            })
            .catch(error => {
              reject(error)
            })
        })
      } else {
        // 手机验证码登录
        // const phonenumber = userInfo.phonenumber
        // return new Promise((resolve, reject) => {
        // phoneLogin(phonenumber, code).then(res => {
        //   let data = res.data
        //   setToken(data.access_token)
        //   this.token = data.access_token
        //   resolve()
        // }).catch(error => {
        //   reject(error)
        // })
        // })
        return Promise.reject()
      }
    },
    // 获取用户信息
    getInfo() {
      return new Promise((resolve, reject) => {
        getInfo()
          .then(res => {
            const user = res.user
            const avatar = user.avatar == '' || user.avatar == null ? defAva : user.avatar

            if (res.roles && res.roles.length > 0) {
              // 验证返回的roles是否是一个非空数组
              this.roles = res.roles
              this.permissions = res.permissions
            } else {
              this.roles = ['ROLE_DEFAULT']
            }
            this.connectSharedWorker()
            this.id = user.userId
            this.name = user.userName
            this.avatar = avatar
            this.isAdmin = user.admin
            this.workbenchRoleType = user.workbenchRoleType
            watermark.setText(user.userName)
            resolve(res)
          })
          .catch(error => {
            reject(error)
          })
      })
    },
    // 退出系统
    logOut() {
      return new Promise((resolve, reject) => {
        logout(this.token)
          .then(() => {
            this.token = ''
            this.roles = []
            this.permissions = []
            removeToken()
            closeConnect()
            resolve()
          })
          .catch(error => {
            reject(error)
          })
      })
    },
    //sharedWorker连接
    connectSharedWorker() {
      let worker = null
      if (typeof SharedWorker !== 'undefined') {
        this.worker = new SharedWorker(new URL('../../../src/assets/shared-worker.js', import.meta.url).href)
        let token = getToken()
        this.worker.port.postMessage({
          data: [
            { key: 'token', value: token },
            { key: 'fingerprint', value: fingerprint.value },
            { key: 'url', value: import.meta.env.VITE_APP_WS_URL },
            // { key: 'url', value: 'wss://api.fklwnkbum.uat.woniu.video/ws-endpoint' },
            // { key: 'url', value: 'ws://10.160.0.1:19029/ws-endpoint' },
            // { key: 'url', value: 'ws://192.168.20.2:19019/ws-endpoint' },
          ],
        })
        // 启动端口通信
        // 监听来自 SharedWorker 的消息
        this.worker.port.onmessage = e => {
          if (e.data.type == 1) {
            remindVisible.value = true
          }
          if (e.data.url) {
            remindUrl.value = e.data.url
          }
          if (e.data.type == 'CLOSE_REMIND') {
            remindVisible.value = false
            remindUrl.value = ''
          }
        }
      } else {
      }
      // initWebSocket(import.meta.env.VITE_APP_WS_URL)
    },
    //发送消息
    sendMessage() {
      //关闭弹窗
      if (this.worker) {
        this.worker.port.postMessage({
          type: 'CLOSE_REMIND',
        })
      }
    },
  },
})

export default useUserStore
