import request from '@/utils/request'


//获取帮助列表
export function getHelpList(params) {
  return request({
    url: '/biz/text/help/list',
    method: 'get',
    params
  })
}

//添加帮助数据 /biz/text/help/add
export function addHelpData(data) {
  return request({
    url: '/biz/text/help/add',
    method: 'post',
    data
  })
}

//修改帮助数据 /biz/text/help/update
export function editHelpData(data) {
  return request({
    url: '/biz/text/help/edit',
    method: 'put',
    data
  })
}

//删除帮助数据 /biz/text/help/delete
export function deleteHelpData(params) {
  return request({
    url: '/biz/text/help/delete',
    method: 'delete',
    params
  })
}

//获取帮助详情 /biz/text/help/{id}
export function getHelpDetail(id) {
    return request({
        url: '/biz/text/help/' + id,
        method: 'get'
    })
}

//修改状态 /biz/text/help/updateStatus
export function updateStatus(data) {
  return request({
    url: '/biz/text/help/updateStatus',
    method: 'put',
    data
  })
}