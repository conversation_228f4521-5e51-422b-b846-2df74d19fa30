import request from '@/utils/request'

// 查询模特信息列表
export function getModelList(params) {
  return request({
    url: '/biz/model/list',
    method: 'get',
    params
  })
}

// 查询模特信息列表-运营端 更换模特 /biz/model/edit-order-change-model-list
export function getChangeModelList(params) {
  return request({
    url: '/biz/model/edit-order-change-model-list',
    method: 'get',
    params
  })
}

// 查询模特详情信息
export function getModelDetail(id) {
  return request({
    url: `/biz/model/${id}`,
    method: 'get'
  })
}

/**
 * 获取模特信息详细信息
 * @param id 
 * @returns 
 */
export function modelMerchantDetails(id) {
  return request({
    url: `/biz/business/account/model/${id}`,
    method: 'get',
  })
}

// 新增模特
export function addModel(data) {
  return request({
    url: '/biz/model',
    method: 'post',
    data
  })
}

// 修改模特
export function editModel(data) {
  return request({
    url: '/biz/model',
    method: 'put',
    data
  })
}

// /biz/model/model-change-record/{modelId} 模特修改记录
export function getModelChangeRecord(modelId) {
  return request({
    url: `/biz/model/model-change-record/${modelId}`,
    method: 'get',
  })
}
// 修改模特关联人员
export function updateRelevance(data) {
  return request({
    url: '/biz/model/update-relevance',
    method: 'post',
    data
  })
}
// 模特启用禁用
export function modelEnable(id, status) {
  return request({
    url: `/biz/model/enable/${id}/${status}`,
    method: 'put',
  })
}
// 更新模特状态
export function updateModelStatus(data) {
  return request({
    url: `/biz/model/update-model-status`,
    method: 'put',
    data
  })
}
// 创建模特后台链接
export function createBackstageLink(id) {
  return request({
    url: `/biz/model/background-link/${id}`,
    method: 'post',
  })
}

// 置顶模特
export function modelTop(id) {
  return request({
    url: `/biz/model/top/${id}`,
    method: 'put',
  })
}

// 置顶模特数量统计
export function modelTopCount() {
  return request({
    url: `/biz/model/top-count`,
    method: 'get',
  })
}

// 修改模特排序
export function modelUpdateSort(data) {
  return request({
    url: `/biz/model/update-sort`,
    method: 'put',
    data
  })
}

// 获取当前分类的指定级别标签
export function modelCategorySelectRank(params) {
  return request({
    url: '/biz/tag/rank',
    method: 'get',
    params
  })
}

// 获取模特姓名和账户名组合列表
export function modelNameAccountSelect(params) {
  return request({
    url: '/biz/model/name-account-options',
    method: 'get',
    params
  })
}

// 获取模特擅长品类下拉框/模特标签下拉框
export function modelCategorySelect(params) {
  return request({
    url: '/biz/tag/list',
    method: 'get',
    params
  })
}

// 获取模特擅长品类下拉框/模特标签下拉框
export function modelCategoryTagSelect(params) {
  return request({
    url: '/biz/tag/tag-select',
    method: 'get',
    params
  })
}

// 添加预选模特列表
export function addPreselectModelList(params) {
  return request({
    url: `/biz/model/add-preselect-model-list`,
    method: 'get',
    params
  })
}

// 模特列表-获取关联人员下拉框（运营端）
export function modelPersonsSelect(params) {
  return request({
    url: `/biz/model/model-persons-select`,
    method: 'get',
    params
  })
}

// 模特列表-获取模特家庭列表
export function modelFamilyList(params) {
  return request({
    url: `/biz/model/family/list`,
    method: 'get',
    params
  })
}

// 模特列表-添加家庭成员-模特列表下拉框
export function modelSelectList(params) {
  return request({
    url: `/biz/model/select-list`,
    method: 'get',
    params
  })
}

// 模特列表-添加家庭成员
export function addFamilyModel(data) {
  return request({
    url: `/biz/model/family/addFamilyModel`,
    method: 'post',
    data
  })
}

// 模特列表-删除家庭成员
export function deleteFamilyModel(data) {
  return request({
    url: `/biz/model/family/deleteFamilyModel`,
    method: 'delete',
    data
  })
}


//更新模特提示语 /biz/model/getMsg/{modelId}
export function getModelMsg(modelId) {
  return request({
    url: `/biz/model/getMsg/${modelId}`,
    method: 'get',
  })
}