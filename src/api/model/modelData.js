import request from '@/utils/request'

///biz/model-data-table/list
export function modelDataTablelist(params) {
  return request({
    url: '/biz/model-data-table/list',
    method: 'get',
    params,
  })
}

///biz/model-data-table/list/service-select
export function modelDataTablelistServiceSelect() {
  return request({
    url: '/biz/model-data-table/list/service-select',
    method: 'get',
  })
}

///biz/model-data-table/list/developer-select
export function modelDataTablelistDeveloperSelect() {
  return request({
    url: '/biz/model-data-table/list/developer-select',
    method: 'get',
  })
}

//添加备注/biz/model-data-table/add-remark
export function modelDataTableAddRemark(data) {
  return request({
    url: '/biz/model-data-table/add-remark',
    method: 'post',
    data,
  })
}

///biz/model-data-table/detail
export function modelDataTableDetail(params) {
  return request({
    url: '/biz/model-data-table/detail',
    method: 'get',
    params,
  })
}

//排单记录/order/model-data-table/order-scheduled-record
export function modelDataTableOrderScheduledRecord(params) {
  return request({
    url: '/order/model-data-table/order-scheduled-record',
    method: 'get',
    params,
  })
}
//统计/order/model-data-table/order-scheduled-record/tag-count
export function modelDataTableOrderScheduledRecordTagCount(params) {
  return request({
    url: '/order/model-data-table/order-scheduled-record/tag-count',
    method: 'get',
    params,
  })
}

///biz/model-data-table/remark-list
export function modelDataTableRemarkList(params) {
  return request({
    url: '/biz/model-data-table/remark-list',
    method: 'get',
    params,
  })
}

///biz/model-data-table/get-last-update-time
export function modelDataTableGetLastUpdateTime(params) {
  return request({
    url: '/biz/model-data-table/get-last-update-time',
    method: 'get',
    params,
  })
}