import request from '@/utils/request'

/**
 * 查询页面配置列表
 * @param {*} params 查询参数
 * @returns 
 */
export function pageConfigList(params) {
  return request({
    url: '/biz/page/config/list',
    method: 'get',
    params
  })
}

/**
 * 获取精选案例配置详细信息
 * @param {*} id 页面配置ID
 * @returns 
 */
export function getChooseCaseInfo(id) {
  return request({
    url: `/biz/page/config/getChooseCaseInfo/${id}`,
    method: 'get',
  })
}

/**
 * 获取页面配置详细信息
 * @param {*} id 页面配置ID
 * @returns 
 */
export function getHomePageInfo(id) {
  return request({
    url: `/biz/page/config/getHomePage/${id}`,
    method: 'get',
  })
}

/**
 * 保存精选案例配置
 * @param {*} data 页面配置信息
 * @returns 
 */
export function saveChooseCase(data) {
  return request({
    url: `/biz/page/config/saveChooseCase`,
    method: 'post',
    data
  })
}

/**
 * 保存首页配置
 * @param {*} data 页面配置信息
 * @returns 
 */
export function saveHomePage(data) {
  return request({
    url: `/biz/page/config/saveHomePage`,
    method: 'post',
    data
  })
}