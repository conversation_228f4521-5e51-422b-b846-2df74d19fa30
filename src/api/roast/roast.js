import request from '@/utils/request'

//获取吐槽列表 /order/roast/list
export function getRoastList(params) {
  return request({
    url: '/order/roast/list',
    method: 'get',
    params,
  })
}

// 处理 /order/handleRoast
export function handleRoast(data) {
  return request({
    url: '/order/roast/handle',
    method: 'post',
    data,
  })
}

// 获取中文部客服下拉 /order/roast/contact-select
export function getContactSelect() {
  return request({
    url: '/order/roast/contact-select',
    method: 'get',
  })
}

// 获取英文部客服下拉 /order/roast/issue-select
export function getIssueSelect() {
  return request({
    url: '/order/roast/issue-select',
    method: 'get',
  })
}

//反馈素材查看 /order/feedback/roastOrderFeedBackList
export function getRoastOrderFeedBackList(params) {
  return request({
    url: '/order/feedback/roastOrderFeedBackList',
    method: 'get',
    params,
  })
}

//吐槽统计 /order/roast/statistics
export function getRoastStatistics() {
  return request({
    url: '/order/roast/statistics',
    method: 'get',
  })
}
