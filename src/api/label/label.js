import request from '@/utils/request'

//查询标签分类列表
export function getLabelList(params) {
  return request({
    url: `/biz/tag/list`,
    method: 'get',
    params,
  })
}

//获取模特标签下拉框
export function modelLabelSelect(id) {
  return request({
    url: `/biz/tag/stair/${id}`,
    method: 'get',
  })
}

//新增類目標簽 /biz/tag/add-category-tag
export function addCategoryTag(data) {
  return request({
    url: `/biz/tag/add-category-tag`,
    method: 'post',
    data,
  })
}

//新增模特标签 /biz/tag/add-model-tag
export function addModelTag(data) {
  return request({
    url: `/biz/tag/add-model-tag`,
    method: 'post',
    data,
  })
}

//查询类目標簽列表 /biz/tag/category-tag-list
export function getCategoryTagList(params) {
  return request({
    url: `/biz/tag/category-tag-list`,
    method: 'get',
    params,
  })
}
//查询模特标签列表 /biz/tag/model-tag-list
export function getModelTagList(params) {
  return request({
    url: `/biz/tag/model-tag-list`,
    method: 'get',
    params,
  })
}

//删除类目标签 /biz/tag/delete-category-tag
export function deleteCategoryTag(params) {
  return request({
    url: `/biz/tag/delete-category-tag`,
    method: 'DELETE',
    params,
  })
}

//删除模特标签 /biz/tag/delete-model-tag
export function deleteModelTag(params) {
  return request({
    url: `/biz/tag/delete-model-tag`,
    method: 'DELETE',
    params,
  })
}

//修改类目标签、启用禁用类目标签状态 /biz/tag/edit-category-tag
export function editCategoryTag(data) {
  return request({
    url: `/biz/tag/edit-category-tag`,
    method: 'put',
    data,
  })
}

//修改模特标签、启用禁用模特标签状态 /biz/tag/edit-model-tag
export function editModelTag(data) {
  return request({
    url: `/biz/tag/edit-model-tag`,
    method: 'put',
    data,
  })
}

//新增标签
export function addtoLabel(data) {
  return request({
    url: '/biz/tag',
    method: 'post',
    data,
  })
}

// 删除标签
export function delLabel(id) {
  return request({
    url: `/biz/tag?id=${id}`,
    method: 'DELETE',
  })
}

// 获取标签详细信息
export function getLabelDetail(id) {
  return request({
    url: `/biz/tag/${id}`,
    method: 'get',
  })
}

// 修改标签
export function editLabel(data) {
  return request({
    url: '/biz/tag',
    method: 'put',
    data,
  })
}

// 修改标签排序
export function tagSort(data) {
  return request({
    url: '/biz/tag/sort',
    method: 'put',
    data,
  })
}

//修改标签排序 /biz/tag/category-tag-sort
export function categoryTagSort(data) {
  return request({    
    url: `/biz/tag/category-tag-sort`,
    method: 'put',
    data,
  })
}
//模特标签排序 /biz/tag/model-tag-sort
export function modelTagSort(data) {
  return request({    
    url: `/biz/tag/model-tag-sort`,
    method: 'put',
    data,
  })
}

// 获取标签所有父类标签
export function getParentLabel(categoryId) {
  return request({
    url: `/biz/tag/stair/${categoryId}`,
    method: 'GET',
  })
}
