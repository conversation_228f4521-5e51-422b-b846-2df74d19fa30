import request from '@/utils/request'

//会员活动 /biz/business/member/activity/backend/list
export function getMemberActivityList() {
  return request({
    url: '/biz/business/member/activity/backend/list',
    method: 'get',
  })
}

//详情 /biz/business/member/activity/info/{id}
export function getMemberActivityInfo(id) {
  return request({
    url: `/biz/business/member/activity/info/${id}`,
    method: 'get',
  })
}

//保存 /biz/business/member/activity/saveMemberActivity
export function saveMemberActivity(data) {
  return request({
    url: '/biz/business/member/activity/saveMemberActivity',
    method: 'post',
    data,
  })
}

//修改 /biz/business/member/activity/updateMemberActivity
export function updateMemberActivity(data) {
  return request({
    url: '/biz/business/member/activity/updateMemberActivity',
    method: 'put',
    data,
  })
}

//更新 /biz/business/member/activity/updateStatus
export function updateMemberActivityStatus(data) {
  return request({
    url: '/biz/business/member/activity/updateStatus',
    method: 'put',
    data,
  })
}
