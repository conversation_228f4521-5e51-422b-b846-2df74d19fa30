import request from '@/utils/request'

// 查询市场渠道列表
export function getMarketList(params) {
  return request({
    url: '/biz/marketing-channel/list',
    method: 'get',
    params
  })
}

//查看市场渠道详情
export function getMarketDetail(id) {
  return request({
    url: '/biz/marketing-channel/detail/' + id,
    method: 'get'
  })
}

//编辑市场渠道 /biz/marketing-channel/edit
export function editMarket(data) {
  return request({
    url: '/biz/marketing-channel/edit',
    method: 'put',
    data
  })
}

//新增市场渠道 
export function addMarket(data) {
  return request({
    url: '/biz/marketing-channel/save',
    method: 'post',
    data
  })
}

//下载物流 /biz/marketing-channel/download-material/{id}
export function downloadMaterial(id) {
  return request({
    url: '/biz/marketing-channel/download-material/' + id,
    method: 'get',
    // responseType: 'blob'
  })
}

//通过code获取企微二维码 /biz/marketing-channel/qrcode/{dedicatedLinkCode}
export function getWechatQrcode(code) {
  return request({
    url: '/biz/marketing-channel/qrcode/' + code,
    method: 'get'
  })
}

//导出接口 /biz/marketing-channel/export
export function exportMarket(params) {
  return request({
    url: '/biz/marketing-channel/export',
    method: 'post',
    params
  })
}

//创建人列表市场渠道 /biz/marketing-channel/createUserList
export function getCreateUserList() {
  return request({
    url: '/biz/marketing-channel/createUserList',
    method: 'get',
  })
}

//市场渠道统计 /biz/marketing-channel/statistics
export function getMarketStatistics(params) {
  return request({    
    url: '/biz/marketing-channel/statistics',
    method: 'get',
    params
  })
}

//获取市场渠道邀请记录 /biz/marketing-channel/getChannelInviteList
export function getChannelInviteList(params) {
  return request({
    url: '/biz/marketing-channel/getChannelInviteList',
    method: 'get',
    params
  })
}

//账号信息/biz/marketing-channel/backend/bizUserList
export function getAccountInfo(params) {
  return request({
    url: '/biz/marketing-channel/backend/bizUserList',
    method: 'get',
    params
  })
}

//获取邀请渠道统计 /biz/marketing-channel/getChannelInviteList/statistics
export function getInviteStatistics(params) {
  return request({
    url: '/biz/marketing-channel/getChannelInviteList/statistics',
    method: 'get',
    params
  })
}