import request from '@/utils/request'

// 获取裂变拉新结算列表
export function getZhongCaoWithdrawalList(params) {
  return request({
    url: '/biz/member-seed-record/backend/queryMemberSeedRecordWithdrawalList',
    method: 'get',
    params,
  })
}

// 获取裂变拉新结算数量统计
export function getFissionCountStatisticsVO() {
  return request({
    url: '/biz/member-seed-record/backend/getFissionCountStatisticsVO',
    method: 'get',
  })
}

// 会员种草记录金额统计
export function getFissionStatisticsVO() {
  return request({
    url: '/biz/member-seed-record/backend/getFissionStatisticsVO',
    method: 'get',
  })
}

// 获取裂变结算记录
export function queryFissionSettleRecordList(params) {
  return request({
    url: `/biz/member-seed-record/backend/queryFissionSettleRecordList`,
    method: 'get',
    params
  })
}

// 获取提现记录详情
export function getMemberSeedRecordWithdrawalDetail(id) {
  return request({
    url: `/biz/member-seed-record/backend/getMemberSeedRecordWithdrawalDetail/${id}`,
    method: 'get',
  })
}

// 审核种草结算记录
export function auditMemberSeedRecord(data) {
  return request({
    url: `/biz/member-seed-record/backend/auditMemberSeedRecord`,
    method: 'put',
    data
  })
}

// 打款账号列表
export function getPayAccountList() {
  return request({
    url: `/biz/member-seed-record/backend/payAccountList`,
    method: 'get',
  })
}