import request from '@/utils/request'

//分销渠道列表 /biz/marketing/center/distribution/backend/list
export function getDistributionList(params) {
  return request({
    url: '/biz/marketing/center/distribution/backend/list',
    method: 'get',
    params,
  })
}

//分销渠道统计 /biz/marketing/center/distribution/backend/statistics
export function getDistributionStatistics(params) {
  return request({
    url: '/biz/marketing/center/distribution/backend/statistics',
    method: 'get',
    params,
  })
}

//分销渠道详情 /biz/marketing/center/distribution/backend/{id}
export function getDistributionDetail(id) {
  return request({
    url: `/biz/marketing/center/distribution/backend/${id}`,
    method: 'get',
  })
}
//新增 /biz/marketing/center/distribution/backend/saveDistribution
export function saveDistribution(data) {
  return request({
    url: '/biz/marketing/center/distribution/backend/saveDistribution',
    method: 'post',
    data,
  })
}

//修改 /biz/marketing/center/distribution/backend/editDistribution
export function editDistribution(data) {
  return request({
    url: '/biz/marketing/center/distribution/backend/editDistribution',
    method: 'put',
    data,
  })
}

//修改分销渠道状态 /biz/marketing/center/distribution/backend/updateStatus
export function updateStatus(data) {
  return request({
    url: '/biz/marketing/center/distribution/backend/updateStatus',
    method: 'put',
    data,
  })
}

//邀请记录列表 /biz/marketing/center/distribution/backend/inviteList
export function getInviteList(params) {
  return request({
    url: '/biz/marketing/center/distribution/backend/inviteList',
    method: 'get',
    params,
  })
}

//获取私密信息 /biz/marketing/center/distribution/backend/privacy/{id}
export function getPrivacy(id) {
  return request({
    url: `/biz/marketing/center/distribution/backend/privacy/${id}`,
    method: 'get',
  })
}

//获取会员折扣  /system/config/getMemberDiscount
export function getMemberDiscount() {
  return request({
    url: '/system/config/getMemberDiscount',
    method: 'get',
  })
}

//修改会员折扣 /biz/marketing/center/distribution/editMemberDiscount
export function updateMemberDiscount(data) {
  return request({
    url: '/biz/marketing/center/distribution/editMemberDiscount',
    method: 'put',
    data,
  })
}

//修改渠道信息记录
export function getDistributionChannelDiscountLogList(params) {
  return request({
    url: '/biz/marketing/center/distribution/getDistributionChannelDiscountLogList',
    method: 'get',
    params
  })
}

//分销渠道列表统计 /biz/marketing/center/distribution/backend/list/statistics
export function getDistributionListStatistics(params) {
  return request({
    url: '/biz/marketing/center/distribution/backend/list/statistics',
    method: 'get',
    params,
  })
}

//预览 /biz/marketing/center/distribution/preview/poster/{id}
export function previewPoster(id) {
  return request({
    url: `/biz/marketing/center/distribution/preview/poster/${id}`,
    method: 'get',
    responseType: 'blob',
  })
}

//查看账号信息 分销渠道 /biz/marketing/center/distribution/backend/bizUserList
export function getBizUserList(params) {
  return request({
    url: '/biz/marketing/center/distribution/backend/bizUserList',
    method: 'get',
    params,
  })  
}
//获取创建人列表 /biz/marketing/center/distribution/createUserList
export function getCreateUserList() {
  return request({
    url: '/biz/marketing/center/distribution/createUserList',
    method: 'get',
  })
}
