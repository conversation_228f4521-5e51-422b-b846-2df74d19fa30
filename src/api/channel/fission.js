import request from '@/utils/request'

//裂变渠道列表 /biz/fission-channel/backend/list
export function getBackendList(params) {
  return request({
    url: '/biz/fission-channel/backend/list',
    method: 'get',
    params,
  })
}

//渠道统计 /biz/fission-channel/backend/statistics
export function getBackendStatistics(params) {
  return request({
    url: '/biz/fission-channel/backend/statistics',
    method: 'get',
    params,
  })
}

//列表统计 /biz/fission-channel/backend/list/statistics
export function getBackendListStatistics(params) {
  return request({
    url: '/biz/fission-channel/backend/list/statistics',
    method: 'get',
    params,
  })
}

// 获取会员裂变折扣 /system/config/getFissionMemberDiscount
export function getFissionMemberDiscount() {
  return request({
    url: '/system/config/getFissionMemberDiscount',
    method: 'get',
  })
}

//修改裂变会员折扣 /system/config/editFissionMemberDiscount
export function editFissionMemberDiscount(data) {
  return request({
    url: '/system/config/editFissionMemberDiscount',
    method: 'put',
    data,
  })
}

//裂变渠道详情 /biz/fission-channel/backend/{id}
export function getBackendDetail(id) {
  return request({
    url: `/biz/fission-channel/backend/${id}`,
    method: 'get',
  })
}

//邀请记录 /biz/fission-channel/backend/inviteList
export function getBackendInviteList(params) {
  return request({
    url: '/biz/fission-channel/backend/inviteList',
    method: 'get',
    params,
  })
}

//会员 /biz/member-channel/fission/member-channel-detail/{id}
export function getMemberChannelDetail(id) {
  return request({
    url: `/biz/member-channel/fission/member-channel-detail/${id}`,
    method: 'get',
  })
}

//裂变会员记录 /biz/member-channel/fission/member-channel-list
export function getMemberChannelList(params) {
  return request({
    url: '/biz/member-channel/fission/member-channel-list',
    method: 'get',
    params,
  })
}

//获取裂变会员折扣 /biz/fission-channel/getFissionMemberDiscount
export function getFissionMemberDiscounts() {
  return request({
    url: '/biz/fission-channel/getFissionMemberDiscount',
    method: 'get',
  })
}

//修改折扣 /biz/fission-channel/editFissionMemberDiscount
export function editFissionMemberDiscounts(data) {
  return request({
    url: '/biz/fission-channel/editFissionMemberDiscount',
    method: 'put',
    data,
  })
}

//获取裂变折扣 /biz/fission-channel/getFissionDiscountV1
export function getFissionDiscountV1() {
  return request({
    url: '/biz/fission-channel/getFissionDiscountV1',
    method: 'get',
  })
}

//通过活动类型获取修改记录 /order/promotion/promotion-activity-amendment-record-list
export function getDistributionChannelDiscountLogList(params) {
  return request({
    url: '/order/promotion/promotion-activity-amendment-record-list',
    method: 'get',
    params
  })
}

//修改裂变 折扣 /biz/fission-channel/editFissionChannelDiscount
export function editFissionChannelDiscount(data) {
  return request({
    url: '/biz/fission-channel/editFissionChannelDiscount',
    method: 'put',
    data,
  })
}

//修改裂变渠道状态 /biz/member-seed-record/backend/updateStatus
export function updateFissionChannelStatus(data) {
  return request({
    url: '/biz/member-seed-record/backend/updateStatus',
    method: 'put',
    data,
  })
}

//查看海报 /biz/fission-channel/preview/poster/{id}
export function previewPoster(id) {
  return request({
    url: `/biz/fission-channel/preview/poster/${id}`,
    method: 'get',
    responseType: 'blob',
  })
}

//裂变渠道结算

///biz/member-channel/fission/member-channel-list 列表
export function getMemberChannelLists(params) {
  return request({
    url: '/biz/member-channel/fission/member-channel-list',
    method: 'get',
    params,
  })
}

//结算记录  /biz/member-channel/fission/member-channel-detail/{id}
export function getMemberChannelDetailss(id) {
  return request({
    url: `/biz/member-channel/fission/member-channel-detail/${id}`,
    method: 'get',
  })
}
//结算 /biz/member-channel/fission/member-channel-settlement
export function memberChannelSettlement(data) {
  return request({
    url: '/biz/member-channel/fission/member-channel-settlement',
    method: 'post',
    data,
  })
}
