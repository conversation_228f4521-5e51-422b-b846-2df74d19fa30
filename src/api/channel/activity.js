import request from '@/utils/request'

// 渠道活动列表
export function channelActivityList(params) {
  return request({
    url: '/biz/marketing/center/distribution/activity',
    method: 'get',
    params,
  })
}

// 新增渠道活动
export function addChannelActivity(data) {
  return request({
    url: '/biz/marketing/center/distribution/activity',
    method: 'post',
    data,
  })
}

// 编辑渠道活动
export function editChannelActivity(data) {
  return request({
    url: '/biz/marketing/center/distribution/activity',
    method: 'put',
    data,
  })
}

// 渠道活动详情
export function channelActivityDetail(id) {
  return request({
    url: `/biz/marketing/center/distribution/activity/${id}`,
    method: 'get',
  })
}

// 渠道信息检索
export function channelInfoList(params) {
  return request({
    url: `/biz/marketing/center/distribution/activity/channel`,
    method: 'get',
    params
  })
}
