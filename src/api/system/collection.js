import request from '@/utils/request'

//获取收款配置 /order/payee/list
export function getPayeeList() {
  return request({
    url: '/order/payee/list',
    method: 'get',
  })
}

//获取主体 /order/payee/type/{#type}/list
export function getSubjectList(type) {
  return request({
    url: `/order/payee/type/${type}/list`,
    method: 'get',
  })
}

//新增主体信息 /order/payee/info/{#id}
export function addSubjectInfo(data) {
  return request({
    url: `/order/payee`,
    method: 'post',
    data,
  })
}

//更新主体信息 /order/payee/info/{#id}
export function updateSubjectInfo(id,data) {
  return request({
    url: `/order/payee/info/${id}`,
    method: 'put',
    data,
  })
}

//历史变更记录 /order/payee/changelog/type/{#type}
export function getChangeLogList(type) {
  return request({
    url: `/order/payee/changelog/type/${type}`,
    method: 'put',
  })
}


//变更主体 /order/payee/active/{#id}
export function getActivePayee(id) {
  return request({
    url: `/order/payee/active/${id}`,
    method: 'put',
  })
}


//获取收款人信息 /order/payee/info/{id}
export function getPayeeInfo(id) {
  return request({
    url: `/order/payee/info/${id}`,
    method: 'get',
  })
}