import request from '@/utils/request'
//获取文本列表
export function getTextList(params) {
  return request({
    url: '/biz/text/list',
    method: 'get',
    params
  })
}
//添加文本
export function addText(data) {
  return request({
    url: '/biz/text/add',
    method: 'post',
    data
  })
}
//编辑文本
export function editText(data) {
    return request({
        url: '/biz/text/edit',
        method: 'put',
        data
    })
}
//删除文本
export function deleteText(params) {
    return request({
        url: '/biz/text/delete',
        method: 'DELETE',
        params
    })
}
//获取文本详情
export function getTextDetail(id) {
    return request({
        url: `/biz/text/${id}`,
        method: 'get',
        
    })
}