import request from '@/utils/request'

/**
 * 工作台-统计
 * @returns 
 */
export function workbenchVideoStatistics(params) {
  return request({
    url: '/order/backend/workbench/videoStatistics',
    method: 'get',
    params
  })
}

/**
 * 工作台-中文部-待审核列表
 * @returns 
 */
export function workbenchUnConfirmList() {
  return request({
    url: '/order/backend/workbench/chinese/unConfirmList',
    method: 'get',
  })
}

/**
 * 工作台-英文部-交易关闭订单列表
 * @returns 
 */
export function workbenchCloseList() {
  return request({
    url: '/order/backend/workbench/english/closeList',
    method: 'get',
  })
}

/**
 * 工作台-英文部-暂停匹配列表
 * @returns 
 */
export function workbenchPauseMatchList() {
  return request({
    url: '/order/backend/workbench/english/pauseMatchList',
    method: 'get',
  })
}

/**
 * 工作台-英文部-待匹配列表
 * @returns 
 */
export function workbenchUnMatchList() {
  return request({
    url: '/order/backend/workbench/english/unMatchList',
    method: 'get',
  })
}

/**
 * 工作台-财务部-会员待审核列表
 * @returns 
 */
export function workbenchFinanceMemberList() {
  return request({
    url: '/order/backend/workbench/finance/workbenchFinanceMemberList',
    method: 'get',
  })
}

/**
 * 工作台-财务部-视频待审核列表
 * @returns 
 */
export function workbenchFinanceVideoList() {
  return request({
    url: '/order/backend/workbench/finance/workbenchFinanceVideoList',
    method: 'get',
  })
}

/**
 * 工作台-剪辑部-被拒绝任务单
 * @returns 
 */
export function workbenchSelectRefuseTaskList() {
  return request({
    url: '/order/backend/workbench/edit/selectRefuseTaskList',
    method: 'get',
  })
}

/**
 * 工作台-剪辑部-待处理任务单
 * @returns 
 */
export function workbenchSelectUnHandleTaskList() {
  return request({
    url: '/order/backend/workbench/edit/selectUnHandleTaskList',
    method: 'get',
  })
}

/**
 * 工作台-剪辑部-待领取素材列表
 * @returns 
 */
export function workbenchUnGetList() {
  return request({
    url: '/order/backend/workbench/edit/unGetList',
    method: 'get',
  })
}