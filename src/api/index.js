import request from '@/utils/request'
import { ElMessage } from 'element-plus'

/**
 * 上传文件
 * @param {*} data FormData对象
 * @returns 
 */
export function uploadFile(data) {
  return request({
    url: '/file/upload',
    method: 'post',
    data,
    headers: {
      repeatSubmit:false
    }
  })
}
/**
 * 上传到云端
 * @param {*} data FormData对象 bucket：credit=凭证 order=订单 model=模特
 * @returns 
 */
export async function uploadCloudFile(data, bucket) {
  return new Promise(async (resolve, reject) => {
    try {
      let name = data.get('file').name || '.jpg'
      const fileSuffix = name.substring(name.lastIndexOf('.') + 1)
      let res = await request({
        url: '/file/sign?fileName=1.' + fileSuffix + (bucket ? '&bucket=' + bucket : ''),
        method: 'get',
        headers: {
          repeatSubmit: false
        }
      })
      let resParams = {
        code: res.code,
        data: {
          id: res.data.objectKey,
          name,
          picUrl: res.data.objectKey,
        }
      }
      let res2 = await fetch(res.data.uploadUrl, {
        method: 'PUT',
        body: data.get('file')
      })
      if(res2.headers.get('etag')) {
        resolve(resParams)
      } else {
        ElMessage.error('etag-上传错误！')
        reject('not etag')
      }
    } catch (error) {
      reject(error)
    }
  })
}