import request from '@/utils/request'

// 查询发票列表
export function financeInvoiceList(params) {
  return request({
    url: '/order/finance/invoice-list',
    method: 'get',
    params
  })
}
// 获取已开票金额
export function financeInvoiceAmount(params) {
  return request({
    url: '/order/finance/get-invoice-amount',
    method: 'get',
    params
  })
}
// 发票数据统计
export function financeInvoiceStatistics() {
  return request({
    url: '/order/finance/invoice-statistics',
    method: 'get',
  })
}

// 查看发票
export function viewFinanceInvoice(invoiceId) {
  return request({
    url: `/order/finance/view-invoice/${invoiceId}`,
    method: 'get',
  })
}

// 确认发票
export function financeConfirmInvoice(params) {
  return request({
    url: `/order/finance/confirm-invoice`,
    method: 'post',
    params
  })
}

// 上传发票
export function financeUploadInvoice(data) {
  return request({
    url: `/order/finance/upload-invoice`,
    method: 'post',
    data
  })
}

//新、
//待开票列表 /order/invoice/to-be-invoiced-list
export function getToBeInvoicedList(params) {
  return request({
    url: '/order/invoice/to-be-invoiced-list',
    method: 'get',
    params
  })
}

//待红冲列表 /order/invoice/to-be-red-invoice-list
export function getToBeRedInvoiceList(params) {
  return request({
    url: '/order/invoice/to-be-red-invoice-list',
    method: 'get',
    params
  })
}

//已完成列表 /order/invoice/invoice-finish-list
export function getInvoiceFinishList(params) {
  return request({
    url: '/order/invoice/invoice-finish-list',
    method: 'get',
    params
  })
}

//发票详情 /order/invoice/back-invoice-detail
export function getBackInvoiceDetail(params) {
  return request({
    url: `/order/invoice/back-invoice-detail`,
    method: 'get',
    params
  })
}

//取消开票 /order/invoice/cancel-invoice
export function cancelInvoice(params) {
  return request({
    url: `/order/invoice/cancel-invoice`,
    method: 'post',
    params
  })
}

//发票扭转记录 /order/invoice/invoice-operate-record
export function getInvoiceOperateRecord(params) {
  return request({
    url: `/order/invoice/invoice-operate-record`,
    method: 'get',
    params
  })
}

//修改发票 /order/invoice/update-invoice
export function updateInvoice(data) {
  return request({
    url: `/order/invoice/update-invoice`,
    method: 'post',
    data
  })
}

//上传发票  /order/invoice/upload-invoice
export function uploadInvoice(data) {
  return request({
    url: `/order/invoice/upload-invoice`,
    method: 'post',
    data
  })
}
//重新上传发票 /order/invoice/re-upload-invoice
export function reUploadInvoice(data) {
  return request({
    url: `/order/invoice/re-upload-invoice`,
    method: 'post',
    data
  })
}

//确认发票 /order/invoice/confirm-invoice
export function confirmInvoice(params) {
  return request({
    url: `/order/invoice/confirm-invoice`,
    method: 'post',
    params
  })
}

//审核发票 /order/invoice/audit-invoice
export function auditInvoice(data) {
  return request({
    url: `/order/invoice/audit-invoice`,
    method: 'post',
    data
  })
}

//红冲详情 /order/invoice/red-invoice-detail
export function getRedInvoiceDetail(params) {
  return request({
    url: `/order/invoice/red-invoice-detail`,
    method: 'get',
    params
  })
}

//标记红冲 /order/invoice/mark-red-invoice
export function markRedInvoice(data) {
  return request({
    url: `/order/invoice/mark-red-invoice`,
    method: 'post',
    data
  })
}

//数量统计 /order/invoice/company-invoice-statistics
export function getbackInvoiceStatistics() {
  return request({
    url: '/order/invoice/back-invoice-statistics',
    method: 'get',
  })
}

//开票金额统计 /order/invoice/invoice-amount-statistics
export function getInvoiceAmountStatistics() {
  return request({
    url: '/order/invoice/invoice-amount-statistics',
    method: 'get',
  })
}

//开票记录 /order/invoice/invoice-record
export function getInvoiceRecord(params) {
  return request({
    url: `/order/invoice/invoice-record`,
    method: 'get',
    params
  })
}

//文件下载获取链接 /system/download
export function getDownloadUrl(params) {
  return request({
    url: `/system/download`,
    method: 'get',
    params
  })
}