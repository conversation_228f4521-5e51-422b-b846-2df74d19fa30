import request from '@/utils/request';
// 查询退款订单列表
export function refundApprovalList(params) {
  return request({
    url: '/order/refund/list',
    method: 'get',
    params,
  });
}

//同意退款
export function consentRefund(data) {
  return request({
    url: '/order/refund/agree-refund',
    method: 'post',
    data,
  });
}
//拒绝退款
export function rejectRefund(data) {
  return request({
    url: '/order/refund/reject-refund',
    method: 'post',
    data,
  });
}

//视频订单详情 /order/finance/receivableAuditInfo/{id}
export function receivableAuditInfo(id) {
  return request({
    url: `/order/finance/receivableAuditInfo/${id}`,
    method: 'get',
  });
}

//未审核统计 /order/finance/unApproveStatusStatistics
export function getUnApproveStatusStatistics() {
  return request({
    url: '/order/finance/unApproveStatusStatistics',
    method: 'get',
  });
}
