import request from '@/utils/request'

// 查询商家信息列表
export function bizMerchantList() {
  return request({
    url: '/biz/merchant/list',
    method: 'get',
  })
}
export function businessAccountList() {
  return request({
    url: '/biz/business/businessAccountList ',
    method: 'get',
  })
}

// 查询应收审核列表
export function receivableAuditList(params) {
  return request({
    url: '/order/finance/receivableAuditList',
    method: 'get',
    params
  })
}
// 查询应收会员订单审核列表
export function receivableMemberAuditList(params) {
  return request({
    url: '/order/finance/member/receivableAuditList',
    method: 'get',
    params
  })
}

// 查询应收审核详情
export function receivableAuditDetail(id) {
  return request({
    url: `/order/finance/receivableAuditDetail/${id}`,
    method: 'get',
  })
}

// 应收审核流程
export function receivableAuditOrder(data) {
  return request({
    url: `/order/finance/auditOrder`,
    method: 'post',
    data
  })
}

//预付款审核列表 /biz/business/backend/businessBalancePrepay/list
export function prepayAuditList(params) {
  return request({
    url: '/biz/business/backend/businessBalancePrepay/list',
    method: 'get',
    params
  })
}

//预付款审核统计 /biz/business/backend/businessBalancePrepay/statistics
export function prepayAuditStatistics() {
  return request({
    url: '/biz/business/backend/businessBalancePrepay/statistics',
    method: 'get',
  })
}

//预付款审批详情 /biz/business/backend/businessBalancePrepay/{id}
export function prepayAuditDetail(id) {
  return request({
    url: `/biz/business/backend/businessBalancePrepay/${id}`,
    method: 'get',
  })
}

//预付款审核  
export function prepayAudit(data) {
  return request({
    url: `/biz/business/backend/businessBalancePrepay/audit`,
    method: 'post',
    data
  })
}

//导出预付款 /biz/business/backend/businessBalancePrepay/list/export
export function prepayExport(params) {
  return request({
    url: '/biz/business/backend/businessBalancePrepay/list/export',
    method: 'post',
    params
  })
}

// 视频应收统计 /order/finance/orderAuditStatusStatistics
export function receivableStatistics() {  
  return request({
    url: '/order/finance/orderAuditStatusStatistics',
    method: 'get',
  })
}

//会员订单应收统计 /order/finance/member/orderAuditStatusStatistics
export function memberReceivableStatistics() {
  return request({
    url: '/order/finance/member/orderAuditStatusStatistics',
    method: 'get',
  })
}