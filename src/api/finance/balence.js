import request from '@/utils/request.js'

// 获取商家余额统计（运营端使用）
export function businessBalanceTotal() {
  return request({
    url: '/biz/business/backend/businessBalanceTotal',
    method: 'get',
  })
}
// 获取商家余额列表（运营端使用）
export function businessBalanceVOs(params) {
  return request({
    url: '/biz/business/backend/businessBalanceList',
    method: 'get',
    params,
  })
}
// 修改商家余额
export function updateBusinessBalance(data) {
  return request({
    url: '/biz/business/backend/balancePayOut',
    method: 'post',
    data,
  })
}
// 添加商家余额预付数据（运营端）
export function addBusinessBalancePrepay(data) {
  return request({
    url: '/biz/business/backend/businessBalancePrepay/save',
    method: 'post',
    data,
  })
}

// 获取商家余额流水列表（运营端使用）
export function businessBalanceFlowList(params) {
  return request({
    url: '/biz/business/backend/businessBalanceFlowList',
    method: 'get',
    params,
  })
}

// 获取商家余额流水列表
export function getBusinessVo(params) {
  return request({
    url: '/biz/business/backend/getBusinessBalanceDetailVo',
    method: 'get',
    params,
  })
}

// 获取余额提现审核表列表（运营端）
export function businessBalanceAuditFlow(params) {
  return request({
    url: '/biz/business/backend/businessBalanceAuditFlow/list',
    method: 'get',
    params,
  })
}

// 获取余额提现审核统计（运营端）
export function businessBalanceAuditFlowStatistics() {
  return request({
    url: '/biz/business/backend/businessBalanceAuditFlow/statistics',
    method: 'get',
  })
}

// 余额提现审核
export function businessBalanceAuditFlowAuditPayOut(data) {
  return request({
    url: '/biz/business/backend/businessBalanceAuditFlow/auditPayOut',
    method: 'post',
    data,
  })
}

//获取余额提现审核详情（运营端）  /biz/business/backend/businessBalanceAuditFlow/{id}
export function businessBalanceAuditFlowDetail(id) {
  return request({
    url: '/biz/business/backend/businessBalanceAuditFlow/' + id,
    method: 'get',
  })
}

// 标记已通知 /biz/business/backend/businessBalanceAuditFlow/markNotice/{id}
export function businessBalanceAuditFlowMarkNotice(id) {
  return request({
    url: '/biz/business/backend/businessBalanceAuditFlow/markNotice/' + id,
    method: 'post',
  })
}

// 根据商家ID获取商家可用余额详情数据 /biz/business/balance/detail/list/{businessId}
export function getBusinessBalanceDetailList(businessId) {
  return request({
    url: '/biz/business/balance/detail/list/' + businessId,
    method: 'get',
  })
}