import request from '@/utils/request'

//退款记录列表 /order/finance/refund-success-list
export function getRefundSuccessList(params) {
  return request({
    url: '/order/finance/refund-success-list',
    method: 'get',
    params
  })
}

//入驻会员 /biz/business/backend/residentBusinessList
export function getResidentBusinessList(params) {
  return request({
    url: '/biz/business/backend/residentBusinessList',
    method: 'get',
    params
  })
}

//会员有效期记录 /biz/business/backend/businessMemberFlowList
export function getBusinessMemberFlowList(params) {
  return request({
    url: '/biz/business/backend/businessMemberFlowList',
    method: 'get',
    params
  })
}