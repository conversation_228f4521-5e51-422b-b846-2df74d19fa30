import request from '@/utils/request'

/**
 * 二维码
 * @param params 
 * @returns 
 */
export function wechatQrcode(params) {
  return request({
    url: '/auth/business/wechat/qrcode',
    method: 'get',
    params
  })
}

/**
 * 二维码
 * @param params 
 * @returns 
 */
export function generateQrcode(params) {
  return request({
    url: '/biz/account/backend/generateQrcode',
    method: 'post',
    params
  })
}

/**
 * 检查二维码状态
 * @param params 
 * @returns 
 */
export function checkQrcode(params) {
  return request({
    url: '/biz/auth/wechat/check',
    method: 'get',
    params
  })
}

///biz/auth/wechat/checkInsertBizUser 新增账号的检查二维码状态
export function checkInsertBizUser(params) {
  return request({
    url: '/biz/auth/wechat/checkInsertBizUser',
    method: 'get',
    params
  })
}

/**
 * 微信授权
 * @param data 
 * @returns 
 */
export function wechatAuth(data) {
  return request({
    url: '/biz/auth/wechat/oauth2',
    method: 'post',
    data
  })
}