import request from '@/utils/request'

// 获取案例分组类表
export function getCaseGroupList(params) {
  return request({
    url: '/biz/operate/casus/group/list',
    method: 'get',
    params
  })
}

// 保存案例分组
export function saveCaseGroup(data) {
  return request({
    url: '/biz/operate/casus/group/save',
    method: 'post',
    data
  })
}

// 修改案例分组
export function updateCaseGroup(data) {
  return request({
    url: '/biz/operate/casus/group/update',
    method: 'put',
    data
  })
}

// 删除案例分组
export function deleteCaseGroup(id) {
  return request({
    url: `/biz/operate/casus/group/${id}`,
    method: 'delete',
  })
}

// 获取案例分组视频列表
export function groupsVideoList(params) {
  return request({
    url: `/biz/operate/casus/group/groupsVideoList`,
    method: 'get',
    params
  })
}

// 清除分组案例视频
export function removeGroupVideo(data) {
  return request({
    url: `/biz/operate/casus/group/removeGroupVideo`,
    method: 'delete',
    data
  })
}

// 获取分组可添加视频列表
export function addGroupsVideoList(params) {
  return request({
    url: `/biz/operate/casus/group/addGroupsVideoList`,
    method: 'get',
    params
  })
}

// 添加分组案例视频
export function addGroupsVideo(data) {
  return request({
    url: `/biz/operate/casus/group/addGroupVideo`,
    method: 'post',
    data
  })
}

// 更新分组视频顺序
export function updateGroupSort(data) {
  return request({
    url: `/biz/operate/casus/group/updateGroupSort`,
    method: 'put',
    data
  })
}