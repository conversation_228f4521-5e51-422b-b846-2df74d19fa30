import request from '@/utils/request'

// 获取案例视频列表
export function getCaseVideoList(params) {
  return request({
    url: '/biz/operate/casus/video/list',
    method: 'get',
    params
  })
}

// 保存案例视频
export function saveCaseVideo(data) {
  return request({
    url: '/biz/operate/casus/video/save',
    method: 'post',
    data
  })
}

// 修改案例视频
export function updateCaseVideo(data) {
  return request({
    url: '/biz/operate/casus/video/update',
    method: 'put',
    data
  })
}

// 删除案例视频
export function deleteCaseVideo(id) {
  return request({
    url: `/biz/operate/casus/video/${id}`,
    method: 'delete',
  })
}