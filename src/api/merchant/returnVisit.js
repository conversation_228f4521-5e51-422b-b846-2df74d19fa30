import request from '@/utils/request'

/**
 * 商家-待回访列表
 * @param {*} params 
 * @returns 
 */
export function getReturnVisitList(params) {
  return request({
    url: '/biz/business-callback/wait-for-return-visit',
    method: 'get',
    params,
  })
}

/**
 * 商家-回访中列表
 * @param {*} params 
 * @returns 
 */
export function getInTheReturnVisitList(params) {
  return request({
    url: '/biz/business-callback/in-the-return-visit',
    method: 'get',
    params,
  })
}

/**
 * 商家-已回访列表
 * @param {*} params 
 * @returns 
 */
export function getAlreadyVisitList(params) {
  return request({
    url: '/biz/business-callback/already-visited',
    method: 'get',
    params,
  })
}

/**
 * 商家-回访状态数统计
 * @returns 
 */
export function getReturnVisitStatusCount() {
  return request({
    url: '/biz/business-callback/return-visit-status-count',
    method: 'get',
  })
}

/**
 * 商家-标记回访
 * @param {*} params 
 * @returns 
 */
export function markReturnVisit(params) {
  return request({
    url: '/biz/business-callback/mark-return-visit',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

/**
 * 商家-填写回访记录-回访账号下拉框
 * @param {*} params 
 * @returns 
 */
export function getWriteReturnVisitAccount(params) {
  return request({
    url: '/biz/business-callback/write-return-visit-account',
    method: 'get',
    params,
  })
}

/**
 * 商家-填写回访记录
 * @param {*} data 
 * @returns 
 */
export function writeReturnVisit(data) {
  return request({
    url: '/biz/business-callback/write-return-visit',
    method: 'post',
    data,
  })
}

/**
 * 商家-回访记录-详情
 * @param {*} params 
 * @returns 
 */
export function getReturnVisitDetail(params) {
  return request({
    url: '/biz/business-callback/callback-detail',
    method: 'get',
    params,
  })
}

/**
 * 商家-回访记录-列表
 * @param {*} params 
 * @returns 
 */
export function getReturnVisitRecord(params) {
  return request({
    url: '/biz/business-callback/return-visit-record',
    method: 'get',
    params,
  })
}

/**
 * 商家-回访记录-回访账号下拉框
 * @param {*} params 
 * @returns 
 */
export function getReturnVisitAccount(params) {
  return request({
    url: '/biz/business-callback/return-visit-account',
    method: 'get',
    params,
  })
}