import request from '@/utils/request'

// 查询商家列表
export function merchantList(params) {
  return request({
    url: '/biz/business/backend/businessList',
    method: 'get',
    params,
  })
}
// 查询商家统计
export function merchantStatisticsList(params) {
  return request({
    url: '/biz/business/backend/businessStatistics',
    method: 'get',
    params,
  })
}
// 创建商家
export function saveMerchant(data) {
  return request({
    url: '/biz/business/backend/saveBusiness',
    method: 'post',
    data,
  })
}
// 编辑商家
export function updateMerchant(data) {
  return request({
    url: '/biz/business/backend/updateBusiness',
    method: 'put',
    data,
  })
}
// 修改商家账号状态
export function updateMerchantStatus(data) {
  return request({
    url: '/biz/business/backend/updateBusinessStatus',
    method: 'put',
    data,
  })
}
// 修改商家客服
export function updateWaiter(data) {
  return request({
    url: '/biz/business/backend/updateWaiter',
    method: 'put',
    data,
  })
}

// 创建账号
export function saveBizUser(data) {
  return request({
    url: '/biz/business/backend/saveBizUser',
    method: 'post',
    data,
  })
}

// 获取账号列表/biz/business/backend/bizUserList
export function bizUserList(params) {
  return request({
    url: '/biz/business/backend/bizUserList',
    method: 'get',
    params,
  })
}

//修改登录账号 /biz/business/backend/editBizUser
export function editBizUser(data) {
  return request({
    url: '/biz/business/backend/editBizUser',
    method: 'put',
    data,
  })
}
//获取商家有效期流水记录/biz/business/backend/businessMemberValidityFlowLis
export function businessMemberValidityFlowLis(params) {
  return request({
    url: '/biz/business/backend/businessMemberValidityFlowList',
    method: 'get',
    params,
  })
}
//修改会员到期时间/biz/business/backend/updateMemberValidity
export function updateMemberValidity(data) {
  return request({
    url: '/biz/business/backend/updateMemberValidity',
    method: 'put',
    data,
  })
}

//获取商家备注 /biz/business/backend/businessRemarkFlowList
export function businessRemarkFlowList(params) {
  return request({
    url: '/biz/business/backend/businessRemarkFlowList',
    method: 'get',
    params,
  })
}

//商家备注 /biz/business/backend/updateBusinessRemark
export function updateBusinessRemark(data) {
  return request({
    url: '/biz/business/backend/updateBusinessRemark',
    method: 'put',
    data,
  })
}
//账号列表修改手机 /business/backend/editBizUserPhone
export function editBizUserPhone(data) {
  return request({
    url: '/biz/business/backend/editBizUserPhone',
    method: 'put',
    data,
  })
}

//商务经理列表 /business/backend/businessManagerList
export function getBusinessManagerList(params) {
  return request({
    url: '/biz/business/backend/businessManagerList',
    method: 'get',
    params,
  })
}

//获取商家有效期流水 /biz/business/backend/getRelationOrder
export function getRelationOrder(params) {
  return request({
    url: '/biz/business/backend/getRelationOrder',
    method: 'get',
    params,
  })
}

//查询商家子账户列表 /biz/business/getSubAccountList/{businessId}
export function getSubAccountList(businessId) {
  return request({
    url: '/biz/business/getSubAccountList/' + businessId,
    method: 'get',
  })
}

//更换主账号获取 /biz/business/backend/queryBusinessAccountListByBusinessId/{businessId}
export function queryBusinessAccountListByBusinessId(businessId) {
  return request({
    url: '/biz/business/backend/queryBusinessAccountListByBusinessId/' + businessId,
    method: 'get',
  })
}

//换绑主账号 /biz/business/backend/exchangeBindOwner
export function exchangeBindOwner(data) {
  return request({
    url: '/biz/business/backend/exchangeBindOwner',
    method: 'put',
    data,
  })
}

//获取商家主账号换绑记录 /biz/business/backend/getOwnerFlowListByBusinessId
export function getOwnerFlowListByBusinessId(params) {
  return request({
    url: '/biz/business/backend/getOwnerFlowListByBusinessId',
    method: 'get',
    params,
  })
}

//更换账号列表账号状态 /backend/editBizUserStatus
export function editBizUserStatus(data) {
  return request({
    url: '/biz/business/backend/editBizUserStatus',
    method: 'put',
    data,
  })
}

//查询是否可七天无理由退款/biz/business/backend/checkUnableSettlement/{businessId}
export function checkUnableSettlement(businessId) {
  return request({
    url: '/biz/business/backend/checkUnableSettlement/' + businessId,
    method: 'get',
  })
}

//通过活动类型获取活动信息 /order/promotion/get-valid-promotion-activity-by-type
export function getValidPromotionActivityByType(params) {
  return request({
    url: '/order/promotion/get-promotion-activity-by-type',
    method: 'get',
    params,
  })
}

//通过活动类型获取修改记录 /order/promotion/promotion-activity-amendment-record-list
export function promotionActivityAmendmentRecordList(params) {
  return request({
    url: '/order/promotion/promotion-activity-amendment-record-list',
    method: 'get',
    params,
  })
}

///order/promotion/update-promotion-activity
export function updatePromotionActivity(data) {
  return request({
    url: '/order/promotion/update-promotion-activity',
    method: 'put',
    data,
  })
}
