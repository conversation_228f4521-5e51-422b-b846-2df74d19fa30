import request from '@/utils/request'

/**
 * 基础看板
 * @returns
 */

export function orderVideoBaseBoard() {
  return request({
    url: '/order/data-statistics/order-video-base-board',
    method: 'get',
  })
}

///order/data-statistics/order-video-service-count

export function orderVideoServiceCount() {
  return request({
    url: '/order/data-statistics/order-video-service-count',
    method: 'get',
  })
}

///order/data-statistics/order-video-trend
export function orderVideoTrend(params) {
  return request({
    url: '/order/data-statistics/order-video-trend',
    method: 'get',
    params,
  })
}

export function orderVideoMatchDurationData(params) {
  return request({
    url: '/order/data-statistics/order-video-match-duration-data',
    method: 'get',
    params,
  })
}

export function orderVideoFeedbackDurationData(params) {
  return request({
    url: '/order/data-statistics/order-video-feedback-duration-data',
    method: 'get',
    params,
  })
}

export function orderVideoDeliveryDurationData(params) {
  return request({
    url: '/order/data-statistics/order-video-delivery-duration-data',
    method: 'get',
    params,
  })
}

export function orderVideoAverageDurationData() {
  return request({
    url: '/order/data-statistics/order-video-average-duration-data',
    method: 'get',
  })
}

export function orderVideoAfterSaleTypeAnalysis(params) {
  return request({
    url: '/order/data-statistics/order-video-after-sale-type-analysis',
    method: 'get',
    params,
  })
}

export function orderVideoCompensationOrderSituation(params) {
  return request({
    url: '/order/data-statistics/order-video-compensation-order-situation',
    method: 'get',
    params,
  })
}