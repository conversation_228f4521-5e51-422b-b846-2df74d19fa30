import request from '@/utils/request'
// /order/data-statistics/customer-service-base-board

/**
 *客服数据-基础看板
 * @returns
 */
export function getCustomerServiceBaseBoard() {
  return request({
    url: '/order/data-statistics/customer-service-base-board',
    method: 'get',
  })
}

//中文部客服数据 /order/data-statistics/chinese-customer-service-data
export function getChineseCustomerServiceData(params) {
  return request({
    url: '/order/data-statistics/chinese-customer-service-data',
    method: 'get',
    params,
  })
}

///order/data-statistics/english-customer-service-data
export function getEnglishCustomerServiceData(params) {
  return request({
    url: '/order/data-statistics/english-customer-service-data',
    method: 'get',
    params,
  })
}
