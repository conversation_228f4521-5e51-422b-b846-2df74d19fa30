import request from '@/utils/request'

/**
 * 模特模特基础信息数据
 * @returns
 */

export function modelBasicsData() {
  return request({
    url: '/biz/data-statistics/model-basics-data',
    method: 'get',
  })
}

///order/data-statistics/model-order-commission-analysis
export function modelOrderCommissionAnalysis(params) {
  return request({
    url: '/order/data-statistics/model-order-commission-analysis',
    method: 'get',
    params,
  })
}

/**
 * 模特数据-模特数量趋势分析
 * @returns
 */
///biz/data-statistics/model-number-trend-analysis
export function modelNumberTrendAnalysis(params) {
  return request({
    url: '/biz/data-statistics/model-number-trend-analysis',
    method: 'get',
    params,
  })
}

/**
 * 模特数据-每月新增模特分析
 * @returns
 */
export function newModelAnalysis(params) {
  return request({
    url: '/biz/data-statistics/new-model-analysis',
    method: 'get',
    params,
  })
}

/**
 * 模特数据-每月淘汰模特分析
 * @returns
 */
//biz/data-statistics/oust-model-analysis
export function oustModelAnalysis(params) {
  return request({
    url: '/biz/data-statistics/oust-model-analysis',
    method: 'get',
    params,
  })
}

/**
 * 模特数据-模特类型分析
 * @returns
 */
export function modelTypeAnalysis(params) {
  return request({
    url: '/biz/data-statistics/model-type-analysis',
    method: 'get',
    params,
  })
}

/**
 * 模特数据-模特接单排行榜
 * @returns
 */
export function modelOrderRanking(params) {
  return request({
    url: '/biz/data-statistics/model-order-ranking',
    method: 'get',
    params,
  })
}

///order/data-statistics/model-success-match-count
/**
 * 模特数据-成功匹配次数
 * @returns
 */
export function modelSuccessMatchCount() {
  return request({
    url: '/order/data-statistics/model-success-match-count',
    method: 'get',
  })
}

export function modelStatusData(params) {
  return request({
    url: '/biz/data-statistics/model-status-data',
    method: 'get',
    params,
  })
}

export function modelOrderScheduledData(params) {
  return request({
    url: '/biz/data-statistics/model-order-scheduled-data',
    method: 'get',
    params,
  })
}