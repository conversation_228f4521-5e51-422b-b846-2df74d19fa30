import request from '@/utils/request'

export function getMemberStatistics(params) {
  return request({
    url: '/biz/data-statistics/business-order-data',
    method: 'get',
    params,
  })
}

export function getMemberTypeStatistics() {
  return request({
    url: '/biz/data-statistics/business-member-type-data',
    method: 'get',
  })
}

export function getMemberExitStatistics(params) {
  return request({
    url: '/biz/data-statistics/business-exit-data',
    method: 'get',
    params,
  })
}

//会员来源分析
export function getMemberSourceStatistics(params) {
  return request({
    url: '/biz/data-statistics/business-source-data',
    method: 'get',
    params,
  })
}

export function getMemberDiscountStatistics(params) {
  return request({
    url: '/order/data-statistics/business-member-discount-situation',
    method: 'get',
    params,
  })
}

export function getMemberTypeOrderStatistics(params) {
  return request({
    url: '/biz/data-statistics/business-member-type-order-data',
    method: 'get',
    params,
  })
}

export function getMemberTrendStatistics(params) {
  return request({
    url: '/biz/data-statistics/business-member-trend-data',
    method: 'get',
    params,
  })
}

export function getMemberBaseRechargeStatistics() {
  return request({
    url: '/biz/data-statistics/business-member-base-recharge-data',
    method: 'get',
  })
}

export function getMemberBaseExporeStatistics() {
  return request({
    url: '/biz/data-statistics/business-member-base-expire-data',
    method: 'get',
  })
}
