import request from '@/utils/request'

//查询订单列表
export function getOrderList(params) {
  return request({
    url: '/order/order/list',
    method: 'get',
    params,
  })
}

//订单各个状态统计
export function getTabsCount() {
  return request({
    url: '/order/order/back-status-count',
    method: 'get',
  })
}

//订单视频统计
export function getVideoCount(params) {
  return request({
    url: '/order/order/video/statistics',
    method: 'get',
    params,
  })
}

//获取订单详情
export function orderDetails(id) {
  return request({
    url: `/order/order/detail/${id}`,
    method: 'get',
  })
}

//获取视频订单详情 /order/order/detail/back/{videoId}
export function videoOrderDetails(videoId) {
  return request({
    url: `/order/order/detail/back/${videoId}`,
    method: 'get',
  })
}

//查询视频订单的匹配情况反馈
export function orderfeedback(id) {
  return request({
    url: `/order/order/case/${id}`,
    method: 'get',
  })
}
//运营发起反馈
export function sendOrderFeedbackCase(data) {
  return request({
    url: `/order/order/send-video-case`,
    method: 'post',
    data,
  })
}
//运营根据匹配情况修改订单
export function updateOrderDetails(data) {
  return request({
    url: `/order/order/operation-video-case`,
    method: 'post',
    data,
  })
}

//更改预选模特状态
export function editPreselectModel(data) {
  return request({
    url: `/order/order/edit-preselect-model`,
    method: 'post',
    data,
  })
}
//获取预选模特列表
export function preselectModelList(videoId) {
  return request({
    url: `/order/order/preselect-model-list/${videoId}`,
    method: 'get',
  })
}
//校验视频订单是否有选定模特
export function checkPreselectModel(data) {
  return request({
    url: `/order/order/check-video-selected`,
    method: 'post',
    data,
  })
}

//添加预选模特
export function addPreselectModel(params) {
  return request({
    url: `/order/order/add-preselect-model`,
    method: 'post',
    params,
  })
}

//运营获取视频订单详情
export function getVideoOrderDetail(id) {
  return request({
    url: `/order/order/operation/${id}`,
    method: 'get',
  })
}
//运营修改视频订单详情
export function editVideoOrder(data) {
  return request({
    url: `/order/order/operation`,
    method: 'put',
    data,
  })
}

// 翻译
export function getTranslate(data) {
  return request({
    url: `/biz/translate`,
    method: 'post',
    data,
  })
}
//单句翻译
export function getOneTranslate(data) {
  return request({
    url: `/biz/translate/translateStr`,
    method: 'post',
    data,
  })
}

//添加反馈素材给商家
export function addFeedbackLink(data) {
  return request({
    url: `/order/feedback`,
    method: 'post',
    data,
  })
}
//根据订单id查询商家反馈素材
export function getFeedbackList(params) {
  return request({
    url: `/order/feedback/lists`,
    method: 'get',
    params,
  })
}
// 模特反馈素材列表
export function feedbackModelList(params) {
  return request({
    url: `/order/feedback/model/list`,
    method: 'get',
    params,
  })
}
// // 运营帮模特上传素材
// export function backHelpUpload(data) {
//   return request({
//     url: `/order/order/back-help-model-upload-material`,
//     method: 'post',
//     data,
//   })
// }
export function backHelpUpload(data) {
  return request({
    url: `/order/edit/back-help-model-upload-material`,
    method: 'post',
    data,
  })
}

// 驳回模特素材
export function rejectModelFeedback(data) {
  return request({
    url: `/order/feedback/model/reject`,
    method: 'post',
    data,
  })
}

// 标记订单
export function markOrder(data) {
  return request({
    url: '/order/order/mark-order',
    method: 'post',
    data,
  })
}
// 标记订单信息
export function getMarkOrderInfo(videoId) {
  return request({
    url: `/order/order/mark-order-info/${videoId}`,
    method: 'get',
  })
}
// 标记订单-主携带订单下拉框
export function getMarkOrderMainCarrtList(videoId) {
  return request({
    url: `/order/order/mark-order-main-carry-list/${videoId}`,
    method: 'get',
  })
}

// 取消订单
export function cancelOrder(params) {
  return request({
    url: `/order/order/cancel`,
    method: 'post',
    params,
  })
}

// 申请退款
export function applyRefund(data) {
  return request({
    url: `/order/refund/apply-refund`,
    method: 'post',
    data,
  })
}
// 申请退款信息
export function getRefundInfo(data) {
  return request({
    url: `/order/refund/get-refund-info`,
    method: 'post',
    data,
  })
}
// 备注
export function addOrderRemark(data) {
  return request({
    url: `/order/comment`,
    method: 'post',
    data,
  })
}
// 备注列表
export function orderRemarkRecords(params) {
  return request({
    url: `/order/comment/list`,
    method: 'get',
    params,
  })
}
// 确认提交预选模特
export function submitPreselectModel(data) {
  return request({
    url: `/order/order/submit-video`,
    method: 'post',
    data,
  })
}

// 发货补货---填写物流单号
export function orderShipping(data) {
  return request({
    url: `/order/order/shipping`,
    method: 'post',
    data,
  })
}

//查询是否标记发货 /order/order/shipping-info/{videoId}
export function getShippingInfo(videoId) {
  return request({
    url: `/order/order/shipping-info/${videoId}`,
    method: 'get',
  })
}

//标记发货 /order/order/shipping-flag
export function shippingFlag(data) {
  return request({
    url: `/order/order/shipping-flag`,
    method: 'post',
    data,
  })
}

// 上传产品图
export function uploadProductImg(params) {
  return request({
    url: `/order/order/upload-product-image`,
    method: 'post',
    params,
  })
}

// 手动抓取产品图
export function crawlProductPic(params) {
  return request({
    url: `/order/order/crawl-product-pic`,
    method: 'post',
    params,
  })
}

// 运营上传素材至平台
export function uploadLink(data) {
  return request({
    url: `/order/order/upload-link`,
    method: 'post',
    data,
  })
}

// 根据条件查询视频订单列表
export function orderVideoList(params) {
  return request({
    url: `/order/order/order-video-list`,
    method: 'get',
    params,
  })
}

// 创建工单
export function createTask(data) {
  return request({
    url: `/order/task/create-task`,
    method: 'post',
    data,
  })
}

// 运营修改订单费用
export function updateOrderVideoPrice(data) {
  return request({
    url: `/order/order/update-order-video-price`,
    method: 'post',
    data,
  })
}

//订单催一催 /order/reminder
export function orderReminder(params) {
  return request({
    url: '/order/reminder',
    method: 'put',
    params,
  })
}

//获取上传素材详情/order/order/get-upload-material/{videoId}
export function getUploadMaterial(videoId) {
  return request({
    url: `/order/order/get-upload-material/${videoId}`,
    method: 'get',
  })
}

//运营帮商家上传素材/order/order/back-help-upload-material
export function backHelpUploadMaterial(data) {
  return request({
    url: `/order/order/back-help-upload-material`,
    method: 'post',
    data,
  })
}

//查询视频订单历史变更记录/order/order/get-video-history-change-record/{videoId}
export function getVideoHistoryChangeRecord(videoId) {
  return request({
    url: `/order/order/get-video-history-change-record/${videoId}`,
    method: 'get',
  })
}

//标记下载状态 /order/feedback/model/{id}/markDownload
export function markDownload(id) {
  return request({
    url: `/order/feedback/model/${id}/markDownload`,
    method: 'post',
  })
}

//确认收货按钮 /order/order/confirmReceipt/{logisticId}
export function confirmReceipt(logisticId) {
  return request({
    url: `/order/order/confirmReceipt/${logisticId}`,
    method: 'put',
  })
}

//修改百度汇率 /order/order/update-baidu-rate
export function updateBaiduRate(data) {
  return request({
    url: `/order/order/update-baidu-rate`,
    method: 'put',
    data,
  })
}

//创建任务单 /order/task/create-task
export function createTaskOrder(data) {
  return request({
    url: `/order/task/create-task`,
    method: 'post',
    data,
  })
}

//校验售后单 /order/task/check-task-exist
export function checkTaskExist(data) {
  return request({
    url: `/order/task/check-task-exist`,
    method: 'post',
    data,
  })
}

//订单回退 /order/order/rollback-order
export function rollbackOrder(data) {
  return request({
    url: `/order/order/rollback-order`,
    method: 'post',
    data,
  })
}

//下载请款清单
export function downloadPayInfo(params) {
  return request({
    url: `/order/pay/download-pay-info`,
    method: 'post',
    params,
  })
}

//查询订单是否合并 /order/merge/check-order-merge
export function checkOrderMerge(params) {
  return request({
    url: `/order/merge/check-order-merge`,
    method: 'get',
    params,
  })
}

//编辑素材 /order/order/edit-upload-material
export function editUploadMaterial(data) {
  return request({
    url: `/order/order/edit-upload-material`,
    method: 'post',
    data,
  })
}

//查询默认剪辑要求 /order/edit/history-clip-record
export function historyClipRecord(params) {
  return request({
    url: `/order/edit/history-clip-record`,
    method: 'get',
    params,
  })
}