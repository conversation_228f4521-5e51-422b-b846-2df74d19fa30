import request from '@/utils/request'

/**
 * 标记订单-主携带订单下拉框
 * @param {*} videoId
 * @returns
 */
export function selectListMainCarry(videoId) {
  return request({
    url: `/order/order/mark-order-main-carry-list/${videoId}`,
    method: 'get',
  })
}

/**
 * 订单列表-获取对接人下拉框
 * @param {*} params
 * @returns
 */
export function selectListContact(params) {
  return request({
    url: `/order/order/order-contact-select`,
    method: 'get',
    params,
  })
}

/**
 * 订单列表-获取出单人下拉框
 * @returns
 */
export function selectListIssue(keyword = undefined) {
  return request({
    url: `/order/order/order-issue-select`,
    method: 'get',
    params: {
      keyword,
    },
  })
}
/**
 * 订单列表-获取下单用户(运营)下拉框
 * @returns
 */
export function selectOrderUser(keyword = undefined) {
  return request({
    url: `/order/order/back-order-user-select`,
    method: 'get',
    params: {
      keyword,
    },
  })
}

/**
 * 订单列表-获取预选模特下拉框
 * @returns
 */
export function selectListPreselectModel(params) {
  return request({
    url: `/order/match/order-preselect-model-select`,
    method: 'get',
    params,
  })
}

/**
 * 订单列表-获取预选添加人下拉框
 * @returns
 */
export function selectListPreselectUser() {
  return request({
    url: `/order/match/order-preselect-user-select`,
    method: 'get',
  })
}

/**
 * 订单列表-获取拍摄模特下拉框
 * @returns
 */
export function selectListShootModel() {
  return request({
    url: `/order/order/order-shoot-model-select`,
    method: 'get',
  })
}

/**
 * 工单列表-获取拍摄模特下拉框
 * @returns
 */
export function taskSelectListShootModel() {
  return request({
    url: `/order/task/shoot-model-list`,
    method: 'get',
  })
}

/**
 * 订单列表-获取下单用户下拉框
 * @returns
 */
export function selectListUser() {
  return request({
    url: `/order/order/order-user-select`,
    method: 'get',
  })
}

/**
 * 订单列表-关联视频下拉框
 * @returns
 */
export function orderVideoList(params) {
  return request({
    url: `/order/order/un-finished-and-need-confirm-order-issue-select`,
    method: 'get',
    params,
  })
}

/**
 * 根据支付类型获取收款账号
 * @returns
 */
export function orderPayeeList(type) {
  return request({
    url: `/order/payee/type/${type}/list`,
    method: 'get',
  })
}

///order/order/back-create-order-user-name-select
export function selectListOrderUser() {
  return request({
    url: `/order/order/back-create-order-user-name-select`,
    method: 'get',
  })
}

/**
 * 待匹配意向模特
 * @returns 
 */
export function selectIntentionModelSelect(params) {
  return request({
    url: `/order/order/order-intention-model-select`,
    method: 'get',
    params,
  })
}
