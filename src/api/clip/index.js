import request from '@/utils/request'

//列表 /order/edit/material-info-list
export function getMaterialInfoList(params) {
  return request({
    url: `/order/edit/material-info-list`,
    method: 'get',
    params,
  })
}

//标记下载 /order/edit/mark-download
export function signMarkDownload(params) {
  return request({
    url: `/order/edit/mark-download`,
    method: 'post',
    params,
  })
}

//剪辑管理-待下载、待剪辑、待反馈、需确认 拍摄模特下拉框 /order/edit/select-shoot-model
export function getSelectShootModel(params) {
  return request({
    url: `/order/edit/select-shoot-model`,
    method: 'get',
    params,
  })
}

//剪辑管理-待下载 领取人下拉框 /order/edit/select-receive-person
export function getSelectReceivePerson() {
  return request({
    url: `/order/edit/select-receive-person`,
    method: 'get',
  })
}

//领取 /order/edit/get-edit
export function getEdit(data) {
  return request({
    url: `/order/edit/get-edit`,
    method: 'post',
    data,
  })
}

//待下载列表统计 /order/edit/get-material-info-statistics
export function getMaterialInfoStatistics() {
  return request({
    url: `/order/edit/get-material-info-statistics`,
    method: 'get',
  })
}

//历史剪辑记录 /order/edit/history-edit-list
export function historyEditList(params) {
  return request({
    url: `/order/edit/history-edit-list`,
    method: 'get',
    params,
  })
}

//标记已剪辑 /order/edit/mark-clip
export function markClip(data) {
  return request({
    url: `/order/edit/mark-clip`,
    method: 'post',
    data,
  })
}

//校验是否能评分 /order/edit/check-can-score
export function checkCanScore(params) {
  return request({
    url: `/order/edit/check-can-score`,
    method: 'get',
    params,
  })
}

//不反馈素材给商家 /order/edit/mark-no-feedback
export function markNoFeedback(data) {
  return request({
    url: `/order/edit/mark-no-feedback`,
    method: 'post',
    data,
  })
}

//剪辑要求 /order/edit/history-clip-record
export function historyClipRecord(params) {
  return request({
    url: `/order/edit/history-clip-record`,
    method: 'get',
    params,
  })
}

//剪辑人 /order/edit/select-edit-person
export function selectEditPerson(params) {
  return request({
    url: `/order/edit/select-edit-person`,
    method: 'get',
    params,
  })
}

//反馈素材给商家 剪辑管理 /order/edit/add-feed-back
export function addFeedBackClip(data) {
  return request({
    url: `/order/edit/add-feed-back`,
    method: 'post',
    data,
  })
}

//待上传 已完成 /order/edit/upload-link-list
export function uploadLinkList(params) {
  return request({
    url: `/order/edit/upload-link-list`,
    method: 'get',
    params,
  })
}

//已关闭 /order/edit/closed-list
export function closedList(params) {
  return request({
    url: `/order/edit/closed-list`,
    method: 'get',
    params,
  })
}

//待上传 已完成拍摄模特下拉框 /order/edit/upload-link-list-select-shoot-model
export function uploadLinkListSelectShootModel(params) {
  return request({
    url: `/order/edit/upload-link-list-select-shoot-model`,
    method: 'get',
    params,
  })
}

//剪辑管理-标记上传账号-下拉框 /order/edit/mark-upload-account-select
export function markUploadAccountSelect(params) {
  return request({
    url: `/order/edit/mark-upload-account-select`,
    method: 'get',
    params,
  })
}

//标记上传账号 /order/edit/mark-upload-account
export function markUploadAccount(data) {
  return request({
    url: `/order/edit/mark-upload-account`,
    method: 'post',
    data,
  })
}

//标记未待确认上传 /order/edit/mark-upload-confirm

export function markUploadConfirm(params) {
  return request({
    url: `/order/edit/mark-upload-confirm`,
    method: 'post',
    params,
  })
}

//取消上传 /order/edit/cancel-upload-link
export function cancelUploadLink(data) {
  return request({
    url: `/order/edit/cancel-upload-link`,
    method: 'post',
    data,
  })
}

//上传成功 /order/edit/upload-link
export function uploadLink(data) {
  return request({
    url: `/order/edit/upload-link`,
    method: 'post',
    data,
  })
}

//上传失败 /order/edit/upload-link-fail
export function uploadLinkFail(data) {
  return request({
    url: `/order/edit/upload-link-fail`,
    method: 'post',
    data,
  })
}

//历史上传记录 /order/edit/history-upload-record
export function historyUploadRecord(params) {
  return request({
    url: `/order/edit/history-upload-record`,
    method: 'get',
    params,
  })
}

// 剪辑管理-标记下载-校验是否存在进行中的任务/order/edit/check-download
export function checkDownload(params) {
  return request({
    url: `/order/edit/check-download`,
    method: 'get',
    params,
  })
}

//视频评论记录 /order/edit/video-score-list
export function videoScoreList(params) {
  return request({
    url: `/order/edit/video-score-list`,
    method: 'get',
    params,
  })
}

//视频评级记录 人 /order/edit/video-score-list-select-evaluate-person
export function videoScoreListSelectEvaluatePerson() {
  return request({
    url: `/order/edit/video-score-list-select-evaluate-person`,
    method: 'get',
  })
}

//视频评价记录 拍摄模特下拉框 /order/edit/video-score-list-select-shoot-model
export function videoScoreListSelectShootModel() {
  return request({
    url: `/order/edit/video-score-list-select-shoot-model`,
    method: 'get',
  })
}

/**
 * 上传账号下拉框
 * @returns 
 */
export function uploadAccountSelect() {
  return request({
    url: `/order/edit/upload-account-select`,
    method: 'get',
  })
}
