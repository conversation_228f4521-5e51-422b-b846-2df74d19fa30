import request from '@/utils/request'

/**
 * 获取物流统计数据
 * @returns 
 */
export function logisticStatistics() {
  return request({
    url: '/order/logistic/follow/getStatisticsVO',
    method: 'get',
  })
}

/**
 * 物流跟进-对接人
 * @param {*} params 
 * @returns 
 */
export function contactSelectList(params) {
  return request({
    url: '/order/logistic/follow/contact-select',
    method: 'get',
    params,
  })
}

/**
 * 物流跟进-出单人
 * @param {*} params 
 * @returns 
 */
export function issueSelectList(params) {
  return request({
    url: '/order/logistic/follow/issue-select',
    method: 'get',
    params,
  })
}

/**
 * 物流跟进-获取模特及数量
 * @param {*} params 
 * @returns 
 */
export function modelListSelectmodelListSelect(params) {
  return request({
    url: '/order/logistic/follow/modelListSelectmodelListSelect',
    method: 'get',
    params,
  })
}

/**
 * 物流跟进-获取商家编码及数量
 * @param {*} params 
 * @returns 
 */
export function memberCodeList(params) {
  return request({
    url: '/order/logistic/follow/memberCodeList',
    method: 'get',
    params,
  })
}

/**
 * 物流跟进列表
 * @param {*} params 
 * @returns 
 */
export function logisticFollowList(params) {
  return request({
    url: '/order/logistic/follow/list',
    method: 'get',
    params,
  })
}

/**
 * 物流跟进列表详情
 * @param {*} id 
 * @returns 
 */
export function logisticFollowDetail(id) {
  return request({
    url: `/order/logistic/follow/detail/${id}`,
    method: 'get',
  })
}

/**
 * 标记通知：通知发货、提醒发货、通知地址变更
 * @param {*} data 
 * @returns 
 */
export function logisticFollowAlertShipping(data) {
  return request({
    url: '/order/logistic/follow/alertShipping',
    method: 'post',
    data,
  })
}

/**
 * 延迟发货
 * @param {*} data 
 * @returns 
 */
export function logisticFollowDelayShipping(data) {
  return request({
    url: '/order/logistic/follow/delayShipping',
    method: 'post',
    data,
  })
}

/**
 * 补充说明
 * @param {*} data 
 * @returns 
 */
export function logisticRemarkReplenish(data) {
  return request({
    url: '/order/logistic/follow/remarkReplenish',
    method: 'post',
    data,
  })
}

/**
 * 物流跟进
 * @param {*} data 
 * @returns 
 */
export function logisticFollowUp(data) {
  return request({
    url: '/order/logistic/follow/logisticFollow',
    method: 'post',
    data,
  })
}

/**
 * 模特确认
 * @param {*} data 
 * @returns 
 */
export function modelConfirm(data) {
  return request({
    url: '/order/logistic/follow/modelConfirm',
    method: 'post',
    data,
  })
}

/**
 * 查看物流单号是否重复
 * @param {*} number 
 * @returns 
 */
export function checkLogisticRepetition(number) {
  return request({
    url: `/order/logistic/follow/logistic/checkRepetition/${number}`,
    method: 'get',
  })
}

/**
 * 修改物流单号
 * @param {*} data 
 * @returns 
 */
export function operationVideoLogistic(data) {
  return request({
    url: '/order/logistic/follow/operationVideoLogistic',
    method: 'post',
    data,
  })
}