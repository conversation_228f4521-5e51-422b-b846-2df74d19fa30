import request from '@/utils/request'

// 获取工单列表
export function taskList(params) {
  return request({
    url: '/order/task/list',
    method: 'get',
    params,
  })
}

export function cancelTask(data) {
  return request({
    url: '/order/task/cancel-task',
    method: 'post',
    data,
  })
}
export function reopenTask(data) {
  return request({
    url: '/order/task/reopen',
    method: 'post',
    data,
  })
}
// 拒绝工单
export function rejectTask(data) {
  return request({
    url: '/order/task/reject-task',
    method: 'post',
    data,
  })
}
export function markHandle(data) {
  return request({
    url: '/order/task/mark-handle',
    method: 'post',
    data,
  })
}

// 拒绝工单
export function markHandleIng(data) {
  return request({
    url: '/order/task/mark-handle-ing',
    method: 'post',
    data,
  })
}


// 获取工单详情
export function taskDetail(taskNum) {
  return request({
    url: `/order/task/get-task-info/${taskNum}`,
    method: 'get',
  })
}

//催单记录
export function remindRecord(params) {
  return request({
    url:'/order/reminder/list-v2',
    method:'get',
    params
  })
}

//获取催单次数
export function getRemindCount() {
  return request({
    url: '/order/reminder/reminder-count',
    method: 'get',
  })
}
