import request from '@/utils/request.js'

//售后单列表 /order/task/backend/after-sale-list
export function getAfterSaleList(params) {
  return request({
    url: '/order/task/backend/after-sale-list',
    method: 'get',
    params,
  })
}

//售后单统计  /order/task/backend/after-sale-count
export function getAfterSaleCount() {
  return request({
    url: '/order/task/backend/after-sale-count',
    method: 'get',
  })
}

//售后单详情 /order/task/backend/get-after-sale-task-info/{taskNum}
export function getAfterSaleDetail(taskNum) {
  return request({
    url: `/order/task/backend/get-after-sale-task-info/${taskNum}`,
    method: 'get',
  })
}

//确认售后 /order/task/backend/afterSale/confirm
export function confirmAfterSale(data) {
  return request({
    url: '/order/task/backend/afterSale/confirm',
    method: 'post',
    data,
  })
}

//拒绝售后 /order/task/backend/afterSale/refuse
export function refuseAfterSale(data) {
  return request({
    url: '/order/task/backend/afterSale/refuse',
    method: 'post',
    data,
  })
}

//重新打开 /order/task/backend/afterSale/reopen
export function reopenAfterSale(data) {
  return request({
    url: '/order/task/backend/afterSale/reopen',
    method: 'post',
    data,
  })
}

//取消售后 /order/task/backend/afterSale/closeOrder
export function cancelAfterSale(data) {
  return request({
    url: '/order/task/backend/afterSale/closeOrder',
    method: 'post',
    data,
  })
}

//撤销申请 /order/task/backend/afterSale/cancelApplication
export function cancelApplication(params) {
  return request({
    url: '/order/task/backend/afterSale/cancelApplication',
    method: 'post',
    params,
  })
}

//完结工单 /order/task/finish-work-order
export function finishAfterSale(params) {
  return request({
    url: '/order/task/backend/afterSale/finish',
    method: 'post',
    params,
  })
}

// 处理人 /order/task/list/assignee/select
export function selectAssigneeList(params) {
  return request({
    url: '/order/task/list/assignee/select',
    method: 'post',
    params,
  })
}

//查询模特 /order/task/list/model/select
export function selectModelList(params) {
  return request({
    url: '/order/task/list/model/select',
    method: 'post',
    params
  })
}

// 提交人 /order/task/list/submit/select
export function selectSubmitList(params) {
  return request({
    url: '/order/task/list/submit/select',
    method: 'post',
    params
  })
}

// 任务关联人
export function selectRelevanceUserList(params) {
  return request({
    url: '/order/task/list/relevance/select',
    method: 'post',
    params
  })
}

//售后单拒绝 /order/task/backend/afterSale/refuseCancelApplication
export function refuseCancelApplication(params) {
  return request({
    url: '/order/task/backend/afterSale/refuseCancelApplication',
    method: 'post',
    params,
  })
}

//售后单同意售后 /order/task/backend/afterSale/agreeCancelApplication
export function agreeCancelApplication(params) {
  return request({
    url: '/order/task/backend/afterSale/agreeCancelApplication',
    method: 'post',
    params,
  })
}

//申请取消 /order/task/backend/afterSale/applicationForCancellation
export function applicationForCancellation(data) {
  return request({
    url: '/order/task/backend/afterSale/applicationForCancellation',
    method: 'post',
    data,
  })
}