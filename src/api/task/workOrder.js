import request from '@/utils/request'

// 工单-处理人下拉列表
export function assigneeSelectList(params) {
  return request({
    url: '/order/task/list/assignee/select',
    method: 'post',
    params,
  })
}

// 工单-历史处理人下拉列表
export function historyAssigneeSelectList() {
  return request({
    url: '/order/task/list/history/assignee/select',
    method: 'post',
  })
}

// 工单-提交人下拉列表
export function submitSelectList(params) {
  return request({
    url: '/order/task/list/submit/select',
    method: 'post',
    params,
  })
}

// 工单-任务关联人下拉列表
export function selectRelevanceUserList(params) {
  return request({
    url: '/order/task/list/relevance/select',
    method: 'post',
    params
  })
}

// 工单-拍摄模特下拉列表
export function modelSelectList(params) {
  return request({
    url: '/order/task/list/model/select',
    method: 'post',
    params,
  })
}

// 获取工单列表统计
export function workOrderCount() {
  return request({
    url: '/order/task/backend/work-order-count',
    method: 'get',
  })
}

// 获取工单列表
export function workOrderList(params) {
  return request({
    url: '/order/task/work-order-list',
    method: 'get',
    params,
  })
}

// 获取工单详情
export function workOrderDetail(taskNum) {
  return request({
    url: `/order/task/get-work-order-info/${taskNum}`,
    method: 'get',
  })
}

// 关闭工单
export function closeWorkOrder(params) {
  return request({
    url: `/order/task/close-work-order`,
    method: 'post',
    params
  })
}

// 重新打开
export function reopenWorkOrder(data) {
  return request({
    url: `/order/task/reopen-work-order`,
    method: 'post',
    data
  })
}

// 完结工单
export function finishWorkOrder(params) {
  return request({
    url: `/order/task/finish-work-order`,
    method: 'post',
    params
  })
}

// 拒绝工单
export function refuseWorkOrder(data) {
  return request({
    url: `/order/task/refuse-work-order`,
    method: 'post',
    data
  })
}

// 指派工单处理人
export function assignHandler(data) {
  return request({
    url: `/order/task/assign-handler`,
    method: 'post',
    data
  })
}

// 新增处理记录
export function addWorkOrderProcessRecord(data) {
  return request({
    url: `/order/task/add-work-order-process-record`,
    method: 'post',
    data
  })
}

// 查询工单处理记录
export function getWorkOrderProcessRecord(taskNum) {
  return request({
    url: `/order/task/work-order-process-record/${taskNum}`,
    method: 'get',
  })
}

// 反馈素材给商家 _获取视频订单相关联的任务单信息
export function getFeedbackMaterialPendingTask(params) {
  return request({
    url: `/order/task/get-feedback-material-pending-task`,
    method: 'get',
    params
  })
}

// 帮模特反馈素材 _获取视频订单相关联的任务单信息
export function getBackHelpModelUploadMaterialPendingTask(params) {
  return request({
    url: `/order/task/get-back-help-model-upload-material-pending-task/${params.videoId}`,
    method: 'get',
    // params
  })
}

// 申请补偿退款 OR 补发物流 _获取视频订单相关联的任务单信息
export function getReturnPendingTask(params) {
  return request({
    url: `/order/task/get-refund-pending-task/${params.videoId}`,
    method: 'get',
    params
  })
}