<template>
  <div class="table-page">
    <slot name="tableHeader"></slot>
    <el-table
      ref="tableRef"
      :data="data"
      style="width: 100%"
      v-loading="loading"
      :header-cell-style="{
        'background-color': '#fafafa',
        color: '#000000d9',
        'font-size': '13px',
      }"
      :height="height"
      :max-height="maxHeight"
      :row-style="rowStyle"
      :row-key="rowKey"
      @selection-change="$emit('selection-change', $event)"
      @sort-change="$emit('sort-change', $event)"
      v-bind="tableOptions"
    >
      <template #empty>
        <slot name="empty">
          <el-empty description="暂无数据" :image-size="80"></el-empty>
        </slot>
      </template>
      <template v-for="(item, index) in columns">
        <el-table-column
          v-if="item.type == 'selection'"
          :key="index"
          :align="item.align || 'center'"
          v-bind="item"
        />
        <el-table-column
          v-if="item.type == 'index'"
          :key="index"
          :align="item.align || 'center'"
          v-bind="item"
        />
        <el-table-column
          v-if="!item.type"
          :key="index"
          :prop="item.prop"
          :label="item.label"
          :sortable="item.sortable || false"
          :align="item.align || 'center'"
          :min-width="item.minWidth || ''"
          :width="item.width || ''"
        >
          <template #header v-if="item.labelSlot">
            <slot :name="item.labelSlot"></slot>
          </template>
          <template v-slot="scope">
            <!-- 插槽 -->
            <template v-if="item.slot">
              <slot :name="item.slot" :row="scope.row" :index="scope.$index" />
            </template>
            <!-- 文字省略提示 -->
            <template v-else-if="item.ellipsis">
              <!-- <el-tooltip
                :content="item.handle ? item.handle(scope.row[item.prop], scope.row) : scope.row[item.prop]"
                :placement="item.tooltipPlacement || 'top'"
                :hide-after="100"
                v-bind="item.tooltip"
              >
                <template #content>
                  <div style="max-width: 600px; white-space: pre-wrap; max-height: 45vh; overflow-y: auto">
                    {{ item.handle ? item.handle(scope.row[item.prop], scope.row) : scope.row[item.prop] }}
                  </div>
                </template> -->
              <div
                class="more-ell template-pre"
                :style="{ '--l': line(item.line), 'text-align': item.align || 'center' }"
                style="white-space: pre-line; work-break: break-all"
                v-ellipsis-tooltips="
                  item.handle ? item.handle(scope.row[item.prop], scope.row) : scope.row[item.prop]
                "
              >
                {{ item.handle ? item.handle(scope.row[item.prop], scope.row) : scope.row[item.prop] || '' }}
              </div>
              <!-- </el-tooltip> -->
            </template>
            <!-- 自定义dom处理 -->
            <template v-else-if="item.html">
              <div v-html="item.html(scope.row[item.prop], scope.row)" :style="item.htmlStyle || {}"></div>
            </template>
            <!-- 直接显示或自定义函数处理 -->
            <template v-else>
              <div style="white-space: pre-wrap">
                {{ item.handle ? item.handle(scope.row[item.prop], scope.row) : scope.row[item.prop] || '' }}
              </div>
            </template>
          </template>
        </el-table-column>
      </template>
      <el-table-column
        v-if="tableAction"
        :label="tableAction.label || '操作'"
        :align="tableAction.align || 'center'"
        v-bind="tableAction"
      >
        <template v-slot="scope">
          <slot name="tableAction" :row="scope.row" :index="scope.$index"></slot>
        </template>
      </el-table-column>
    </el-table>
    <div class="tablePage" v-if="isTablePage && !isFloatPage">
      <el-pagination
        background
        @size-change="val => $emit('pageChange', { currentPage: 1, pageSize: val })"
        @current-change="val => $emit('pageChange', { currentPage: val, pageSize: props.pageSize })"
        :current-page="props.currentPage"
        :page-sizes="props.pageSizes"
        :page-size="props.pageSize"
        :layout="layout"
        :total="props.total"
      />
      <slot name="tableFootLeft"></slot>
    </div>
    <div v-if="isFloatPage && isTablePage">
      <PaginationFloatBar
        :current-page="props.currentPage"
        :page-size="props.pageSize"
        @update:current-page="val => $emit('pageChange', { currentPage: val, pageSize: props.pageSize })"
        :total="props.total"
      >
        <slot name="pageLeft"></slot>
      </PaginationFloatBar>
    </div>
  </div>
</template>

<script setup name="ElTablePage">
const props = defineProps({
  columns: {
    type: Array,
    default: () => [],
    required: true,
  },
  data: {
    type: Array,
    default: () => [],
    required: true,
  },
  loading: {
    type: Boolean,
    default: false,
  },
  rowStyle: {
    type: Object,
    default: () => {},
  },
  layout: {
    type: String,
    default: 'total, prev, pager, next, jumper',
  },
  height: {
    type: [String, Number],
    default: undefined,
  },
  maxHeight: {
    type: [String, Number],
    default: undefined,
  },
  rowKey: {
    type: [String, Function],
    required: false,
  },
  isTablePage: {
    type: Boolean,
    default: true,
  },
  isFloatPage: {
    type: Boolean,
    default: true,
  },
  tableAction: {
    type: [Object, Boolean],
  },
  tableOptions: {
    type: Object,
    default: () => ({}),
  },
  tableFootLeft: {
    type: Object,
    default: () => ({}),
  },
  currentPage: {
    type: Number,
    default: 1,
  },
  pageSizes: {
    type: Array,
    default: () => [10, 20, 50, 100],
  },
  pageSize: {
    type: Number,
    default: 20,
  },
  total: {
    type: Number,
    default: 0,
  },
})

const tableRef = ref()

function line(n) {
  if (n && typeof n == 'number') {
    return n
  }
  return 1
}
// 切换行选中
function toggleRowSelection(data) {
  nextTick(() => {
    if (Array.isArray(data)) {
      data.forEach(row => {
        tableRef.value.toggleRowSelection(row)
      })
    } else {
      tableRef.value.toggleRowSelection(data)
    }
  })
}
// 取消行选中
function clearRowSelection(data) {
  nextTick(() => {
    if (Array.isArray(data)) {
      data.forEach(row => {
        tableRef.value.toggleRowSelection(row, false)
      })
    } else {
      tableRef.value.toggleRowSelection(data, false)
    }
  })
}
// 清空所有选中
function clearSelection() {
  tableRef.value.clearSelection()
}

function clearSort() {
  tableRef.value.clearSort()
}

defineExpose({ toggleRowSelection, clearSelection, clearRowSelection, clearSort })
</script>

<style lang="scss" scoped>
.table-page {
  padding-bottom: 45px;
  .tablePage {
    display: flex;
    justify-content: space-between;
    flex-direction: row-reverse;
    margin-top: 12px;
  }
  :deep(.el-table) {
    // margin-bottom: 45px;
    .el-table__header-wrapper {
      .seltAllbtnDis {
        .cell {
          .el-checkbox {
            .el-checkbox__input {
              .el-checkbox__inner {
                background-color: var(--el-checkbox-disabled-input-fill);
                border-color: var(--el-checkbox-disabled-border-color);
                cursor: not-allowed;
              }
            }
          }
          // visibility: hidden !important;
        }
      }
    }
  }
}
</style>
