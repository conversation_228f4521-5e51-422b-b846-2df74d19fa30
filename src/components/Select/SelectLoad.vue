<template>
  <!-- :model-value="modelValue" -->
  <el-select-v2
    v-model="value"
    :options="list"
    :props="{
      label: keyLabel,
      value: keyValue,
    }"
    @change="change"
    filterable
    :multiple="multiple"
    :disabled="disabled"
    :remote="remote"
    :placeholder="placeholder"
    :remote-method="handleRemoteMethod"
    :loading="loading"
    :collapse-tags="collapseTags"
    :reserve-keyword="reserveKeyword"
    clearable
    style="width: 240px"
    no-data-text="无数据"
    no-match-text="无匹配数据"
    ref="elSelectDropdownRef"
  >
    <!-- <el-option v-for="item in list" :key="item[keyValue]" :label="item[keyLabel]" :value="item[keyValue]">
      <slot :row="item"></slot>
    </el-option>
    <template #loading>
      <svg class="circular" viewBox="0 0 50 50">
        <circle class="path" cx="25" cy="25" r="20" fill="none" />
      </svg>
    </template> -->
  </el-select-v2>
</template>

<script setup name="SelectLoad">
import { nextTick, watch, watchEffect } from 'vue'

const props = defineProps({
  modelValue: {
    type: [String, Number, Array],
  },
  // 开启远程搜索
  remote: {
    type: Boolean,
    default: false,
  },
  // 搜索字段名
  keyWord: {
    type: String,
    default: 'key',
  },
  keyValue: {
    type: String,
    default: 'value',
  },
  keyLabel: {
    type: String,
    default: 'label',
  },
  // 请求
  request: {
    type: Function,
    required: true,
  },
  requestParams: {
    type: Object,
    default: () => ({}),
  },
  // 请求赋值处理
  requestCallback: {
    type: Function,
    default: data => data,
  },
  placeholder: {
    type: String,
    default: '请选择',
  },
  multiple: {
    type: Boolean,
    default: false,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  reserveKeyword: {
    type: Boolean,
    default: true,
  },
  collapseTags: {
    type: Boolean,
    default: false,
  }
})

const elSelectDropdownRef = ref()

const emits = defineEmits(['update:modelValue', 'change'])

const list = ref([])
const loading = ref(false)
const value = ref()

watch(() => props.modelValue, val => {
  value.value = val
},{
  immediate: true,
})

// watchEffect(() => {
//   value.value = props.modelValue
// })

let timer

const handleRemoteMethod = query => {
  if (timer) {
    clearTimeout(timer)
  }
  timer = setTimeout(() => {
    loading.value = true
    getList(query)
  }, 500)
}

function change(value) {
  emits('update:modelValue', value)
  let data = list.value.find(item => item[props.keyValue] == value)
  emits('change', value, data)
}
let load = false
function getList(query) {
  if (load) return
  load = true
  props
    .request({
      // pageNum: pageNum.value,
      // pageSize: pageSize.value,
      ...props.requestParams,
      [props.keyWord]: query,
    })
    .then(res => {
      list.value = props.requestCallback(res)
    })
    .finally(() => {
      loading.value = false
      load = false
    })
}

getList()
</script>
