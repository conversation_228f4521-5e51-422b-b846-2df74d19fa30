<template>
  <div class="flex-center qrcode-box">
    <div
      class="code-img"
      :class="{
        large: size === 'large',
        small: size === 'small',
      }"
      v-loading="qrcodeLoading"
    >
      <div class="flex-column code-tips-modal cur" v-if="qrCodeError" @click="load">
        <el-icon :size="iconSize" color="#fa5151"><WarningFilled /></el-icon>
        <div class="tips-1">错误提示</div>
        <div class="tips-2">{{ qrCodeError }}</div>
      </div>

      <div class="flex-column code-tips-modal" v-else-if="qrCodeStatus === 'LOGIN_SUCCESS'">
        <el-icon :size="iconSize" color="#07c160"><SuccessFilled /></el-icon>
        <div class="tips-1">扫码成功</div>
      </div>
      <div class="flex-column code-tips-modal" v-else-if="qrCodeStatus === 'LOGINING'">
        <el-icon :size="iconSize" color="#07c160"><SuccessFilled /></el-icon>
        <div class="tips-1">已扫码</div>
        <div class="tips-2">请根据微信提示操作</div>
      </div>
      <div class="flex-column code-tips-modal cur" v-else-if="qrCodeStatus === 'DISABLE'" @click="load">
        <el-icon :size="iconSize" color="#fa5151"><CircleCloseFilled /></el-icon>
        <div class="tips-1">账号被禁用</div>
        <!-- <div class="tips-2">请刷新重试</div> -->
      </div>
      <div
        class="flex-column code-tips-modal cur"
        v-else-if="qrCodeStatus === 'BINDING'"
        @click="load"
      >
        <el-icon :size="iconSize" color="#fa5151"><WarningFilled /></el-icon>
        <div class="tips-1">该微信已被绑定</div>
        <div class="tips-2">刷新</div>
      </div>
      <div
        class="flex-column code-tips-modal cur"
        v-else-if="qrCodeStatus === 'EXPIRE' || qrCodeStatus === 'UNKNOWN'"
        @click="load"
      >
        <el-icon :size="iconSize" color="#fa5151"><WarningFilled /></el-icon>
        <div class="tips-1">{{ qrCodeStatus === 'EXPIRE' ? '二维码已过期' : '未知错误' }}</div>
        <div class="tips-2">请刷新重试</div>
      </div>

      <img v-if="qrCodeDataUrl" :src="qrCodeDataUrl" alt="" />
    </div>
  </div>
</template>

<script setup>
import QRCode from 'qrcode'
import { ElMessage, ElMessageBox } from 'element-plus'
import { wechatQrcode, checkQrcode } from '@/api/wechat'

const props = defineProps({
  // 获取code
  httpCode: {
    type: Function,
    default: wechatQrcode,
  },
  // 检测code状态
  checkCode: {
    type: Function,
    default: checkQrcode,
  },
  // 自动状态检测
  autoCheckCode: {
    type: Boolean,
    default: true,
  },
  // 0:登录 1:绑定 2:验证
  codeType: {
    type: Number,
    default: 0,
  },
  delay: {
    type: Number,
    default: 1300,
  },
  size: {
    type: String,
    default: '',
  },
})

defineExpose({
  load,
  clear,
})

const emits = defineEmits(['change','error','success'])

const qrcodeLoading = ref(false)
const qrCodeDataUrl = ref('')
const qrCodeError = ref('')
const qrCodeStatus = ref('WAITING')
let timer
let checkErrorIndex = 0
let isAlert = true

const iconSize = computed(() => {
  if (props.size === 'large') {
    return 55
  }
  if (props.size === 'small') {
    return 30
  }
  return 42
})

function load() {
  getCode()
}
function clear() {
  checkErrorIndex = 0
  if (timer) clearInterval(timer)
}

function getCode() {
  qrcodeLoading.value = true
  qrCodeStatus.value = 'WAITING'
  props
    .httpCode({ type: props.codeType })
    .then(async (res) => {
      try {
        const dataUrl = await QRCode.toDataURL(res.data.qrcode, {
          errorCorrectionLevel: 'H',
          width: '100%',
        })
        qrCodeDataUrl.value = dataUrl
        qrCodeError.value = ''
        checkErrorIndex = 5
        checkCode(res.data.ticket)
      } catch (error) {
        qrCodeError.value = error?.data?.msg || 'Failed to generate QR Code'
        emits('error', error)
        console.error('Failed to generate QR Code', error)
      }
    })
    .catch((err) => {
      qrCodeError.value = err?.data?.msg || 'Failed to obtain QR code'
      emits('error', err)
      console.error('Failed to obtain QR code', err)
    })
    .finally(() => (qrcodeLoading.value = false))
}
// 方案1
// function checkCode(ticket: string) {
//   timer = setInterval(() => {
//     props
//       .checkCode({ ticket })
//       .then((res: any) => {
//         if(props.autoCheckCode) {
//           updateCodeStatus(res.data.loginStatus, { ticket, ...res.data })
//         }
//         emits('change', res, ticket, updateCodeStatus, clear)
//       })
//       .catch((error: any) => {
//         emits('error', error)
//         checkErrorIndex--
//         if (checkErrorIndex === 0) {
//           clear()
//           qrCodeStatus.value = 'UNKNOWN'
//         }
//       })
//   }, props.delay)
// }
// 方案2
async function checkCode(ticket) {
  while (checkErrorIndex) {
    await new Promise((reslove, reject) => {
      setTimeout(() => {
        props
          .checkCode({ ticket })
          .then((res) => {
            let next = () => reslove(true)
            let stop = (err) => reject(err)
            if (props.autoCheckCode) {
              updateCodeStatus(res.data.loginStatus, { ticket, ...res.data })
              reslove(true)
              next = () => {}
              stop = () => {}
            }
            emits('change', {
              res,
              ticket,
              type: props.codeType,
              update: updateCodeStatus,
              next,
              stop,
            })
          })
          .catch((error) => {
            emits('error', error)
            checkErrorIndex--
            if (checkErrorIndex === 0) {
              clear()
              qrCodeStatus.value = 'UNKNOWN'
              reject(false)
            }
          })
      }, props.delay)
    })
  }
}
function updateCodeStatus(status, data) {
  switch (status) {
    case 'WAITING':
      qrCodeStatus.value = 'WAITING'
      break
    case 'LOGINING':
      qrCodeStatus.value = 'LOGINING'
      break
    case 'DISABLE':
      ElMessage.error('您的账户已被禁用！')
      clear()
      qrCodeStatus.value = 'DISABLE'
      break
    case 'BINDING':
      ElMessage.error('该账户已被绑定！')
      clear()
      qrCodeStatus.value = 'BINDING'
      break
    case 'EXPIRE':
      clear()
      qrCodeStatus.value = 'EXPIRE'
      if (isAlert) {
        isAlert = false
        ElMessageBox.alert('此二维码已过期！请刷新', '扫码提示', {
          autofocus: false,
          confirmButtonText: '刷新',
          callback: () => {
            isAlert = true
            load()
          },
        })
      }
      break
    case 'LOGIN_SUCCESS':
    case 'LOGIN_NO_PHONE':
      qrCodeStatus.value = 'LOGIN_SUCCESS'
      clear()
      emits('success', data)
      break
    case 'ERROR':
      qrCodeError.value = data
      clear()
      break
    default:
      ElMessage.error('未知错误！请刷新重试')
      clear()
      qrCodeStatus.value = 'UNKNOWN'
      break
  }
}

onUnmounted(() => {
  clear()
})

load()
</script>

<style scoped lang="scss">
.qrcode-box {
  .code-img {
    position: relative;
    width: 140px;
    height: 140px;

    img {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      z-index: 1;
    }

    .code-tips-modal {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      z-index: 2;
      color: var(--text-color);
      gap: 10px;
      background-color: rgba(255, 255, 255, 0.95);
      justify-content: center;

      .tips-1 {
        text-align: center;
        font-size: 18px;
      }
      .tips-2 {
        text-align: center;
        font-size: 13px;
      }
    }

    .cur {
      cursor: pointer;
    }

    &.large {
      width: 180px;
      height: 180px;

      .code-tips-modal {
        .tips-1 {
          font-size: 20px;
        }
        .tips-2 {
          font-size: 14px;
        }
      }
    }
    &.small {
      width: 100px;
      height: 100px;

      .code-tips-modal {
        gap: 0px;

        .tips-1 {
          font-size: 16px;
        }
        .tips-2 {
          width: 90%;
          font-size: 12px;
        }
      }
    }
  }
}
</style>
