<template>
  <el-dialog
    v-model="dialogVisible"
    width="500px"
    :title="title"
    align-center
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="confirm-box">
      <el-upload
        class="upload-proof"
        ref="uploadProofRef"
        :file-list="fileList"
        drag
        :multiple="multiple"
        action=""
        :auto-upload="false"
        @change="handleChange"
        @remove="handleRemove"
      >
        <slot>
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text flex-column">
            <span class="up-text">选择要上传的文件</span>
            <span>或将其拖放到此处</span>
          </div>
        </slot>
      </el-upload>
    </div>
    <template #footer>
      <div class="flex-center btn">
        <slot name="button">
          <el-button v-btn plain round @click="close">取消</el-button>
          <el-button v-btn type="primary" round @click="onConfirm">确认</el-button>
        </slot>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ElMessage, ElLoading } from "element-plus"
import { uploadCloudFile,uploadFile } from '@/api/index'
import { handlePasteFile, beforeUpload } from '@/utils/public'
import { unref } from "vue"

const dialogVisible = ref(false)
const uploadProofRef = ref(null)
const fileList = ref([])

const props = defineProps({
  modelValue: {
    type: Array,
  },
  title: {
    type: String,
    default: '文件上传',
  },
  autoUpload: {
    type: Boolean,
    default: true,
  },
  // MB
  size: {
    type: Number,
    default: 5,
  },
  // 0不限制上传个数
  limit: {
    type: Number,
    default: 0,
  },
  fileType: {
    type: Array,
    default: () => ['jpg', 'jpeg', 'png'],
  },
  multiple: {
    type: Boolean,
    default: true,
  },
  // 上传成功前，例：需要在上传成功返回时直接请求
  beforeSuccess: {
    type: Function,
  },
  //上传类型
  bucketType:{
    type: String,
    default: '',
  },
})

defineExpose({
  open,
  close,
})

const emits = defineEmits(['update:modelValue', 'change', 'success'])

function open() {
  dialogVisible.value = true
}
function close() {
  dialogVisible.value = false
}
function handleClose() {
  fileList.value = []
  uploadProofRef.value?.clearFiles()
}

function handlePaste(event) {
  if (!dialogVisible.value) return
  handlePasteFile(event, {
    fileType: props.fileType,
    size: props.size,
  }).then(files => {
    if (files.length) {
      if (props.limit && files.length > props.limit) {
        ElMessage.warning(`最多只能上传${props.limit}个文件`)
        return
      }
      files.forEach(file => {
        fileList.value.push({
          raw: file,
          name: file.name,
          size: file.size,
          status: 'ready',
          uid: file.uid,
        })
      })
      emits('change', fileList.value)
    }
  })
}

function handleChange(file, files) {
  fileList.value = files
  emits('change', fileList.value)
}
function handleRemove(file, files) {
  fileList.value = files
  emits('change', fileList.value)
}

function onConfirm() {
  // console.log(fileList.value);
  if (!fileList.value.length) {
    ElMessage.warning('请选择要上传的文件')
    return
  }
  if (props.limit && fileList.value.length > props.limit) {
    ElMessage.warning(`最多只能上传${props.limit}个文件`)
    return
  }
  if (
    beforeUpload(fileList.value, {
      size: props.size,
      fileType: props.fileType,
    })
  ) {
    if (!props.autoUpload) {
      emits('success', unref(fileList.value))
      close()
      return
    }
    const el_loading = ElLoading.service({
      lock: true,
      text: '正在上传中，请稍后',
      background: 'rgba(0, 0, 0, 0.7)',
    })
    let requests = []
    fileList.value.forEach(f => {
      const fromData = new FormData()
      fromData.append('file', f.raw)
      // props.isCloudUpload ? requests.push(uploadCloudFile(fromData)) : requests.push(uploadFile(fromData))
      requests.push(uploadCloudFile(fromData,props.bucketType))
    })
    Promise.all(requests)
      .then(res => {
        let data = []
        res.forEach(item => {
          data.push(item.data)
        })
        emits('update:modelValue', data)
        if (props.beforeSuccess) {
          props.beforeSuccess(data, () => {
            el_loading.close()
            close()
          })
        } else {
          emits('success', data)
          el_loading.close()
          close()
        }
      })
      .catch(() => el_loading.close())
  }
}

onMounted(() => {
  document.addEventListener('paste', handlePaste)
})
onUnmounted(() => {
  document.removeEventListener('paste', handlePaste)
})
</script>

<style scoped lang="scss">
.confirm-box {
  gap: 20px;
  // max-height: 300px;
  padding-bottom: 15px;
  position: relative;

  .upload-proof {
    width: 100%;
    // height: 180px;

    :deep(.el-upload-dragger) {
      padding: 10px;
    }
    :deep(.el-upload-list) {
      max-height: 100px;
      overflow-y: auto;
    }
    :deep(.el-upload-list__item) {
      width: 100%;
      height: auto;
    }
  }

  .el-icon--upload {
    color: var(--el-color-primary);
  }
  .up-text {
    font-size: 16px;
    color: var(--el-color-primary);
  }
}
.btn {
  .el-button {
    padding: 8px 20px;
  }
}
</style>
