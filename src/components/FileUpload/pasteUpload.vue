<template>
  <div class="upload-proof-box">
    <el-upload
      class="upload-proof"
      ref="UploadProofRef"
      v-show="!limit || limit > modelValue.length || alwaysShow"
      drag
      :multiple="multiple"
      action=""
      :auto-upload="false"
      :show-file-list="false"
      :disabled="(limit && limit <= modelValue.length) || disabled"
      :class="{ 'upload-disabled': (limit && limit <= modelValue.length) || disabled }"
      @change="handleChange"
    >
      <slot>
        <el-icon class="el-icon--upload" size="50"><upload-filled /></el-icon>
        <div class="el-upload__text flex-column">
          <span class="up-text">选择要上传的文件</span>
          <span>或将其拖放{{ pasteDisabled ? '' : '、粘贴' }}到此处</span>
        </div>
      </slot>
    </el-upload>
    <template v-if="showFileList">
      <div v-for="(f, i) in props.modelValue" class="flex-start gap-10 file-item">
        <div class="one-ell text" @click="handleView(f.picUrl)">{{ f.name }}</div>
        <el-icon @click="handleView(f.picUrl)"><View /></el-icon>
        <el-icon v-if="deleteButton" @click="handleRemove(i)"><Delete /></el-icon>
        <el-icon v-if="handleShowDown(f.picUrl)" @click="getFileUrl(f.picUrl)"><Download /></el-icon>
      </div>
    </template>
  </div>
</template>

<script setup>
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'
import { uploadCloudFile } from '@/api/index'
import { handlePasteFile, beforeUpload } from '@/utils/public'
import { downUrlFile } from '@/utils/index'
import { useViewer } from '@/hooks/useViewer'
import { getCurrentInstance, nextTick } from 'vue'
import { debounce } from '@/utils/public'

const { showViewer } = useViewer()
const UploadProofRef = ref()
const { proxy } = getCurrentInstance()

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => [],
  },
  title: {
    type: String,
    default: '文件上传',
  },
  // MB
  size: {
    type: Number,
    default: 10,
  },
  // 0不限制上传个数
  limit: {
    type: Number,
    default: 0,
  },
  fileType: {
    type: Array,
    default: () => ['jpg', 'jpeg', 'png'],
  },
  multiple: {
    type: Boolean,
    default: true,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  bucket: {
    type: String,
    default: '',
  },
  showFileList: {
    type: Boolean,
    default: false,
  },
  deleteButton: {
    type: Boolean,
    default: true,
  },
  isClear: {
    type: Boolean,
    default: false,
  },
  alwaysShow: {
    type: Boolean,
    default: false,
  },
  curIndex: {
    type: Number,
    default: null,
  },
  resolutionRatio: {
    type: Boolean,
    default: false,
  },
  // 禁用粘贴上传
  pasteDisabled: {
    type: Boolean,
    default: false,
  },
})

const emits = defineEmits(['update:modelValue', 'change', 'success', 'view-pdf'])

function handlePaste(event) {
  if ((props.limit && props.limit <= props.modelValue.length) || props.disabled) return
  handlePasteFile(event, {
    fileType: props.fileType,
    size: props.size,
  }).then(files => {
    if (files.length) {
      emits('change', files)
      upload(
        files.map(file => ({
          raw: file,
          name: file.name,
          size: file.size,
          status: 'ready',
          uid: file.uid,
        }))
      )
    }
  })
}
const changeTimeout = ref(null)
function handleChange(file, files) {
  emits('change', files)
  if (changeTimeout.value) {
    clearTimeout(changeTimeout.value)
  }
  changeTimeout.value = setTimeout(() => {
    upload(files) // 只处理最后一次变化的文件
  }, 300) // 300毫秒后执行，可以根据需求调整延迟时间
  // upload(files)
}

async function handleResolutionRatio(files) {
  const maxWidth = 3840
  const maxHeight = 2160
  const minWidth = 640
  const minHeight = 360
  const reader = new FileReader()
  let isUpload = true
  return new Promise(resolve => {
    reader.readAsDataURL(files[0].raw)
    reader.onload = async e => {
      const img = new Image()
      img.src = reader.result
      img.onload = async () => {
        const width = img.width
        const height = img.height
        if (width > maxWidth || height > maxHeight || width < minWidth || height < minHeight) {
          isUpload = false
        }
        resolve(isUpload)
      }
    }
  })
}

async function upload(files) {
  if (props.limit && props.modelValue.length + files.length > props.limit) {
    ElMessage.warning(`最多只能上传${props.limit}个文件`)
    UploadProofRef.value?.clearFiles()
    return
  }

  if (props.resolutionRatio) {
    const isUpload = await handleResolutionRatio(files)
    if (!isUpload) {
      ElMessage.warning('分辨率推荐上传 1920*1080px （最小上传640*360px/最大上传3840*2160px）')
      UploadProofRef.value?.clearFiles()
      return
    }
  }

  if (
    beforeUpload(files, {
      size: props.size,
      fileType: props.fileType,
    })
  ) {
    const el_loading = ElLoading.service({
      lock: true,
      text: '正在上传中，请稍后',
      background: 'rgba(0, 0, 0, 0.7)',
    })
    let requests = []
    files.forEach(f => {
      const fromData = new FormData()
      fromData.append('file', f.raw)
      requests.push(uploadCloudFile(fromData, props.bucket))
    })
    // 上传文件问题处理
    // props.isClear ? UploadProofRef.value?.clearFiles() : ''
    Promise.all(requests)
      .then(res => {
        ElMessage.success('上传成功')
        if (props.curIndex || props.curIndex == 0) {
          emits('success', res, props.curIndex)
        } else {
          emits('success', res)
        }
        res.forEach(item => {
          props.modelValue.push(item.data)
        })
        validate()
      })
      .finally(() => {
        UploadProofRef.value?.clearFiles()
        el_loading.close()
      })
  } else {
    UploadProofRef.value?.clearFiles()
  }
}

function handleView(url) {
  const fileSuffix = url.substring(url.lastIndexOf('.') + 1).toLowerCase()
  if (fileSuffix === 'pdf') {
    emits('view-pdf', url)
  } else if (fileSuffix === 'ofd' || fileSuffix === 'xml') {
    ElMessageBox.alert(`暂不支持预览${fileSuffix}文件`, '温馨提示', {
      confirmButtonText: '知道了',
    })
  } else if (fileSuffix === 'jpg' || fileSuffix === 'png' || fileSuffix === 'jpeg') {
    showViewer([url])
  }
}

function handleRemove(i) {
  props.modelValue.splice(i, 1)
  validate()
}

function handleShowDown(url) {
  const fileSuffix = url.substring(url.lastIndexOf('.') + 1).toLowerCase()
  if (fileSuffix === 'ofd' || fileSuffix === 'xml') {
    return true
  }
}

function getFileUrl(objectKey) {
  const fileSuffix = objectKey.substring(objectKey.lastIndexOf('/') + 1)
  downUrlFile(objectKey, fileSuffix)
}

function validate() {
  nextTick(() => {
    if (proxy.$parent?.validate) proxy.$parent.validate()
  })
}

onMounted(() => {
  if (!props.pasteDisabled) document.addEventListener('paste', handlePaste)
})

onBeforeUnmount(() => {
  if (!props.pasteDisabled) document.removeEventListener('paste', handlePaste)
})
</script>

<style scoped lang="scss">
.upload-proof-box {
  width: 100%;

  .upload-proof {
    position: relative;
    width: 100%;
    // height: 180px;

    :deep(.el-upload-dragger) {
      padding: 10px;
    }
    :deep(.el-upload-list) {
      max-height: 100px;
      overflow-y: auto;
    }
    :deep(.el-upload-list__item) {
      width: 100%;
      height: auto;
    }

    &.upload-disabled {
      &::after {
        content: '';
        display: block;
        cursor: not-allowed;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(255, 255, 255, 0.4);
      }
    }
  }
  .file-item {
    width: 100%;

    .text {
      max-width: 90%;
      cursor: pointer;

      &:hover {
        color: var(--el-color-primary);
      }
    }
    .el-icon {
      cursor: pointer;
      color: var(--el-color-primary);
    }
  }
}

.el-icon--upload {
  color: var(--el-color-primary);
  margin-bottom: 0;
}
.el-upload__text {
  font-size: 14px;
  line-height: 20px;
}
</style>
