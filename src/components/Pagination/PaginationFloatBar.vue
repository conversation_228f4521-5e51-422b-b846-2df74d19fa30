<template>
  <div class="pagination-float-bar" :style="{ left: sidebar.opened ? '160px' : '54px' }">
    <div class="left-content">
      <slot></slot>
    </div>
    <div class="flex-start gap-10 right-pagination">
      <el-button class="back-to-top" icon="Upload" v-btn @click="scrollToTop">返回顶部</el-button>
      <el-pagination
        background
        :layout="layout"
        :total="total"
        :page-size="pageSize"
        :current-page="currentPage"
        @current-change="onPageChange"
        @size-change="onSizeChange"
        :page-sizes="pageSizes"
      />
    </div>
  </div>
</template>

<script setup>
import useAppStore from '@/store/modules/app'
const { scrollToTop } = inject('appMainScroll')
const sidebar = computed(() => useAppStore().sidebar)

const props = defineProps({
  total: { type: Number, required: true },
  pageSize: { type: Number, default: 20 },
  currentPage: { type: Number, default: 1 },
  pageSizes: { type: Array, default: () => [10, 20, 30, 50] },
  layout: {
    type: String,
    default: 'total, prev, pager, next, jumper',
  },
})

const emits = defineEmits(['update:currentPage', 'update:pageSize'])

function onPageChange(page) {
  emits('update:currentPage', page, props.pageSize)
}
function onSizeChange(size) {
  emits('update:pageSize', size)
}
</script>

<style scoped>
.pagination-float-bar {
  position: fixed;
  left: 160px;
  right: 0;
  bottom: 0;
  z-index: 999;
  background: #fff;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  justify-content: start;
  padding: 12px 24px;
  gap: 12px;
  transition: left 0.3s ease;
}
.left-content {
  display: flex;
  align-items: center;
  /* gap: 12px; */
}

.right-pagination {
  margin-left: auto;
}
</style>
