<template>
  <el-input
    v-model="textValue"
    type="textarea"
    :rows="rows"
    :placeholder="placeholder"
    :maxlength="length"
    :show-word-limit="isShowLimit"
    @input="handleInput"
    @keydown="handleInputFocus"
    resize="none"
  />
</template>

<script setup name="InputText">
const props = defineProps({
  modelValue: {
    type: String,
  },
  placeholder: {
    type: String,
    default: '请输入',
  },
  rows: {
    type: Number,
    default: 3,
  },
  length: {
    type: Number,
    default: 800,
  },
  isShowLimit: {
    type: Boolean,
    default: true,
  },
})
const textValue = ref('')
const emits = defineEmits(['update:modelValue'])
defineExpose({
  resetValue,
})
const handleInput = () => {
  updateFormattedText()
}

function resetValue(val) {
  textValue.value = val || ''
  console.log('value reseted', val);
  
  updateFormattedText()
}

const handleInputFocus = event => {
  if (event.isComposing) return
  const start = event.target.selectionStart
  const end = event.target.selectionEnd
  if (event.key === 'Enter') {
    event.preventDefault()
    const lines = textValue.value.split('\n').length
    let step = 4
    if (lines === 1) step = 5
    if (lines < 99) {
      const value = textValue.value
      textValue.value = value.substring(0, start) + '\n ' + value.substring(end)
      updateFormattedText()
      nextTick(() => {
        const selectLines = value.substring(0, end).split('\n').length
        event.target.selectionStart = event.target.selectionEnd = start + step - (selectLines < 9 ? 1 : 0)
        // event.target.selectionStart = event.target.selectionEnd = start + step - (lines < 9 ? 1 : 0)
        event.target.blur()
        event.target.focus()
      })
    }
  } else if (event.key === 'Backspace') {
    const value = textValue.value
    if (start === end && start > 3) {
      const reg = /(\n\d{1,3}\.)$/
      const reg2 = /(\n\d{1,3})$/
      const str = value.substring(0, start)
      const exec = reg.exec(str)
      const exec2 = reg2.exec(str)
      // console.log(exec,111, exec2);
      if (reg.test(str) && exec) {
        event.preventDefault()
        textValue.value = value.substring(0, exec['index']) + value.substring(start)
        updateFormattedText()
        event.target.selectionStart = event.target.selectionEnd = start - 1
      } else if (reg2.test(str) && exec2) {
        event.preventDefault()
        textValue.value = value.substring(0, exec2['index']) + value.substring(start)
        updateFormattedText()
        event.target.selectionStart = event.target.selectionEnd = start - 1
      }
    }
  }
}

const updateFormattedText = () => {
  const lines = textValue.value.split('\n')
  const formattedLines = lines.map((line, index) => {
    if (!line.startsWith(`${index + 1}.`)) {
      return `${index + 1}.${line.replace(/^\d+\.*/, '')}`
    }
    return line
  })
  let temp = formattedLines.join('\n')
  if (temp.length > props.length) {
    temp = temp.substring(0, props.length)
  }
  textValue.value = temp
  emits('update:modelValue', textValue.value)
}
</script>

<style scoped lang="scss"></style>
