<template>
  <div style="border: 1px solid #ccc">
    <Toolbar
      style="border-bottom: 1px solid #ccc"
      :editor="editorRef"
      :defaultConfig="toolbarConfig"
      :mode="'default'"
    />
    <Editor
      style="height: 350px; overflow-y: hidden; word-break: break-word"
      v-model:content="content"
      :defaultConfig="editorConfig"
      :mode="'default'"
      @onCreated="handleCreated"
      @onChange="handleChanged"
    />
  </div>
</template>

<script setup>
import '@wangeditor/editor/dist/css/style.css' // 引入 css
import { onBeforeUnmount, ref, shallowRef, onMounted, getCurrentInstance } from 'vue'
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'

import { uploadCloudFile } from '@/api/index'
import { getToken } from '@/utils/auth'
const { proxy } = getCurrentInstance()

const props = defineProps({
  /* 编辑器的内容 */
  modelValue: {
    type: String,
  },
  /* 高度 */
  height: {
    type: Number,
    default: null,
  },
  /* 最小高度 */
  minHeight: {
    type: Number,
    default: null,
  },
  /* 只读 */
  readOnly: {
    type: Boolean,
    default: false,
  },
  /* 上传文件大小限制(MB) */
  fileSize: {
    type: Number,
    default: 5,
  },
  /* 类型（base64格式、url格式） */
  type: {
    type: String,
    default: 'base64',
  },
  //最大高度
  maxHeight: {
    type: Number,
    default: null,
  },
})

const headers = ref({
  Authorization: 'Bearer ' + getToken(),
})

const uploadUrl = ref(import.meta.env.VITE_APP_BASE_API + '/file/upload') // 上传的图片服务器地址
// 编辑器实例，必须用 shallowRef
const editorRef = shallowRef()

// 内容 HTML
const valueHtml = ref('')

const content = ref('')
watch(
  () => props.modelValue,
  v => {
    if (v !== content.value) {
      content.value = v === undefined || v == null ? '<p></p>' : v
      editorRef.value.setHtml(content.value)
    }
  },
  { immediate: true }
)

// 模拟 ajax 异步获取内容
onMounted(() => {
  setTimeout(() => {
    valueHtml.value = ''
  }, 1500)
})

const toolbarConfig = {
  toolbarKeys: [
    // 一些常用的菜单 key
    'bold', // 加粗
    'italic', // 斜体
    'through', // 删除线
    'underline', // 下划线
    'bulletedList', // 无序列表
    'numberedList', // 有序列表
    'color', // 文字颜色
    'fontSize', // 字体大小
    'lineHeight', // 行高
    'uploadImage', // 上传图片
    'delIndent', // 缩进
    'indent', // 增进
    'divider', // 分割线
    'justifyCenter', // 居中对齐
    'justifyJustify', // 两端对齐
    'justifyLeft', // 左对齐
    'justifyRight', // 右对齐
    'clearStyle', // 清除格式
    'insertLink', // 插入链接
  ],
}
const editorConfig = {
  placeholder: '请输入文本内容...',
  readOnly: props.readOnly,
  MENU_CONF: {
    uploadImage: {
      async customUpload(file, insertFn) {
        const isJPG = beforeUploadImage(file)
        if (!isJPG) return
        const formData = new FormData()
        formData.append('file', file)
        uploadCloudFile(formData).then(res => {
          if (res.code === 200) {
            const url = proxy.$picUrl + res.data.picUrl + '!fullSizecompress'
            insertFn(url, '', '')
          }
        })
      },
    },
  },
}

//上传前的图片上传的处理
function beforeUploadImage(file) {
  const type = ['image/jpeg', 'image/jpg', 'image/png', 'image/svg', 'image/bmp']
  const isJPG = type.includes(file.type)
  //检验文件格式
  if (!isJPG) {
    proxy.$modal.msgError(`图片格式错误!`)
    return false
  }
  // 校检文件大小
  if (props.fileSize) {
    const isLt = file.size / 1024 / 1024 < props.fileSize
    if (!isLt) {
      proxy.$modal.msgError(`上传文件大小不能超过${props.fileSize}MB!`)
      return false
    }
  }
  return true
}

// 组件销毁时，也及时销毁编辑器
onBeforeUnmount(() => {
  const editor = editorRef.value
  if (editor == null) return
  editor.destroy()
})

const handleCreated = editor => {
  editorRef.value = editor // 记录 editor 实例，重要！
}
const handleChanged = html => {
  proxy.$emit('update:modelValue', editorRef.value.getHtml())
}
</script>

<style lang="scss" scoped>
:deep(.w-e-text-placeholder) {
  top: 10px;
}
</style>
