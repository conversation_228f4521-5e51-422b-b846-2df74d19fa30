<template>
  <div style="display: flex;flex-wrap: wrap;gap: 10px;">
    <div class="image-list" v-for="(item, i) in modelValue" :key="item.id">
      <div class="image-modal flex-around">
        <el-icon size="20" color="#ffffffc4">
          <Delete style="cursor: pointer" @click="doDeleteProUrl(i)" />
        </el-icon>
        <el-icon size="20" color="#ffffffc4">
          <View style="cursor: pointer" @click="doShowProUrl(i)" />
        </el-icon>
      </div>
      <el-image
        ref="imgViewRef"
        preview-teleported
        :src="$picUrl + item.picUrl + '!squarecompress'"
        fit="fill"
        class="image-item"
        :preview-src-list="item.picUrl ? [item.picUrl] : []"
      ></el-image>
    </div>
    <div class="image-upload" @click="openDialog" v-if="modelValue && modelValue.length < limit">
      <el-icon size="28" color="#909399"><Plus /></el-icon>
    </div>
    <!-- <DragUploadDialog ref="DragUploadDialogRef" /> -->
  </div>
</template>

<script setup>
import DragUploadDialog from '@/components/FileUpload/dragUploadDialog.vue'

import { useViewer } from '@/hooks/useViewer'
const { showViewer } = useViewer()

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => [],
  },
  defaultUpload: {
    type: Boolean,
    default: true,
  },
  limit: {
    type: Number,
    default: 1,
  }
})

const DragUploadDialogRef = ref(null)

const emits = defineEmits(['update:modelValue', 'openUploadDialog'])

//打开上传的弹窗
function openDialog() {
  //   if (props.defaultUpload) {
  //     DragUploadDialogRef.value?.open()
  //   } else {
  //     emits('openUploadDialog')
  //   }
  emits('openUploadDialog')
}

function doDeleteProUrl(index) {
  const newModelValue = [...props.modelValue] // 创建一个新数组
  newModelValue.splice(index, 1) // 移除指定索引的元素
  emits('update:modelValue', newModelValue) // 发射更新事件
}

function doShowProUrl(index) {
  const list = props.modelValue?.map(item => item.picUrl) || []
  showViewer(list, { index })
}
</script>

<style scoped lang="scss">
.image-list {
  height: 70px;
  position: relative;
  // margin-right: 10px;
  // margin-bottom: 10px;
  .image-modal {
    position: absolute;
    top: 0;
    left: 0;
    width: 70px;
    height: 100%;
    border-radius: 6px;
    background-color: #2c2b2b66;
    z-index: 9;
    opacity: 0;
    &:hover {
      opacity: 1;
    }
  }
}
.image-item {
  border-radius: 6px;
  box-sizing: border-box;
  width: 70px;
  height: 70px;
  // margin-right: 10px;
}
//上传样式
.image-upload {
  box-sizing: border-box;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px dashed var(--el-border-color-darker);
  width: 70px;
  height: 70px;
  border-radius: 6px;
  background-color: var(--el-fill-color-lighter);
  cursor: pointer;
  margin-bottom: 10px;
  // margin-top: -10px;
  &:hover {
    border-color: #409eff;
  }
}
</style>
