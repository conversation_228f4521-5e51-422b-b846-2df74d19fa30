<template>
  <div>
    <div class="corner-mark-hint" v-if="row.goodsInfoChange">调</div>
    <div class="product-info-box">
      <div class="product-info-btn" :class="buttonClass">
        <slot name="productInfoBtn" :row="row"></slot>
      </div>
      <div style="padding-right: 38px">
        <span style="margin-right: 5px;">视频编码：{{ row.videoCode }}</span>
        <el-tag type="warning" round size="small" v-if="row.isGund == 1">通品</el-tag>
        <el-tag type="success" round size="small" v-else-if="row.isGund == 0">非通品</el-tag>
        <el-tag type="info" round size="small" v-else-if="row.isGund == 3">暂未选择</el-tag>
      </div>
      <div class="one-ell" style="padding-right: 38px">中文名称：{{ row.productChinese }}</div>
      <div class="one-ell" style="padding-right: 38px">英文名称：{{ row.productEnglish }}</div>
      <div class="one-ell productLink">
        产品链接：
        <el-link :underline="false" target="_blank" type="primary" :href="row.productLink">
          {{ row.productLink }}
        </el-link>
      </div>
      <biz-model-platform :value="row.platform" />
      <biz-model-type :value="row.modelType" />
      <biz-nation :value="row.shootingCountry" />
      <template v-for="op in videoFormatOptions" :key="op.value">
        <el-tag v-if="op.value == row.videoFormat" type="warning" size="small" round>
          {{ op.label }}
        </el-tag>
      </template>
    </div>
  </div>
</template>

<script setup>
import { videoFormatOptions } from '@/views/order/list/data.js'

defineProps({
  row: {
    type: Object,
    required: true,
  },
  buttonClass: {
    type: Array,
    default: () => ['flex-column gap-5'],
  },
})
</script>

<style scoped lang="scss">
.product-info-box {
  position: relative;

  .product-info-btn {
    position: absolute;
    top: 0;
    right: -3px;

    .btn {
      padding: 2px 4px;
      height: auto;
      font-size: 12px;
    }
  }
}
</style>
