<template>
  <div>
    <div class="corner-mark-hint" v-if="row.productPicChange">调</div>
    <div class="top-tag" :class="topTagClass">
      <el-tag v-if="row.isCare" type="danger" size="small" round>照顾单</el-tag>
      <el-tag
        v-if="
          row.orderVideoRefund &&
          (row.orderVideoRefund.refundStatus === 0 || row.orderVideoRefund.refundStatus == 1)
        "
        type="warning"
        size="small"
        round
      >
        退款中
      </el-tag>
    </div>
    <el-image
      style="width: 90px; height: 90px; cursor: pointer"
      :src="
        row.productPic
          ? $picUrl +
            row.productPic +
            '?imageMogr2/format/webp/interlace/1/quality/100/interlace/0/thumbnail/200x'
          : ''
      "
      fit="scale-down"
      preview-teleported
      @click="() => row.productPic && showViewer([$picUrl + row.productPic])"
    >
      <template #error>
        <img :src="$picUrl + 'static/assets/no-img.png'" alt="" style="width: 100%; height: 100%" />
      </template>
    </el-image>
    <slot name="imgBottom" :row="row"></slot>
  </div>
</template>

<script setup>
import { useViewer } from '@/hooks/useViewer'

defineProps({
  row: {
    type: Object,
    required: true,
  },
  topTagClass: {
    type: Array,
    default: () => ['flex-column gap-5'],
  },
})

const { showViewer } = useViewer()
</script>
