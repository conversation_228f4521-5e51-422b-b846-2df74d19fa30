:deep(.el-table) {
  .el-table__cell {
    position: relative;
  }

  .top-tag {
    position: absolute;
    top: 2px;
    left: 2px;
    z-index: 9;
  }

  .corner-mark-hint {
    z-index: 999;
    position: absolute;
    right: 0px;
    top: 0px;
    color: #00bfbf;
    // background-color: #d0efef;
    padding: 0px 4px 6px;
    background-image: url('@/assets/icons/svg/corner-mark-flag.svg');
  }

  .primary-row {
    --el-table-tr-bg-color: var(--el-color-primary-light-9);
  }

  .warning-row {
    --el-table-tr-bg-color: var(--el-color-warning-light-9);
  }

  .disabled-row {
    --el-table-tr-bg-color: var(--el-color-info-light-7);
  }

  .productLink {
    position: relative;
    padding-right: 5px;

    :deep(.el-link) {
      display: contents;

      .el-link__inner {
        display: inline;
      }
    }
  }
}