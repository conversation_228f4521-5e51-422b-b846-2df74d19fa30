<template>
  <el-dialog
    v-model="dialogVisible"
    width="70%"
    v-bind="$attrs"
    align-center
    destroy-on-close
    :fullscreen="fullscreen"
    :close-on-click-modal="true"
    @close="closeChange"
    :style="dialogStyle"
  >
    <!-- <template #header>
      <div class="flex-end gap-10">
        <el-icon class="icon-FullScreen" @click="fullscreen = !fullscreen" v-if="!fullscreen">
          <FullScreen />
        </el-icon>
      </div>
    </template> -->
    <template v-if="url">
      <template v-if="urlType === 'pdf'">
        <IFrame :src="url" :subtractHeight="100"></IFrame>
      </template>
      <template v-else-if="urlType === 'ofd'">
        <div v-html="ofdDiv"></div>
      </template>
      <template v-else-if="urlType === 'xml'"></template>
    </template>
    <template v-if="showFooterButton" #footer>
      <div class="dialog-footer">
        <slot name="footerButton">
          <el-button v-btn @click="close">{{ cancelButtonText }}</el-button>
        </slot>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import IFrame from '@/components/iFrame/index.vue'
// import { parseOfdDocument, renderOfd } from 'ofd.js'
import { ref } from 'vue'

const props = defineProps({
  url: {
    type: String,
    default: '',
  },
  showFooterButton: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: '',
  },
  cancelButtonText: {
    type: String,
    default: '关闭',
  },
  dialogStyle: {
    type: Object,
    default: () => ({}),
  },
})

const urlType = computed(() => {
  if (props.url) {
    const fileSuffix = props.url.substring(props.url.lastIndexOf('.') + 1).toLowerCase()
    // if (fileSuffix === 'ofd') {
    //   getOFD()
    // }
    return fileSuffix
  }
  return ''
})

const fullscreen = ref(false)
const ofdDiv = ref('')

const emits = defineEmits(['confirm', 'close', 'before-close'])

defineExpose({
  open,
  close,
})

const dialogVisible = ref(false)

// 打开
function open() {
  dialogVisible.value = true
}
// 关闭
function close() {
  dialogVisible.value = false
}
// 关闭时回调
function closeChange() {
  emits('close')
}

async function getOFD() {
  // parseOfdDocument({
  //   ofd: props.url,
  //   // signaturesCallback(signatures) {
  //   //   console.log(signatures)
  //   //   let screenWidth = 1050 //设置内容的显示框大小
  //   //   //将流数据渲染给接受的div盒子
  //   //   renderOfd(0, screenWidth).then(res => {
  //   //     console.log(res)
  //   //     ofdDiv.value = res
  //   //     // const divs = res
  //   //     // let contentDiv = document.getElementById('divId') // 获取盒子元素
  //   //     // contentDiv.innerHTML = ''
  //   //     // for (const div of divs) {
  //   //     //   contentDiv.appendChild(div)
  //   //     // }
  //   //   })
  //   // },
  //   success(core) {
  //     console.log(core)
  //   },
  //   fail(error) {
  //     console.log(error)
  //   },
  // })
}
</script>

<style scoped lang="scss"></style>
