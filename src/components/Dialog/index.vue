<template>
  <el-dialog
    v-model="dialogVisible"
    v-bind="$attrs"
    align-center
    @close="closeChange"
    :style="dialogStyle"
  >
    <slot></slot>
    <template v-if="showFooterButton" #footer>
      <div class="dialog-footer">
        <slot name="footerButton">
          <el-button v-btn @click="close">{{ cancelButtonText }}</el-button>
          <el-button v-btn type="success" @click="sub">
            {{ confirmButtonText }}
          </el-button>
        </slot>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>

const props = defineProps({
  showFooterButton: {
    type: Boolean,
    default: true
  },
  beforeClose: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: ''
  },
  cancelButtonText: {
    type: String,
    default: '取消'
  },
  confirmButtonText: {
    type: String,
    default: '确定'
  },
  dialogStyle: {
    type: Object,
    default: () => ({})
  }
})

const emits = defineEmits(['confirm', 'close', 'before-close'])

defineExpose({
  open,
  close,
})

const dialogVisible = ref(false)

// 打开
function open() {
  dialogVisible.value = true
}
// 关闭
function close() {
  // 关闭前回调
  if (props.beforeClose) {
    emits('before-close', () => dialogVisible.value = false)
    return
  }
  dialogVisible.value = false
}
// 确定
function sub() {
  emits('confirm')
}
// 关闭时回调
function closeChange() {
  emits('close')
}
</script>

<style scoped lang="scss">

</style>