<template>
  <el-dialog v-model="dialogVisible" width="70%" @close="closeChange" modal-class="video-iframe-dialog">
    <template #header>
      <div></div>
    </template>
    <el-icon class="close-icon" :size="34" color="#ffffff99" @click="close()"><CircleClose /></el-icon>
    <IFrame v-if="videoSrc" :src="videoSrc"></IFrame>
  </el-dialog>
</template>

<script setup>
import IFrame from '@/components/iFrame/index.vue'

const dialogVisible = ref(false)

const props = defineProps({
  videoSrc: {
    type: String,
    default: '',
  },
})
const emits = defineEmits(['close'])

const open = () => {
  dialogVisible.value = true
}

const close = () => {
  dialogVisible.value = false
}

const closeChange = () => {
  emits('close')
}

defineExpose({
  open,
  close,
})
</script>

<style scoped lang="scss">
.close-icon {
  position: fixed;
  top: 30px;
  right: 30px;
  cursor: pointer;
}
</style>

<style lang="scss">
.video-iframe-dialog {
  padding: 10px;

  .el-overlay-dialog {
    .el-dialog {
      margin: 47px auto;

      .el-dialog__header {
        padding: 0;
        margin: 0;

        .el-dialog__headerbtn {
          display: none;
        }
      }
      .el-dialog__body {
        padding: 0;
      }
    }
  }
}
</style>
