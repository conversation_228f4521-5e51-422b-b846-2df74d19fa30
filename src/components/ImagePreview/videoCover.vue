<template>
  <div class="vc-box" :style="boxStyle">
    <div class="image-box" :style="{
      width: width,
      height: height
    }">
      <div class="play-modal flex-center">
        <el-icon :size="size" :color="color"><VideoPlay /></el-icon>
      </div>
      <div class="platform-title" v-if="platform == '0'">Amazon</div>
      <div class="platform-title" v-if="platform == '1'">TikTok</div>
      <el-image
        :src="$picUrl + src + suffix"
        :fit="fit"
        class="cover-img"
        preview-teleported
      >
        <template #error>
          <div class="image-error">
            <el-icon :size="28" color="#ccc"><Picture /></el-icon>
          </div>
        </template>
      </el-image>
    </div>
    <div class="text-n-all vc-title" :style="{ width }">
      {{ title }}
    </div>
  </div>
</template>

<script setup>

const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  platform: {
    type: String,
    default: ''
  },
  width: {
    type: String,
    default: '140px'
  },
  height: {
    type: String,
    default: '140px'
  },
  color: {
    type: String,
    default: '#ffffffc4'
  },
  size: {
    type: Number,
    default: 38
  },
  fit: {
    type: String,
    default: 'contain'
  },
  suffix: {
    type: String,
    default: ''
  },
  src: {
    type: String,
    default: ''
  },
  boxStyle: {
    type: Object,
    default: () => ({})
  }
})
</script>

<style scoped lang="scss">
.vc-box {
  position: relative;

  .platform-title {
    position: absolute;
    top: 2px;
    right: 5px;
    font-size: 12px;
    font-weight: 400;
    color: #fffc;
    text-shadow: 1px 1px 1px #000c;
    z-index: 8;
  }

  .image-box {
    width: 100px;
    height: 100px;
    flex-shrink: 0;
    position: relative;
    cursor: pointer;
    background: #f5f7fa;
    border-radius: 8px;
    border: 1px solid #f4f4f4;
    overflow: hidden;
  
    &:hover {
      .play-modal {
        opacity: 1;
      }
    }
  
    .el-image {
      width: 100%;
      height: 100%;
    }
    
    .play-modal {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: #66666666;
      z-index: 9;
      opacity: 0;
    }

    .cover-img {
      width: 100%;
      height: 100%;
    }
    .image-error {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
  .vc-title {
    font-size: 16px;
    font-weight: 500;
    text-align: center;
  }
}
</style>