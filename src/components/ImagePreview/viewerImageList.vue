<template>
  <div
    class="viewer-list"
    :class="{
      'viewer-list-start': layout === 'start',
      'viewer-list-center': layout === 'center',
      'viewer-list-column': layout === 'column',
    }"
  >
    <slot name="header"></slot>
    <template v-if="urlName">
      <div class="viewer-list-img-item" v-for="(item, i) in data" :key="item[urlName] + i">
        <el-image :src="$picUrl + item[urlName] + suffix" :fit="fit" />
        <span class="item-actions">
          <el-icon @click="handlePreview(item[urlName], i)"><View /></el-icon>
          <el-icon v-if="showDownloadBtn" @click="handleDownload(item[urlName], item[fileName])">
            <Download />
          </el-icon>
          <el-icon v-if="showDeleteBtn" @click="handleDelete(item, i)"><Delete /></el-icon>
        </span>
      </div>
    </template>
    <template v-else>
      <div class="viewer-list-img-item" v-for="(item, i) in data" :key="item + i">
        <el-image :src="$picUrl + item + suffix" :fit="fit" />
        <span class="item-actions">
          <el-icon @click="handlePreview(item, i)"><View /></el-icon>
          <el-icon v-if="showDownloadBtn" @click="handleDownload(item)"><Download /></el-icon>
          <el-icon v-if="showDeleteBtn" @click="handleDelete(item, i)"><Delete /></el-icon>
        </span>
      </div>
    </template>
    <slot name="footer"></slot>
  </div>
</template>

<script setup>
import { useViewer } from '@/hooks/useViewer'
import { downUrlFile } from '@/utils/download'

const { showViewer } = useViewer()

const props = defineProps({
  data: {
    type: Array,
    default: () => [],
  },
  urlName: {
    type: String,
    default: '',
  },
  fileName: {
    type: String,
    default: 'name',
  },
  suffix: {
    type: String,
    default: '!fullSizecompress',
  },
  isPreviewAll: {
    type: Boolean,
    default: false,
  },
  showDeleteBtn: {
    type: Boolean,
    default: true,
  },
  showDownloadBtn: {
    type: Boolean,
    default: false,
  },
  customized: {
    type: Boolean,
    default: false,
  },
  layout: {
    type: String,
    default: 'start',
  },
  fit: {
    type: String,
    default: 'fill',
  },
  curIndex: {
    type: Number,
    default: null,
  },
})

const emits = defineEmits(['delete'])

const previewUrl = ref([])

function handlePreview(url, i) {
  if (props.isPreviewAll) {
    previewUrl.value = props.data.map(item => (props.urlName ? item[props.urlName] : item))
    showViewer(previewUrl.value, { index: i, raw: props.customized })
  } else {
    previewUrl.value = [url]
    showViewer(previewUrl.value, {raw: props.customized })
  }
}
function handleDelete(item, i) {
  if (props.curIndex || props.curIndex == 0) {
    emits('delete', item, i, props.curIndex)
  } else {
    emits('delete', item, i)
  }
}
function handleDownload(url, name) {
  if (!name) {
    name = new Date().getTime() + ''
  }
  downUrlFile(url, name)
}
</script>

<style scoped lang="scss">
.viewer-list {
  --image-width: 80px;
  --image-height: 80px;
  height: auto;
  // min-height: calc(var(--image-height)  + 14px);
  // max-height: calc(var(--image-height) * 2 + 15px);
  // overflow-y: auto;
}
.viewer-list-start {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: center;

  gap: 10px;
}
.viewer-list-center {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
}
.viewer-list-column {
  display: flex;
  align-items: center;
  flex-direction: column;
  gap: 10px;
}
.viewer-list-img-item {
  position: relative;
  width: var(--image-width);
  height: var(--image-height);

  .el-image {
    width: 100%;
    height: 100%;
  }
  .item-actions {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    color: #fff;
    font-size: 20px;
    opacity: 0;
    background-color: #00000080;
    transition: opacity 0.3s;

    &:hover {
      opacity: 1;
    }

    .el-icon {
      cursor: pointer;
    }
  }
}
</style>
