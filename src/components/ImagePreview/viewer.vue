<template>
  <ElImageViewer
    v-if="imgViewerVisible"
    ref="ElImageViewerRef"
    hide-on-click-modal
    :url-list="imgViewerUrl"
    :initial-index="imgViewerIndex"
    :max-scale="1"
    :min-scale="1"
    :z-index="9998"
    teleported
    @switch="switchChange"
    @close="imgViewerVisible = false"
  >
    <div class="image-viewer-mini-pic">
      <div
        class="img-list"
        :style="{
          transform: `translateX(-${imgViewerIndex * 45}px)`,
        }"
      >
        <img
          v-for="(src, i) in imgViewerUrl"
          :key="i"
          :src="src"
          alt=""
          :class="{
            active: i === imgViewerIndex,
          }"
          @click="ElImageViewerRef?.setActiveItem(i)"
        />
      </div>
    </div>
  </ElImageViewer>
</template>

<script setup>
import { ref } from 'vue'
import { useViewer } from '@/hooks/useViewer'

const ElImageViewerRef = ref()

const { imgViewerVisible, imgViewerUrl, imgViewerIndex } = useViewer()

const switchChange = (index) => {
  imgViewerIndex.value = index
}
</script>

<style scoped lang="scss">
.image-viewer-mini-pic {
  position: absolute;
  left: 0;
  bottom: 0;
  z-index: 3;
  width: 100%;
  height: 50px;
  background: rgb(0 0 0 / 40%);
  overflow: hidden;

  &::before {
    content: '';
    display: block;
    float: left;
    width: calc(50% - 15px);
    height: 100%;
  }

  img {
    width: 40px;
    height: 50px;
    cursor: pointer;
    object-fit: cover;
    filter: brightness(0.8);

    &.active {
      cursor: auto;
      filter: brightness(1);
    }

    & + img {
      margin-left: 5px;
    }
  }

  .img-list {
    transition: 0.3s;
  }
}
</style>
