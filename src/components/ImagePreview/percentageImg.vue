<template>
  <div class="percentage-img-box" :style="`width:${realWidth};border-radius:${radius};`">
    <div class="inner" :class="[direction]">
      <el-image :src="url" :fit="fit" class="content-img" @click="preview">
        <template #error>
          <div class="image-error">
            <el-icon :size="28" color="#ccc"><Picture /></el-icon>
          </div>
        </template>
      </el-image>
      <div class="more" v-if="moreLength" @click.stop="more">
        <div class="num">{{ moreLength > 99 ? '99+' : moreLength }}</div>
        <el-icon class="icon-gengduo"><CopyDocument /></el-icon>
      </div>
    </div>
    <slot name="footer"></slot>
  </div>
</template>

<script setup>
import { useViewer } from '@/hooks/useViewer'

const { showViewer } = useViewer()

const emits = defineEmits(['more'])

const props = defineProps({
  src: {
    type: String,
    default: '',
  },
  moreLength: {
    type: Number,
    default: 0,
  },
  fit: {
    type: String,
    default: 'fill',
  },
  width: {
    type: [Number, String],
    default: '100%',
  },
  radius: {
    type: String,
    default: '0px',
  },
  // horizontal 横屏4:3 / vertical 竖屏3:4
  direction: {
    type: String,
    default: 'vertical',
  },
})

const url = computed(() => {
  if (!props.src) {
    return ''
  }
  let suffix = ''
  if (props.direction === 'horizontal') {
    suffix = '!4x3compress'
  }
  if (props.direction === 'vertical') {
    suffix = '!3x4compress'
  }
  return props.src + suffix
})

const realSrcList = computed(() => {
  if (!props.src) {
    return
  }
  // let real_src_list = props.src.split(",");
  // let srcList = [];
  // real_src_list.forEach(item => {
  //   return srcList.push(item);
  // });
  // return srcList;
  return [props.src]
})
const realWidth = computed(() => (typeof props.width == 'string' ? props.width : `${props.width}px`))

const preview = () => {
  showViewer([props.src], {
    scale: props.direction === 'vertical' ? '3x4' : '4x3',
  })
}

const more = () => {
  emits('more')
}
</script>

<style scoped lang="scss">
.percentage-img-box {
  margin: auto;
  // border: 1px solid #eaeaea;
  overflow: hidden;
  position: relative;

  .inner {
    position: relative;
    width: 100%;
    border: 1px solid #eaeaea;

    &.horizontal {
      padding-top: 75%; // 4:3
      // padding-top: 56%; // 16:9
    }
    &.vertical {
      padding-top: 133%; // 3:4
      // padding-top: 178%; // 9:16
    }

    .content-img {
      cursor: pointer;
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }
  }

  .image-error {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .more {
    position: absolute;
    bottom: 0;
    right: 0;
    z-index: 9;
    width: 22px;
    height: 20px;
    text-align: center;
    line-height: 16px;
    color: #fff;
    font-size: 13px;
    cursor: pointer;

    .num {
      transform: scale(0.6);
      line-height: 20px;
    }

    .icon-gengduo {
      position: absolute;
      top: -2px;
      left: 2px;
      color: #fff;
      width: 20px;
      height: 20px;
      font-size: 16px;
      transform: rotate(180deg);
    }
  }
}
</style>
