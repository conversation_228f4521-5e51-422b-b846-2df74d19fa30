<template>
  <div class="task-button" :style="{ width: `${props.width}px` }">
    <el-checkbox-group :model-value="props.modelValue" @change="handleFinishTask">
      <el-checkbox-button :value="-1">
        <div class="box-item-content">
          <div>
            &nbsp;不完结任务
          </div>
          <el-icon class="is-checked-icon"><SuccessFilled /></el-icon>
        </div>
      </el-checkbox-button>
      <div class="finish-task-box">
        <el-checkbox-button v-for="item in props.checkList" :value="item.id" :key="item.id">
          <div class="box-item-content" v-if="handleItemLength(item)">
            <div class="content-type">{{ item.taskType == 1 ? '售后' : '工单' }}</div>
            <div class="one-ell" style="margin-left: 10px">
              <span>{{ item.submitTime }}：</span>
              <span v-if="item.workOrderType">{{ workOrderTypeMap[item.workOrderType] }}-</span>
              <span v-else>{{ handleAfterType(item) }}-</span>
              <span>{{ item.content }}</span>
            </div>
            <el-icon class="is-checked-icon"><SuccessFilled /></el-icon>
          </div>
          <el-tooltip v-else effect="dark" placement="top" :hide-after="150">
            <template #content>
              <div style="max-width: 600px; white-space: pre-wrap; max-height: 45vh; overflow-y: auto">
                {{
                  item.submitTime +
                  '：' +
                  (item.workOrderType ? workOrderTypeMap[item.workOrderType] + '-' : '') +
                  item.content
                }}
              </div>
            </template>
            <div class="box-item-content">
              <div class="content-type">{{ item.taskType == 1 ? '售后' : '工单' }}</div>
              <div class="one-ell" ref="textVal" style="margin-left: 10px">
                <span>{{ item.submitTime }}：</span>
                <span v-if="item.workOrderType">{{ workOrderTypeMap[item.workOrderType] }}-</span>
                <span v-else>{{ handleAfterType(item) }}-</span>
                <span>{{ item.content }}</span>
              </div>
              <el-icon class="is-checked-icon"><SuccessFilled /></el-icon>
            </div>
          </el-tooltip>
        </el-checkbox-button>
      </div>
    </el-checkbox-group>
  </div>
</template>

<script setup>
import { stringLength } from '@/utils/index.js'
import { workOrderTypeMap, afterSaleVideoTypeList, afterSalePicTypeList } from '@/views/task/data.js'

const props = defineProps({
  checkList: {
    type: Array,
    default: [],
  },
  modelValue: {
    type: Array,
    default: [],
  },
  width: {
    type: Number,
    default: 100,
  },
})

const emits = defineEmits(['update:modelValue'])

function handleAfterType(data) {
  let text = ''
  if (data.afterSalePicType) {
    text = afterSalePicTypeList.find(item => item.value == data.afterSalePicType)?.label
  }
  if (data.afterSaleVideoType) {
    text = afterSaleVideoTypeList.find(item => item.value == data.afterSaleVideoType)?.label
  }
  return text
}

function handleItemLength(data) {
  let length = stringLength(data.submitTime) + stringLength(data.content)
  return length < (props.width - 72) / 7.4
}

function handleFinishTask(val) {
  if (val && val.length > 0) {
    if (val.includes(-1) && val[val.length - 1] == -1) {
      emits('update:modelValue', [-1])
    } else {
      emits(
        'update:modelValue',
        val.filter(item => item != -1)
      )
    }
  } else {
    emits('update:modelValue', val)
  }
}
</script>

<style scoped lang="scss">
.task-button {

  :deep(.el-checkbox-group) {
    .el-checkbox-button {
      width: 100%;
      margin-bottom: 10px;
      font-weight: 500;
      --el-checkbox-button-checked-bg-color: var(--el-color-white);
      // --el-checkbox-button-checked-border-color: var(--el-color-primary);
  
      .el-checkbox-button__inner {
        border-radius: 4px;
        padding: 8px 3px;
        text-align: left;
        width: 100%;
      }
      
      &.is-focus {
        --el-checkbox-button-checked-border-color: var(--el-border-color);
        
        .el-checkbox-button__inner {
          border-left-color: var(--el-border-color);
        }
      }
      &.is-checked {
        --el-checkbox-button-checked-border-color: var(--el-color-primary);

        .el-checkbox-button__inner {
          color: var(--el-color-primary);
          border-left-color: var(--el-color-primary);
        }
        
        .is-checked-icon {
          display: block;
        }
        .box-item-content {
          padding-right: 20px;
        }
      }
    }
    .finish-task-box {
  
      .el-checkbox-button {
        .el-checkbox-button__inner {
          padding: 0 !important;
          text-align: left;
          width: 100%;
        }
      }
    }
    .box-item-content {
      position: relative;
      display: flex;
      align-items: center;
      font-size: 14px;
  
      .content-type {
        background-color: var(--el-color-info-light-9);
        color: var(--el-text-color-regular);
        padding: 8px 5px;
        border-radius: 4px 0 0 4px;
        border-right: 1px solid var(--el-border-color);
      }

      .one-ell {
        span {
          line-height: 16px;
        }
      }
  
      .is-checked-icon {
        display: none;
        position: absolute;
        top: 50%;
        right: 5px;
        transform: translateY(-50%);
      }
    }
  }
}
</style>
