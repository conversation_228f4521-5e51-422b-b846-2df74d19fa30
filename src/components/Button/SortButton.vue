<template>
  <div
    class="flex-start gap-5 sort-button"
    :class="{ 'is-loading': loading, 'is-disabled': disabled }"
    @click="handleClick"
  >
    <slot>排序</slot>
    <!-- <el-icon color="#bebebe">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024">
        <defs>
          <linearGradient id="desc" x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" style="stop-color: #bebebe; stop-opacity: 1" />
            <stop offset="50%" style="stop-color: #bebebe; stop-opacity: 1" />
            <stop offset="50%" style="stop-color: #473838; stop-opacity: 1" />
            <stop offset="100%" style="stop-color: #473838; stop-opacity: 1" />
          </linearGradient>
          <linearGradient id="asc" x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" style="stop-color: #473838; stop-opacity: 1" />
            <stop offset="50%" style="stop-color: #473838; stop-opacity: 1" />
            <stop offset="50%" style="stop-color: #bebebe; stop-opacity: 1" />
            <stop offset="100%" style="stop-color: #bebebe; stop-opacity: 1" />
          </linearGradient>
        </defs>
        <path :fill="color" d="m512 128 288 320H224zM224 576h576L512 896z"></path>
      </svg>
    </el-icon> -->
    <div class="flex-column" style="transform: scale(0.9)">
      <el-icon @click.stop="handleSort('ASC')" :color="asc_color" style="margin-bottom: -3px">
        <CaretTop />
      </el-icon>
      <el-icon @click.stop="handleSort('DESC')" :color="desc_color" style="margin-top: -3px">
        <CaretBottom />
      </el-icon>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  modelValue: {
    type: String,
    required: true,
  },
  loading: {
    type: Boolean,
    default: false,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
})
const emits = defineEmits(['update:modelValue', 'change'])

const color = computed(() => {
  if (props.modelValue === 'ASC') {
    return 'url(#asc)'
  }
  if (props.modelValue === 'DESC') {
    return 'url(#desc)'
  }
  return 'currentColor'
})
const asc_color = computed(() => {
  if (props.modelValue === 'ASC') {
    return '#473838'
  }
  return '#bebebe'
})
const desc_color = computed(() => {
  if (props.modelValue === 'DESC') {
    return '#473838'
  }
  return '#bebebe'
})

const handleSort = type => {
  let val = props.modelValue === type ? '' : type
  emits('update:modelValue', val)
  emits('change', val)
}

const handleClick = () => {
  // emits('update:modelValue', '')
  // emits('change', '')
  if (props.loading || props.disabled) return
  let val = ''
  if (props.modelValue === '') {
    val = 'ASC'
  } else if (props.modelValue === 'ASC') {
    val = 'DESC'
  }
  emits('update:modelValue', val)
  emits('change', val)
}
</script>

<style scoped lang="scss">
.sort-button {
  user-select: none;
  cursor: pointer;
  line-height: 1;

  &.is-loading {
    cursor: progress;
  }
  &.is-disabled {
    cursor: no-drop;
  }
}
</style>
