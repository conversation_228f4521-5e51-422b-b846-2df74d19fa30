<template>
  <el-button v-btn v-bind="$attrs" @click="handleClick">
    <slot>复制</slot>
  </el-button>
</template>

<script setup>
import { ElMessage } from 'element-plus'
import { isSafari,isIOS } from '@/utils/index'

const props = defineProps({
  copyContent: {
    type: String,
    default: '',
  },
  asyncCopyContent: {
    type: Function,
  },
})

function handleClick() {
  if (props.asyncCopyContent) {
    props.asyncCopyContent().then(res => {
      copy(res)
    })
  } else {
    copy(props.copyContent)
  }
}

function copy(text) {
  if (text == null || text === undefined || text === '') return
  let isios = isIOS()
  if (!navigator.clipboard || isios || isSafari()) {
    const dom = document.createElement('textarea')
    dom.value = text
    dom.style.position = 'fixed'
    dom.style.top = '-500px'
    dom.style.right = '-500px'
    document.body.appendChild(dom)
    dom.select()
    if (isios) {
      iosCopyToClipboard(dom)
    } else {
      let successful = document.execCommand('copy')
      successful ? ElMessage.success('已复制到剪贴板!') : ElMessage.warning('您的浏览器不支持复制功能!')
      document.body.removeChild(dom)
    }
  } else {
    navigator.clipboard
      .writeText(text)
      .then(function () {
        ElMessage.success('已复制到剪贴板')
      })
      .catch(function (err) {
        ElMessage.error('Failed to copy text:')
        console.error('Failed to copy text: ', err)
      })
  }
}

function iosCopyToClipboard(el) {
  var oldContentEditable = el.contentEditable,
    oldReadOnly = el.readOnly,
    range = document.createRange()

  el.contentEditable = true
  el.readOnly = false
  range.selectNodeContents(el)

  var s = window.getSelection()
  s.removeAllRanges()
  s.addRange(range)

  el.setSelectionRange(0, 999999)

  el.contentEditable = oldContentEditable
  el.readOnly = oldReadOnly

  let successful = document.execCommand('copy')
  successful ? ElMessage.success('已复制到剪贴板!') : ElMessage.warning('您的浏览器暂不支持复制')
  document.body.removeChild(el)
}
</script>
