<template>
  <el-button
    v-btn
    v-bind="$attrs"
    :loading="exportLoading"
    :disabled="props.exportDisabled"
    @click="handleExport"
  >
    {{ exportBtnText }}
  </el-button>
</template>

<script setup name="DownloadBtn">
const { proxy } = getCurrentInstance()

const props = defineProps({
  text: {
    type: String,
    default: '导出',
  },
  loadingText: {
    type: String,
    default: '导出中',
  },
  url: {
    type: String,
    required: true,
  },
  params: {
    type: [Object, Array, Function],
    default: () => ({}),
  },
  fileName: String,
  config: {
    type: Object,
    default: () => ({}),
  },
  message: {
    type: String,
    default: '确认导出?',
  },
  title: {
    type: String,
    default: '提示',
  },
  options: {
    type: Object,
  },
  isAsnyc: {
    type: Boolean,
    default: false,
  },
  exportDisabled: {
    type: Boolean,
    default: false,
  },
})

const emits = defineEmits(['success'])

const exportLoading = ref(false)
const exportBtnText = ref(props.text)

function handleExport() {
  proxy.$confirm(props.message, props.title, props.options).then(() => {
    exportLoading.value = true
    exportBtnText.value = props.loadingText
    proxy
      .download(props.url, handleParams(props.params), props.fileName, props.config, props.isAsnyc)
      .then(() => {
        emits('success')
      })
      .finally(() => {
        exportLoading.value = false
        exportBtnText.value = props.text
      })
  })
}

function handleParams(params) {
  if (typeof params === 'function') {
    return params()
  }
  return params
}
</script>
