<template>
  <div class="icon-container">
    <div class="icon-box">
      <img v-if="score >= 7.8" class="icon-box-img" src="@/assets/icons/gold_icon.png" />
      <img v-else class="icon-box-img" src="@/assets/icons/silver_icon.png" />
    </div>
    <div class="icon-text-wrapper">
      <div
        class="icon-text"
        :style="{
          color: score >= 7.8 ? '#734E26' : '#667296',
        }"
      >
        {{ score.toFixed(1) }}
      </div>
    </div>
    <!-- <div class="icon-text-wrapper">
      <div
        class="icon-text"
        :style="{
          color: score >= 7.8 ? '#734E26' : '#667296',
        }"
      >
        {{ score.toFixed(1) }}
      </div>
    </div> -->
  </div>
</template>

<script setup>
const props = defineProps({
  score: {
    type: Number,
    required: true,
  },
})
</script>

<style scoped lang="scss">
.icon-container {
  position: relative;
  width: 32px;
  .icon-box {
    width: 32px;
    height: 16px;
    &-img {
      width: 100%;
    }
  }
  // .icon-text-wrapper {
  //   line-height: 16px;
  //   position: absolute;
  //   top: 50%;
  //   left: 50%;
  //   transform: translate(-50%, -50%);
  //   font-size: 13px;
  //   font-weight: bold;
  //   white-space: nowrap;
  // }
  .icon-text-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .icon-text {
    font-size: 13px;
    font-weight: bold;
    white-space: nowrap;
    // text-shadow: ;
  }
}
</style>
