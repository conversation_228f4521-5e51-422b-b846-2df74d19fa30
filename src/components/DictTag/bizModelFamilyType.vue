<template>
  <!-- 模特家庭成员类型 -->
  <template v-for="dict in model_family_relationship_type" :key="dict.value">
    <span v-if="tag === 'text' && data.includes(dict.value)">
      <slot :dict="dict">{{ dict.label }}</slot>
    </span>
    <el-tag v-else-if="tag === 'tag' && data.includes(dict.value)" :type="type" :size="size" :round="round">
      <slot :dict="dict">{{ dict.label }}</slot>
    </el-tag>
  </template>
</template>

<script setup>
const { proxy } = getCurrentInstance()
const { model_family_relationship_type } = proxy.useDict('model_family_relationship_type')

const props = defineProps({
  value: {
    type: [String, Number, Array],
    default: '',
  },
  tag: {
    type: String,
    default: 'tag',
  },
  type: {
    type: String,
    default: 'warning',
  },
  size: {
    type: String,
    default:'small',
  },
  round: {
    type: <PERSON><PERSON><PERSON>,
    default: true,
  },
})

const data = computed(() => {
  if (Array.isArray(props.value)) {
    return props.value.map(v => v + '')
  }
  if (typeof props.value === 'string' && props.value.indexOf(',') > -1) {
    return props.value.split(',')
  }
  return [props.value + '']
})
</script>

<style scoped lang="scss">
.el-tag {
  margin-right: 5px;
}
</style>
