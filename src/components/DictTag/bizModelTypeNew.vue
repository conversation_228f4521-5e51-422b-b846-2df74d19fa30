<template>
  <!-- 模特类型 -->
  <template v-for="dict in biz_model_type" :key="dict.value">
    <div :class="value == '1' ? 'blue-box' : 'orange-box'" v-if="tag === 'tag' && data.includes(dict.value)">
      <el-tag :type="type" :size="size" :round="round">
        <slot :dict="dict">{{ dict.label }}</slot>
      </el-tag>
    </div>
  </template>

  <el-tag v-if="data === '3' || data[0] === '3'" :type="type" :size="size" :round="round">影/素都可以</el-tag>
</template>

<script setup>
const { proxy } = getCurrentInstance()
const { biz_model_type } = proxy.useDict('biz_model_type')

const props = defineProps({
  value: {
    type: [String, Number, Array],
    default: '',
  },
  tag: {
    type: String,
    default: 'tag',
  },
  type: {
    type: String,
    default: 'warning',
  },
  size: {
    type: String,
    default: 'small',
  },
  round: {
    type: Boolean,
    default: true,
  },
  isNewTag: {
    type: Boolean,
    default: false,
  },
})

const data = computed(() => {
  if (Array.isArray(props.value)) {
    return props.value.map(v => v + '')
  }
  if (typeof props.value === 'string') {
    if (props.value.indexOf('3') > -1) {
      return '3'
    }
    if (props.value.indexOf(',') > -1) {
      return props.value.split(',')
    }
  }
  return [props.value + '']
})
</script>

<style scoped lang="scss">
.el-tag {
  margin-right: 5px;
}
.blue-box {
  :deep(.el-tag) {
    background-color: #ddebff;
    border-color: #3c8cff;
    color: #3c8cff;
    width: 90px;
  }
}
.orange-box {
  :deep(.el-tag) {
    background-color: #fff2de;
    border-color: #ff9900;
    color: #ff9900;
    width: 90px;
  }
}
</style>
