<template>
    <!-- 模特合作状态 -->
    <template v-for="dict in biz_model_status" :key="dict.value">
      <span v-if="tag === 'text' && data.includes(dict.value)">
        <slot :dict="dict">{{ dict.label }}</slot>
      </span>
      <el-tag v-else-if="tag === 'tag' && data.includes(dict.value)" :type="type" :size="size" :round="round">
        <slot :dict="dict">{{ dict.label }}</slot>
      </el-tag>
    </template>
  </template>
  
  <script setup>
  const { proxy } = getCurrentInstance()
  const { biz_model_status } = proxy.useDict('biz_model_status')
  
  const props = defineProps({
    value: {
      type: [String, Number, Array],
      default: '',
    },
    tag: {
      type: String,
      default: 'tag',
    },
    type: {
      type: String,
      default: 'warning',
    },
    size: {
      type: String,
      default:'small',
    },
    round: {
      type: Boolean,
      default: true,
    },
  })
  
  const data = computed(() => {
    if (Array.isArray(props.value)) {
      return props.value.map(v => v + '')
    }
    if (typeof props.value === 'string' && props.value.indexOf(',') > -1) {
      return props.value.split(',')
    }
    return [props.value + '']
  })
  </script>
  
  <style scoped lang="scss">
  .el-tag {
    margin-right: 5px;
  }
  </style>
  