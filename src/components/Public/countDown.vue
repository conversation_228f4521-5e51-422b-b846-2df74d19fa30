<template>
  <span>{{ remainingTime }}</span>
</template>

<script setup>

const props = defineProps({
  endTime: {
    type: [Number, String],
    required: true,
  },
  h: {
    type: String,
    default: ':'
  },
  m: {
    type: String,
    default: ':'
  },
  s: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['end'])

const remainingTime = ref('')

const updateTime = () => {
  const now = new Date().getTime()
  let distance = 0
  if(typeof props.endTime === 'string') {
    distance = new Date(props.endTime).getTime() - now
  } else {
    distance = props.endTime - now
  }

  if (distance < 0) {
    remainingTime.value = `00${props.h}00${props.m}00${props.m}`
    clearInterval(intervalId.value)
    emit('end')
  } else {
    const hours = Math.floor(distance / (1000 * 60 * 60))
    const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60))
    const seconds = Math.floor((distance % (1000 * 60)) / 1000)

    remainingTime.value =
      hours.toString().padStart(2, '0') +
      props.h +
      minutes.toString().padStart(2, '0') +
      props.m +
      seconds.toString().padStart(2, '0') +
      props.s
  }
}

const intervalId = ref()

onMounted(() => {
  updateTime()
  intervalId.value = setInterval(updateTime, 1000)
})

onUnmounted(() => {
  if (intervalId.value) {
    clearInterval(intervalId.value)
  }
})
</script>
