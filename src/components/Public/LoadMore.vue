<template>
  <p class="flex-center gap-5 load-more" ref="LoadMoreRef" @click="handleClick">
    <template v-if="status === 'loadmore'">{{ loadmoreText }}</template>
    <template v-else-if="status === 'refresh'">{{ refreshText }}</template>
    <template v-else-if="status === 'loading'">
      <el-icon class="is-loading"><Loading /></el-icon>
      <span>{{ loadingText }}</span>
    </template>
    <template v-else-if="status === 'nomore'">{{ nomoreText }}</template>
  </p>
</template>

<script setup>

const LoadMoreRef = ref()

//'loadmore' | 'loading' | 'nomore' | 'refresh'

const props = defineProps({
  status: {
    type: String,
    default: 'loadmore',
  },
  loadmoreText: {
    type: String,
    default: '加载更多',
  },
  refreshText: {
    type: String,
    default: '刷新',
  },
  loadingText: {
    type: String,
    default: '加载中',
  },
  nomoreText: {
    type: String,
    default: '没有更多了~',
  },
  loadmore: {
    type: Function,
    default: () => {},
  },
  auto: {
    type: Boolean,
    default: true,
  },
  isClick: {
    type: Boolean,
    default: true,
  },
  rootMargin: {
    type: String,
    default: '0px',
  },
})

const emits = defineEmits(['update:status'])

const handleClick = () => {
  if (!props.isClick || props.status === 'loading' || props.status === 'nomore') return
  props.loadmore()
  emits('update:status', 'loading')
}

let ob
onMounted(() => {
  if (props.auto) {
    ob = new IntersectionObserver(
      entries => {
        const entry = entries[0]
        if (entry.isIntersecting && props.status === 'loadmore') {
          props.loadmore()
          emits('update:status', 'loading')
        }
      },
      {
        rootMargin: props.rootMargin,
        threshold: 0.75,
      }
    )
    ob.observe(LoadMoreRef.value)
  }
})
onBeforeUnmount(() => {
  if (ob) ob.unobserve(LoadMoreRef.value)
})
</script>

<style scoped lang="scss">
.load-more {
  text-align: center;
  margin: 10px 0;
  color: var(--van-gray-6);
  font-size: 14px;
  background-color: transparent;
  line-height: 18px;
}
</style>
