<template>
  <div class="confirm-dialog">
    <div class="confirm-content">你的模特选单啦！！！</div>
    <div class="confirm-tips">请前往【预选管理】-我的预选中查看具体订单</div>
    <div class="flex-end confirm-btns">
      <el-button link style="font-size: 12px; color: #fff" v-btn @click="closeHindBox()">暂时忽略</el-button>
      <el-button style="font-size: 12px" round v-btn size="small" @click="goPreselect()" text bg>
        立即处理
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { checkPermi } from '@/utils/permission'
import { ElMessage } from 'element-plus'
// import { useWebsocket } from '@/hooks/useWebsocket'
// const { closeRemindVisible } = useWebsocket()
import useUserStore from '@/store/modules/user'
import { useRemindBox } from '@/hooks/useRemindBox'
const { remindVisible, remindUrl, initCurTab } = useRemindBox()
const router = useRouter()
const route = useRoute()

function goPreselect() {
  if (!checkPermi(['task:preselection:list'])) return ElMessage.warning('暂无权限,请联系管理员')
  if (route.fullPath === remindUrl.value) {
    initCurTab.value = true
  }
  router.push(remindUrl.value)
  remindVisible.value = false
  useUserStore().sendMessage()
}

function closeHindBox() {
  remindUrl.value = ''
  remindVisible.value = false
  useUserStore().sendMessage()
}
</script>

<style scoped lang="scss">
.confirm-dialog {
  background: #aaaaaafa;
  color: #fff;
  width: 300px;
  padding: 15px;
  position: fixed;
  right: 1%;
  bottom: 2%;
  z-index: 99999;
  border-radius: 20px;
  .confirm-content {
    font-size: 20px;
  }
  .confirm-tips {
    margin-top: 5px;
    font-size: 12px;
  }
  .confirm-btns {
    margin-top: 10px;
  }
}
</style>
