/* form禁用样式 */

$d-color: #212121;
$d-bg-color: #fff;

/* 输入框 */
:deep(.el-input.is-disabled) {
  // .el-input__wrapper {
  //   background-color: $d-bg-color;
  // }
  .el-input__inner {
    -webkit-text-fill-color: $d-color;
    // background-color: $d-bg-color;

    &::placeholder {
      -webkit-text-fill-color: var(--el-disabled-text-color);
    }
  }
}
:deep(.el-textarea.is-disabled) {
  // background-color: $d-bg-color;

  .el-textarea__inner {
    -webkit-text-fill-color: $d-color;
    // background-color: $d-bg-color;

    &::placeholder {
      -webkit-text-fill-color: var(--el-disabled-text-color);
    }
  }
}

/* 日期选择器 */
:deep(.el-date-editor.is-disabled) {

  .el-input__wrapper {
    background-color: $d-bg-color;
  }
  input {
    -webkit-text-fill-color: $d-color;
    // background-color: $d-bg-color;

    &::placeholder {
      -webkit-text-fill-color: var(--el-disabled-text-color);
    }
  }
}

/* 单选框 */
:deep(.el-radio.is-disabled.is-checked) {
  .el-radio__inner {
    border-color: var(--el-color-primary);
    background: var(--el-color-primary);
    &::after {
      background-color: $d-bg-color;
    }
  }
  .el-radio__label {
    color: $d-color;
  }
}
:deep(.el-radio-group) {
  .el-radio-button.is-disabled.is-active {
    .el-radio-button__inner {
      background-color: var(--el-radio-button-checked-bg-color,var(--el-color-primary));
      border-color: var(--el-radio-button-checked-border-color,var(--el-color-primary));
      color: #fff;
    }
  }
}
:deep(.el-radio-button.is-disabled.is-active) {
  .el-radio-button__inner {
    background-color: var(--el-radio-button-checked-bg-color,var(--el-color-primary));
    border-color: var(--el-radio-button-checked-border-color,var(--el-color-primary));
    color: #fff;
  }
}

/* 多选框 */

:deep(.el-checkbox-group) {
  .el-checkbox.is-disabled.is-checked {
    .el-checkbox__inner {
      background-color: var(--el-checkbox-checked-bg-color);
      border-color: var(--el-checkbox-checked-input-border-color);
      color: #fff;

      &::after {
        border-color: var(--el-checkbox-checked-icon-color);
      }
    }
    .el-checkbox__label {
      color: $d-color;
    }
  }
  .el-checkbox-button.is-disabled.is-checked {
    .el-checkbox-button__inner {
      background-color: var(--el-checkbox-button-checked-bg-color,var(--el-color-primary));
      border-color: var(--el-checkbox-button-checked-border-color,var(--el-color-primary));
      color: #fff;
    }
  }
}
:deep(.el-checkbox-group) {
  .el-checkbox-button.is-checked {
    .el-checkbox-button__inner {
      background-color: var(--el-checkbox-button-checked-bg-color,var(--el-color-primary));
      border-color: var(--el-checkbox-button-checked-border-color,var(--el-color-primary));
      color: #fff;
    }
  }
}
:deep(.el-checkbox-button.is-disabled.is-checked) {
  .el-checkbox-button__inner {
    background-color: var(--el-checkbox-button-checked-bg-color,var(--el-color-primary));
    border-color: var(--el-checkbox-button-checked-border-color,var(--el-color-primary));
    color: #fff;
  }
}

/* 下拉框 */
:deep(.el-select) {
  .is-disabled {
    .el-input__inner {
      color: $d-color;
    }

  }
}

/* 级联选择器 */
:deep(.el-cascader) {
  .is-disabled {
    .el-input__inner {
      color: $d-color;
    }
  }
}