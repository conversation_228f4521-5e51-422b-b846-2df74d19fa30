/*
* description: 自定义element-ui的radio-button和checkbox-button的样式
*/
:deep(.el-radio-button) {
    margin: 0 10px 10px 0;

    &.is-active {
      box-shadow: none;
    }

    .el-radio-button__inner {
      border-left: var(--el-border);
      border-radius: var(--el-border-radius-base);
      box-shadow: 0 0 0 0;
    }
  }
  :deep(.el-radio-group) {
    .el-radio-button.is-active {
      .el-radio-button__inner {
        box-shadow: none;
      }
    }
  }


  :deep(.el-checkbox-button) {
    margin: 0 10px 10px 0;

    &.is-checked {
      box-shadow: none;
    }

    .el-checkbox-button__inner {
      font-weight: 500;
      border-left: var(--el-border);
      border-radius: var(--el-border-radius-base);
    }
  }
  
  :deep(.el-checkbox-group) {
    .el-checkbox-button.is-checked {
      .el-checkbox-button__inner {
        box-shadow: none;
      }
    }
    .el-checkbox-button.is-focus {
        .el-checkbox-button__inner {
          box-shadow: none;
          border-left-color: #409eff;
        }
      }
  }