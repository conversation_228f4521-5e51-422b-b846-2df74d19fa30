// cover some element-ui styles

.el-form {
  .el-form-item {
    .el-form-item__content {
      --el-checkbox-font-weight: 500;
    }
  }
}

.el-select {
  --el-select-width: 200px;
}

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type='file'] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}

.cell {
  .el-tag {
    margin-right: 0px;
  }
}

.small-padding {
  .cell {
    padding-left: 5px;
    padding-right: 5px;
  }
}

.fixed-width {
  .el-button--mini {
    padding: 7px 10px;
    width: 60px;
  }
}

.status-col {
  .cell {
    padding: 0 10px;
    text-align: center;

    .el-tag {
      margin-right: 0px;
    }
  }
}

// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
  .el-dialog__title {
    font-weight: 600;
  }
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block;
  }
}

// fix date-picker ui bug in filter-item
.el-range-editor.el-input__inner {
  display: inline-flex !important;
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}

.el-menu--collapse > div > .el-submenu > .el-submenu__title .el-submenu__icon-arrow {
  display: none;
}

.el-dropdown .el-dropdown-link {
  color: var(--el-color-primary) !important;
}
/* 
  message-box 样式调整
*/
.el-message-box {
  --el-messagebox-font-size: 16px;
  --el-messagebox-content-font-size: 16px;

  .el-message-box__input {
    padding: 15px 0 20px;
  }

  .el-message-box__message {
    p {
      margin: initial;
      line-height: initial;
    }

    &:first-child(p) {
      margin: 0;
      line-height: 24px;
    }
    a,
    a:focus,
    a:hover {
      cursor: pointer;
      color: -webkit-link;
      text-decoration: underline;
    }
  }
}
/* 自定义messagebox样式 */
.custom-message-box {
  padding: 12px 0;

  .el-message-box__title {
    padding-left: 20px;
    font-weight: 600;
  }
  .el-message-box__content {
    box-sizing: border-box;
    padding: 5px 20px;
    min-height: 60px;

    .el-message-box__container {
      align-items: flex-start;

      .el-message-box__message span {
        color: #999;
        font-size: 14px;
      }
    }
    .el-icon.el-message-box__status {
      font-size: 20px;
      margin-top: 5px;
    }
  }
  .el-message-box__btns {
    padding-top: 8px;
    border-top: 1px solid #e4e4e4;
    margin-top: 12px;

    .el-button {
      margin: 0 12px 0 0;
      padding: 8px 26px;
    }
    .el-button + .el-button {
      margin-left: 0;
    }
  }
}
.custom-message-boxV1 {
  .el-message-box__message {
    width: 100%;
  }
}

/* 图片预览处理 */
.el-image-viewer__wrapper {
  .el-image-viewer__btn.el-image-viewer__actions {
    display: none;
  }
}
/* 根据图片的实际宽高比调整高度 */
@media (orientation: portrait) {
  /* 垂直 */
  .el-image-viewer__wrapper .el-image-viewer__canvas .el-image-viewer__img {
    max-width: 88vmin !important;
    max-height: 88vh !important;
  }
}
@media (orientation: landscape) {
  /* 水平 */
  .el-image-viewer__wrapper .el-image-viewer__canvas .el-image-viewer__img {
    max-width: 88vw !important;
    max-height: 88vmin !important;
  }
}
/* 模特头像大小 */
.model-avatar {
  --el-avatar-size: 40px;
  --el-avatar-icon-size: calc(var(--el-avatar-size) / 2 + 2px);
  vertical-align: middle;
}

.el-tag {
  white-space: pre-wrap;
  word-break: keep-all;
}
