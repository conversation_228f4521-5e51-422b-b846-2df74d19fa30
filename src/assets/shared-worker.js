// import { useWebsocket } from '@/hooks/useWebsocket'
// const { initWebSocket, closeConnect, socket } = useWebsocket()
// 维护所有连接的端口

// import { ref } from 'vue'
// import { fingerprint } from '@/utils/fingerprintjs2'
// import { getToken } from '@/utils/auth'

let socket = null
let retries = 0 // 重试计数器
let retryTimer = null // 重试定时器
const MAX_RETRIES = 3 // 最大重试次数
const BASE_DELAY = 30000 // 基础重试间隔（30秒）
const SILENT_PERIOD = 300000 // 静默时间（300秒）
const HEARTBEAT_INTERVAL = 15000 // 提取心跳间隔常量

let heartbeatIntervalId = null // 心跳定时器引用
let socketUrl = null
// let remindVisible = false

const ports = []
let port = null
let token = ''
let fingerprint = ''

//webSocketUrl websocket地址动态设置
const initWebSocket = webSocketUrl => {
  socketUrl = webSocketUrl
  if (socket && [WebSocket.CONNECTING, WebSocket.OPEN].includes(socket.readyState)) {
    return // 防止重复初始化
  }
  const wsUrlRequest = constructWsUrl(webSocketUrl)
  socket = new WebSocket(wsUrlRequest)

  socket.onopen = () => {
    console.log('WebSocket connected')
    retries = 0 // 连接成功时重置计数器
    clearInterval(heartbeatIntervalId)
    heartbeatIntervalId = setInterval(() => {
      if (socket.readyState === WebSocket.OPEN) {
        socket.send(JSON.stringify({ type: 'HEARTBEAT' }))
      }
    }, HEARTBEAT_INTERVAL)
  }

  // type=1 模特选单
  socket.onmessage = event => {
    /* 处理消息逻辑 */
    if (event.data) {
      const data = JSON.parse(event.data)
      if (data.result === 'HB_ACK') {
        console.log('Received PONG')
      }
      if (data.type && data.type == '1') {
        // port.postMessage(data.type)
        //广播数据
        broadcast(data)
        // remindVisible = true
      }
    }
  }

  socket.onerror = error => {
    console.error('WebSocket error:')
    handleReconnect()
  }

  socket.onclose = event => {
    // if (!event.wasClean) {
    handleReconnect()
    // }
  }

  // setInterval(() => {
  //   if (socket.readyState === WebSocket.OPEN) {
  //     socket.send(JSON.stringify({ type: 'HEARTBEAT' }))
  //   }
  // }, 15000)
}

// 处理重连逻辑
const handleReconnect = () => {
  if (retries >= MAX_RETRIES) {
    // 超过最大重试次数，进入静默期
    console.log('Reached max retries, entering silent period')
    retryTimer = setTimeout(() => {
      retries = 0 // 静默期结束后重置计数器
      initWebSocket(socketUrl) // 重新尝试连接
    }, SILENT_PERIOD)
  } else {
    // 指数退避：延迟时间 = 基础间隔 * 2^重试次数
    const delay = BASE_DELAY * Math.pow(2, retries)
    retryTimer = setTimeout(() => {
      retries++
      initWebSocket(socketUrl)
    }, delay)
  }
}

//关闭连接
const closeConnect = () => {
  retryTimer ? clearTimeout(retryTimer) : ''
  heartbeatIntervalId ? clearInterval(heartbeatIntervalId) : ''
  if (socket) {
    socket.close()
    socket = null
  }
}

// 关闭提醒弹窗
// const closeRemindVisible = () => {
//   remindVisible = false
// }

const constructWsUrl = constructWsUrl => {
  return `${constructWsUrl}?authorization=${token}&fp=${fingerprint}` // 通过 Query 传参
}

// 处理 SharedWorker 连接
self.onconnect = e => {
  port = e.ports[0]
  ports.push(port)

  // 初始化端口通信
  port.start()
  // 首次连接时初始化 WebSocket
  // if (!socket) initWebSocket('ws://192.168.20.2:19019/ws-endpoint')
  // ws://10.160.0.1:19029/ws-endpoint

  // 监听来自页面的消息（如发送数据到 WebSocket）
  port.onmessage = e => {
    if (e.data && e.data.data && e.data.data.length > 0) {
      token = e.data.data[0].value
      fingerprint = e.data.data[1].value
      let url = e.data.data[2].value
      if (!socket) initWebSocket(url)
    }
    if (e.data && e.data.type && e.data.type == 'CLOSE_REMIND') {
      broadcast(e.data)
    }
  }

  // 端口关闭时清理
  port.addEventListener('close', () => {
    const index = ports.indexOf(port)
    if (index > -1) ports.splice(index, 1)
    if (ports.length == 0) closeConnect()
  })
}

// 群发消息
const broadcast = data => {
  ports.forEach(port => {
    port.postMessage(data)
  })
}
