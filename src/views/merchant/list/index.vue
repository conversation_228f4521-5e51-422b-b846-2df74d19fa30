<template>
  <div>
    <Statistic />

    <el-dialog v-model="dialogVisible" title="子账号二维码" width="300" align-center>
      <div ref="wx" class="flex-column" id="wx">
        <div class="flex-start logo">
          <img src="@/assets/logo/logo.png" alt="" />
          蜗牛海拍
        </div>
        <div style="font-size: 16px; font-weight: 600">{{ qrName || '' }}</div>
        <div style="font-size: 16px; font-weight: 600; margin-top: 3px">邀请您加入</div>
        <img class="qrcode-img" v-if="qrCodeDataUrl" :src="qrCodeDataUrl" alt="" />
        <div class="tips">请使用微信扫一扫</div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="saveQrCode()">下载</el-button>
        </div>
      </template>
    </el-dialog>
    <ElTablePage
      style="padding: 20px 20px 65px 20px"
      ref="tableRef"
      :columns="columns"
      :data="tableData"
      :loading="tableLoading"
      :currentPage="pageNum"
      :pageSize="pageSize"
      :total="total"
      :tableAction="{
        width: '120',
        fixed: 'right',
      }"
      :tableOptions="{
        border: true,
      }"
      @page-change="pageChange"
      row-key="id"
      @selection-change="selectionChange"
      @sort-change="sortChange"
    >
      <template #tableHeader>
        <div>
          <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="90px" @submit.prevent>
            <el-form-item label="搜索">
              <el-input
                v-model="queryParams.searchName"
                clearable
                style="width: 300px"
                placeholder="请输入会员编码或公司名称"
              >
                <!-- <template #prepend>
                  <el-select
                    v-model="queryParams.select"
                    placeholder="请选择"
                    clearable
                    style="width: 100px"
                  >
                    <el-option
                        v-for="item in querySelectList"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    />
                  </el-select>
                </template> -->
              </el-input>
            </el-form-item>
            <!-- <el-form-item label="账号身份" prop="memberType">
              <el-select v-model="queryParams.memberType" placeholder="请选择" clearable style="width: 200px">
                <el-option
                  v-for="item in memberTypeList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item> -->
            <el-form-item label="账号身份" prop="merchantType">
              <el-select v-model="queryParams.isProxy" placeholder="请选择" clearable style="width: 200px">
                <el-option
                  v-for="item in merchantTypeList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="会员状态" prop="memberStatusList">
              <el-select
                v-model="queryParams.memberStatusList"
                placeholder="请选择"
                clearable
                multiple
                collapse-tags
                style="width: 200px"
              >
                <el-option
                  v-for="item in memberStatusList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <!-- <el-form-item label="账号状态" prop="accStatus">
              <el-select v-model="queryParams.status" placeholder="请选择" clearable style="width: 200px">
                <el-option
                  v-for="item in accStatusList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item> -->
            <el-form-item label="会员开通时间" style="width: 400px" label-width="100px">
              <el-date-picker
                v-model="queryParams.registerTime"
                format="YYYY/M/D HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="datetimerange"
                range-separator="-"
                unlink-panels
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
                :shortcuts="shortcuts"
              ></el-date-picker>
            </el-form-item>
            <el-form-item label="会员有效期" style="width: 400px">
              <el-date-picker
                v-model="queryParams.memberValidity"
                format="YYYY/M/D HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="datetimerange"
                range-separator="-"
                unlink-panels
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
                :shortcuts="shortcuts"
              ></el-date-picker>
            </el-form-item>
            <el-form-item label="定向筛选" prop="isExistRecentOrder">
              <el-select
                v-model="queryParams.isExistRecentOrder"
                placeholder="请选择"
                clearable
                style="width: 200px"
              >
                <el-option :value="1" :label="'近30天有排单商家'"></el-option>
                <el-option :value="0" :label="'近30天没有排单商家'"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="对接客服" prop="waiterId">
              <SelectLoad
                v-model="queryParams.waiterId"
                :request="listUser"
                :requestCallback="res => res.data"
                :totalCallback="res => res.data.length"
                keyValue="userId"
                keyLabel="userName"
                keyWord="userName"
                placeholder="请选择负责对接的客服"
                style="width: 200px"
              />
            </el-form-item>
            <el-form-item label="是否已分配客服" label-width="120px" prop="isAssignWaiter">
              <el-select
                v-model="queryParams.isAssignWaiter"
                placeholder="请选择"
                clearable
                style="width: 200px"
              >
                <el-option :value="1" :label="'已分配'"></el-option>
                <el-option :value="0" :label="'未分配'"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="售前" prop="connectUserName">
              <el-select
                v-model="queryParams.connectUserName"
                placeholder="请选择售前"
                clearable
                filterable
                style="width: 200px"
              >
                <el-option
                  v-for="(item, index) in businessManagerList"
                  :key="index"
                  :label="item"
                  :value="item"
                ></el-option>
              </el-select>
              <!-- <SelectLoad
                v-model="queryParams.businessManagerId"
                :request="listUser"
                :requestCallback="res => res.data"
                :totalCallback="res => res.data.length"
                keyValue="userId"
                keyLabel="userName"
                keyWord="userName"
                placeholder="请选择商务经理"
                style="width: 200px"
              /> -->
            </el-form-item>
            <el-form-item label="商家标识" prop="businessIdentifier">
              <el-select
                v-model="queryParams.businessIdentifier"
                placeholder="请选择"
                clearable
                style="width: 200px"
              >
                <el-option
                  v-for="item in merchantIdentificationList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="重要程度" prop="customerType">
              <el-select
                v-model="queryParams.customerType"
                placeholder="请选择"
                clearable
                style="width: 200px"
              >
                <el-option :value="0" :label="'一般客户'"></el-option>
                <el-option :value="1" :label="'重要客户'"></el-option>
                <el-option :value="3" :label="'普通账户'"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button v-btn type="primary" icon="Search" native-type="submit" @click="onQuery">
                搜索
              </el-button>
              <el-button v-btn icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div class="flex-between" style="margin-bottom: 10px">
          <!-- <el-button v-btn
              type="primary"
              icon="Plus"
              v-hasPermi="['merchant:list:add']"
              @click="openMerchantForm()"
            >
              新增商家账号
            </el-button> -->
          <div>
            <!-- <DownloadBtn
              v-hasPermi="['merchant:manage:export']"
              type="success"
              plain
              icon="Download"
              url="/biz/business/backend/businessList/export"
              :params="handleParams()"
              fileName="商家列表.xlsx"
            /> -->
            <el-button
              v-btn
              style="margin-left: 15px"
              type="primary"
              icon="Edit"
              :disabled="multipleSelection.length === 0"
              @click="handleBatchEdit"
              v-hasPermi="['merchant:manage:changeConectUser']"
            >
              批量更换对接人
            </el-button>
            <el-button
              v-btn
              style="margin-left: 15px"
              type="primary"
              @click="handleActiveConfig"
              v-hasPermi="['merchant:manage:active']"
            >
              活动配置
            </el-button>
            <el-button
              v-btn
              style="margin-left: 15px"
              type="primary"
              @click="handleService"
              v-hasPermi="['merchant:manage:service']"
            >
              服务费配置
            </el-button>
          </div>
        </div>
      </template>
      <template #accountNum="{ row }">
        <div style="cursor: pointer; line-height: 60px" @click="showAccountDialog(row)">
          {{ row.accountNum }}
        </div>
      </template>
      <template #info="{ row }">
        <div style="text-align: left">
          <div>
            微信: {{ row.nickName }}
            <!-- || row.accountName -->
            <el-tag v-if="row.isProxy" type="warning" size="small" round>代理</el-tag>
          </div>
          <div>公司名称：{{ row.name || '-' }}</div>
          <div>
            公司规模：{{
              row.scale ? companyScaleList.find(item => item.value == row.scale)?.label || '-' : '-'
            }}
          </div>
          <div>会员开通时间：{{ row.createTime.replace('T', ' ').replace('.000+08:00', '') }}</div>
        </div>
      </template>
      <!-- <template #status="{ row }">
        <el-switch
          :model-value="row.status"
          style="--el-switch-off-color: #ff4949"
          :active-value="0"
          :inactive-value="1"
          @click="switchChange(row)"
        />
      </template> -->
      <template #remark="{ row }">
        <el-tooltip effect="dark" placement="top-start">
          <template #content>
            <div style="max-width: 800px">{{ row.remark || '-' }}</div>
          </template>
          <div class="more-ell">{{ row.remark || '-' }}</div>
        </el-tooltip>
      </template>

      <template #tableAction="{ row }">
        <el-button
          v-btn
          link
          type="primary"
          v-hasPermi="['merchant:manage:detail']"
          @click="viewMerchantInfo(row)"
        >
          查看
        </el-button>
        <el-button
          v-btn
          link
          type="primary"
          @click="openMerchantForm(row.ownerAccount)"
          v-hasPermi="['merchant:manage:edit']"
        >
          编辑
        </el-button>
        <div>
          <el-button
            v-btn
            link
            type="primary"
            @click="openMerchantTime(row.id, row.memberValidity)"
            v-hasPermi="['merchant:manage:updateMemberValidity']"
          >
            修改会员时间
          </el-button>
        </div>
        <el-button
          v-btn
          link
          type="primary"
          @click="openchangeOwnerDialog(row.id, row.name, row.memberCode)"
          v-hasPermi="['merchant:manage:changeOwner']"
        >
          更换主账号
        </el-button>
        <div>
          <el-button
            v-btn
            link
            type="primary"
            @click="openRemarkDialog(row.id, row.ownerAccount)"
            v-hasPermi="['merchant:manage:remark']"
          >
            备注
          </el-button>
        </div>
        <div>
          <el-button
            v-btn
            link
            type="primary"
            @click="saveCode(row.memberCode, row.name)"
            v-hasPermi="['merchant:manage:upload-code']"
          >
            子账号邀请码
          </el-button>
        </div>
      </template>
      <template #pageLeft>
        <DownloadBtn
          v-hasPermi="['merchant:manage:export']"
          type="success"
          plain
          icon="Download"
          url="/biz/business/backend/businessList/export"
          :params="handleParams()"
          fileName="商家列表.xlsx"
        />
      </template>
    </ElTablePage>

    <MerchantForm ref="MerchantFormRef" @success="handleQuery()" />
    <MerchantTime ref="MerchantTimeRef" @success="handleQuery()" />
    <MerchantInfo ref="MerchantInfoRef" />
    <BatchUpdatePersons
      ref="BatchUpdatePersonsRef"
      :selection="multipleSelection"
      @success="updatePersonsSuccess"
    >
      共
      <span style="color: red">{{ multipleSelection.length }}</span>
      位商家需修改关联人员
    </BatchUpdatePersons>
    <MerchantRemark ref="MerchantRemarkRef" @success="handleQuery()" />
    <AccountDialog ref="AccountDialogRef" />
    <ChangeOwnerDialog ref="ChangeOwnerDialogRef" @success="handleQuery()" />
    <ServiceDialog ref="ServiceDialogRef" />
    <ActivityDialog ref="ActivityDialogRef" />
  </div>
</template>

<script setup>
import QRCode from 'qrcode'
import ElTablePage from '@/components/Table/ElTablePage'
import Statistic from '@/views/merchant/list/components/statistic'
import MerchantForm from '@/views/merchant/list/components/merchantForm'
import ChangeOwnerDialog from '@/views/merchant/list/components/changeOwnerDialog.vue'
import SelectLoad from '@/components/Select/SelectLoad.vue'
import MerchantTime from '@/views/merchant/list/components/merchantTime'
import MerchantInfo from '@/views/merchant/list/components/merchantInfo'
import BatchUpdatePersons from '@/views/merchant/list/components/batchUpdatePersons'
import MerchantRemark from '@/views/merchant/list/components/merchantRemark.vue'
import AccountDialog from '@/views/merchant/list/components/accountDialog.vue'
import ServiceDialog from '@/views/merchant/list/components/serviceDialog.vue'
import ActivityDialog from '@/views/merchant/list/components/activityDialog.vue'
import { merchantList, updateMerchantStatus, getBusinessManagerList } from '@/api/merchant/merchant'
import DownloadBtn from '@/components/Button/DownloadBtn.vue'
import { listUser } from '@/api/system/user'
import auth from '@/plugins/auth'
import { ElMessage } from 'element-plus'
import html2canvas from 'html2canvas'

import {
  memberTypeList,
  merchantTypeList,
  memberStatusList,
  accStatusList,
  setMealTypeList,
  companyScaleList,
  merchantIdentificationList,
  customerTypeList,
  channelTypeList,
} from '../data'

const { proxy } = getCurrentInstance()

const shortcuts = [
  {
    text: '今天',
    value: () => {
      const start = new Date()
      start.setHours(0, 0, 0, 0)
      const end = new Date()
      end.setHours(23, 59, 59, 59)
      return [start, end]
    },
  },
]

const columns = [
  { type: 'selection', width: '55', reserveSelection: true },
  // { prop: 'ownerAccount', label: '商家账号', minWidth: '160' },
  { slot: 'info', prop: 'info', label: '商家信息', minWidth: '260' },
  {
    prop: 'memberCode',
    label: '会员编码',
    width: '80',
    // handle: (data, row) => { memberType
    //   let str = ''
    //   if (data) {
    //     str = `会员账号\n会员编码：${row.memberCode}`
    //   } else {
    //     str = '非会员'
    //   }
    //   return str
    // },
  },
  // { slot: 'status', prop: 'status', label: '账号状态', width: '80' },
  {
    prop: 'connectUserName',
    label: '售前',
    width: '100',
    handle: (data, row) => {
      return row.connectUserName ? row.connectUserName : '-'
    },
  },
  {
    prop: 'waiterName',
    label: '对接客服',
    width: '100',
    handle: (data, row) => {
      return row.waiterName && row.waiterName != '' ? row.waiterName : '-'
    },
  },
  {
    prop: 'memberValidity',
    label: '会员信息',
    width: '175',
    handle: (data, row) => {
      let str = '-'
      if (data) {
        str = `${
          setMealTypeList.find(item => item.value == row.memberPackageType).label || ''
        }\n有效期：至${data.replace('T', ' ').replace('.000+08:00', '')}`
      }
      return str
    },
  },
  {
    prop: 'customerType',
    label: '重要程度',
    width: '100',
    handle: val => {
      let m = customerTypeList.find(item => item.value == val)
      return m ? m.label : '-'
    },
  },
  // {
  //   label: '会员来源',
  //   prop: 'channelType',
  //   width: '150',
  //   handle: (data, row) => {
  //     if (row.channelType == 1) {
  //       return row.channelName ? 'SC-' + row.channelName : '-'
  //     }
  //     if (row.channelType == 2) {
  //       return row.channelName ? 'FX-' + row.channelName : '-'
  //     }
  //     if (row.channelType == 7) {
  //       return row.channelName ? 'LB-' + row.nickName : '-'
  //     }
  //     let s = channelTypeList.find(item => item.value == row.channelType)
  //     if (s) {
  //       return s.label
  //     }
  //     return '-'
  //   },
  // },
  {
    label: '商家标识',
    prop: 'businessIdentifier',
    width: '80',
    handle: val => {
      let m = merchantIdentificationList.find(item => item.value == val)
      return m ? m.label : '-'
    },
  },
  {
    prop: 'memberStatus',
    label: '会员状态',
    width: '90',
    handle: data => {
      let m = memberStatusList.find(item => item.value == data)
      return m ? m.label : '-'
    },
  },
  { slot: 'accountNum', prop: 'accountNum', label: '子账号数量', width: '100' },
  {
    prop: 'orderNum',
    label: '订单总数',
    width: '110',
    sortable: 'custom',
    handle: data => {
      return data || '-'
    },
  },
  {
    prop: 'afterSaleRate',
    label: '售后率',
    width: '100',
    handle: data => {
      return data ? data + '%' : '-'
    },
  },
  {
    prop: 'recentOrderNum',
    label: '近30天排单量',
    width: '120',
    handle: data => {
      return data || '-'
    },
  },
  {
    prop: 'preFinishOrderNum',
    label: '待完成量',
    width: '100',
    handle: data => {
      return data || '-'
    },
  },
  {
    slot: 'remark',
    label: '备注',
    width: '120',
  },
]

const tableRef = ref()
const tableData = ref([])
const pageNum = ref(1)
const pageSize = ref(20)
const total = ref(0)
const tableLoading = ref(false)
const multipleSelection = ref([])

const MerchantFormRef = ref()
const MerchantTimeRef = ref()
const MerchantInfoRef = ref()
const BatchUpdatePersonsRef = ref()
const MerchantRemarkRef = ref()

const queryParams = ref({
  val: '',
  select: 'name',
  searchName: '',
  memberType: '',
  merchantType: '',
  memberStatusList: [],
  status: '',
  isProxy: '',
  registerTime: [],
  memberValidity: [],
  isExistRecentOrder: '',
  waiterId: '',
  businessIdentifier: '',
  isAssignWaiter: '',
  connectUserName: '',
  isAsc: '',
  customerType: '',
})

function resetQuery() {
  queryParams.value = {
    val: '',
    select: 'name',
    searchName: '',
    memberType: '',
    isProxy: '',
    merchantType: '',
    memberStatusList: [],
    status: '',
    registerTime: [],
    memberValidity: [],
    isExistRecentOrder: '',
    waiterId: '',
    businessIdentifier: '',
    connectUserName: '',
    isAssignWaiter: '',
    isAsc: '',
    customerType: '',
  }
  tableRef.value?.clearSort()
  handleQuery()
}
function onQuery() {
  pageNum.value = 1
  handleQuery()
}
function handleParams() {
  let { registerTime, memberValidity, val, select, ...data } = queryParams.value
  if (registerTime && registerTime.length) {
    data.businessCreateBegin = registerTime[0]
    data.businessCreateEnd = registerTime[1]
  }
  if (select) {
    data[select] = val
  }
  if (memberValidity && memberValidity.length) {
    data.memberValidityBegin = memberValidity[0]
    data.memberValidityEnd = memberValidity[1]
  }
  return data
}
function handleQuery() {
  tableLoading.value = true
  merchantList({
    pageNum: pageNum.value,
    pageSize: pageSize.value,
    ...handleParams(),
  })
    .then(res => {
      tableData.value = res.data.rows
      total.value = res.data.total
    })
    .finally(() => (tableLoading.value = false))
}
// 分页跳转
function pageChange(page) {
  pageNum.value = page.currentPage
  pageSize.value = page.pageSize
  handleQuery()
}
// 多选
function selectionChange(arr) {
  multipleSelection.value = arr
}
// 排序
function sortChange(sort) {
  sort.order == 'ascending' ? (queryParams.value.isAsc = '1') : (queryParams.value.isAsc = '')
  queryParams.value.orderByType = 1
  if (!sort.order) queryParams.value.orderByType = null
  handleQuery()
}
// 批量更换对接人
function handleBatchEdit() {
  BatchUpdatePersonsRef.value.open()
}

//活动配置
const ActivityDialogRef = ref()
function handleActiveConfig() {
  ActivityDialogRef.value.open()
}
//服务费配置
const ServiceDialogRef = ref()
function handleService() {
  ServiceDialogRef.value.open()
}

// 修改批量更换对接人成功
function updatePersonsSuccess() {
  tableRef.value.clearSelection()
  // pageNum.value = 1
  handleQuery()
}
// 新增/编辑
function openMerchantForm(row) {
  MerchantFormRef.value.open(row)
}
//查看/修改
function openMerchantTime(id, lastTime) {
  MerchantTimeRef.value.open(id, lastTime)
}
// 查看商家信息
function viewMerchantInfo(row) {
  MerchantInfoRef.value.open(row)
}
//备注
function openRemarkDialog(id, ownerAccount) {
  MerchantRemarkRef.value.open(id, ownerAccount)
}

//更换主账号弹窗
const ChangeOwnerDialogRef = ref()
function openchangeOwnerDialog(id, name, code) {
  ChangeOwnerDialogRef.value.open(id, name, code)
}

const dialogVisible = ref(false)
//下载子账号邀请码
const qrCodeDataUrl = ref('')
const qrName = ref('')
async function saveCode(memberCode, name) {
  if (!memberCode) return proxy.$message.warning('暂无子账号')
  const http = encodeURIComponent(import.meta.env.VITE_APP_INVITE_API + '/wechat/invite')
  const url = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx552e48004afb1d3e&redirect_uri=${http}&response_type=code&scope=snsapi_userinfo&state=SubAccountVer${memberCode}`
  try {
    const dataUrl = await QRCode.toDataURL(url, {
      errorCorrectionLevel: 'H',
      width: '100%',
    })
    // downUrlFile(dataUrl, `子账号邀请码.png`)
    qrCodeDataUrl.value = dataUrl
    qrName.value = memberCode + ' - ' + (name || '')
    dialogVisible.value = true
    // proxy.$message.success('下载成功')
  } catch (error) {
    console.error('Failed to generate QR Code', error)
  }
}

const wx = ref('')
// 保存二维码截图
async function saveQrCode() {
  // saveLoading.value = true
  try {
    const dom = document.getElementById('wx')
    if (!dom) {
      // saveLoading.value = false
      ElMessage.error('保存出错了！')
      console.error('Element null')
      return
    }
    const canvas = await html2canvas(dom, {
      useCORS: true,
      scale: 2,
      allowTaint: true,
      backgroundColor: '#fff',
      // onclone: cb => {
      //   const d = cb.getElementById('wn-invite-qrcode-box')
      //   if(d) {
      //     d.style.display = 'flex'
      //   }
      // }
    })
    const img = canvas.toDataURL('image/png')
    const a = document.createElement('a')
    a.href = img
    a.download = qrName.value + '邀请码.png'
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    ElMessage.success('下载成功')
    // saveLoading.value = false
  } catch (error) {
    // saveLoading.value = false
    console.error('Error capturing element:', error)
    ElMessage.error('保存出错了！')
  }
}

// 更新账号状态
function switchChange(row) {
  if (!auth.hasPermi('merchant:manage:status')) {
    proxy.$modal.msgWarning('无权限操作！')
    return
  }
  let val = row.status
  let text = val === 0 ? '禁用' : '启用'
  proxy.$modal
    .confirm('确认要' + text + '' + row.ownerAccount + '商家账号吗?')
    .then(() => {
      return updateMerchantStatus({ ownerAccount: row.ownerAccount, status: val ? 0 : 1 })
    })
    .then(() => {
      proxy.$modal.msgSuccess(text + '成功')
      row.status = val
      handleQuery()
    })
    .catch(() => {
      row.status = val ? 1 : 0
    })
}

const businessManagerList = ref([])
function getBusinessList() {
  getBusinessManagerList({ type: 1 }).then(res => {
    businessManagerList.value = res.data
  })
}
const AccountDialogRef = ref(null)
function showAccountDialog(row) {
  AccountDialogRef.value?.open(row.id)
}

onMounted(() => {
  getBusinessList()
  handleQuery()
})
</script>

<style lang="scss">
#wx {
  font-size: 20px;
  color: var(--text-color);
  padding: 20px 0;

  .logo {
    font-weight: bold;
    color: #000;
    margin-bottom: 10px;

    img {
      width: 30px;
      height: 30px;
    }
  }

  .qrcode-img {
    margin: 10px 0;
    width: 180px;
    height: 180px;
  }

  .tips {
    font-size: 14px;
    color: #6a7484;
  }
}
</style>
<style scoped lang="scss">
.el-popper {
  max-width: 500px;
}
</style>
