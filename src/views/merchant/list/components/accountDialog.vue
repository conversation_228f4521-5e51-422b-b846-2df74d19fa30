<template>
  <div>
    <el-dialog
      title="子账号列表"
      v-model="visible"
      width="1100px"
      align-center
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      :close="handleClose"
    >
      <el-table :data="subAccountList" style="max-height: 600px; min-height: 600px; overflow: auto">
        <el-table-column prop="id" label="ID" width="80" align="center"></el-table-column>
        <el-table-column prop="nickName" label="绑定微信" width="120" align="center">
          <template v-slot="{ row }">
            <div>{{ row.nickName || '-' }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="员工姓名" width="120" align="center">
          <template v-slot="{ row }">
            <div>{{ row.name || '-' }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="phone" label="手机号" width="110" align="center">
          <template v-slot="{ row }">
            <div>{{ row.phone || '-' }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="connectUserName" label="售前" align="center">
          <template v-slot="{ row }">
            <div>{{ row.connectUserName || '-' }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="申请加入时间" width="170" align="center">
          <template v-slot="{ row }">
            <div>{{ row.createTime || '-' }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="auditTime" label="审核时间" width="170" align="center">
          <template v-slot="{ row }">
            <div>{{ row.auditTime || '-' }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="auditStatus" label="审核状态" width="100" align="center">
          <template v-slot="{ row }">
            <span>
              {{
                row.auditStatus == 0
                  ? '待审核'
                  : row.auditStatus == 1
                  ? '已审核'
                  : row.auditStatus == 2
                  ? '已拒绝'
                  : '-'
              }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="子账号状态" width="100" align="center">
          <template v-slot="{ row }">
            <span>
              {{ row.status == 0 ? '正常' : row.status == 1 ? '禁用' : row.status == 3 ? '已解绑' : '-' }}
            </span>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script setup>
import { getSubAccountList } from '@/api/merchant/merchant'

defineExpose({ open, handleClose })

const visible = ref(false)
const subAccountList = ref([])

function open(id) {
  visible.value = true
  getSubAccountList(id).then(res => {
    subAccountList.value = res.data?.rows || []
  })
}

function handleClose() {
  visible.value = false
  subAccountList.value = []
}
</script>

<style lang="scss" scoped></style>
