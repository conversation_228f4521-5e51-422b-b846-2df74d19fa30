<template>
  <el-dialog
    v-model="dialogVisible"
    width="550px"
    title="商家备注"
    align-center
    destroy-on-close
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="close"
  >
    <div>
      <el-input
        type="textarea"
        show-word-limit
        v-model="remark"
        placeholder="请输入备注内容"
        maxlength="1000"
        :rows="5"
      />
      <div class="flex-end" style="margin-top: 10px">
        <el-button round type="primary" v-btn style="padding: 8px 25px" @click="confirmRemark">
          备注
        </el-button>
      </div>
      <div>
        <h3>历史备注记录</h3>
        <div style="max-height: 500px; overflow-y: auto">
          <el-timeline style="max-width: 600px" v-if="formList.length && formList.length > 0">
            <el-timeline-item
              placement="top"
              v-for="(item, index) in formList"
              :key="index"
              :timestamp="item.createBy"
            >
              <div style="color: #909399">
                <div style="margin-bottom: 8px">{{ item.creatTime }}</div>
                <div class="text-wrap">备注：{{ item.remark }}</div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import { ElMessage } from 'element-plus'
import { businessRemarkFlowList, updateBusinessRemark } from '@/api/merchant/merchant'

const dialogVisible = ref(false)
const remark = ref('')
const ownerAccountId = ref('')
const formList = ref([])
const businessId = ref('')

const emits = defineEmits(['success'])

defineExpose({
  open,
  close,
})

function confirmRemark() {
  if (remark.value == '') {
    return ElMessage.warning('请输入备注内容')
  }
  updateBusinessRemark({ remark: remark.value, ownerAccount: ownerAccountId.value }).then(res => {
    ElMessage.success('备注成功')
    remark.value = ''
    emits('success')
    getRemarkFlowList(businessId.value)
  })
}

function close() {
  remark.value = ''
  ownerAccountId.value = ''
  formList.value = []
  businessId.value = ''
  dialogVisible.value = false
}

function getRemarkFlowList(id) {
  businessRemarkFlowList({ businessId: id }).then(res => {
    formList.value = res.data || []
  })
}

function open(id, account) {
  ownerAccountId.value = account
  businessId.value = id
  getRemarkFlowList(id)
  dialogVisible.value = true
}
</script>

<style scoped lang="scss">
.text-wrap {
  word-break: break-all;
  line-break: anywhere;
}
</style>
