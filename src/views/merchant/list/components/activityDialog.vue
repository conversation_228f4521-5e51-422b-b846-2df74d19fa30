<template>
  <div>
    <el-dialog
      align-center
      append-to-body
      title="活动配置"
      v-model="isShow"
      width="600px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="close"
    >
      <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="活动名称:">
          <el-radio-group v-model="status">
            <el-radio-button label="每月首单立减" :value="1" />
          </el-radio-group>
        </el-form-item>
        <el-form-item label="活动规则:">
          <div>
            <div style="color: #333; font-size: 13px">商家每月第一单减免相应优惠金额</div>
            <div style="font-size: 12px; margin-top: -10px">
              (以商家维度，无论是哪个账号下单，订单创建成功即锁定优惠)
            </div>
          </div>
        </el-form-item>
        <el-form-item label="优惠方案:" prop="amount" style="width: 90%">
          <el-input-number
            v-model="form.amount"
            placeholder="请输入"
            @keydown="channelInputLimit"
            style="width: 100%"
            :precision="2"
            :step="1"
            :max="99.99"
            :min="0"
            :controls="false"
            clearable
          >
            <template #suffix>
              <span>美元</span>
            </template>
          </el-input-number>
        </el-form-item>
        <el-form-item label="活动时间:" prop="sinceTime" style="width: 90%">
          <el-date-picker
            v-model="form.sinceTime"
            format="YYYY-M-D HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="datetimerange"
            range-separator="-"
            unlink-panels
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
            :shortcuts="shortcuts"
          ></el-date-picker>
        </el-form-item>
      </el-form>
      <div class="flex-center">
        <el-button v-btn round style="padding: 0 44px" @click="close">取消</el-button>
        <el-button v-btn round style="padding: 0 30px" type="primary" @click="confirm" v-loading="loading">
          确认提交
        </el-button>
      </div>
      <Title>历史修改记录</Title>
      <div style="max-height: 400px; overflow-y: auto">
        <el-timeline style="max-width: 600px">
          <el-timeline-item :hollow="true" v-for="(item, index) in historyList" :key="index">
            <template #default>
              <div style="display: grid; gap: 3px 0">
                <div style="font-size: 14px; color: 3333">{{ item.createBy }}</div>
                <div class="content">修改时间:{{ item.createTime }}</div>
                <div class="content">优惠方案:{{ item.amount }}美元</div>
                <div class="content">活动时间:{{ item.startTime }}至{{ item.endTime }}</div>
              </div>
            </template>
          </el-timeline-item>
        </el-timeline>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import Title from '@/components/Public/title.vue'
import { ElMessage } from 'element-plus'
import {
  getValidPromotionActivityByType,
  promotionActivityAmendmentRecordList,
  updatePromotionActivity,
} from '@/api/merchant/merchant'

const isShow = ref(false)

const emits = defineEmits(['success'])
const loading = ref(false)

const formRef = ref()

const status = 1

const shortcuts = [
  {
    text: '今天',
    value: () => {
      const start = new Date()
      start.setHours(0, 0, 0, 0)
      const end = new Date()
      end.setHours(23, 59, 59, 59)
      return [start, end]
    },
  },
]
const form = ref({
  amount: 0,
  sinceTime: [],
})

const historyList = ref([])

const rules = {
  amount: [{ required: true, message: '请输入正确的金额', trigger: 'blur' }],
  sinceTime: [{ required: true, message: '请选择活动时间', trigger: 'change' }],
}

const open = () => {
  getValidPromotionActivityByType({ type: 4 }).then(res => {
    form.value.amount = res.data.amount
    form.value.sinceTime = [res.data.startTime, res.data.endTime]
    form.value.id = res.data.id
  })
  getHistoryData()
  isShow.value = true
}

function getHistoryData() {
  promotionActivityAmendmentRecordList({ type: 4 }).then(res => {
    historyList.value = res.data
  })
}

const close = () => {
  formRef.value.resetFields()
  isShow.value = false
}

const confirm = () => {
  loading.value = true
  formRef.value.validate(valid => {
    if (valid) {
      const parmas = {
        startTime: form.value.sinceTime[0],
        endTime: form.value.sinceTime[1],
        amount: form.value.amount,
        id: form.value.id,
      }
      updatePromotionActivity(parmas)
        .then(res => {
          ElMessage.success('提交成功')
          close()
          // getHistoryData()
        })
        .finally(() => {
          loading.value = false
        })
    } else {
      loading.value = false
    }
  })
}

const channelInputLimit = e => {
  const key = e.key
  if (key === 'Enter') e.preventDefault()
  const notAllowList = ['e', '+', '-']
  if (notAllowList.includes(key)) {
    e.returnValue = false
    return false
  }
  return true
}

defineExpose({ open, close })
</script>

<style lang="scss" scoped>
@import '@/assets/styles/customForm.scss';
@import '@/assets/styles/form-disabled.scss';
:deep(.el-input-number) {
  .el-input__inner {
    text-align: left;
  }
}
.title {
  font-size: 13px;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
}
.content {
  color: #999;
  font-size: 12px;
}
</style>
