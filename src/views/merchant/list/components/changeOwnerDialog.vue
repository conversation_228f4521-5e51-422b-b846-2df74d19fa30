<template>
  <div>
    <el-dialog
      v-model="dialogVisible"
      width="600px"
      style="max-height: 700px"
      title="更换商家主账号"
      align-center
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="close"
    >
      <div>
        <el-form label-width="100px" :model="form" ref="formRef" :rules="rules">
          <el-form-item label="公司名称">
            <div>{{ bussinessName }}（{{ memberCode }}）</div>
          </el-form-item>
          <el-form-item label="主账号" prop="accountId">
            <el-select v-model="form.accountId" placeholder="请选择" filterable clearable style="width: 90%">
              <el-option v-for="item in ownerList" :key="item.id" :value="item.id" :label="item.showLabel">
                {{ item.showLabel }}
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <div style="flex: 1; margin-right: 10%" class="flex-end">
              <el-button type="primary" v-btn @click="handleFormSubmit">确定</el-button>
            </div>
          </el-form-item>
        </el-form>
        <div>
          <h3>历史更换记录</h3>
          <div style="max-height: 400px; overflow-y: auto" v-if="activities && activities.length > 0">
            <el-timeline style="max-width: 90%; margin-top: 20px">
              <el-timeline-item v-for="(item, index) in activities" :key="index">
                <template #default>
                  <div class="history-item">
                    <div>{{ item.createBy }}</div>
                    <div>{{ item.createTime }}</div>
                    <div>
                      主账号【微信：{{ item.originAccountNickName }}/姓名：{{ item.originAccountName }}】更换为【微信：{{
                        item.accountNickName
                      }}/姓名：{{ item.accountName }}】
                    </div>
                  </div>
                </template>
              </el-timeline-item>
            </el-timeline>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import {
  queryBusinessAccountListByBusinessId,
  exchangeBindOwner,
  getOwnerFlowListByBusinessId,
} from '@/api/merchant/merchant'

import { ElMessage } from 'element-plus'

const dialogVisible = ref(false)

defineExpose({
  open,
  close,
})

const emits = defineEmits(['success'])

const formRef = ref(null)
const form = ref({
  accountId: '',
  businessId: '',
})

const activities = ref([])

const rules = {
  accountId: [
    {
      required: true,
      message: '请选择主账号',
      trigger: 'blur',
    },
  ],
}

const ownerList = ref([])
const bussinessName = ref('')
const memberCode = ref('')
function open(id, name, code) {
  bussinessName.value = name
  memberCode.value = code
  form.value.businessId = id
  getAccountList()
  getChangeDetail()
  dialogVisible.value = true
}

function close() {
  form.value = {
    accountId: '',
    businessId: '',
  }
  ownerList.value = []
  activities.value = []
  bussinessName.value = ''
  memberCode.value = ''
  formRef.value.resetFields()
  dialogVisible.value = false
}

function getAccountList() {
  queryBusinessAccountListByBusinessId(form.value.businessId).then(res => {
    if (res.data && res.data.rows && res.data.rows.length > 0) {
      ownerList.value = res.data.rows.map(item => {
        item.showLabel = `微信名：${item.nickName}/姓名：${item.name}`
        return item
      })
    }
  })
}

function getChangeDetail() {
  getOwnerFlowListByBusinessId({ businessId: form.value.businessId }).then(res => {
    activities.value = res.data.rows || []
  })
}

function handleFormSubmit() {
  formRef.value.validate(valid => {
    if (valid) {
      exchangeBindOwner(form.value).then(res => {
        ElMessage.success('更换成功')
        form.value.accountId = ''
        getAccountList()
        getChangeDetail()
        emits('success')
      })
    }
  })
}
</script>

<style scoped lang="scss">
.history-item {
  display: grid;
  gap: 8px;
  color: #999;
}
</style>
