<template>
  <div>
    <el-dialog
      align-center
      append-to-body
      title="服务费配置"
      v-model="isShow"
      width="600px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="close"
    >
      <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
        <div class="title">代理服务费配置</div>
        <el-form-item label="原价" prop="originPriceProxy">
          <el-input-number
            v-model="form.originPriceProxy"
            style="width: 90%"
            placeholder="请输入"
            @keydown="channelInputLimit"
            :precision="2"
            :step="1"
            :max="99.99"
            :min="0"
            :controls="false"
            clearable
          >
            <template #suffix>
              <span>美元</span>
            </template>
          </el-input-number>
        </el-form-item>
        <el-form-item label="现价" prop="currentPriceProxy">
          <el-input-number
            v-model="form.currentPriceProxy"
            style="width: 90%"
            placeholder="请输入"
            @keydown="channelInputLimit"
            :precision="2"
            :step="1"
            :max="99.99"
            :min="0"
            :controls="false"
            clearable
          >
            <template #suffix>
              <span>美元</span>
            </template>
          </el-input-number>
        </el-form-item>
        <div class="title">非代理服务费配置</div>
        <el-form-item label="原价" prop="originPrice">
          <el-input-number
            v-model="form.originPrice"
            style="width: 90%"
            placeholder="请输入"
            @keydown="channelInputLimit"
            :precision="2"
            :step="1"
            :max="99.99"
            :min="0"
            :controls="false"
            clearable
          >
            <template #suffix>
              <span>美元</span>
            </template>
          </el-input-number>
        </el-form-item>
        <el-form-item label="现价" prop="currentPrice">
          <el-input-number
            v-model="form.currentPrice"
            style="width: 90%"
            placeholder="请输入"
            @keydown="channelInputLimit"
            :precision="2"
            :step="1"
            :max="99.99"
            :min="0"
            :controls="false"
            clearable
          >
            <template #suffix>
              <span>美元</span>
            </template>
          </el-input-number>
        </el-form-item>
        <el-form-item label="生效时间" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio-button label="立即生效" :value="1" />
            <el-radio-button label="到点生效" :value="2" />
          </el-radio-group>
        </el-form-item>
        <el-form-item style="margin-top: -20px" label="" prop="sinceTime" v-if="form.status == '2'">
          <el-date-picker
            v-model="form.sinceTime"
            type="datetime"
            placeholder="选择开始生效时间"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
      </el-form>
      <div class="flex-center">
        <el-button v-btn round style="padding: 0 44px" @click="close">取消</el-button>
        <el-button v-btn round style="padding: 0 30px" type="primary" @click="confirm" v-loading="loading">
          确定提交
        </el-button>
      </div>
      <Title>历史修改记录</Title>
      <div style="max-height: 400px; overflow-y: auto">
        <el-timeline style="max-width: 600px">
          <el-timeline-item :hollow="true" v-for="(item, index) in historyList" :key="index">
            <template #default>
              <div style="display: grid; gap: 3px 0">
                <div style="font-size: 14px; color: 3333">{{ item.createBy }}</div>
                <div class="content">修改时间:{{ item.createTime }}</div>
                <div class="content">生效时间:{{ item.sinceTime }}</div>
                <div
                  class="content"
                  v-if="
                    item.originPriceProxyPrevious != item.originPriceProxy ||
                    item.currentPriceProxyPrevious != item.currentPriceProxy
                  "
                >
                  代理服务费：
                  <template v-if="item.originPriceProxyPrevious != item.originPriceProxy">
                    原价由{{ item.originPriceProxyPrevious }}美金变更为{{ item.originPriceProxy }}美金；
                  </template>

                  <template v-if="item.currentPriceProxyPrevious != item.currentPriceProxy">
                    现价由{{ item.currentPriceProxyPrevious }}美金变更为{{ item.currentPriceProxy }}美金
                  </template>
                </div>
                <div
                  class="content"
                  v-if="
                    item.originPricePrevious != item.originPrice ||
                    item.currentPricePrevious != item.currentPrice
                  "
                >
                  非代理服务费：
                  <template v-if="item.originPricePrevious != item.originPrice">
                    原价由{{ item.originPricePrevious }}美金变更为{{ item.originPrice }}美金；
                  </template>
                  <template v-if="item.currentPricePrevious != item.currentPrice">
                    现价由{{ item.currentPricePrevious }}美金变更为{{ item.currentPrice }}美金
                  </template>
                </div>
              </div>
            </template>
          </el-timeline-item>
        </el-timeline>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import Title from '@/components/Public/title.vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getBeforeDate } from '@/utils/index.js'
import { parseTime } from '@/utils/ruoyi'

import { getServiceConfig, getServiceConfigLog, updateServiceConfig } from '@/api/system/user'

const isShow = ref(false)

const emits = defineEmits(['success'])
const loading = ref(false)

const formRef = ref()

const form = ref({
  originPriceProxy: 0,
  currentPriceProxy: 0,
  originPrice: 0,
  currentPrice: 0,
  sinceTime: '',
  status: 1,
})

const historyList = ref([])

const rules = {
  originPriceProxy: [{ required: true, message: '请输入原价', trigger: 'blur' }],
  currentPriceProxy: [{ validator: checkCurProxy, required: true, trigger: 'blur' }],
  originPrice: [{ required: true, message: '请输入原价', trigger: 'blur' }],
  currentPrice: [{ validator: checkCur, required: true, trigger: 'blur' }],
  status: [{ required: true, message: '请选择生效时间', trigger: 'change' }],
  sinceTime: [{ required: true, message: '选择开始生效时间', trigger: 'change' }],
}

function checkCurProxy(rule, value, callback) {
  if (value === 0) {
    callback()
  }
  if (value == '' || !value) {
    callback(new Error('请输入现价'))
  }
  if (form.value.originPriceProxy < value) {
    callback(new Error('现价不能大于原价'))
  } else {
    callback()
  }
}

function checkCur(rule, value, callback) {
  if (value === 0) {
    callback()
  }
  if (value == '' || !value) {
    callback(new Error('请输入现价'))
  }
  if (form.value.originPrice < value) {
    callback(new Error('现价不能大于原价'))
  } else {
    callback()
  }
}

const open = () => {
  getServiceConfig().then(res => {
    form.value = res.data
    if (form.value.status != 1) {
      form.value.status = 2
    }
  })
  getHistoryData()
  isShow.value = true
}

function getHistoryData() {
  getServiceConfigLog().then(res => {
    historyList.value = res.data
  })
}

const close = () => {
  formRef.value.resetFields()
  isShow.value = false
}

const confirm = () => {
  loading.value = true
  formRef.value.validate(valid => {
    if (valid) {
      const parmas = {
        ...form.value,
        status: form.value.status == '1' ? 1 : null,
        createBy: null,
        sinceTime: form.value.status == '1' ? parseTime(new Date()) : form.value.sinceTime,
      }
      updateServiceConfig(parmas)
        .then(res => {
          ElMessage.success('修改成功')
          getHistoryData()
        })
        .finally(() => {
          loading.value = false
        })
    } else {
      loading.value = false
    }
  })
}

const channelInputLimit = e => {
  const key = e.key
  if (key === 'Enter') e.preventDefault()
  const notAllowList = ['e', '+', '-']
  if (notAllowList.includes(key)) {
    e.returnValue = false
    return false
  }
  return true
}

defineExpose({ open, close })
</script>

<style lang="scss" scoped>
@import '@/assets/styles/customForm.scss';
@import '@/assets/styles/form-disabled.scss';
:deep(.el-input-number) {
  .el-input__inner {
    text-align: left;
  }
}
.title {
  font-size: 13px;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
}
.content {
  color: #999;
  font-size: 12px;
}
</style>
