<template>
  <div class="flex-center statistic-box">
    <div v-for="item in statisticList" :key="item.label" class="flex-column statistic-item">
      <el-statistic :value="item.number"></el-statistic>
      <div class="flex-center tips">
        {{ item.label }}
        <el-tooltip
          effect="dark"
          :content="item.tips"
          placement="top"
        >
          <el-icon><QuestionFilled /></el-icon>
        </el-tooltip>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useTransition } from '@vueuse/core'
import { merchantStatisticsList } from '@/api/merchant/merchant'

const businessTotal = ref(0)
const historyMemberTotal = ref(0)
const memberTotal = ref(0)
const expireMemberTotal = ref(0)
const orderVideoTotal = ref(0)
const recentOrderTotal = ref(0)
const preFinishOrderTotal = ref(0)

const duration = 1000

const statisticList = ref([
  { label: '商家总数', number: useTransition(businessTotal, { duration }), tips: '商家主账号历史注册数量' },
  { label: '历史会员总数', number: useTransition(historyMemberTotal, { duration }), tips: '历史以来，成为过会员的数量' },
  { label: '当前会员总数', number: useTransition(memberTotal, { duration }), tips: '当前有效期内的会员数' },
  { label: '已过期会员', number: useTransition(expireMemberTotal, { duration }), tips: '当前已过期的会员数' },
  { label: '视频总数量', number: useTransition(orderVideoTotal, { duration }), tips: '所有商家的视频排单数量（支付成功即纳入统计，发生退款未扣除）' },
  { label: '近30天排单量', number: useTransition(recentOrderTotal, { duration }), tips: '近30天，所有商家的视频排单数量（支付成功即纳入统计，发生退款未扣除）' },
  { label: '待完成单量', number: useTransition(preFinishOrderTotal, { duration }), tips: '待完成状态下的订单数量' },
])

defineExpose({
  load
})

function load() {
  merchantStatisticsList().then(res => {
    if(res.data) {
      businessTotal.value = res.data.businessTotal
      historyMemberTotal.value = res.data.historyMemberTotal
      memberTotal.value = res.data.memberTotal
      expireMemberTotal.value = res.data.expireMemberTotal
      orderVideoTotal.value = res.data.orderVideoTotal
      recentOrderTotal.value = res.data.recentOrderTotal
      preFinishOrderTotal.value = res.data.preFinishOrderTotal
    }
  })
}

load()
</script>

<style scoped lang="scss">
.statistic-box {
  margin: 30px 0 15px;
  flex-wrap: wrap;

  .statistic-item {
    flex-shrink: 0;
    gap: 8px;
    width: 150px;
    padding: 20px 15px;
    margin: -1px 0 0 -1px;
    border: 1px solid #ccc;

    .tips {
      font-size: 15px;
    }
  }
}
</style>