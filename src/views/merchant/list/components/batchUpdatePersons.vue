<template>
  <!-- 修改关联人员 -->
  <el-dialog v-model="dialogVisible" :title="title" width="350" append-to-body align-center center :close-on-click-modal="false">
    <SelectLoad
      style="width: 100%;"
      v-model="dialogPersons"
      :request="listUser"
      :requestCallback="res => res.data"
      :totalCallback="res => res.data.length"
      keyValue="userId"
      keyLabel="userName"
      keyWord="userName"
    />
    <div style="margin-top: 20px;width: 100%;text-align: center;">
      <slot>
        共 <span style="color: red;">{{props.selection.length}}</span> 位
      </slot>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button v-btn plain @click="dialogVisible = false">取消</el-button>
        <el-button v-btn type="primary" @click="confirmAddPersons">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import SelectLoad from '@/components/Select/SelectLoad';
import { listUser } from "@/api/system/user";
import { updateWaiter } from '@/api/merchant/merchant'

const { proxy } = getCurrentInstance();

let props = defineProps({
  title: {
    type: String,
    default: '批量更换中文部客服'
  },
  selection: {
    type: Array,
    default: () => []
  },
})

const emits = defineEmits(['success'])

defineExpose({
  open,
  close
})

const dialogVisible = ref(false)
const dialogPersons = ref('')

function open() {
  dialogPersons.value = ''
  dialogVisible.value = true
}
function close() {
  dialogVisible.value = false
}
// 确认更换对接人
function confirmAddPersons() {
  if(!dialogPersons.value){
    proxy.$modal.msgError('请选择中文部客服！')
    return
  }
  proxy.$modal.confirm('确认保存吗？', '提示', {}).then(() => {
    proxy.$modal.loading('正在保存中')
    let ids = props.selection.map(item => item.id)
    updateWaiter({
      businessIds: ids,
      waiterId: dialogPersons.value
    }).then(res => {
      dialogVisible.value = false
      proxy.$modal.msgSuccess('保存成功')
      emits('success')
    }).finally(() => proxy.$modal.closeLoading())
  })

}
</script>

<style scoped lang="scss">

</style>
