<template>
  <el-dialog
    v-model="dialogVisible"
    width="550px"
    align-center
    destroy-on-close
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="false"
  >
    <template #header>
      <div class="flex-between">
        <span style="font-size: 18px">{{ title }}</span>
        <el-button link @click="handleClose">
          <el-icon size="16"><Close /></el-icon>
        </el-button>
      </div>
    </template>
    <div class="form-box" v-loading="loading">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="150px">
        <el-form-item v-if="formType === 'detail'" label="会员目前到期时间：">
          <!-- {{ merchantMemberValidity ? merchantMemberValidity.split('T')[0] : '-' }} -->
          {{ memberLastTime ? memberLastTime : '-' }}
          <el-icon @click="showEdit" style="margin-left: 10px; cursor: pointer" color="#2878ff" size="16">
            <Edit />
          </el-icon>
        </el-form-item>
        <el-form-item v-if="formType === 'detail'" label="修改记录：">
          <el-timeline style="max-width: 600px" v-if="formList.length && formList.length > 0">
            <el-timeline-item
              placement="top"
              v-for="(item, index) in formList"
              :key="index"
              :timestamp="`${item.createBy}在${item.createTime}进行修改`"
            >
              <div>
                <div>原会员到期时间：{{ item.originMemberValidity }}</div>
                <div>修改会员到期时间为：{{ item.resultMemberValidity }}</div>
                <div>
                  修改原因：{{
                    editReasonList.find(data => data.value == item.changeReasonType)?.label || '-'
                  }}
                </div>
                <div>备注：{{ item.remark }}</div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </el-form-item>
        <el-form-item v-if="formType === 'edit'" label="修改会员到期时间：" prop="memberValidity">
          <el-date-picker
            :disabled="form.changeReasonType == 2"
            v-model="form.memberValidity"
            format="YYYY/M/D"
            value-format="YYYY-MM-DD"
            type="date"
            style="width: 320px"
          ></el-date-picker>
        </el-form-item>
        <el-form-item v-if="formType === 'edit' && memberData" label="关联会员订单：" :required="true">
          <div style="width: 320px">
            <span>订单号: {{ memberData.orderNum || '-' }}</span>
            ,
            <span>
              套餐类型:
              {{ setMealTypeList.find(item => item.value == memberData.memberPackageType)?.label || '-' }},
            </span>
            <span>
              实付金额: {{ memberData.realPayAmountCurrency }} {{ handleCurrency(memberData.currency) }}
            </span>
          </div>
        </el-form-item>
        <div v-if="refundable.reason == 2 && formType === 'edit'" style="color: #d9001b; font-size: 12px; margin:-20px 0 8px 110px" class="flex-center">
          <el-icon color="#d9001b"><Warning /></el-icon>
          当前商家存在进行中订单，不支持选择7天无理由。
        </div>
        <el-form-item v-if="formType === 'edit'" label="修改原因：" prop="remark">
          <el-radio-group v-model="form.changeReasonType" @change="handleReasonTypeChange">
            <el-radio-button
              v-for="item in editReasonList"
              :key="item.value"
              :value="item.value"
              :disabled="!refundable.canApply && item.value == 2"
            >
              {{ item.label }}
            </el-radio-button>
          </el-radio-group>
          <div
            v-if="isShowReasonTypeErr"
            style="color: #f56c6c; font-size: 12px; margin-top: 10px; line-height: 0"
          >
            请选择修改原因
          </div>
          <el-input
            v-model="form.remark"
            placeholder="请输入修改原因"
            style="width: 320px; margin-top: 10px"
            maxlength="64"
            type="textarea"
            resize="none"
            :rows="4"
          />
        </el-form-item>
      </el-form>
    </div>
    <template #footer v-if="formType == 'edit'">
      <div class="flex-center btn">
        <slot name="button">
          <el-button v-btn plain @click="closeEdit">取消</el-button>
          <el-button v-btn type="primary" @click="onConfirmEdit">确定</el-button>
        </slot>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import {
  updateMemberValidity,
  businessMemberValidityFlowLis,
  getRelationOrder,
  checkUnableSettlement,
} from '@/api/merchant/merchant'
import { setMealTypeList } from '@/views/finance/data.js'
import { editReasonList } from '@/views/merchant/data.js'

import { ElMessage } from 'element-plus'

const { proxy } = getCurrentInstance()
const { sys_money_type } = proxy.useDict('sys_money_type')

const dialogVisible = ref(false)
const loading = ref(false)
const title = ref('查看会员时间')
const formType = ref('detail')
const merchantAcc = ref(0)

const disabledDate = time => {
  // return time.getTime() < new Date(merchantMemberValidity.value).getTime() - 1000 * 60 * 60 * 24
  return time.getTime() < new Date().getTime()
}

const form = ref({
  id: '',
  memberValidity: '',
  remark: '',
  changeReasonType: '',
})
const isShowReasonTypeErr = ref(false)

const formList = ref([])

const rules = {
  memberValidity: [{ required: true, message: '请选择到期时间', trigger: 'blur' }],
  remark: [{ required: true, message: '请输入修改原因', trigger: 'blur' }],
}

function checkRemark(rule, value, callback) {
  if (!value || value.length < 1) {
    return callback(new Error('请输入修改原因'))
  } else if (!form.value.changeReasonType || form.value.changeReasonType === '') {
    return callback(new Error('请选择修改原因'))
  } else {
    return callback()
  }
}

const emits = defineEmits(['success'])

defineExpose({
  open,
  close,
})

const memberLastTime = ref('')

function open(acc, lastTime) {
  memberLastTime.value = lastTime
  form.value.memberValidity = lastTime
  title.value = '查看会员时间'
  merchantAcc.value = acc
  getMerchantInfo()
  dialogVisible.value = true
}
function close() {
  dialogVisible.value = false
}
function handleClose() {
  if (formType.value === 'edit') {
    closeEdit()
  } else {
    reset()
    close()
  }
  //   reset()
  //   info.value = {}
  //   merchantMemberValidity.value = ''
}
function reset() {
  formList.value = []
  memberLastTime.value = ''
  memberData.value = {
    orderNum: '',
    memberPackageType: '',
    realPayAmount: '',
  }
  form.value = {
    id: '',
    remark: '',
    memberValidity: '',
    changeReasonType: '',
  }
}
function handleCurrency(val) {
  if (val == '1') {
    return '人民币'
  }
  return sys_money_type.value.find(item => item.value == val)?.label || '-'
}

function showEdit() {
  title.value = '修改会员时间'
  formType.value = 'edit'
  isShowReasonTypeErr.value = false
  getMemberInfo()
}

function handleReasonTypeChange(val) {
  isShowReasonTypeErr.value = false
  if (val == 2) {
    const today = new Date()
    const yesterday = new Date(today)
    yesterday.setDate(today.getDate() - 1)
    const formattedYesterday = yesterday.toISOString().split('T')[0]
    form.value.memberValidity = formattedYesterday
  }
  // else {
  //   form.value.memberValidity = memberLastTime.value
  // }
}

const refundable = ref({})
const memberData = ref({
  orderNum: '',
  memberPackageType: '',
  realPayAmount: '',
})
function getMemberInfo() {
  getRelationOrder({ businessId: merchantAcc.value }).then(res => {
    memberData.value = res.data
  })
  checkUnableSettlement(merchantAcc.value).then(res => {
    refundable.value = res.data
  })
}

function getMerchantInfo() {
  loading.value = true
  businessMemberValidityFlowLis({
    businessId: merchantAcc.value,
  })
    .then(res => {
      if (res.data?.rows?.length) {
        formList.value = res.data.rows
      }
    })
    .finally(() => (loading.value = false))
}

function closeEdit() {
  title.value = '查看会员时间'
  formType.value = 'detail'
  form.value.memberValidity = memberLastTime.value
  form.value.remark = ''
  form.value.changeReasonType = ''
  isShowReasonTypeErr.value = false
}

function onConfirmEdit() {
  proxy.$refs.formRef.validate(valid => {
    if (!form.value.changeReasonType) {
      isShowReasonTypeErr.value = true
      return
    } else {
      isShowReasonTypeErr.value = false
    }
    if (valid) {
      loading.value = true
      updateMemberValidity({ ...form.value, id: merchantAcc.value })
        .then(res => {
          if (res.code === 200) {
            ElMessage.success('修改成功')
            memberLastTime.value = form.value.memberValidity
            form.value.remark = ''
            form.value.changeReasonType = ''
            title.value = '查看会员时间'
            formType.value = 'detail'
            getMerchantInfo()
            emits('success')
          }
        })
        .finally(() => (loading.value = false))
    }
  })
}
</script>

<style scoped lang="scss">
.form-box {
  max-height: 600px;
  overflow: auto;
  .wechat-box {
    .code-img {
      min-width: 150px;
      min-height: 150px;
    }
    span {
      color: red;
      font-size: 13px;
    }
  }
}
</style>
