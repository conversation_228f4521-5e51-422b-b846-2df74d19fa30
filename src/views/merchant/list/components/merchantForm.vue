<template>
  <el-dialog
    v-model="dialogVisible"
    width="550px"
    :title="title"
    align-center
    destroy-on-close
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <div class="form-box" v-loading="loading">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="130px">
        <el-form-item v-if="formType === 'edit'" label="手机号">
          {{ info.phone }}
        </el-form-item>
        <el-form-item v-if="formType === 'edit'" label="姓名">
          {{ info.accountName }}
        </el-form-item>
        <el-form-item v-if="formType === 'edit'" label="微信名">
          {{ info.nickName }}
        </el-form-item>
        <el-form-item label="公司名称" prop="name">
          <el-input
            v-model="form.name"
            placeholder="请输入商家公司名称"
            style="width: 280px"
            clearable
            max-length="50"
          />
        </el-form-item>
        <el-form-item label="公司规模" prop="scale">
          <el-select clearable v-model="form.scale" placeholder="请选择公司规模" style="width: 280px">
            <el-option v-for="item in companyScaleList" :key="item" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="账号身份" prop="isProxy">
          <el-radio-group v-model="form.isProxy">
            <el-radio v-for="item in merchantTypeList" :key="item.value" :value="item.value">
              {{ item.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="发货电话" prop="phoneVisible">
          <el-radio-group v-model="form.phoneVisible">
            <el-radio v-for="item in phoneShowList" :key="item.value" :value="item.value">
              {{ item.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="商家标识" prop="businessIdentifier">
          <el-radio-group v-model="form.businessIdentifier">
            <el-radio
              v-for="(item, index) in merchantIdentificationList"
              :key="item.value"
              :value="item.value"
            >
              {{ item.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="重要程度" prop="customerType">
          <el-radio-group v-model="form.customerType">
            <el-radio v-for="item in customerTypeList" :key="item.value" :value="item.value">
              {{ item.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="关联客服" prop="waiterId">
          <SelectLoad
            v-model="form.waiterId"
            :request="listUser"
            :requestCallback="res => res.data"
            :totalCallback="res => res.data.length"
            keyValue="userId"
            keyLabel="userName"
            keyWord="userName"
            placeholder="请选择负责对接的客服"
            style="width: 280px"
          />
        </el-form-item>
        <el-form-item v-if="formType === 'add'" label="微信绑定" prop="wechat" required>
          <div class="wechat-box">
            <QrCode style="justify-content: flex-start" :code-type="1" @success="checkSuccess" />
            <template v-if="qrcodeSuccess">
              <p>绑定成功</p>
            </template>
            <template v-else>
              <span>请将此二维码截图，发送给商家,并使用微信扫码绑定，绑定成功后账号即可创建</span>
              <br />
              <span>温馨提示：重新打开窗口后二维码会更新</span>
            </template>
          </div>
        </el-form-item>
        <!-- <el-form-item v-if="formType === 'edit'" label="会员目前到期时间">
          {{ merchantMemberValidity ? merchantMemberValidity.split('T')[0] : '-' }}
        </el-form-item>
        <el-form-item v-if="formType === 'edit'" label="会员有效期" props="memberValidity">
          <el-date-picker
            v-model="form.memberValidity"
            format="YYYY/M/D"
            value-format="YYYY-MM-DD"
            type="date"
            :disabled-date="disabledDate"
            style="width: 280px"
          ></el-date-picker>
        </el-form-item> -->
      </el-form>
    </div>
    <template #footer>
      <div class="flex-center btn">
        <slot name="button">
          <el-button v-btn plain @click="close">取消</el-button>
          <el-button v-btn type="primary" @click="onConfirm">确定</el-button>
        </slot>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import SelectLoad from '@/components/Select/SelectLoad.vue'
import QrCode from '@/components/QrCode/index.vue'
import { listUser } from '@/api/system/user'
import { merchantList, saveMerchant, updateMerchant } from '@/api/merchant/merchant'
import { merchantTypeList, customerTypeList, companyScaleList, phoneShowList } from '../../data'

const { proxy } = getCurrentInstance()

const merchantIdentificationList = [
  { label: '新客', value: 0 },
  { label: '老客', value: 1 },
]

const dialogVisible = ref(false)
const loading = ref(false)
const title = ref('新增商家')
const formType = ref('add')
const merchantAcc = ref(0)
const merchantMemberValidity = ref('')
const info = ref({})
const qrcodeSuccess = ref(false)
const ticket = ref('')

const disabledDate = time => {
  // return time.getTime() < new Date(merchantMemberValidity.value).getTime() - 1000 * 60 * 60 * 24
  return time.getTime() < new Date().getTime()
}

const form = ref({
  scale: '',
  name: '',
  isProxy: 0,
  customerType: 0,
  waiterId: '',
  phoneVisible: 0,
  businessIdentifier: 0,
  // memberValidity: '',
})

const rules = {
  name: [{ required: true, message: '请输入商家公司名称', trigger: 'blur' }],
  isProxy: [{ required: true, message: '请选择账号身份', trigger: 'blur' }],
  // scale: [{ required: true, message: '请选择公司规模', trigger: 'blur' }],
  customerType: [{ required: true, message: '请选择重要程度', trigger: 'blur' }],
  waiterId: [{ required: true, message: '请选择对接客服', trigger: 'blur' }],
  phoneVisible: [{ required: true, message: '请选择发货电话', trigger: 'blur' }],
  businessIdentifier: [{ required: true, message: '请选择标识', trigger: 'blur' }],
  // memberValidity: [{ required: true, message: '请选择会员有效期', trigger: 'blur' }],
  wechat: [{ required: true, validator: checkWechat, trigger: 'blur' }],
}

function checkWechat(rule, value, callback) {
  if (!ticket.value) {
    return callback(new Error('请绑定微信'))
  }
  return callback()
}

const emits = defineEmits(['success'])

defineExpose({
  open,
  close,
})

function open(acc) {
  title.value = '新增商家'
  formType.value = 'add'
  if (acc) {
    merchantAcc.value = acc
    title.value = '编辑商家信息'
    formType.value = 'edit'
    getMerchantInfo()
  }
  dialogVisible.value = true
}
function close() {
  dialogVisible.value = false
}
function handleClose() {
  reset()
  info.value = {}
  merchantMemberValidity.value = ''
}
function reset() {
  form.value = {
    name: '',
    isProxy: 0,
    customerType: 0,
    waiterId: '',
    phoneVisible: 0,
    businessIdentifier: 0,
    // memberValidity: '',
  }
  qrcodeSuccess.value = false
  ticket.value = ''
}

function checkSuccess(data) {
  proxy.$modal.msgSuccess('绑定成功！')
  ticket.value = data.ticket
  qrcodeSuccess.value = true
}

function getMerchantInfo() {
  loading.value = true
  merchantList({
    ownerAccount: merchantAcc.value,
  })
    .then(res => {
      if (res.data?.rows?.length) {
        form.value = {
          name: res.data.rows[0].name,
          isProxy: res.data.rows[0].isProxy,
          customerType: res.data.rows[0].customerType,
          waiterId: res.data.rows[0].waiterId,
          // memberValidity: res.data.rows[0].memberValidity,
          ownerAccount: res.data.rows[0].ownerAccount,
          scale: res.data.rows[0].scale,
          phoneVisible: res.data.rows[0].phoneVisible || 0,
          businessIdentifier: res.data.rows[0].businessIdentifier,
        }
        if(form.value.businessIdentifier == 2) {
          form.value.businessIdentifier = null
        }
        // merchantMemberValidity.value = res.data.rows[0].memberValidity
        info.value = res.data.rows[0]
      }
    })
    .finally(() => (loading.value = false))
}

function onConfirm() {
  proxy.$refs.formRef.validate(valid => {
    if (valid) {
      let params = {
        name: form.value.name,
        isProxy: form.value.isProxy,
        customerType: form.value.customerType,
        waiterId: form.value.waiterId,
        scale: form.value.scale,
        phoneVisible: form.value.phoneVisible,
        businessIdentifier: form.value.businessIdentifier,
      }
      if (formType.value === 'add') {
        if (!qrcodeSuccess.value) {
          proxy.$modal.msgError('请使用微信扫码绑定！')
          return
        }

        params.ticket = ticket.value

        proxy.$modal.loading('正在创建中')
        saveMerchant(params)
          .then(res => {
            proxy.$modal.alert(
              `
                <div style="font-size: 16px;margin: 15px 0;">账号：${res.data.account}</div>
                <div style="font-size: 16px;margin: 15px 0;">密码：${res.data.password}</div>
                <div style="color: red;margin: 15px 0;">请妥善保存账户密码，商家也可自行重置密码</div>
              `,
              '商家新增成功',
              {
                center: true,
                dangerouslyUseHTMLString: true,
                confirmButtonText: '我知道了',
                callback: () => {},
              }
            )
            emits('success')
            close()
          })
          .finally(() => proxy.$modal.closeLoading())
      }

      if (formType.value === 'edit') {
        // params.memberValidity = form.value.memberValidity
        params.ownerAccount = form.value.ownerAccount

        proxy.$modal.loading('正在修改中')
        updateMerchant(params)
          .then(res => {
            proxy.$modal.msgSuccess('修改成功!')
            emits('success')
            close()
          })
          .finally(() => proxy.$modal.closeLoading())
      }
    }
  })
}
</script>

<style scoped lang="scss">
.form-box {
  .wechat-box {
    .code-img {
      min-width: 150px;
      min-height: 150px;
    }
    span {
      color: red;
      font-size: 13px;
    }
  }
}
</style>
