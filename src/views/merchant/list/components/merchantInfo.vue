<template>
  <div>
    <el-dialog
      v-model="dialogVisible"
      width="550px"
      title="商家信息"
      class="info-dialog"
      align-center
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div class="info-box" v-loading="loading">
        <Title>基础信息</Title>

        <el-row>
          <el-col :span="12">
            <label>公司名称：</label>
            <span>{{ info.name || '-' }}</span>
          </el-col>
          <!-- <el-col :span="12">
            <label>商家账号：</label>
            <span>{{ info.ownerAccount }}</span>
          </el-col> -->
          <el-col :span="12">
            <label>公司规模：</label>
            <span>{{ info.scale ? companyScaleList.find(item => item.value == info.scale)?.label || '-' : '-'}}</span>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <label>注册时间：</label>
            <span>{{ info.createTime }}</span>
          </el-col>
          <el-col :span="12">
            <label>商家类型：</label>
            <template v-for="item in merchantTypeList" :key="item.value">
              <span v-if="item.value == info.isProxy">
                {{ item.label }}
              </span>
            </template>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <label>绑定微信：</label>
            <span>{{ info.nickName }}</span>
          </el-col>
          <el-col :span="12">
            <label>账号状态：</label>
            <template v-for="item in accStatusList" :key="item.value">
              <span v-if="item.value == info.status">
                {{ item.label }}
              </span>
            </template>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <label>重要程度：</label>
            <template v-for="item in customerTypeList" :key="item.value">
              <span v-if="item.value == info.customerType">
                {{ item.label }}
              </span>
            </template>
          </el-col>
          <el-col :span="12">
            <label>对接客服：</label>
            <span>{{ info.waiterName || '-' }}</span>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <label>发货电话：</label>
            <template v-for="item in phoneShowList" :key="item.value">
              <span v-if="item.value == info.phoneVisible">
                {{ item.label }}
              </span>
            </template>
          </el-col>
          <el-col :span="12">
            <label>商家标识：</label>
            <template v-for="item in merchantIdentificationList" :key="item.value">
              <span v-if="item.value == info.businessIdentifier">
                {{ item.label }}
              </span>
            </template>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <label>售前：</label>
            <span>{{ info.connectUserName || '-' }}</span>
          </el-col>
        </el-row>

        <Title>订单信息</Title>

        <div class="flex-start order-info">
          <span>订单总数：{{ info.orderNum || '-' }}</span>
          <el-divider direction="vertical" />
          <span>近30天排单量：{{ info.recentOrderNum || '-' }}</span>
          <el-divider direction="vertical" />
          <span>待完成量：{{ info.preFinishOrderNum || '-' }}</span>
        </div>

        <Title>会员信息</Title>

        <template v-if="info.memberCode">
          <div class="vip-info">会员编码：{{ info.memberCode }}</div>
          <div class="vip-info">会员状态：{{ memberStatus }}</div>
          <div class="vip-info">
            会员首次购买：{{
              info.memberPackageType == 0
                ? '季度会员'
                : info.memberPackageType == 1
                ? '一年会员'
                : info.memberPackageType == 2
                ? '三年会员'
                : ''
            }}（{{ info.memberFirstTime }}）
          </div>
          <div class="vip-info">
            会员最近购买时间：{{
              info.memberPackageType == 0
                ? '季度会员'
                : info.memberPackageType == 1
                ? '一年会员'
                : info.memberPackageType == 2
                ? '三年会员'
                : ''
            }}（{{ info.memberLastTime }}）
          </div>
          <div class="vip-info">会员最新有效期至：{{ info.memberValidity }}</div>
        </template>
        <template v-else>
          <div class="vip-info">会员编码：-</div>
          <div class="vip-info">会员状态：-</div>
          <div class="vip-info">会员首次购买：-</div>
          <div class="vip-info">会员最近购买时间：-</div>
          <div class="vip-info">会员最新有效期至：-</div>
        </template>
      </div>
      <template #footer>
        <el-button v-btn plain @click="close">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import Title from '@/components/Public/title'
import { merchantList } from '@/api/merchant/merchant'
import {
  merchantTypeList,
  customerTypeList,
  accStatusList,
  memberStatusList,
  phoneShowList,
  merchantIdentificationList,
  setMealTypeList,
  companyScaleList,
} from '../../data'

const dialogVisible = ref(false)

const loading = ref(false)
const info = ref()
const memberStatus = computed(() => {
  let m = memberStatusList.find(item => item.value == info.value.memberStatus)
  return m ? m.label : '非会员'
})

const dialogData = ref()
defineExpose({
  open,
  close,
})

function open(data) {
  dialogVisible.value = true
  info.value = data
}

function close() {
  dialogVisible.value = false
}
</script>

<style scoped lang="scss">
.info-box {
  font-size: 14px;

  .el-row {
    margin-bottom: 10px;
  }

  label {
    display: inline-block;
    width: 88px;
    text-align: right;
    font-weight: 500;
    color: #000;
  }

  span {
    font-weight: 500;
    color: #000;
  }

  .order-info {
    margin: 0 0 10px 18px;
    font-weight: 500;
    color: #000;
    gap: 20px;
  }

  .vip-info {
    margin: 0 0 10px 18px;
    font-weight: 500;
    color: #000;
  }
}

:deep(.el-dialog .el-dialog__body) {
  padding: 0 20px;
}
</style>
