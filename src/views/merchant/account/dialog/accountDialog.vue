<template>
  <div>
    <el-dialog
      :title="title"
      v-model="dialogVisible"
      align-center
      width="550px"
      destroy-on-close
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="close"
    >
      <el-form
        ref="formRef"
        :model="form"
        :validate-on-rule-change="false"
        :rules="rules"
        label-width="130px"
      >
        <el-form-item label="手机号" prop="phone" v-if="formType === 'add'">
          <el-input v-model="form.phone" placeholder="请输入手机号" style="width: 280px" maxlength="11" />
        </el-form-item>
        <el-form-item label="手机号" v-else>
          <div>{{ form.phone }}</div>
        </el-form-item>
        <el-form-item label="姓名" prop="name">
          <el-input v-model="form.name" placeholder="请输入姓名" style="width: 280px" />
        </el-form-item>
        <!-- <el-form-item label="公司名称" prop="businessName">
          <el-input v-model="form.businessName" placeholder="请输入公司名称" style="width: 280px" />
        </el-form-item> -->
        <!-- <el-form-item label="账号身份" prop="isProxy">
          <el-radio-group v-model="form.isProxy">
            <el-radio v-for="item in merchantTypeList" :key="item.value" :label="item.value">
              {{ item.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item> -->
        <el-form-item label="重要程度" prop="customerType">
          <el-radio-group v-model="form.customerType">
            <el-radio
              v-for="item in customerTypeList"
              :key="item.value"
              :label="item.value"
              :value="item.value"
              :disabled="isCustomerType && item.value == 2"
            >
              {{ item.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <!-- :key="rules.waiterId[0].required" -->
        <el-form-item label="对接客服" prop="waiterId">
          <SelectLoad
            v-model="form.waiterId"
            :request="listUser"
            :requestCallback="res => res.data"
            :totalCallback="res => res.data.length"
            keyValue="userId"
            keyLabel="userName"
            keyWord="userName"
            placeholder="请选择负责对接的客服"
            style="width: 280px"
          />
        </el-form-item>
        <el-form-item label="微信绑定" v-if="formType === 'add'" prop="wechat" required>
          <div class="wechat-box">
            <QrCode
              ref="qrcodeRef"
              style="justify-content: flex-start"
              :httpCode="generateQrcode"
              :checkCode="checkInsertBizUser"
              :code-type="0"
              @success="checkSuccess"
            />
            <template v-if="qrcodeSuccess">
              <p>绑定成功</p>
            </template>
            <template v-else>
              <span>请将此二维码截图，发送给商家,并使用微信扫码绑定，绑定成功后账号即可创建</span>
              <br />
              <span>温馨提示：重新打开窗口后二维码会更新</span>
            </template>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="flex-center btn">
          <slot name="button">
            <el-button v-btn plain @click="close">取消</el-button>
            <el-button v-btn type="primary" @click="onConfirm">确定</el-button>
          </slot>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import SelectLoad from '@/components/Select/SelectLoad.vue'
import QrCode from '@/components/QrCode/index.vue'
import { customerTypeList } from '../../data.js'
import { generateQrcode, checkInsertBizUser } from '@/api/wechat'
import { saveBizUser, editBizUser } from '@/api/merchant/merchant'

import { listUser } from '@/api/system/user'
import { watch } from 'vue'
import { ElMessage } from 'element-plus'
const { proxy } = getCurrentInstance()

defineProps({
  title: {
    type: String,
    default: '新增账号',
  },
})

const qrcodeRef = ref(null)
const emits = defineEmits(['success'])
defineExpose({ open, close })

const isCustomerType = computed(() => form.value.customerType != 2 && form.value.accountType == 1)

const qrcodeSuccess = ref(false)
const dialogVisible = ref(false)
const formType = ref('add')
const formRef = ref(null)
const ticket = ref('')

const form = ref({
  phone: '',
  businessName: '',
  isProxy: 0,
  customerType: 2,
  waiterId: '',
  name: '',
})

const rules = ref({
  phone: [
    { required: true, message: '请输入正确的手机号码', trigger: 'blur' },
    { pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: '请输入正确的手机号码', trigger: 'change' },
  ],
  isProxy: [{ required: true, message: '请选择账号身份', trigger: 'blur' }],
  name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
  customerType: [{ required: true, message: '请选择重要程度', trigger: 'blur' }],
  waiterId: [{ required: false, message: '请选择对接客服', trigger: 'blur' }],
  //   memberValidity: [{ required: true, message: '请选择会员有效期', trigger: 'blur' }],
  wechat: [{ required: true, validator: checkWechat, trigger: 'blur' }],
})

watch(
  () => form.value.customerType,
  newVal => {
    newVal == 2 ? (rules.value.waiterId[0].required = false) : (rules.value.waiterId[0].required = true)
  }
)

function checkWechat(rule, value, callback) {
  if (!ticket.value) {
    return callback(new Error('请绑定微信'))
  }
  return callback()
}

function open(type, row = {}) {
  formType.value = type
  if (type === 'edit') {
    form.value = { ...row }
  }
  dialogVisible.value = true
}

function close() {
  formRef.value.resetFields()
  form.value = {
    phone: '',
    businessName: '',
    isProxy: 0,
    customerType: 2,
    waiterId: '',
    name: '',
  }
  qrcodeSuccess.value = false
  ticket.value = ''
  dialogVisible.value = false
}

function checkSuccess(data) {
  proxy.$modal.msgSuccess('绑定成功！')
  ticket.value = data.ticket
  qrcodeSuccess.value = true
}

function onConfirm() {
  formRef.value.validate(valid => {
    if (valid) {
      if (formType.value === 'add') {
        const params = { ...form.value }

        if (!qrcodeSuccess.value) {
          proxy.$modal.msgError('请使用微信扫码绑定！')
          return
        }
        const data = {
          // businessName: params.businessName,
          customerType: params.customerType,
          isProxy: params.isProxy,
          name: params.name,
          phone: params.phone,
          waiterId: params.waiterId,
          ticket: ticket.value,
        }
        // params.ticket = ticket.value
        proxy.$modal.loading('正在创建中')
        saveBizUser(data)
          .then(res => {
            if (res.data.loginStatus && res.data.loginStatus === 'BINDING') {
              ElMessage.warning('微信号已绑定')
              qrcodeRef.value?.clear()
              qrcodeRef.value?.load()
              return
            }
            if (res.data.loginStatus && res.data.loginStatus === 'EXPIRE') {
              ElMessage.warning('二维码已过期')
              qrcodeRef.value?.clear()
              qrcodeRef.value?.load()
              return
            }
            ElMessage.success('创建成功')
            emits('success')
            close()
          })
          .finally(() => proxy.$modal.closeLoading())
      } else {
        const params = { ...form.value }
        const data = {
          // businessName: params.businessName,
          customerType: params.customerType,
          isProxy: params.isProxy,
          name: params.name,
          // phone: params.phone,
          waiterId: params.waiterId,
          id: params.id,
        }
        proxy.$modal.loading('正在提交')
        editBizUser(data)
          .then(res => {
            ElMessage.success('修改成功')
            emits('success')
            close()
          })
          .finally(() => proxy.$modal.closeLoading())
      }
    }
  })
}
</script>

<style scoped lang="scss">
.wechat-box {
  .code-img {
    min-width: 150px;
    min-height: 150px;
  }
  span {
    color: red;
    font-size: 13px;
  }
}
</style>
