<template>
  <div class="return-visit-page">
    <el-tabs v-model="curTab" class="tabs" @tab-change="handleStatusChange">
      <el-tab-pane v-for="(tab, i) in tabList" :key="tab" :name="tab.value" :label="tab.label">
        <template #label>
          <div>
            {{ tab.label }}
            {{ '(' }}
            <span style="color: red">{{ tab.number }}</span>
            {{ ')' }}
          </div>
        </template>
      </el-tab-pane>
    </el-tabs>

    <div class="search-box">
      <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="88px" @submit.prevent>
        <el-form-item label="搜索" label-width="50px">
          <el-input
            v-model="queryParams.keyword"
            clearable
            style="width: 250px"
            placeholder="请输入公司名称/会员编码"
          />
        </el-form-item>
        <el-form-item label="回访事件">
          <el-select
            v-model="queryParams.incident"
            placeholder="请选择"
            filterable
            multiple
            collapse-tags
            collapse-tags-tooltip
            clearable
            style="width: 180px"
          >
            <el-option
              v-for="item in incidentOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button v-btn type="primary" icon="Search" native-type="submit" @click="onQuery">搜索</el-button>
          <el-button v-btn icon="Refresh" plain @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <ElTablePage
      ref="tableRef"
      :columns="columns"
      :data="tableData"
      :loading="tableLoading"
      :currentPage="currentPage"
      :pageSize="pageSize"
      :total="total"
      :tableOptions="{
        border: true,
      }"
      :tableAction="{
        width: '190',
        fixed: 'right',
      }"
      @page-change="pageChange"
      row-key="id"
      :key="curTab"
    >
      <template #info="{ row }">
        <div style="text-align: left">
          <div>名称：{{ row.name || '-' }}{{ row.memberCode ? `(${row.memberCode})` : '' }}</div>
          <div>
            规模：{{ row.scale ? companyScaleList.find(item => item.value == row.scale)?.label || '-' : '-' }}
          </div>
        </div>
      </template>
      <template #memberValidity="{ row }">
        <div style="text-align: left">
          <div>开始：{{ row.memberFirstTime }}</div>
          <div>过期：{{ row.memberValidity }}</div>
        </div>
      </template>
      <template #accountNum="{ row }">
        <div style="cursor: pointer; line-height: 60px" @click="showAccountDialog(row)">
          {{ row.accountNum }}
        </div>
      </template>
      <template #incident="{ row }">
        <div v-for="item in row.businessCallbackEventVOS">
          <span>{{ handleIncident(item) }}</span>
          <span v-if="row.status == 1 && item.callbackTime">
            (
            <span v-if="handleTime(item.callbackTime)" style="color: #fe9400">
              {{ item.callbackTime }}
            </span>
            <span v-else>{{ item.callbackTime }}</span>
            )
          </span>
        </div>
      </template>
      <template #tableAction="{ row }">
        <el-button
          v-if="checkPermi(['merchant:returnVisit:record'])"
          v-btn
          plain
          size="small"
          type="primary"
          @click="toRecords(row.id)"
        >
          查看记录
        </el-button>
        <el-button
          v-if="row.status == 1 && checkPermi(['merchant:returnVisit:markers'])"
          v-btn
          plain
          size="small"
          type="primary"
          @click="markersRecord(row)"
        >
          标记回访
        </el-button>
        <el-button
          v-if="(row.status == 2 || row.status == 3) && checkPermi(['merchant:returnVisit:edit'])"
          v-btn
          plain
          size="small"
          type="primary"
          @click="openRecordDialog(row)"
        >
          填写记录
        </el-button>
      </template>
    </ElTablePage>

    <el-dialog
      v-model="dialogRecordVisible"
      width="700px"
      title="填写回访记录"
      align-center
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="closeChange"
    >
      <el-form ref="dialogRecordRef" :model="dialogRecordForm" :rules="rules" label-width="100px">
        <el-form-item label="回访事件">
          <div>{{ dialogRecordForm.eventList }}</div>
        </el-form-item>
        <el-form-item label="回访账号" prop="account">
          <el-select
            v-model="dialogRecordForm.account"
            placeholder="请选择回访账号"
            filterable
            clearable
            :loading="accountListLoading"
            style="width: 500px"
          >
            <el-option
              v-for="item in accountList"
              :key="item.id"
              :label="handleAccLabel(item)"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="反馈类型" prop="type">
          <el-select
            v-model="dialogRecordForm.type"
            placeholder="请选择反馈类型"
            filterable
            multiple
            clearable
            style="width: 500px"
          >
            <el-option
              v-for="item in feedbackTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="回访内容" prop="content">
          <el-input
            style="width: 500px"
            show-word-limit
            maxlength="1000"
            type="textarea"
            :rows="6"
            resize="none"
            v-model="dialogRecordForm.content"
            placeholder="请输入回访内容"
          />
        </el-form-item>
        <el-form-item label="图片">
          <PasteUpload
            v-if="fileList.length < 10"
            style="width: 500px; margin-bottom: 10px"
            v-model="fileList"
            :limit="10"
            :size="5"
          />
          <div style="margin-top: -10px" v-if="fileList.length < 10">
            <div>
              1、请上传图片大小不超过
              <span style="color: #d9001b">5M</span>
              ，格式为
              <span style="color: #d9001b">png/jpg/jpeg</span>
              的图片
            </div>
            <div>2、最多可以上传10张</div>
          </div>
          <div style="width: 100%">
            <ViewerImageList
              v-if="fileList.length > 0"
              urlName="picUrl"
              :data="fileList"
              is-preview-all
              @delete="deleteImg"
            />
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer flex-center">
          <el-button v-btn @click="dialogRecordVisible = false">取消</el-button>
          <el-button v-btn type="primary" @click="confirmFillingRecord">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <AccountDialog ref="AccountDialogRef" />
  </div>
</template>

<script setup>
import ElTablePage from '@/components/Table/ElTablePage.vue'
import PasteUpload from '@/components/FileUpload/pasteUpload'
import ViewerImageList from '@/components/ImagePreview/viewerImageList.vue'
import AccountDialog from '@/views/merchant/list/components/accountDialog.vue'
import { checkPermi } from '@/utils/permission'
import {
  getReturnVisitList,
  getInTheReturnVisitList,
  getAlreadyVisitList,
  getReturnVisitStatusCount,
  markReturnVisit,
  getWriteReturnVisitAccount,
  writeReturnVisit,
} from '@/api/merchant/returnVisit'
import { companyScaleList, incidentOptions, feedbackTypeOptions } from '../data'

const { proxy } = getCurrentInstance()
const router = useRouter()

const tabList = ref([
  { value: 1, label: '待回访', number: 0 },
  { value: 2, label: '回访中', number: 0 },
  { value: 3, label: '已回访', number: 0 },
])

const tableData = ref([])
const tableLoading = ref(false)
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const columnList = [
  {
    label: '商家信息',
    prop: 'info',
    minWidth: '250',
    slot: 'info',
  },
  {
    label: '对接客服',
    prop: 'waiterName',
    width: '180',
    handle: data => {
      return data || '-'
    },
  },
  {
    label: '会员有效期',
    prop: 'memberValidity',
    minWidth: '230',
    slot: 'memberValidity',
  },
  {
    label: '子账号数量',
    prop: 'accountNum',
    width: '100',
    slot: 'accountNum',
  },
  {
    label: '订单总数',
    prop: 'orderNum',
    width: '100',
  },
  {
    label: '近30天排单量',
    prop: 'recentOrderNum',
    width: '110',
  },
  {
    label: '待完成量',
    prop: 'preFinishOrderNum',
    width: '100',
  },
  {
    label: '回访事件',
    prop: 'incident',
    minWidth: '300',
    slot: 'incident',
  },
  {
    label: '标记时间',
    prop: 'markTime',
    width: '180',
  },
  {
    label: '记录时间',
    prop: 'writeTime',
    width: '180',
  },
]
const columns = computed(() => {
  if (curTab.value === 1) {
    return columnList.filter(item => item.label !== '记录时间' && item.label !== '标记时间')
  }
  if (curTab.value === 2) {
    return columnList.filter(item => item.label !== '记录时间')
  }
  return columnList
})

const curTab = ref(1)
const queryParams = ref({
  keyword: '',
  incident: '',
})

const dialogRecordRef = ref()
const dialogRecordVisible = ref(false)
const fileList = ref([])
const dialogRecordForm = ref({
  id: '',
  businessId: '',
  account: '',
  type: [],
  content: '',
  eventList: '',
})
const accountListLoading = ref(false)
const rules = ref({
  account: [{ required: true, message: '请选择回访账号', trigger: 'blur' }],
  type: [{ required: true, message: '请选择反馈类型', trigger: 'blur' }],
  content: [{ required: true, message: '请输入回访内容', trigger: ['blur', 'change'] }],
})
const accountList = ref([])

const AccountDialogRef = ref(null)
function showAccountDialog(row) {
  AccountDialogRef.value?.open(row.businessId)
}

function handleAccLabel(item) {
  let owner = item.isOwnerAccount === 1 ? '主账号' : '子账号'
  return `${owner}，微信：${item.nickName || '-'}，姓名：${item.name || '-'}`
}

function handleTime(time) {
  if (time) {
    // 当前时间 > 回访时间的第二天0点
    let t = new Date(time)
    return new Date().getTime() > new Date(t.getFullYear(), t.getMonth(), t.getDate() + 1, 0, 0, 0).getTime()
  }
  return false
}

function onQuery() {
  tableLoading.value = true
  let params = {
    event: queryParams.value.incident,
    keyword: queryParams.value.keyword,
    pageNum: currentPage.value,
    pageSize: pageSize.value,
    status: curTab.value,
  }
  if (curTab.value === 1) {
    getReturnVisitList(params)
      .then(res => {
        if (res.code === 200 && curTab.value === 1) {
          tableData.value = res.data.rows
          total.value = res.data.total
        }
      })
      .finally(() => {
        if (curTab.value === 1) tableLoading.value = false
      })
  } else if (curTab.value === 2) {
    getInTheReturnVisitList(params)
      .then(res => {
        if (res.code === 200 && curTab.value === 2) {
          tableData.value = res.data.rows
          total.value = res.data.total
        }
      })
      .finally(() => {
        if (curTab.value === 2) tableLoading.value = false
      })
  } else if (curTab.value === 3) {
    getAlreadyVisitList(params)
      .then(res => {
        if (res.code === 200 && curTab.value === 3) {
          tableData.value = res.data.rows
          total.value = res.data.total
        }
      })
      .finally(() => {
        if (curTab.value === 3) tableLoading.value = false
      })
  }
  getAccountList()
}
function resetQuery() {
  queryParams.value = {
    keyword: '',
    incident: '',
  }
  currentPage.value = 1
  onQuery()
}
function pageChange(page) {
  currentPage.value = page.currentPage
  pageSize.value = page.pageSize
  onQuery()
}

function handleStatusChange(name) {
  curTab.value = name
  currentPage.value = 1
  onQuery()
}

function handleIncident(row) {
  let data = incidentOptions.find(item => item.value === row.callbackEvent)
  if (data) {
    return data.label
  }
  return ''
}

function toRecords(id) {
  // router.push(`/merchant/returnVisit/records/${id}`)
  const { href } = router.resolve({ path: `/merchant/returnVisit/records/${id}` })
  window.open(href, '_blank')
}

function markersRecord(row) {
  proxy.$modal.confirm('确认将该商家标记为回访中', '提示', {}).then(() => {
    proxy.$modal.loading('正在标记中...')
    markReturnVisit({
      id: row.id,
    })
      .then(res => {
        if (res.code === 200) {
          proxy.$modal.msgSuccess('标记成功')
          onQuery()
        }
      })
      .finally(() => {
        proxy.$modal.closeLoading()
      })
  })
}

function closeChange() {
  accountList.value = []
  fileList.value = []
  dialogRecordForm.value = {
    id: '',
    businessId: '',
    account: '',
    type: [],
    content: '',
    eventList: '',
  }
}
function openRecordDialog(row) {
  // 获取回访账号列表
  accountListLoading.value = true
  getWriteReturnVisitAccount({
    businessId: row.businessId,
  })
    .then(res => {
      if (res.data?.length) {
        accountList.value = res.data
      }
    })
    .finally(() => {
      accountListLoading.value = false
    })

  let eventList = []
  if (row.businessCallbackEventVOS.length > 0) {
    row.businessCallbackEventVOS.forEach(item => {
      eventList.push(handleIncident(item))
    })
  }
  dialogRecordForm.value.id = row.id
  dialogRecordForm.value.businessId = row.businessId
  dialogRecordForm.value.eventList = eventList.join('/')
  dialogRecordVisible.value = true
}
function deleteImg(index) {
  fileList.value.splice(index, 1)
}
function confirmFillingRecord() {
  dialogRecordRef.value?.validate(valid => {
    if (valid) {
      let objectKeys = []
      if (fileList.value && fileList.value.length > 0) {
        objectKeys = fileList.value.map(item => item.picUrl)
      }
      proxy.$modal.loading('正在提交中...')
      writeReturnVisit({
        objectKeys,
        id: dialogRecordForm.value.id,
        businessId: dialogRecordForm.value.businessId,
        accountId: dialogRecordForm.value.account,
        callbackContent: dialogRecordForm.value.content,
        feedbackType: dialogRecordForm.value.type,
      })
        .then(res => {
          if (res.code === 200) {
            dialogRecordVisible.value = false
            proxy.$modal.msgSuccess('提交成功')
            onQuery()
          }
        })
        .finally(() => {
          proxy.$modal.closeLoading()
        })
    }
  })
}

function getAccountList() {
  getReturnVisitStatusCount().then(res => {
    if (res.code === 200) {
      tabList.value.forEach(item => {
        if (item.value === 1) {
          item.number = res.data.waitForReturnVisitCount
        } else if (item.value === 2) {
          item.number = res.data.inTheReturnVisitCount
        } else if (item.value === 3) {
          item.number = res.data.alreadyVisitedCount
        }
      })
    }
  })
}

onQuery()
</script>

<style scoped lang="scss">
.return-visit-page {
  padding: 20px;
}
</style>
