<template>
  <div class="return-visit-records-page">
    <el-card style="width: 100%">
      <div class="flex-between" style="padding: 15px 5px" v-loading="detailLoading">
        <div class="flex-start head-box">
          <div class="company-box">
            <div class="company-name">{{ detailData.name || '-' }}</div>
            <div class="company-code">会员编码：{{ detailData.memberCode || '-' }}</div>
          </div>
          <div class="info-box">
            <div class="title">公司规模</div>
            <div class="info" v-if="detailData.scale">
              {{ companyScaleList.find(item => item.value == detailData.scale)?.label || '-' }}
            </div>
            <div class="info" v-else>-</div>
          </div>
          <div class="info-box">
            <div class="title">对接客服</div>
            <div class="info">{{ detailData.waiterName || '-' }}</div>
          </div>
          <div class="info-box">
            <div class="title">会员有效期</div>
            <div class="info">{{ detailData.memberValidity || '-' }}</div>
          </div>
        </div>
        <div class="flex-start head-box">
          <el-divider direction="vertical" style="height: 52px" />
          <div class="info-box">
            <div class="title">子账号数量</div>
            <div class="info bold" style="cursor: pointer" @click="showAccountDialog">
              {{ detailData.accountNum >= 0 ? detailData.accountNum : '-' }}
            </div>
          </div>
          <div class="info-box">
            <div class="title">订单总数</div>
            <div class="info bold">{{ detailData.orderNum >= 0 ? detailData.orderNum : '-' }}</div>
          </div>
          <div class="info-box">
            <div class="title">近30天排单量</div>
            <div class="info bold">
              {{ detailData.recentOrderNum >= 0 ? detailData.recentOrderNum : '-' }}
            </div>
          </div>
          <div class="info-box">
            <div class="title">待完成量</div>
            <div class="info bold">
              {{ detailData.preFinishOrderNum >= 0 ? detailData.preFinishOrderNum : '-' }}
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <div class="search-box">
      <el-form
        :model="queryParams"
        ref="queryRef"
        :inline="true"
        label-width="88px"
        :disabled="detailLoading"
        @submit.prevent
      >
        <el-form-item label="回访事件">
          <el-select
            v-model="queryParams.incident"
            placeholder="请选择"
            filterable
            multiple
            collapse-tags
            collapse-tags-tooltip
            clearable
            style="width: 230px"
          >
            <el-option
              v-for="item in incidentOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="回访账号">
          <el-select
            v-model="queryParams.account"
            placeholder="请选择"
            filterable
            multiple
            collapse-tags
            collapse-tags-tooltip
            clearable
            :loading="accountLoading"
            style="width: 230px"
          >
            <el-option
              v-for="item in accountList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="反馈类型">
          <el-select
            v-model="queryParams.type"
            placeholder="请选择"
            filterable
            multiple
            collapse-tags
            collapse-tags-tooltip
            clearable
            style="width: 230px"
          >
            <el-option
              v-for="item in feedbackTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button v-btn type="primary" icon="Search" native-type="submit" @click="onQuery">搜索</el-button>
          <el-button v-btn icon="Refresh" plain @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <ElTablePage
      ref="tableRef"
      :columns="columns"
      :data="tableData"
      :loading="tableLoading"
      :currentPage="currentPage"
      :pageSize="pageSize"
      :total="total"
      :tableOptions="{
        border: true,
      }"
      @page-change="pageChange"
      row-key="id"
    >
      <template #incident="{ row }">
        <div v-for="item in row.businessCallbackEventVOS">
          {{ handleIncident(item) }}
        </div>
      </template>
      <template #account="{ row }">
        <div>
          <div v-if="row.accountIsOwnerAccount === 1">主账号</div>
          <div v-else>子账号</div>
          <div>微信：{{ row.accountNickName || '-' }}/姓名：{{ row.accountName || '-' }}</div>
        </div>
      </template>
      <template #pic="{ row }">
        <el-button v-if="row.objectKeys?.length" v-btn link type="primary" @click="handleView(row)">
          查看
        </el-button>
        <div v-else>-</div>
      </template>
    </ElTablePage>

    <AccountDialog ref="AccountDialogRef" />
  </div>
</template>

<script setup>
import ElTablePage from '@/components/Table/ElTablePage.vue'
import AccountDialog from '@/views/merchant/list/components/accountDialog.vue'
import { useViewer } from '@/hooks/useViewer'
import { companyScaleList, incidentOptions, feedbackTypeOptions } from '../data'
import { getReturnVisitDetail, getReturnVisitRecord, getReturnVisitAccount } from '@/api/merchant/returnVisit'

const { proxy } = getCurrentInstance()
const { showViewer } = useViewer()
const route = useRoute()

const detailData = ref({})
const detailLoading = ref(false)

const AccountDialogRef = ref()

const queryParams = ref({
  incident: '',
  account: '',
  type: '',
})
const accountList = ref([])
const accountLoading = ref(false)

const tableData = ref([])
const tableLoading = ref(false)
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const columns = [
  {
    label: '回访事件',
    prop: 'incident',
    width: '200',
    slot: 'incident',
  },
  {
    label: '回访账号',
    align: 'left',
    prop: 'account',
    minWidth: '200',
    slot: 'account',
  },
  {
    label: '反馈类型',
    prop: 'feedbackType',
    width: '150',
    handle: data => {
      if (data == null) return '-'
      let res = feedbackTypeOptions.filter(item => data.indexOf(item.value) > -1).map(item => item.label)
      return res.join('，') || '-'
    },
  },
  {
    label: '回访内容',
    prop: 'callbackContent',
    minWidth: '300',
  },
  {
    label: '图片',
    prop: 'pic',
    width: '100',
    slot: 'pic',
  },
  {
    label: '记录时间',
    prop: 'writeTime',
    width: '180',
  },
  {
    label: '记录人',
    prop: 'writeBy',
    width: '150',
  },
]

function onQuery() {
  if (!route.params.id) {
    proxy.$modal.msgError('链接有误！')
    return
  }
  if (!detailData.value.businessId) {
    init()
    return
  }
  tableLoading.value = true
  let accountName = []
  if (queryParams.value.account) {
    let res = accountList.value.filter(item => queryParams.value.account.includes(item.value))
    if (res) accountName = res.map(item => item.accountName)
  }
  getReturnVisitRecord({
    pageNum: currentPage.value,
    pageSize: pageSize.value,
    businessId: detailData.value.businessId,
    event: queryParams.value.incident,
    feedbackType: queryParams.value.type,
    accountName,
  })
    .then(res => {
      if (res.code === 200) {
        tableData.value = res.data.rows
        total.value = res.data.total
      }
    })
    .finally(() => {
      tableLoading.value = false
    })
}
function resetQuery() {
  queryParams.value = {
    incident: '',
    account: '',
    type: '',
  }
  currentPage.value = 1
  onQuery()
}
function pageChange(page) {
  currentPage.value = page.currentPage
  pageSize.value = page.pageSize
  onQuery()
}

function handleView(row) {
  if (row.objectKeys?.length) showViewer(row.objectKeys)
}

function showAccountDialog() {
  if (detailData.value.accountNum >= 0) {
    AccountDialogRef.value?.open(detailData.value.businessId)
  }
}

function handleIncident(row) {
  let data = incidentOptions.find(item => item.value === row.callbackEvent)
  if (data) {
    return data.label
  }
  return ''
}

// 获取下拉账号列表
function getAccountList() {
  accountLoading.value = true
  getReturnVisitAccount({
    businessId: detailData.value.businessId,
  })
    .then(res => {
      if (res.code === 200) {
        accountList.value = res.data.map(item => ({
          ...item,
          label: handleAccLabel(item),
          value: item.accountName,
        }))
      }
    })
    .finally(() => {
      accountLoading.value = false
    })
}
function handleAccLabel(item) {
  let owner = item.accountIsOwnerAccount === 1 ? '主账号' : '子账号'
  return `${owner}，微信：${item.accountNickName || '-'}，姓名：${item.accountName || '-'}`
}

async function init() {
  if (!route.params.id) {
    proxy.$modal.msgError('访问的链接有误！')
    return
  }
  detailLoading.value = true
  tableLoading.value = true
  await getReturnVisitDetail({ id: route.params.id })
    .then(res => {
      if (res.code === 200) {
        detailData.value = res.data
        getAccountList()
      }
    })
    .catch(() => {
      tableLoading.value = false
    })
    .finally(() => {
      detailLoading.value = false
    })
  onQuery()
}
init()
</script>

<style scoped lang="scss">
.return-visit-records-page {
  padding: 20px;

  .head-box {
    gap: 30px;

    .company-box {
      .company-name {
        line-height: 26px;
        font-size: 16px;
      }
      .company-code {
        width: fit-content;
        font-size: 13px;
        color: #fff;
        background-color: rgb(255, 169, 76);
        border-radius: 18px;
        padding: 5px 12px;
      }
    }

    .info-box {
      .title {
        font-size: 13px;
        color: #999;
        line-height: 26px;
      }
      .info {
        font-size: 16px;
        line-height: 26px;

        &.bold {
          text-align: center;
          font-size: 18px;
          font-weight: bold;
        }
      }
    }
  }

  .search-box {
    margin-top: 30px;
  }
}
</style>
