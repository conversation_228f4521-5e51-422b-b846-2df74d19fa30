export const memberTypeList = [
  { label: '会员', value: 1 },
  { label: '非会员', value: 0 },
]

export function getMemberTypeLabel(value) {
  return memberTypeList.find(item => item.value === value).label
}

export const merchantTypeList = [
  { label: '代理', value: 1 },
  { label: '非代理', value: 0 },
]

export function getMerchantTypeLabel(value) {
  return merchantTypeList.find(item => item.value === value).label
}

export const memberStatusList = [
  { label: '非会员', value: 0 },
  { label: '正常', value: 1 },
  { label: '即将过期', value: 2 },
  { label: '已过期', value: 3 },
]

export function getMemberStatusLabel(value) {
  return memberStatusList.find(item => item.value === value).label
}

export const accStatusList = [
  { label: '正常', value: 0 },
  { label: '禁用', value: 1 },
]

export function getAccStatusLabel(value) {
  return accStatusList.find(item => item.value === value).label

}

export const customerTypeList = [
  { label: '一般客户', value: 0 },
  { label: '重要客户', value: 1 },
]

export function getCustomerTypeLabel(value) {
  return customerTypeList.find(item => item.value === value).label
}
