<template>
  <div style="margin: 20px">
    <el-card style="width: 300px; margin-bottom: 20px">
      <div class="flex-column">
        <el-statistic :value="walletAmount" :precision="2">
          <template #title>
            <div class="title">当前商家总余额（元）</div>
          </template>
        </el-statistic>
      </div>
    </el-card>
    <!-- <el-statistic
      title="当前商家总余额(元)"
      :value=totalBalance
      style="
        text-align: center;
        background-color: rgb(250, 239, 224);
        width: 15vw;
        height: 9vh;
        padding: 10px 0 0 0;
        border-radius: 10px;
        margin: 10px 0;
      "
    /> -->
    <ElTablePage
      ref="tableRef"
      :columns="columns"
      :data="pageData"
      :loading="tableLoading"
      :currentPage="currentPage"
      :pageSize="pageSize"
      :total="total"
      :tableAction="{
        width: '190',
        fixed: 'right',
      }"
      @page-change="pageChange"
      row-key="id"
    >
      <template #tableHeader>
        <div>
          <el-form :inline="true" :model="queryParams" @submit.prevent>
            <el-form-item label="搜索">
              <el-input
                v-model="queryParams.searchName"
                style="width: 210px"
                clearable
                placeholder="请输入会员编码或公司名称"
              ></el-input>
            </el-form-item>
            <el-form-item label="账号身份">
              <el-select v-model="queryParams.isProxy" placeholder="请选择" clearable style="width: 210px">
                <el-option
                  v-for="dict in merchantTypeList"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="会员状态">
              <el-select
                v-model="queryParams.memberStatus"
                placeholder="请选择"
                clearable
                style="width: 210px"
              >
                <el-option
                  v-for="dict in memberStatusList"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="账号状态">
              <el-select v-model="queryParams.status" placeholder="请选择" clearable style="width: 210px">
                <el-option
                  v-for="dict in accStatusList"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button v-btn type="primary" icon="Search" native-type="submit" @click="onQuery">
                搜索
              </el-button>
              <el-button v-btn icon="Refresh" @click="resetQuery">重置</el-button>
              <!-- <DownloadBtn
                v-hasPermi="['merchant:balance:export']"
                type="success"
                plain
                icon="Download"
                url="/biz/business/backend/export/businessBalanceList"
                :params="queryParams"
                fileName="商家余额列表.xlsx"
              /> -->
            </el-form-item>
          </el-form>
        </div>
      </template>
      <template #info="{ row }">
        <div>
          <el-row>
            {{ row.accountName ? row.accountName : row.nickName }}
            <el-tag type="warning" size="small" v-if="row.isProxy == 1" style="border-radius: 10px">
              代理
            </el-tag>
          </el-row>
          <el-row>公司名称: {{ row.name || '-' }}</el-row>
          <el-row>会员开通时间: {{ row.createTime || '-' }}</el-row>
        </div>
      </template>
      <template #tableAction="{ row }">
        <el-button
          v-btn
          v-hasPermi="['merchant:balance:details']"
          link
          type="primary"
          @click="toDetailPage(row)"
        >
          查看明细
        </el-button>
        <el-button
          v-btn
          v-hasPermi="['merchant:balance:withdraw']"
          v-if="row.balance != 0"
          link
          type="primary"
          @click="openDialog(row)"
        >
          发起提现
        </el-button>
        <el-button
          v-btn
          v-hasPermi="['merchant:balance:add-imprest']"
          link
          type="primary"
          @click="addImprest(row)"
        >
          增加钱包余额
        </el-button>
      </template>
      <!-- <template #tableFootLeft>
        <div> -->
      <!-- <DownloadBtn
            type="success"
            v-hasPermi="['merchant:balance:export']"
            plain
            icon="Download"
            url="/biz/business/backend/export/businessBalanceList"
            :params="queryParams"
            fileName="商家余额列表.xlsx"
            text="导出商家余额"
          />
          <el-button
            type="success"
            plain
            icon="Download"
            v-hasPermi="['merchant:balance:export-detail']"
            @click="exportDetailAll"
          >
            导出全部余额明细
          </el-button> -->
      <!-- <DownloadBtn
            type="success"
            plain
            icon="Download"
            url="/biz/business/backend/residentBusinessList/export"
            fileName="商家余额明细列表.xlsx"
            text="导出全部余额明细"
          /> -->
      <!-- </div>
      </template> -->
      <template #pageLeft>
        <DownloadBtn
          type="success"
          v-hasPermi="['merchant:balance:export']"
          plain
          icon="Download"
          url="/biz/business/backend/export/businessBalanceList"
          :params="queryParams"
          fileName="商家余额列表.xlsx"
          text="导出商家余额"
        />
        <el-button
          type="success"
          plain
          icon="Download"
          v-hasPermi="['merchant:balance:export-detail']"
          @click="exportDetailAll"
        >
          导出全部余额明细
        </el-button>
      </template>
    </ElTablePage>
    <el-dialog
      v-model="dialogConfirmVisible"
      width="1000px"
      title="发起提现"
      align-center
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="closeChange"
    >
      <!-- <div style="margin-bottom: 10px">当前可用余额(元): {{ dialogData.remainBalance }}</div> -->
      <el-table
        ref="withdrawTableRef"
        border
        :data="withdrawList"
        @selection-change="handleSelectionChange"
        @row-click="handleCurrentChange"
        style="max-height: 300px; overflow: auto"
      >
        <el-table-column align="center" type="selection" width="55" />
        <el-table-column label="单号" align="center" prop="number"></el-table-column>
        <el-table-column label="视频编码" prop="videoCode" align="center">
          <template v-slot="{ row }">
            <div>{{ row.videoCode || '-' }}</div>
          </template>
        </el-table-column>
        <el-table-column label="下单运营" prop="createOrderUserName" align="center">
          <template v-slot="{ row }">
            <div>{{ row.createOrderUserName || '-' }}</div>
          </template>
        </el-table-column>
        <el-table-column label="类型" prop="origin" align="center">
          <template v-slot="{ row }">
            {{ originTypeList.find(item => item.value == row.origin)?.label || '' }}
          </template>
        </el-table-column>
        <el-table-column label="备注" prop="remark" align="center" width="110px">
          <template v-slot="{ row }">
            <div
              v-if="
                (row.remark && stringLength(row.remark) > 10) ||
                (row.containPresentedAmount && row.containPresentedAmount > 0 && stringLength(row.remark) > 6)
              "
            >
              <el-tooltip effect="dark" :hide-after="0" placement="top">
                <template #content>
                  <div style="max-width: 600px; white-space: pre-wrap; max-height: 45vh; overflow-y: auto">
                    <span style="color: #f59a23">
                      {{ row.containPresentedAmount ? `赠送${row.containPresentedAmount}元` : '' }}
                    </span>
                    {{
                      row.containPresentedAmount
                        ? row.remark
                          ? row.remark
                          : ''
                        : row.remark
                        ? row.remark
                        : '-'
                    }}
                  </div>
                </template>
                <div class="one-ell" style="text-align: center">
                  <span style="color: #f59a23">
                    {{ row.containPresentedAmount ? `赠送${row.containPresentedAmount}元` : '' }}
                  </span>
                  {{ row.remark }}
                </div>
              </el-tooltip>
            </div>
            <span v-else>
              <span style="color: #f59a23">
                {{ row.containPresentedAmount ? `赠送${row.containPresentedAmount}元` : '' }}
              </span>
              {{
                row.containPresentedAmount ? (row.remark ? row.remark : '') : row.remark ? row.remark : '-'
              }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="余额增加时间" prop="createTime" align="center"></el-table-column>
        <el-table-column label="已用金额(元)" prop="useBalance" align="center"></el-table-column>
        <el-table-column label="可提现金额(元)" prop="validBalance" align="center"></el-table-column>
      </el-table>
      <div style="margin: 10px 0; color: #f59a23c5; font-size: 12px">
        请注意：发起后，商家余额将被锁定，无法继续使用
      </div>
      <div style="margin-bottom: 10px">合计提现金额&emsp;{{ dialogData.useBalance }}元</div>
      <el-form ref="dialogs" :model="dialogData" :rules="rules" label-width="100px">
        <!-- <el-form-item label="提现金额:" prop="useBalance">
          <el-col :span="21">
            <div class="dialog-input">
              <el-input-number
                title=""
                v-model="dialogData.useBalance"
                :controls="false"
                placeholder="请输入提现金额（元）"
                :precision="2"
                :step="1"
                :max="dialogData.remainBalance"
                :min="0"
                clearable
                style="width: 350px"
              />
            </div>
          </el-col>
        </el-form-item>
        <div style="padding-left: 100px; margin-bottom: 10px; color: red">
          请注意：发起后，商家余额将被锁定，无法继续使用
        </div> -->
        <el-form-item label="提现方式" prop="withdrawWay">
          <el-select v-model="dialogData.withdrawWay" placeholder="请选择" clearable style="width: 700px">
            <el-option
              v-for="item in withdrawTypeList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            style="width: 700px"
            show-word-limit
            maxlength="1000"
            type="textarea"
            :rows="4"
            resize="none"
            v-model="dialogData.applyRemark"
            placeholder="请输入备注"
          />
        </el-form-item>
        <el-form-item label="图片">
          <PasteUpload
            v-if="fileList.length < 10 && dialogConfirmVisible"
            style="width: 700px; margin-bottom: 10px"
            v-model="fileList"
            :limit="10"
            :size="5"
          />
          <div style="margin-top: -10px" v-if="fileList.length < 10">
            请上传10张大小不超过
            <span style="color: #d9001b">5M</span>
            ，格式为
            <span style="color: #d9001b">png/jpg/jpeg</span>
            的图片
          </div>
          <div style="width: 100%">
            <ViewerImageList
              v-if="fileList.length > 0"
              urlName="picUrl"
              :data="fileList"
              is-preview-all
              @delete="deleteImg"
            />
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer flex-center">
          <el-button v-btn @click="dialogConfirmVisible = false">取消</el-button>
          <el-button
            v-btn
            type="primary"
            :disabled="!dialogData.useBalance || dialogData.useBalance == 0"
            @click="confirmWithDrew"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
      v-model="dialogVisible"
      width="650px"
      title="增加钱包余额"
      align-center
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form ref="formRef" :model="dialogForm" label-width="120px" :rules="formRules">
        <el-form-item label="申请增加金额" prop="amount">
          <el-input-number
            title=""
            v-model="dialogForm.amount"
            controls-position="right"
            :precision="2"
            :step="1"
            :max="99999"
            :min="0"
            clearable
            @keydown="channelInputLimit"
            style="width: 90%"
          />
          <div style="color: #d9001b; width: 90%; line-height: 20px; font-size: 12px">
            请注意：请在确认商家付款后再申请增加预付款金额，预付款金额将会直接增加至商家的余额内，商家可使用余额支付订单
          </div>
        </el-form-item>
        <el-form-item label="含赠送金额" prop="containPresentedAmount">
          <el-input-number
            title=""
            v-model="dialogForm.containPresentedAmount"
            controls-position="right"
            :precision="2"
            :step="1"
            :max="dialogForm.amount"
            :min="0"
            clearable
            @keydown="channelInputLimit"
            style="width: 90%"
          />
        </el-form-item>
        <el-form-item label="支付凭证" prop="file">
          <PasteUpload
            style="width: 90%"
            v-model="dialogForm.file"
            :limit="1"
            :multiple="false"
            :fileType="['jpg', 'jpeg', 'png']"
            show-file-list
          />
        </el-form-item>
        <el-form-item label="备注" prop="applyRemark">
          <el-input
            placeholder="请输入备注"
            v-model="dialogForm.applyRemark"
            show-word-limit
            type="textarea"
            maxlength="100"
            style="width: 90%"
          />
        </el-form-item>
        <el-form-item label="支付方式" prop="payType">
          <el-radio-group v-model="dialogForm.payType" @change="payTypeChange">
            <el-radio v-for="item in payTypeListOptions" :value="item.value" :key="item.value">
              {{ item.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="全币种支付类型" prop="payTypeDetail" v-if="dialogForm.payType == 7">
          <el-select v-model="dialogForm.payTypeDetail" style="width: 90%" placeholder="请选择全币种支付类型">
            <el-option
              v-for="(item, index) in payMoneyTypeAllList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="收款账号" prop="accountId" v-if="dialogForm.payType != 99">
          <el-select
            v-model="dialogForm.accountId"
            placeholder="请选择"
            clearable
            style="width: 90%"
            :disabled="accountListLoading"
          >
            <el-option
              v-for="item in accountList"
              :key="item.id"
              :value="item.detailId"
              :label="item.accountName"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="收款账号" v-else>-</el-form-item>
        <el-form-item label="应付金额">
          <span v-if="payTypeMap['全币种'] == dialogForm.payType">
            {{ amountPayable }}CNY / {{ amountDollarPayble }}USD
          </span>
          <span v-else>{{ amountPayable }}元</span>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="flex-center">
          <el-button v-btn @click="closeDialogForm">取消</el-button>
          <el-button v-btn type="primary" :disabled="accountListLoading" @click="confirmAddImprest">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
      v-model="showExportDetailDialog"
      width="550px"
      title="导出全部余额明细"
      align-center
      @close="closeExportDialog"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div style="margin-bottom: 20px">请选择需要导出的时间范围</div>
      <el-form :model="exportDetailForm" label-width="120px">
        <el-form-item label="时间范围选择:">
          <el-date-picker
            v-model="exportDetailForm.times"
            format="YYYY/M/D HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="datetimerange"
            range-separator="-"
            unlink-panels
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
            :disabled-date="disabledDate"
          ></el-date-picker>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="flex-center">
          <el-button v-btn @click="closeExportDialog">取消</el-button>
          <DownloadBtn
            type="primary"
            url="/biz/business/backend/businessBalanceFlowDetailList/export"
            :params="getDownloadParams"
            fileName="商家余额明细列表.xlsx"
            text="确定"
            :exportDisabled="!exportDetailForm.times || exportDetailForm.times.length == 0"
            @success="closeExportDialog"
          />
          <!-- <el-button v-btn type="primary" @click="export">
            确定
          </el-button> -->
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup>
import ElTablePage from '@/components/Table/ElTablePage.vue'
import PasteUpload from '@/components/FileUpload/pasteUpload'
import ViewerImageList from '@/components/ImagePreview/viewerImageList.vue'
import { accStatusList } from '@/views/merchant/balance/data.js'
import { withdrawTypeList } from '@/views/task/data.js'
import { useTransition } from '@vueuse/core'
import { payTypeList, payTypeMap } from '@/utils/dict.js'
import { stringLength } from '@/utils/index'
import { originTypeList } from '@/views/merchant/data.js'
import {
  businessBalanceTotal,
  businessBalanceVOs,
  updateBusinessBalance,
  addBusinessBalancePrepay,
  getBusinessBalanceDetailList,
} from '@/api/finance/balence.js'
import { getExchangeRate } from '@/api/finance/index.js'
import { payMoneyTypeAllList } from '@/views/finance/data'
import { orderPayeeList } from '@/api/order/select.js'
import { ElMessage } from 'element-plus'
import DownloadBtn from '@/components/Button/DownloadBtn.vue'
import { merchantTypeList } from '@/views/merchant/data.js'
import currency from 'currency.js'

const payTypeListOptions = payTypeList.filter(item => item.value != 5)

const { proxy } = getCurrentInstance()
const router = useRouter()
const pageData = ref([])
const tableLoading = ref(false)
const currentPage = ref(1)
const pageSize = ref(50)
const total = ref(0)
const totalBalance = ref(0)
const walletAmount = useTransition(totalBalance, { duration: 1500 })
const queryParams = ref({
  searchName: '',
  isProxy: '',
  memberStatus: '',
  status: '',
})
const memberStatusList = ref([
  { label: '正常', value: 1 },
  { label: '即将过期', value: 2 },
  { label: '已过期', value: 3 },
])
const rules = ref({
  withdrawWay: [{ required: true, message: '请选择提现方式', trigger: 'change' }],
  useBalance: [
    { required: true, message: '请输入提现金额', trigger: 'blur' },
    { type: 'number', message: '请输入数字', trigger: 'blur' },
    { pattern: /^[0-9]+(.[0-9]{1,2})?$/, message: '最多保留二位小数', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value > dialogData.value.remainBalance) {
          callback(new Error('提现金额不能大于当前可用余额'))
        } else {
          callback()
        }
      },
      trigger: 'blur',
    },
  ],
})

const columns = ref([
  // {
  //   label: '商家账号',
  //   prop: 'ownerAccount',
  //   width: '200',
  // },
  {
    label: '商家信息',
    slot: 'info',
    minWidth: '300',
    align: 'left',
  },
  {
    label: '会员编码',
    prop: 'memberCode',
    width: '200',
  },
  {
    label: '账号状态',
    prop: 'status',
    width: '200',
    handle: status => {
      return accStatusList.find(item => item.value === status)?.label
    },
  },
  {
    label: '会员状态',
    prop: 'memberStatus',
    width: '200',
    handle: memberStatus => {
      return memberStatusList.value.find(item => item.value == memberStatus)?.label
    },
  },
  {
    label: '钱包余额',
    width: '150',
    prop: 'balance',
  },
  {
    label: '可用余额',
    width: '150',
    prop: 'validBalance',
    handle: data => data || 0,
  },
])
const dialogData = ref({
  withdrawWay: '',
  applyRemark: '',
  useBalance: 0,
  businessId: '',
  businessBalanceDetailLocks: [],
})
const dialogConfirmVisible = ref(false)

const accountList = ref([])

const openDialog = row => {
  if (row.validBalance <= 0) {
    ElMessage.error('当前商家可用余额不足')
    return
  }
  getWithdrawList(row.id)
  dialogConfirmVisible.value = true
  dialogData.value = {
    useBalance: 0,
    withdrawWay: '',
    applyRemark: '',
    businessId: row.id,
    businessBalanceDetailLocks: [],
  }
}

function getWithdrawList(businessId) {
  getBusinessBalanceDetailList(businessId).then(res => {
    withdrawList.value = res.data || []
  })
}

const dialogs = ref(null)
const closeChange = () => {
  dialogs.value.resetFields()
  withdrawList.value = []
  fileList.value = []
  withdrawTableRef.value.clearSelection()
  dialogConfirmVisible.value = false
}

//发起提现单号列表
const withdrawTableRef = ref(null)
const withdrawList = ref([])
const fileList = ref([])

function deleteImg(index) {
  fileList.value.splice(index, 1)
}

function handleCurrentChange(row) {
  withdrawTableRef.value.toggleRowSelection(row)
}

function handleSelectionChange(val) {
  let tempAmount = 0
  val.forEach(item => {
    tempAmount += parseFloat(item.validBalance)
  })
  if (val && val.length > 0) {
    const tempList = []
    val.forEach(item => {
      const params = {
        useBalance: item.useBalance,
        balanceDetailId: item.id,
        payOutAmount: item.validBalance,
      }
      tempList.push(params)
    })
    dialogData.value.businessBalanceDetailLocks = tempList
  } else {
    dialogData.value.businessBalanceDetailLocks = []
  }
  dialogData.value.useBalance = tempAmount.toFixed(2)
}

const confirmWithDrew = () => {
  proxy.$refs['dialogs'].validate(valid => {
    if (valid) {
      // if (fileList.value.length) {
      //   params.shippingPic = fileList.value.map(item => item.picUrl)
      // }
      let payoutResourceUrlList = []
      if (fileList.value && fileList.value.length > 0) {
        payoutResourceUrlList = fileList.value.map(item => item.picUrl)
      }
      updateBusinessBalance({
        // withdrawWay: dialogData.value.withdrawWay,
        // applyRemark: dialogData.value.applyRemark,
        // businessId: dialogData.value.businessId,
        // useBalance: dialogData.value.useBalance,
        ...dialogData.value,
        payoutResourceUrlList: payoutResourceUrlList,
      }).then(() => {
        ElMessage.success('提现发起成功')
        handleQuery()
        getBalance()
        dialogConfirmVisible.value = false
      })
    }
  })
}

const dialogVisible = ref(false)
const formRules = ref({
  amount: [
    { required: true, message: '请输入申请金额', trigger: 'blur' },
    { type: 'number', min: 0.01, max: 99999, message: '请输入大于0的数字', trigger: 'blur' },
  ],
  payTypeDetail: [{ required: true, message: '请选择全币种支付类型', trigger: 'change' }],
  containPresentedAmount: [{ required: true, message: '请输入赠送金额', trigger: 'blur' }],
  file: [{ required: true, type: 'array', message: '请上传支付凭证', trigger: 'blur' }],
  payType: [{ required: true, message: '请选择支付方式', trigger: 'change' }],
  accountId: [{ required: true, message: '请选择收款账号', trigger: 'change' }],
})
const dialogForm = ref({
  id: '',
  amount: 0,
  containPresentedAmount: 0,
  file: [],
  applyRemark: '',
  accountId: '',
  payType: 1,
  payTypeDetail: '',
})
const accountListLoading = ref(false)

const exchangeRate = ref(7)

const addImprest = row => {
  getExchangeRate().then(res => {
    if (res.data && typeof res.data.realTimeExchangeRate === 'number') {
      exchangeRate.value = res.data.realTimeExchangeRate
    }
  })
  dialogForm.value.id = row.id
  dialogForm.value.amount = 0
  dialogForm.value.containPresentedAmount = 0
  dialogForm.value.applyRemark = ''
  dialogForm.value.accountId = ''
  dialogForm.value.payType = 1
  dialogForm.value.file.length = 0
  dialogVisible.value = true
  getAccountList(payTypeListOptions[0].value)
}

const amountPayable = computed(() => {
  return currency(dialogForm.value.amount).subtract(dialogForm.value.containPresentedAmount).value.toFixed(2)
})

const amountDollarPayble = computed(() => {
  let n = Math.floor(
    currency(dialogForm.value.amount || 0, { precision: 4 })
      .subtract(dialogForm.value.containPresentedAmount || 0)
      .divide(exchangeRate.value)
      .multiply(100).value
  )
  return currency(n).divide(100).value.toFixed(2)
})

const channelInputLimit = e => {
  const key = e.key
  if (key === 'Enter') e.preventDefault()
  const notAllowList = ['e', '+', '-']
  if (notAllowList.includes(key)) {
    e.returnValue = false
    return false
  }
  return true
}

const closeDialogForm = () => {
  dialogVisible.value = false
  proxy.$refs['formRef'].resetFields()
}

const confirmAddImprest = () => {
  proxy.$refs['formRef'].validate(valid => {
    if (valid) {
      proxy.$modal.confirm('确定提交增加钱包余额？', '提交确认', {}).then(() => {
        proxy.$modal.loading('正在提交中')
        addBusinessBalancePrepay({
          businessId: dialogForm.value.id,
          amount: dialogForm.value.amount,
          containPresentedAmount: dialogForm.value.containPresentedAmount,
          resourceIds: [dialogForm.value.file[0].picUrl],
          applyRemark: dialogForm.value.applyRemark,
          payType: dialogForm.value.payType,
          accountId: dialogForm.value.accountId,
          payTypeDetail:
            dialogForm.value.payType == payTypeMap['全币种'] ? dialogForm.value.payTypeDetail : null,
          payAmountDollar:
            dialogForm.value.payType == payTypeMap['全币种'] ? amountDollarPayble.value : undefined,
        })
          .then(() => {
            dialogVisible.value = false
            proxy.$modal.msgSuccess('增加成功')
            handleQuery()
          })
          .finally(() => proxy.$modal.closeLoading())
      })
    }
  })
}

const onQuery = () => {
  currentPage.value = 1
  fetchData()
}
const handleQuery = () => {
  fetchData()
}
const resetQuery = () => {
  queryParams.value = {
    searchName: '',
    isProxy: '',
    memberStatus: '',
    status: '',
  }
  handleQuery()
}
const fetchData = () => {
  tableLoading.value = true
  businessBalanceVOs({
    pageNum: currentPage.value,
    pageSize: pageSize.value,
    ...queryParams.value,
  }).then(res => {
    pageData.value = res.data.rows
    total.value = res.data.total
    tableLoading.value = false
  })
}
const getBalance = () => {
  businessBalanceTotal().then(res => {
    totalBalance.value = res.data
  })
}
const pageChange = page => {
  currentPage.value = page.currentPage
  pageSize.value = page.pageSize
  handleQuery()
}
const toDetailPage = row => {
  const path = '/merchant/balance/info/' + row.id
  const { href } = router.resolve({ path })
  window.open(href, '_blank')
  // router.push({
  //   path: '/merchant/balance/info/' + row.id,
  //   params: {
  //     businessId: row.ownerAccount,
  //     balance: row.balance,
  //   },
  // })
}

function payTypeChange(val) {
  getAccountList(val)
}

function getAccountList(type) {
  accountListLoading.value = true
  accountList.value.length = 0
  orderPayeeList(type)
    .then(res => {
      dialogForm.value.accountId = ''
      if (res.data?.length) {
        accountList.value = res.data
        let activeAcc = accountList.value.find(item => item.status == 1)
        if (activeAcc) {
          dialogForm.value.accountId = activeAcc.detailId
        }
      }
    })
    .catch(() => {
      dialogForm.value.accountId = ''
    })
    .finally(() => (accountListLoading.value = false))
}

//导出余额全部明细
const showExportDetailDialog = ref(false)
const exportDetailForm = ref({
  times: [],
})
function exportDetailAll() {
  showExportDetailDialog.value = true
}
function closeExportDialog() {
  exportDetailForm.value.times = []
  showExportDetailDialog.value = false
}

function disabledDate(date) {
  return date < new Date('2025-01-05 00:00:00')
}

function getDownloadParams() {
  if (exportDetailForm.value.times && exportDetailForm.value.times.length > 0) {
    return {
      orderTimeBegin: exportDetailForm.value.times[0],
      orderTimeEnd: exportDetailForm.value.times[1],
    }
  }
}

handleQuery()
getBalance()
</script>

<style lang="scss" scoped>
.dialog-input {
  :deep(.el-input-number) {
    .el-input__inner {
      text-align: left;
    }
  }
}
</style>
