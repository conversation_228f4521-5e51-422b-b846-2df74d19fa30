<template>
  <el-container :v-loading="tableLoading">
    <el-main>
      <div class="info-haed">
        <el-row>
          <el-col :span="8">
            <div style="font-weight: 600">{{ infoData.name || '-' }}</div>
            <el-tag style="margin-top: 10px" effect="dark" type="warning" round>
              会员编码：{{ infoData.memberCode }}
            </el-tag>
          </el-col>
          <el-col :span="3">
            <div class="label">钱包余额</div>
            <div class="value">{{ infoData.balance }}</div>
          </el-col>
          <el-col :span="3">
            <div class="label">可用余额</div>
            <div class="value">{{ infoData.validBalance }}</div>
          </el-col>
          <el-col :span="3">
            <div class="label">已锁定金额</div>
            <div class="value">{{ infoData.useBalance }}</div>
          </el-col>
          <el-col :span="1">
            <div style="border-left: 1px solid #efefef; width: 1px; height: 100%; margin: 0 auto"></div>
          </el-col>
          <el-col :span="3">
            <div class="label">已用金额</div>
            <div class="value">{{ infoData.useBalanceTotal }}</div>
          </el-col>
          <el-col :span="3">
            <div class="label">已提现金额</div>
            <div class="value">{{ infoData.withdrawTotal }}</div>
          </el-col>
        </el-row>
        <!-- <el-descriptions title="余额明细">
          <el-descriptions-item>
            <span v-if="infoData!=null">
              商家ID:{{ infoData.ownerAccount }}
            </span>
            <span v-if="infoData!=null" class="ml10">
              剩余余额 :{{ infoData.validBalance }}
            </span>
          </el-descriptions-item>
        </el-descriptions> -->
      </div>
      <ElTablePage
        ref="tableRef"
        :columns="columns"
        :data="pageData"
        :loading="tableLoading"
        :currentPage="currentPage"
        :pageSize="pageSize"
        :total="total"
        @page-change="pageChange"
        row-key="id"
      >
        <template #tableHeader>
          <el-form :inline="true" :model="queryParams" @submit.prevent>
            <el-form-item label="搜索">
              <el-input
                placeholder="请输入搜索内容"
                style="width: 200px"
                v-model="queryParams.keyword"
                clearable
              />
            </el-form-item>
            <el-form-item label="收支来源">
              <el-select v-model="queryParams.origins" placeholder="请选择">
                <el-option
                  v-for="item in originTypeList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button
                v-btn
                type="primary"
                icon="Search"
                native-type="submit"
                @click="getBusinessBalanceFlowList"
              >
                搜索
              </el-button>
              <el-button v-btn icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </template>
        <template #linkInfo="{ row }">
          <div>
            <el-row v-if="row.videoCode">视频编号: {{ row.videoCode }}</el-row>
            <el-row v-if="row.orderNum">订单号: {{ row.orderNum }}</el-row>
            <el-row
              style="line-break: anywhere; text-align: left"
              v-if="row.numberList && row.numberList.length > 0 && row.origin != 7 && row.origin != 8"
            >
              退款单号: {{ row.numberList.join('、') }}
            </el-row>
            <el-row v-if="row.withdrawNumber && row.origin == 6">提现单号: {{ row.withdrawNumber }}</el-row>
            <template v-if="row.origin != 4 && row.origin != 5">
              <el-row v-if="row.refundNum">退款审批号: {{ row.refundNum }}</el-row>
              <el-row
                v-if="row.numberList && row.numberList.length > 0 && (row.origin == 7 || row.origin == 8)"
              >
                预付单号: {{ row.numberList.join('、') }}
              </el-row>
              <el-row v-if="row.prepayNum && (row.origin == 7 || row.origin == 8)">
                预付审批号：{{ row.prepayNum }}
              </el-row>
            </template>
            <span v-if="!row.videoCode && !row.orderNum && !row.refundNum && !row.numberList">-</span>
          </div>
        </template>
        <template #amount="{ row }">
          <div :style="{ color: row.amount < 0 ? 'red' : '#000' }">
            {{ row.amount }}
          </div>
        </template>
        <template #pageLeft>
          <DownloadBtn
            style="margin-right: 15px"
            type="success"
            plain
            icon="Download"
            url="/biz/business/backend/export/businessBalanceFlowList"
            v-hasPermi="['merchant:balance:info-export']"
            :params="queryParams"
            fileName="商家余额明细列表.xlsx"
          />
        </template>
      </ElTablePage>
    </el-main>
  </el-container>
</template>
<script setup>
import { useRoute } from 'vue-router'
import { businessBalanceFlowList, getBusinessVo } from '@/api/finance/balence.js'
import ElTablePage from '@/components/Table/ElTablePage.vue'
import DownloadBtn from '@/components/Button/DownloadBtn.vue'
import { originTypeList } from '@/views/merchant/data.js'
const route = useRoute()
const columns = [
  {
    label: '余额流水号',
    prop: 'flowOrderNo',
    handle: flowOrderNo => {
      return flowOrderNo ? flowOrderNo : '-'
    },
  },
  {
    label: '交易时间',
    prop: 'orderTime',
  },
  {
    label: '收支类型',
    prop: 'type',
    handle: type => {
      return typeList.find(item => item.value === type)?.label
    },
  },
  {
    label: '金额(元)',
    prop: 'amount',
    slot: 'amount',
  },
  {
    label: '收支来源',
    prop: 'origin',
    handle: originType => {
      return originTypeList.find(item => item.value == originType)?.label
    },
  },
  {
    label: '余额(元)',
    prop: 'balance',
  },
  {
    label: '关联单号',
    minWidth: 200,
    slot: 'linkInfo',
  },
]
const pageData = ref([])
const tableLoading = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const queryParams = ref({
  businessId: '',
  keyword: '',
  origins: '',
})
const infoData = ref({
  name: '',
  memberCode: '',
  balance: '',
  validBalance: '',
  useBalance: '',
  useBalanceTotal: '',
  withdrawTotal: '',
  ownerAccount: '',
})
// 订单类型(0-收入、1-支出)
const typeList = [
  {
    label: '收入',
    value: 0,
  },
  {
    label: '支出',
    value: 1,
  },
]

const resetQuery = () => {
  queryParams.value = {
    businessId: route.params.businessId,
    keyword: '',
    type: '',
  }
  currentPage.value = 1
  getBusinessBalanceFlowList()
}
const pageChange = async page => {
  currentPage.value = page.currentPage
  pageSize.value = page.pageSize
  await getBusinessBalanceFlowList()
}
const getBusinessBalanceFlowList = async () => {
  tableLoading.value = true
  const params = {
    pageNum: currentPage.value,
    pageSize: pageSize.value,
    ...queryParams.value,
  }
  const { data } = await businessBalanceFlowList(params)
  pageData.value = data.rows
  total.value = data.total
  tableLoading.value = false
}
onMounted(() => {
  queryParams.value.businessId = route.params.businessId
  getBusinessBalanceFlowList()
  businessVo()
})
const businessVo = async () => {
  const { data } = await getBusinessVo({
    businessId: queryParams.value.businessId,
  })
  infoData.value = data
}
</script>

<style scoped lang="scss">
.info-haed {
  background-color: #fff;
  padding: 20px 13px;
  border-radius: 4px;
  gap: 10px;
  position: relative;
  // box-shadow: var(--el-box-shadow-light);
  box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.12);
  color: #666;
  margin-bottom: 20px;
  .value {
    font-weight: 600;
    margin-top: 15px;
  }
  .label {
    margin-top: 4px;
    color: #7f7f7f;
    font-size: 12px;
  }
}
.el-col-8,
.el-col-3 {
  text-align: center;
}
</style>
