<template>
  <div>
    <el-dialog
      v-model="dialogVisible"
      :title="textTitle"
      :close-on-click-modal="false"
      width="800px"
      align-center
      @close="close"
    >
      <el-form ref="textFormRef" :model="textForm" label-width="80px" :rules="rules">
        <el-form-item label="文本名称" prop="name">
          <el-input v-model="textForm.name" placeholder="请输入文本名称" />
        </el-form-item>
        <el-form-item label="文本内容" prop="content">
          <!-- <Editor ref="editorRef" v-model="textForm.content" :maxHeight="300"></Editor> -->
           <WangEditor v-model="textForm.content"/>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="textForm.remark"
            type="textarea"
            rows="6"
            maxlength="300"
            resize="none"
            show-word-limit
            placeholder="请输入备注内容"
          />
        </el-form-item>
        <!-- <el-form-item>
          <el-button @click="close">取消</el-button>
          <el-button @click="submitForm" type="primary">保存</el-button>
        </el-form-item> -->
      </el-form>
      <template #footer>
        <div class="flex-center">
          <el-button v-btn @click="close">取消</el-button>
          <el-button v-btn type="primary" @click="submitForm">保存</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
// Wangeditor富文本编辑器
import WangEditor from "@/components/WangEditor"
import { ref } from 'vue'
import { addText, editText, getTextDetail } from '@/api/system/text'
import { ElMessage } from 'element-plus'

const textFormRef = ref(null)
const editorRef = ref(null)
const dialogVisible = ref(false)
const textTitle = ref('')
const textType = ref('')
const textForm = ref({
  name: '',
  content: '',
  remark: '',
})
const emits = defineEmits(['success'])
defineExpose({ open, close })

const rules = {
  name: [{ required: true, trigger: 'blur', message: '请输入文本名称' }],
}

function open(type, id = null) {
  textType.value = type
  if (type === 'add') {
    textTitle.value = '添加文本'
  } else {
    textTitle.value = '编辑文本'
    handleDetail(id)
  }
  dialogVisible.value = true
}

function close() {
  emits('success')
  textFormRef.value.resetFields()
  // editorRef.value.clearContent()
  textForm.value = {
    name: '',
    content: '',
    remark: '',
  }
  dialogVisible.value = false
}

function handleDetail(id) {
  // TODO: 获取详情数据
  getTextDetail(id).then(res => {
    textForm.value = res.data
  })
}

function submitForm() {
  textFormRef.value.validate(valid => {
    if (valid) {
      if (textType.value === 'add') {
        // TODO: 新增数据
        addText(textForm.value).then(res => {
          ElMessage.success('新增成功')
          close()
        })
      } else {
        editText(textForm.value).then(res => {
          ElMessage.success('修改成功')
          close()
        })
      }

      // TODO: 保存数据
    }
  })
}
</script>

<style scoped lang="scss">
:deep(.el-dialog) {
  border-radius: 15px;
}
</style>
