<template>
  <div style="padding: 20px">
    <ElTablePage
      ref="tableRef"
      :columns="columns"
      :data="tableData"
      :loading="tableLoading"
      :currentPage="pageNum"
      :pageSize="pageSize"
      :total="total"
      :isTablePage="false"
      :tableAction="{
        width: '170',
        fixed: 'right',
      }"
      @page-change="pageChange"
    >
      <template #tableHeader>
        <div style="margin-bottom: 10px">
          <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="68px" @submit.prevent>
            <el-form-item label="文本名称">
              <el-input
                v-model="queryParams.name"
                clearable
                style="width: 300px"
                placeholder="请输入页面名称"
              ></el-input>
            </el-form-item>
            <el-form-item label="文本ID">
              <el-input
                v-model="queryParams.id"
                clearable
                style="width: 180px"
                placeholder="请输入页面id"
              ></el-input>
            </el-form-item>
            <el-form-item>
              <el-button v-btn type="primary" native-type="submit" @click="handleQuery">搜索</el-button>
              <el-button v-btn @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
          <el-button type="primary" v-hasPermi="['system:text:add']" @click="handleTextInfo('add')">
            新增
          </el-button>
        </div>
      </template>
      <template #remark="{ row }">
        <div class="one-ell" style="text-align: center">
          {{ row.remark }}
        </div>
      </template>

      <template #tableAction="{ row }">
        <el-button
          v-btn
          link
          type="primary"
          @click="handleTextInfo('edit', row.id)"
          v-hasPermi="['system:text:edit']"
        >
          修改
        </el-button>
        <el-button
          v-if="row.canDelete == 0"
          v-btn
          link
          type="primary"
          @click="deleteTextInfo(row.id)"
          v-hasPermi="['system:text:delete']"
        >
          删除
        </el-button>
      </template>
    </ElTablePage>
    <TextDialog ref="textDialogRef" @success="init" />
  </div>
</template>

<script setup>
import ElTablePage from '@/components/Table/ElTablePage'
import { ref } from 'vue'
import TextDialog from '@/views/system/text/components/textDialog.vue'
import { ElMessageBox, ElMessage } from 'element-plus'

import { getTextList, deleteText } from '@/api/system/text.js'

const textDialogRef = ref(null)

const columns = [
  {
    prop: 'id',
    label: '文本ID',
    width: '150',
  },
  { prop: 'name', label: '文本名称', minWidth: '250' },
  {
    prop: 'createTime',
    label: '创建时间',
    width: '200',
  },
  {
    slot: 'remark',
    prop: 'remark',
    label: '备注',
    minWidth: '230',
  },
]

const tableData = ref([])
const pageNum = ref(1)
const pageSize = ref(20)
const total = ref(0)
const tableLoading = ref(false)

const queryParams = ref({
  name: '',
  id: '',
})

function resetQuery() {
  queryParams.value = {
    name: '',
    id: '',
  }
  init()
}

function init() {
  handleQuery()
}

function handleQuery() {
  tableLoading.value = true
  getTextList({
    //   pageNum: pageNum.value,
    //   pageSize: pageSize.value,
    ...queryParams.value,
  })
    .then(res => {
      tableData.value = res.data
    })
    .finally(() => {
      tableLoading.value = false
    })
}
// 分页跳转
function pageChange(page) {
  pageNum.value = page.currentPage
  pageSize.value = page.pageSize
  init()
}

function handleTextInfo(type, id = null) {
  textDialogRef.value.open(type, id)
}

function deleteTextInfo(id) {
  ElMessageBox.confirm('确认删除该文本吗？', '提示').then(() => {
    deleteText({ id }).then(() => {
      ElMessage.success('删除成功')
      init()
    })
  })
}

init()
</script>

<style scoped lang="scss"></style>
