<template>
  <div class="app-container">
    <h4 class="form-header h4">基本信息</h4>
    <el-form :model="form" label-width="80px">
      <el-row>
        <el-col :span="8" :offset="0">
          <el-form-item label="账号名称" prop="userName">
            <el-input v-model="form.userName" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8" :offset="0">
          <el-form-item label="手机号" prop="phonenumber">
            <el-input v-model="form.phonenumber" disabled />
          </el-form-item>
        </el-col>
      </el-row>

      <!--        <h4 class="form-header h4">数据权限</h4>-->
      <!--        <el-form-item label="可查看范围" prop="dataScope" label-width="90px">-->
      <!--          <el-radio-group v-model="form.dataScope">-->
      <!--            <el-radio v-for="item in dataScopeOptions" :key="item.value" :value="item.value" :label="item.value">{{item.label}}</el-radio>-->
      <!--          </el-radio-group>-->
      <!--        </el-form-item>-->
    </el-form>

    <h4 class="form-header h4">公共权限配置</h4>
    <div class="flex-start" style="margin-bottom: 20px">
      <span class="label-box">工作台展示面板：</span>
      <el-radio-group v-model="workbench">
        <el-radio-button
          v-for="item in workbench_auth_list"
          :label="item.value ? item.label : `&emsp;无&emsp;`"
          :value="item.value"
          :key="item.value"
        />
      </el-radio-group>
    </div>
    <div class="flex-start" style="margin-bottom: 20px">
      <span class="label-box">预选管理-沟通中：</span>
      <el-radio-group v-model="preselection_my_1">
        <el-radio-button label="查看自己" :value="0" />
        <el-radio-button label="查看全部" :value="1" />
      </el-radio-group>
    </div>

    <h4 class="form-header h4">角色信息</h4>
    <el-table
      v-loading="loading"
      :row-key="getRowKey"
      @row-click="clickRow"
      ref="roleRef"
      @selection-change="handleSelectionChange"
      :data="roles.slice((pageNum - 1) * pageSize, pageNum * pageSize)"
    >
      <el-table-column type="selection" :reserve-selection="true" width="55"></el-table-column>
      <el-table-column label="序号" width="55" type="index" align="center">
        <template #default="scope">
          <span>{{ (pageNum - 1) * pageSize + scope.$index + 1 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="角色编号" align="center" prop="roleId" />
      <el-table-column label="角色名称" align="center" prop="roleName" />
      <el-table-column label="权限字符" align="center" prop="roleKey" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="pageNum" v-model:limit="pageSize" />

    <el-form label-width="100px">
      <div style="text-align: center; margin-left: -120px; margin-top: 30px">
        <el-button v-btn @click="close()">返 回</el-button>
        <el-button v-btn type="primary" @click="submitForm()" :loading="saveLoading">保 存</el-button>
      </div>
    </el-form>
  </div>
</template>

<script setup name="AuthRole">
import { getAuthRole, updateAuthRole, editWorkbenchRole } from '@/api/system/user'
import { workbench_auth_map, workbench_auth_list } from '@/views/system/workbench/data'
import useUserStore from '@/store/modules/user'

const userStore = useUserStore()
const route = useRoute()
const { proxy } = getCurrentInstance()

const loading = ref(true)
const saveLoading = ref(false)
const total = ref(0)
const pageNum = ref(1)
const pageSize = ref(10)
const roleIds = ref([])
const roles = ref([])
const form = ref({
  phonenumber: undefined,
  userName: undefined,
  userId: undefined,
  dataScope: undefined,
})
const workbench = ref(0)
const preselection_my_1 = ref(1)

/** 数据范围选项*/
const dataScopeOptions = ref([
  { value: '1', label: '全部数据' },
  // { value: "2", label: "自定数据" },
  // { value: "3", label: "本部门数据" },
  { value: '4', label: '本部门及以下' },
  { value: '5', label: '仅本人' },
])

/** 单击选中行数据 */
function clickRow(row) {
  proxy.$refs['roleRef'].toggleRowSelection(row)
}
/** 多选框选中数据 */
function handleSelectionChange(selection) {
  roleIds.value = selection.map(item => item.roleId)
}
/** 保存选中的数据编号 */
function getRowKey(row) {
  return row.roleId
}
/** 关闭按钮 */
function close() {
  const obj = { path: '/system/user' }
  proxy.$tab.closeOpenPage(obj)
}
/** 提交按钮 */
async function submitForm() {
  const userId = form.value.userId
  const rIds = roleIds.value.join(',')
  saveLoading.value = true
  try {
    await editWorkbenchRole({
      userId: userId,
      workbenchRoleType: workbench.value,
      selectionManagement: preselection_my_1.value,
    })
    updateAuthRole({ userId: userId, roleIds: rIds, dataScope: form.value.dataScope }).then(response => {
      proxy.$modal.msgSuccess('授权成功')
      saveLoading.value = false
      close()
    })
  } catch (error) {
    console.error(error)
    saveLoading.value = false
  }
}

;(() => {
  const userId = route.params && route.params.userId
  if (userId) {
    loading.value = true
    getAuthRole(userId).then(response => {
      form.value = response.user
      roles.value = response.roles
      total.value = roles.value.length
      workbench.value = response.user.workbenchRoleType || workbench_auth_map['无']
      preselection_my_1.value = response.user.selectionManagement || 0
      nextTick(() => {
        roles.value.forEach(row => {
          if (row.flag) {
            proxy.$refs['roleRef'].toggleRowSelection(row)
          }
        })
      })
      loading.value = false
    })
  }
})()
</script>

<style lang="scss" scoped>
.label-box {
  font-weight: 700;
  color: var(--el-text-color-regular);
  font-size: 14px;
  margin-left: 11px;
}
</style>
