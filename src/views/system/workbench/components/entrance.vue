<template>
  <div class="entrance-container">
    <div class="entrance-header">
      <div class="flex-start gap-5 entrance-header-title">快捷入口</div>
    </div>

    <div class="entrance-content">
      <div
        class="flex-column entrance-item"
        v-for="item in entranceListWithPermission"
        :key="item.name"
        @click="handleClick(item)"
      >
        <img :src="getImageUrl(item.icon)" alt="" />
        <div class="entrance-item-label">{{ item.name }}</div>
      </div>
    </div>

    <el-empty
      v-if="!entranceListWithPermission.length"
      description="暂无权限，可联系管理员开通"
      :image-size="120"
    >
      <template #image>
        <img src="@/assets/icons/workbench/lock.png" alt="" />
      </template>
    </el-empty>
  </div>
</template>

<script setup>
import { checkPermi } from '@/utils/permission'
import { orderStatusMap } from '@/views/order/list/data.js'
import { invoiceStatusMap } from '@/views/finance/invoice/data.js'
import useUserStore from '@/store/modules/user'
import { workbench_auth_map } from '@/views/system/workbench/data'

const imagesMap = import.meta.glob('@/assets/icons/workbench/entrance/*.png', {
  eager: true,
  import: 'default',
})

const props = defineProps({
  auth: {
    type: Number,
    default: 0,
  },
})
const getImageUrl = name => {
  const imageEntry = Object.entries(imagesMap).find(([path]) => path.includes(`/${name}.`))
  return imageEntry ? imageEntry[1] : ''
}

const store = useUserStore()
const router = useRouter()

function routerNewWindow(path, query = {}) {
  const { href } = router.resolve({ path, query })
  window.open(href, '_blank')
}

const entranceList = ref([
  {
    name: '审核订单',
    icon: 'icon-1',
    link: `/order/list?tab=${orderStatusMap['待确认']}`,
    permission: ['order:manage:edit'],
    roleTypes: [1],
  },
  {
    name: '订单跟进',
    icon: 'icon-2',
    link: `/order/list`,
    permission: ['order:manage:list'],
    roleTypes: [1],
  },
  {
    name: '物流跟进',
    icon: 'icon-3',
    link: `/task/logistics`,
    permission: ['task:logistics:list'],
    roleTypes: [1],
  },
  {
    name: '补偿审批',
    icon: 'icon-4',
    link: '/finance/refundApproval?r_type=1',
    permission: ['finance:refundApproval:list'],
    roleTypes: [1],
  },
  {
    name: '提现通知',
    icon: 'icon-5',
    link: '/task/inform/list',
    permission: ['task:inform:list'],
    roleTypes: [1],
  },
  {
    name: '商家吐槽',
    icon: 'icon-6',
    link: '/tease',
    permission: ['system:tease:list'],
    roleTypes: [1],
  },
  {
    name: '增加钱包余额',
    icon: 'icon-7',
    link: '/merchant/balance',
    permission: ['merchant:balance:add-imprest'],
    roleTypes: [1],
  },
  {
    name: '发起提现',
    icon: 'icon-8',
    link: '/merchant/balance',
    permission: ['merchant:balance:withdraw'],
    roleTypes: [1],
  },
  {
    name: '订单池',
    icon: 'icon-9',
    link: `/task/preselectionModel/list?tab=3`,
    permission: ['task:preselection:list'],
    roleTypes: [2],
    path: '/task/preselectionModel/list',
    query: { tab: 3 },
  },
  {
    name: '反馈素材',
    icon: 'icon-10',
    link: `/order/list?tab=${orderStatusMap['待完成']}`,
    permission: ['order:manage:list'],
    roleTypes: [2],
    path: '/order/list',
    query: { tab: orderStatusMap['待完成'] },
  },
  {
    name: '提交模特',
    icon: 'icon-11',
    link: '/task/preselectionModel/list?tab=2',
    permission: ['my:preselection:waitSubmit'],
    roleTypes: [2],
    path: '/task/preselectionModel/list',
    query: { tab: 2 },
  },
  {
    name: '钱包充值审核',
    icon: 'icon-12',
    link: '/finance/receivableApprove?tab=3',
    permission: ['finance:prepay:audit'],
    roleTypes: [3],
  },
  {
    name: '审核发票',
    icon: 'icon-13',
    link: '/finance/invoice?s=5',
    permission: ['finance:invoice:audit'],
    roleTypes: [3],
  },
  {
    name: '上传发票',
    icon: 'icon-14',
    link: '/finance/invoice?s=1',
    permission: ['finance:invoice:upload'],
    roleTypes: [3],
  },
  {
    name: '确认发票',
    icon: 'icon-15',
    link: '/finance/invoice?s=2',
    permission: ['finance:invoice:confirm'],
    roleTypes: [3],
  },
  {
    name: '红冲发票',
    icon: 'icon-16',
    link: `/finance/invoice?tab=${invoiceStatusMap['待红冲']}`,
    permission: ['finance:invoice:list'],
    roleTypes: [3],
  },
  {
    name: '收支明细',
    icon: 'icon-17',
    link: `/finance/incomeInfo`,
    permission: ['finance:order:list'],
    roleTypes: [3],
  },
  {
    name: '财务对账',
    icon: 'icon-18',
    link: `/finance/verification`,
    permission: ['finance:verification:list'],
    roleTypes: [3],
  },
  {
    name: '裂变结算',
    icon: 'icon-19',
    link: `/finance/fissionChannel`,
    permission: ['finance:fission:channel'],
    roleTypes: [3],
  },
])

const entranceListWithPermission = computed(() => {
  let arr = []
  if (store.permissions.length) {
    arr = entranceList.value.filter(
      item => checkPermi(item.permission) && item.roleTypes.includes(store.workbenchRoleType)
    )
  }
  return arr
})

function handleClick(data) {
  if (props.auth == workbench_auth_map['英文部']) {
    routerNewWindow(data.path, data.query)
  } else {
    router.push(data.link)
  }
}
</script>

<style lang="scss" scoped>
:deep(.el-empty) {
  --el-empty-padding: 20px 0 30px;

  .el-empty__description {
    margin: 0;
  }
}
.entrance-container {
  width: 100%;
  margin-bottom: 16px;
  background: #fff;
  border-radius: 6px;
  box-shadow: var(--el-box-shadow-light);

  .entrance-header {
    height: 48px;
    background: #e5f2ff;
    border-radius: 6px 6px 0 0;
    padding: 12px 20px;

    &-title {
      color: #333;
      font-size: 16px;
      line-height: 24px;

      &::before {
        content: '';
        display: inline-block;
        width: 3px;
        height: 16px;
        background: #409efe;
        border-radius: 3px;
      }
    }
  }

  .entrance-content {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 20px 38px;
    padding: 20px;

    .entrance-item {
      min-width: 70px;
      gap: 3px;
      cursor: pointer;

      img {
        width: 40px;
        height: 40px;
      }

      .entrance-item-label {
        font-size: 14px;
        color: #333;
        line-height: 22px;
      }

      &:hover {
        .entrance-item-label {
          color: var(--el-color-primary);
        }
      }
    }
  }
}
</style>
