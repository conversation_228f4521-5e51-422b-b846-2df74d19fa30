<template>
  <div class="table-container">
    <div class="flex-between table-header">
      <div class="flex-start gap-5 table-header-title">{{ title }}</div>
      <div class="flex-end gap-10">
        <div class="flex-start gap-5 table-header-tip" v-if="total >= 5">
          <el-icon><WarningFilled /></el-icon>
          <span>仅展示最新5笔{{ title }}</span>
        </div>
        <el-button link type="primary" @click="handleHeadBtnAction">
          <template #icon>
            <img src="@/assets/icons/workbench/arrow-r.png" alt="" />
          </template>
          查看更多
        </el-button>
      </div>
    </div>

    <div class="table-box" v-if="!tableData?.length">
      <TableEmpty :title="title" :emptyIcon="emptyIcon" />
    </div>

    <div class="table-box" v-else>
      <ElTablePage
        v-if="columns && columns.length"
        ref="tableRef"
        :columns="columns"
        :data="tableData"
        :loading="tableLoading"
        :currentPage="1"
        :pageSize="5"
        :total="total"
        :tableAction="{
          width: '120',
          fixed: 'right',
        }"
        :tableOptions="{
          border: false,
          'header-cell-style': {
            'background-color': '#fff !important',
            color: '#00000080',
            'font-size': '13px',
          },
        }"
        :isTablePage="false"
      >
        <template #empty>
          <TableEmpty :title="title" :emptyIcon="emptyIcon" />
        </template>
        <!-- 产品图 -->
        <template #productPic="{ row }">
          <el-image
            style="width: 90px; height: 90px"
            :src="
              row.productPic
                ? $picUrl +
                  row.productPic +
                  '?imageMogr2/format/webp/interlace/1/quality/100/interlace/0/thumbnail/200x'
                : ''
            "
            fit="scale-down"
            preview-teleported
            @click="() => row.productPic && showViewer([$picUrl + row.productPic])"
          >
            <template #error>
              <img :src="$picUrl + 'static/assets/no-img.png'" alt="" style="width: 100%; height: 100%" />
            </template>
          </el-image>
        </template>
        <!-- 产品信息 -->
        <template #productInfo="{ row }">
          <div>
            <span class="gray-text">视频编码：</span>
            <span>{{ row.videoCode || '-' }}</span>
            <el-tag v-if="row.isCare" type="danger" size="small" round>照顾单</el-tag>
            <el-tag v-if="row.hasRejectedModel" type="warning" size="small" round>商家驳回</el-tag>
          </div>
          <div class="one-ell" v-ellipsis-tooltips="row.productChinese">
            <span class="gray-text">产品名称：</span>
            <span>{{ row.productChinese || '-' }}</span>
          </div>
        </template>
        <template #productInfo2="{ row }">
          <div>
            <span class="gray-text">视频编码：</span>
            <span>{{ row.videoCode || '-' }}</span>
            <el-tag v-if="row.isCare" type="danger" size="small" round>照顾单</el-tag>
          </div>
          <div class="one-ell" v-ellipsis-tooltips="row.productChinese">
            <span class="gray-text">产品名称：</span>
            <span>{{ row.productChinese || '-' }}</span>
          </div>
          <div>
            <span class="gray-text">照片数量：</span>
            <span>{{ handlePicCount(row.picCount) }}</span>
          </div>
        </template>
        <!-- 下单运营 -->
        <template #createOrderUser="{ row }">
          <div>{{ row.createOrderUserNickName || row.createOrderUserName || '-' }}</div>
        </template>
        <!-- 关联客服 -->
        <template #contact="{ row }">
          <div>{{ row.contact?.name || '-' }} / {{ row.issue?.name || '-' }}</div>
        </template>
        <!-- 链接 -->
        <template #link="{ row }">
          <div class="one-ell">
            <el-link class="link-text" type="primary" target="_blank" :href="row.link">{{ row.link }}</el-link>
          </div>
        </template>
        <!-- 意向模特 -->
        <template #intentionModel="{ row }">
          <div class="flex-start gap-5" v-if="row.intentionModel">
            <el-avatar
              class="model-avatar"
              icon="UserFilled"
              :src="$picUrl + row.intentionModel.modelPic + '!1x1compress'"
            />
            <div class="model-info">
              <div>{{ row.intentionModel.name }}</div>
              <biz-model-type :value="row.intentionModel.type" />
            </div>
          </div>
          <div v-else style="width: 50px; text-align: center">-</div>
        </template>
        <!-- 拍摄模特 -->
        <template #shootModel="{ row }">
          <div class="flex-start gap-5" v-if="row.shootModel">
            <el-avatar
              class="model-avatar"
              icon="UserFilled"
              :src="$picUrl + row.shootModel.modelPic + '!1x1compress'"
            />
            <div class="model-info">
              <div>{{ row.shootModel.name }}</div>
              <biz-model-type :value="row.shootModel.type" />
            </div>
          </div>
          <div v-else style="width: 50px; text-align: center">-</div>
        </template>
        <!-- 订单金额 -->
        <template #amount="{ row }">
          <div>
            <template v-if="row.isMergeOrder">
              <div>合计美元：${{ row.mergeOrderAmountDollar }}</div>
              <div>合计人民币：￥{{ row.mergeOrderAmount }}</div>
            </template>
            <template v-else>
              <div>美元：${{ row.orderAmountDollar }}</div>
              <div>百度汇率：{{ row.currentExchangeRate || '-' }}</div>
              <div>人民币：￥{{ row.orderAmount }}</div>
            </template>
          </div>
        </template>
        <!-- 套餐金额 -->
        <template #packageAmount="{ row }">
          <div>美元: ${{ row.packageAmount || '-' }}</div>
          <div>百度汇率: {{ row.currentExchangeRate || '-' }}</div>
          <div>人民币: ￥{{ row.orderAmount || '-' }}</div>
        </template>
        <!-- 优先级 -->
        <template #priority="{ row }">
          <el-tag :type="row.priority === 1 ? 'danger' : 'primary'" size="small">
            {{ priorityList.find(item => item.value === row.priority)?.label }}
          </el-tag>
        </template>
        <!-- 图片 -->
        <template #issuePic="{ row }">
          <el-button
            v-if="row.issuePic && row.issuePic.length"
            v-btn
            link
            type="primary"
            @click="showViewer(row.issuePic)"
          >
            查看
          </el-button>
          <span v-else>-</span>
        </template>
        <!-- 任务单问题 -->
        <template #taskProblem="{ row }">
          <div>
            <el-tag v-if="row.taskType == 2" type="warning" size="small">
              {{ workOrderTypeMap[row.workOrderType] }}
            </el-tag>
            <template v-else>
              <el-tag v-if="row.afterSaleClass == 1" type="warning" size="small">
                {{ afterSaleVideoTypeList.find(item => item.value == row.afterSaleVideoType)?.label }}
              </el-tag>
              <el-tag v-else type="warning" size="small">
                {{ afterSalePicTypeList.find(item => item.value == row.afterSalePicType)?.label }}
              </el-tag>
            </template>
          </div>
          <div class="one-ell" style="text-align: center" v-ellipsis-tooltips="row.content">
            {{ row.content }}
          </div>
        </template>

        <template #tableAction="{ row }">
          <el-button v-btn link type="primary" @click="handleBtnAction(row)">
            {{ actionButtonText }}
          </el-button>
        </template>
      </ElTablePage>
    </div>
  </div>
</template>

<script setup>
import ElTablePage from '@/components/Table/ElTablePage'
import TableEmpty from '@/views/system/workbench/components/tableEmpty.vue'
import { priorityList } from '@/views/task/data.js'
import { afterSaleVideoTypeList, afterSalePicTypeList, workOrderTypeMap } from '@/views/task/data.js'
import { picCountNumOptions } from '@/views/order/list/data.js'
import { useViewer } from '@/hooks/useViewer'

const { showViewer } = useViewer()
const props = defineProps({
  columns: {
    type: Array,
    default: () => [],
  },
  request: {
    type: Function,
  },
  title: {
    type: String,
    default: '',
  },
  emptyIcon: {
    type: String,
    default: 'order',
  },
  actionButtonText: {
    type: String,
    default: '去处理',
  },
})

const emits = defineEmits(['action', 'head-action'])

const tableData = ref([])
const tableLoading = ref(false)
const total = ref(0)

function handlePicCount(val) {
  let str = picCountNumOptions.find(item => item.value == val)
  return str ? str.label : '-'
}

function handleHeadBtnAction() {
  emits('head-action', props.title, tableData.value.length)
}
function handleBtnAction(row) {
  emits('action', row, props.title)
}

onMounted(() => {
  if (props.request) {
    tableLoading.value = true
    props
      .request()
      .then(res => {
        tableData.value = res.data.rows
        total.value = res.data.total
      })
      .finally(() => {
        tableLoading.value = false
      })
  }
})
</script>

<style lang="scss" scoped>
.table-container {
  width: 100%;
  margin-bottom: 16px;
  background: #fff;
  border-radius: 6px;
  box-shadow: var(--el-box-shadow-light);

  .table-header {
    height: 48px;
    background: #e5f2ff;
    border-radius: 6px 6px 0 0;
    padding: 12px 20px;

    &-title {
      color: #333;
      font-size: 16px;
      line-height: 24px;

      &::before {
        content: '';
        display: inline-block;
        width: 3px;
        height: 16px;
        background: #409efe;
        border-radius: 3px;
      }
    }
    &-tip {
      font-size: 14px;
      color: #777;
      line-height: 22px;
    }
  }

  .table-box {
    padding: 16px 30px 24px;

    :deep(.el-empty) {
      .el-empty__description {
        margin-top: 0;

        p {
          line-height: 1.6;
        }
      }
    }

    .gray-text {
      color: #777;
    }

    .model-info {
      text-align: left;
    }

    :deep(.el-link) {
      &.link-text {
        display: inline;

        .el-link__inner {
          display: inline;
        }
      }
    }
  }
}
</style>
