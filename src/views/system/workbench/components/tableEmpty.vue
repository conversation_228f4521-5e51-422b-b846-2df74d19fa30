<template>
  <el-empty :description="`暂无${title}`" :image-size="120">
    <template #image>
      <img v-if="emptyIcon === 'order'" src="@/assets/icons/workbench/empty-order.png" alt="" />
      <img
        v-else-if="emptyIcon === 'order-member'"
        src="@/assets/icons/workbench/empty-order-member.png"
        alt=""
      />
      <img
        v-else-if="emptyIcon === 'order-search'"
        src="@/assets/icons/workbench/empty-order-search.png"
        alt=""
      />
      <img
        v-else-if="emptyIcon === 'order-trading'"
        src="@/assets/icons/workbench/empty-order-trading.png"
        alt=""
      />
      <img
        v-else-if="emptyIcon === 'order-error'"
        src="@/assets/icons/workbench/empty-order-error.png"
        alt=""
      />
      <img v-else-if="emptyIcon === 'video'" src="@/assets/icons/workbench/empty-video.png" alt="" />
      <img
        v-else-if="emptyIcon === 'material'"
        src="@/assets/icons/workbench/empty-material.png"
        alt=""
      />
    </template>
  </el-empty>
</template>

<script setup>
defineProps({
  title: {
    type: String,
    default: '',
  },
  emptyIcon: {
    type: String,
    default: 'order',
  },
})
</script>