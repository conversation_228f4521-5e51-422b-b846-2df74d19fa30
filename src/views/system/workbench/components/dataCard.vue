<template>
  <div class="card-container">
    <div class="flex-start gap-5 card-title">数据概览</div>

    <div class="flex-between card-content" v-loading="loading">
      <div
        class="card-box"
        v-for="(item, index) in card_list"
        :key="index + '_' + item.number"
        @click="handleClick(item)"
      >
        <div class="flex-between">
          <div class="card-item">
            <div class="card-item-title">
              {{ item.title }}
            </div>
            <div class="card-item-number">
              {{ item.number }}
            </div>
          </div>
          <img :src="item.icon" alt="" />
        </div>
        <div class="flex-between card-footer">
          <template v-if="item.statistics && item.statistics.length">
            <div
              class="card-footer-item"
              v-for="(st, index) in item.statistics"
              :key="index + '_' + st.value"
              @click.stop="handleClick(st)"
            >
              {{ st.label }}：{{ st.value }}
            </div>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { workbench_auth_map, data_card_list } from '@/views/system/workbench/data'
import useUserStore from '@/store/modules/user'
import { workbenchVideoStatistics } from '@/api/system/workbench'

const userStore = useUserStore()
const router = useRouter()

const card_list = ref([])
const loading = ref(false)

const props = defineProps({
  auth: {
    type: Number,
    default: workbench_auth_map['无'],
  },
})

function routerNewWindow(path, query = {}) {
  const { href } = router.resolve({ path, query })
  window.open(href, '_blank')
}

function handleClick(item) {
  if (item.link) {
    let path = item.link
    if (props.auth == workbench_auth_map['英文部']) {
      routerNewWindow(item.path, item.query)
      return
    }
    if ((item.number || item.value) && item.query) {
      path += item.query
    }
    router.push(path)
  }
}

onMounted(() => {
  if (userStore.workbenchRoleType) {
    loading.value = true
    workbenchVideoStatistics()
      .then(res => {
        let data = data_card_list.filter(item => item.auth === userStore.workbenchRoleType)
        let list = data.length ? data[0].list : []
        if (res.data) {
          list.forEach(item => {
            if (item.field && !isNaN(res.data[item.field])) {
              item.number = res.data[item.field] || 0
            }
            if (item.statistics && item.statistics.length) {
              item.statistics.forEach(st => {
                if (st.field && !isNaN(res.data[st.field])) {
                  st.value = res.data[st.field] || 0
                }
              })
            }
          })
        }
        card_list.value = list
      })
      .finally(() => {
        loading.value = false
      })
  }
})
</script>

<style lang="scss" scoped>
.card-container {
  width: 100%;
  min-width: 1000px;
  min-height: 120px;
  margin-bottom: 16px;
  padding-bottom: 16px;
  background: #fff;
  border-radius: 6px;
  box-shadow: var(--el-box-shadow-light);

  .card-title {
    color: #333;
    font-size: 16px;
    line-height: 24px;
    padding: 12px 20px;

    &::before {
      content: '';
      display: inline-block;
      width: 3px;
      height: 16px;
      background: #409efe;
      border-radius: 3px;
    }
  }

  .card-content {
    gap: 14px;
    padding: 0 20px;

    .card-box {
      flex-grow: 1;
      height: 120px;
      box-shadow: 0px 0px 12px 0px rgba(79, 126, 247, 0.1);
      border-radius: 6px;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      cursor: pointer;

      .card-item {
        .card-item-title {
          font-size: 14px;
          color: #333;
          line-height: 22px;
        }
        .card-item-number {
          font-weight: bold;
          font-size: 26px;
          color: #333;
          line-height: 28px;
          margin-top: 12px;
        }
      }
      img {
        width: 64px;
        height: 64px;
      }
      .card-footer {
        min-height: 20px;

        .card-footer-item {
          font-size: 12px;
          color: #409efe;
          line-height: 20px;
        }
      }
    }
  }
}
</style>
