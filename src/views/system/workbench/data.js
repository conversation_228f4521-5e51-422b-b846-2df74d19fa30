import icon1 from '@/assets/icons/workbench/icon-1.png'
import icon2 from '@/assets/icons/workbench/icon-2.png'
import icon3 from '@/assets/icons/workbench/icon-3.png'
import icon4 from '@/assets/icons/workbench/icon-4.png'
import icon5 from '@/assets/icons/workbench/icon-5.png'
import icon6 from '@/assets/icons/workbench/icon-6.png'
import icon7 from '@/assets/icons/workbench/icon-7.png'
import icon8 from '@/assets/icons/workbench/icon-8.png'
import icon9 from '@/assets/icons/workbench/icon-9.png'
import icon10 from '@/assets/icons/workbench/icon-10.png'
import icon11 from '@/assets/icons/workbench/icon-11.png'
import icon12 from '@/assets/icons/workbench/icon-12.png'
import icon13 from '@/assets/icons/workbench/icon-13.png'
import icon14 from '@/assets/icons/workbench/icon-14.png'
import icon15 from '@/assets/icons/workbench/icon-15.png'
import icon16 from '@/assets/icons/workbench/icon-16.png'

import { orderStatusMap } from '@/views/order/list/data.js'
import { afterSaleStatusMap, workOrderStatusMap } from '@/views/task/data.js'
import { curTabStatusMap } from '@/views/task/clip/data.js'

const workbench_auth_list = [
  {
    label: '无',
    value: 0,
  },
  {
    label: '中文部',
    value: 1,
  },
  {
    label: '英文部',
    value: 2,
  },
  {
    label: '财务部',
    value: 3,
  },
  {
    label: '剪辑部',
    value: 4,
  },
]
const workbench_auth_map = {}
workbench_auth_list.forEach(item => {
  workbench_auth_map[item.label] = item.value
  workbench_auth_map[item.value] = item.label
})

const data_card_list = [
  {
    list: [],
    auth: workbench_auth_map['无'],
  },
  {
    auth: workbench_auth_map['中文部'],
    list: [
      {
        title: '待审核订单(笔)',
        number: 0,
        field: 'unConfirmCount',
        icon: icon1,
        link: `/order/list?tab=${orderStatusMap['待确认']}`,
      },
      {
        title: '暂停匹配订单(笔)',
        number: 0,
        field: 'pauseMatchCount',
        icon: icon2,
        link: `/order/list?tab=${orderStatusMap['待匹配']}&match_s=2`,
      },
      {
        title: '物流异常订单(笔)',
        number: 0,
        field: 'logisticAnomalyCount',
        icon: icon3,
        link: `/order/list?tab=${orderStatusMap['待完成']}&logistic_s=${btoa('1,7,9')}`,
      },
      {
        title: '工单待处理',
        number: 0,
        field: 'unHandleWorkOrderCount',
        icon: icon4,
        link: `/task/workOrder?tab=${workOrderStatusMap['待处理']}&type=2`,
      },
      {
        title: '售后待处理',
        number: 0,
        field: 'afterSaleTotalCount',
        icon: icon5,
        link: `/task/workOrder?type=1`,
        query: `&relevance=1&s=${btoa(afterSaleStatusMap['待处理'] + ',' + afterSaleStatusMap['处理中'])}`,
        statistics: [
          {
            field: 'unHandleAfterSaleCount',
            label: '待处理',
            value: 0,
            link: `/task/workOrder?tab=${afterSaleStatusMap['待处理']}&type=1`,
            query: `&relevance=1`,
          },
          {
            field: 'handlingAfterSaleCount',
            label: '处理中',
            value: 0,
            link: `/task/workOrder?tab=${afterSaleStatusMap['处理中']}&type=1`,
            query: `&relevance=1`,
          },
        ],
      },
    ],
  },
  {
    auth: workbench_auth_map['英文部'],
    list: [
      {
        title: '待沟通订单',
        number: 0,
        field: 'unContactTotalCount',
        icon: icon6,
        link: `/task/preselectionModel/list?tab=1&s=${btoa('0,1')}`,
        path: '/task/preselectionModel/list',
        query: { tab: 1, s: btoa('0,1') },
        statistics: [
          {
            field: 'unContactCount',
            label: '未沟通',
            value: 0,
            link: `/task/preselectionModel/list?tab=1&s=${btoa('0')}`,
            path: '/task/preselectionModel/list',
            query: { tab: 1, s: btoa('0') },
          },
          {
            field: 'contactingCount',
            label: '沟通中',
            value: 0,
            link: `/task/preselectionModel/list?tab=1&s=${btoa('1')}`,
            path: '/task/preselectionModel/list',
            query: { tab: 1, s: btoa('1') },
          },
        ],
      },
      {
        title: '待收货订单(笔)',
        number: 0,
        field: 'unReceivingCount',
        icon: icon7,
        link: `/order/list?tab=${orderStatusMap['待完成']}&logistic_s=${btoa('1,2,3,4,5,6,7,9')}`,
        path: '/order/list',
        query: { tab: orderStatusMap['待完成'], logistic_s: btoa('1,2,3,4,5,6,7,9') },
      },
      {
        title: '售后待处理',
        number: 0,
        field: 'afterSaleTotalCount',
        icon: icon5,
        link: `/task/workOrder?type=1`,
        query: `&relevance=1&s=${btoa(afterSaleStatusMap['待处理'] + ',' + afterSaleStatusMap['处理中'])}`,
        path: '/task/workOrder',
        query: { type: 1, tab: afterSaleStatusMap['待处理'], logistic_s: btoa('1,2,3,4,5,6,7,9') },
        statistics: [
          {
            field: 'unHandleAfterSaleCount',
            label: '待处理',
            value: 0,
            link: `/task/workOrder?tab=${afterSaleStatusMap['待处理']}&type=1`,
            query: `&relevance=1`,
            path: '/task/workOrder',
            query: {
              type: 1,
              tab: afterSaleStatusMap['待处理'],
              logistic_s: btoa('1,2,3,4,5,6,7,9'),
              relevance: 1,
            },
          },
          {
            field: 'handlingAfterSaleCount',
            label: '处理中',
            value: 0,
            link: `/task/workOrder?tab=${afterSaleStatusMap['处理中']}&type=1`,
            query: `&relevance=1`,
            path: '/task/workOrder',
            query: {
              type: 1,
              tab: afterSaleStatusMap['处理中'],
              logistic_s: btoa('1,2,3,4,5,6,7,9'),
              relevance: 1,
            },
          },
        ],
      },
      {
        title: '工单待处理',
        number: 0,
        field: 'unHandleWorkOrderCount',
        icon: icon4,
        link: `/task/workOrder?tab=${workOrderStatusMap['待处理']}&type=2`,
        query: `&relevance=1`,
        path: '/task/workOrder',
        query: { type: 2, tab: workOrderStatusMap['待处理'], relevance: 1 },
      },
    ],
  },
  {
    auth: workbench_auth_map['财务部'],
    list: [
      {
        title: '视频待审核(笔)',
        number: 0,
        field: 'videoUnAuditCount',
        icon: icon8,
        link: `/finance/receivableApprove?as=0`,
      },
      {
        title: '会员待审核(笔)',
        number: 0,
        field: 'memberUnAuditCount',
        icon: icon9,
        link: `/finance/receivableApprove?as=0&tab=2`,
      },
      {
        title: '待开票(笔)',
        number: 0,
        field: 'quantityToBeInvoiced',
        icon: icon10,
        link: `/finance/invoice`,
      },
      {
        title: '提现待审批(笔)',
        number: 0,
        field: 'payoutUnAuditCount',
        icon: icon11,
        link: `/finance/withdrawDeposit`,
      },
      {
        title: '分销待结算(笔)',
        number: 0,
        field: 'distributionUnSettleCount',
        icon: icon12,
        link: `/finance/distributionChannel?status=0`,
      },
    ],
  },
  {
    auth: workbench_auth_map['剪辑部'],
    list: [
      {
        title: '待下载(笔)',
        number: 0,
        field: 'downloadCount',
        icon: icon13,
        link: `/task/clip/list?tab=${curTabStatusMap['待下载']}`,
      },
      {
        title: '待剪辑(笔)',
        number: 0,
        field: 'toBeEditedCount',
        icon: icon14,
        link: `/task/clip/list?tab=${curTabStatusMap['待剪辑']}`,
      },
      {
        title: '待反馈(笔)',
        number: 0,
        field: 'waitForFeedbackCount',
        icon: icon15,
        link: `/task/clip/list?tab=${curTabStatusMap['待反馈']}`,
      },
      {
        title: '待上传(笔)',
        number: 0,
        field: 'unUploadLinkCount',
        icon: icon16,
        link: `/task/clip/list?tab=${curTabStatusMap['待上传']}`,
      },
      {
        title: '工单待处理',
        number: 0,
        field: 'unHandleWorkOrderCount',
        icon: icon4,
        link: `/task/workOrder?tab=${workOrderStatusMap['待处理']}&type=2`,
        query: `&assignee=1`,
      },
      {
        title: '售后待处理',
        number: 0,
        field: 'afterSaleTotalCount',
        icon: icon5,
        link: `/task/workOrder?tab=${afterSaleStatusMap['待处理']}&type=1`,
        query: `&assignee=1`,
      },
    ],
  },
]

export { workbench_auth_list, workbench_auth_map, data_card_list }
