<template>
  <div class="workbench-container">
    <h2 v-if="!userStore.workbenchRoleType || userStore.isAdmin" style="margin: 0px">欢迎使用</h2>

    <DataCard v-if="userStore.workbenchRoleType" :auth="auth" />

    <el-row :gutter="16" v-if="userStore.workbenchRoleType">
      <el-col :span="16">
        <template v-if="auth === workbench_auth_map['中文部']">
          <TableContainer
            :columns="list_columns"
            :request="workbenchUnConfirmList"
            title="待审核订单"
            empty-icon="order"
            @action="handleAction"
            @head-action="handleHeadAction"
          />
        </template>
        <template v-if="auth === workbench_auth_map['英文部']">
          <TableContainer
            :columns="list_columns"
            :request="workbenchUnMatchList"
            title="待匹配订单"
            empty-icon="order"
            @action="handleAction"
            @head-action="handleHeadOpenAction"
          />
          <TableContainer
            :columns="trading_columns"
            :request="workbenchCloseList"
            title="交易关闭订单"
            empty-icon="order-trading"
            @action="handleAction"
            @head-action="handleHeadOpenAction"
          />
        </template>
        <template v-if="auth === workbench_auth_map['财务部']">
          <TableContainer
            :columns="v_audit_columns"
            :request="workbenchFinanceVideoList"
            title="视频待审核订单"
            empty-icon="video"
            @action="handleAction"
            @head-action="handleHeadAction"
          />
          <TableContainer
            :columns="m_audit_columns"
            :request="workbenchFinanceMemberList"
            title="会员待审核订单"
            empty-icon="order-member"
            @action="handleAction"
            @head-action="handleHeadAction"
          />
        </template>
        <template v-if="auth === workbench_auth_map['剪辑部']">
          <TableContainer
            :columns="material_columns"
            :request="workbenchUnGetList"
            title="待领取素材"
            empty-icon="material"
            @action="handleAction"
            @head-action="handleHeadAction"
          />
          <TableContainer
            :columns="task_columns"
            :request="workbenchSelectUnHandleTaskList"
            title="待处理任务单"
            empty-icon="order"
            @action="handleAction"
            @head-action="handleHeadAction"
          />
        </template>
      </el-col>
      <el-col :span="8">
        <Entrance v-if="auth !== workbench_auth_map['剪辑部']" :auth="auth" />
        <template v-if="auth === workbench_auth_map['剪辑部']">
          <TableContainer
            :columns="task_reject_columns"
            :request="workbenchSelectRefuseTaskList"
            title="任务单被拒绝"
            empty-icon="order-error"
            action-button-text="查看"
            @action="handleAction"
            @head-action="handleHeadAction"
          />
        </template>
        <template v-if="auth === workbench_auth_map['英文部']">
          <TableContainer
            :columns="pause_columns"
            :request="workbenchPauseMatchList"
            title="暂停匹配订单"
            empty-icon="order-search"
            action-button-text="查看"
            @action="handleAction"
            @head-action="handleHeadOpenAction"
          />
        </template>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import DataCard from '@/views/system/workbench/components/dataCard.vue'
import Entrance from '@/views/system/workbench/components/entrance.vue'
import TableContainer from '@/views/system/workbench/components/tableContainer.vue'
import { workbench_auth_map } from '@/views/system/workbench/data'
import { orderStatusMap, picCountNumOptions } from '@/views/order/list/data.js'
import {
  afterSaleVideoTypeList,
  afterSalePicTypeList,
  workOrderTypeMap,
  workOrderStatusMap,
} from '@/views/task/data.js'
import { setMealTypeList } from '@/views/finance/data.js'
import { curTabStatusMap } from '@/views/task/clip/data.js'
import { payTypeMapNew } from '@/utils/dict'
import useUserStore from '@/store/modules/user'
import {
  workbenchUnConfirmList,
  workbenchCloseList,
  workbenchPauseMatchList,
  workbenchUnMatchList,
  workbenchFinanceVideoList,
  workbenchFinanceMemberList,
  workbenchUnGetList,
  workbenchSelectUnHandleTaskList,
  workbenchSelectRefuseTaskList,
} from '@/api/system/workbench'

const userStore = useUserStore()
const router = useRouter()

const auth = ref(workbench_auth_map['无'])

// 待审核/待匹配 列表
const list_columns = [
  { slot: 'productInfo', prop: 'productInfo', label: '产品信息', minWidth: '200', align: 'left' },
  {
    prop: 'picCount',
    label: '照片数量',
    width: '100',
    handle: data => {
      let str = picCountNumOptions.find(item => item.value == data)
      return str ? str.label : '-'
    },
  },
  { slot: 'createOrderUser', prop: 'user', label: '下单运营 ', width: '120' },
  { slot: 'intentionModel', prop: 'intentionModel', label: '意向模特 ', minWidth: '180', align: 'left' },
]
// 交易关闭列表
const trading_columns = [
  { slot: 'productInfo', prop: 'productInfo', label: '产品信息', minWidth: '200', align: 'left' },
  {
    prop: 'picCount',
    label: '照片数量',
    width: '100',
    handle: data => {
      let str = picCountNumOptions.find(item => item.value == data)
      return str ? str.label : '-'
    },
  },
  { slot: 'shootModel', prop: 'shootModel', label: '拍摄模特 ', minWidth: '180', align: 'left' },
  { prop: 'statusTime', label: '关闭时间 ', width: '180' },
]
// 暂停匹配订单
const pause_columns = [
  { prop: 'productChinese', label: '产品信息', minWidth: '120', align: 'left', ellipsis: true },
  { prop: 'videoCode', label: '视频编码 ', width: '120' },
  { prop: 'pauseReason', label: '暂停原因 ', minWidth: '120', align: 'left', ellipsis: true },
]

// 视频待审核列表
const v_audit_columns = [
  {
    prop: 'merchantInfo',
    label: '商家信息',
    minWidth: '200',
    align: 'left',
    handle: data => {
      return (data?.businessName || '') + (data?.memberCode ? '(' + data?.memberCode + ')' : '')
    },
  },
  { slot: 'amount', prop: 'amount', label: '订单金额', width: '180', align: 'left' },
  { prop: 'payType', label: '支付方式', width: '180', handle: data => payTypeMapNew[data] || '-' },
  {
    prop: 'submitCredentialTime',
    label: '提交信息',
    width: '170',
    html: (data, row) => {
      return `<div>${row.createOrderUserName}</div>
    <div>${data || ''}</div>`
    },
  },
]
// 会员待审核列表
const m_audit_columns = [
  {
    prop: 'orderNum',
    label: '订单号',
    minWidth: '200',
    align: 'left',
    handle: data => data || '-',
  },
  {
    prop: 'packageType',
    label: '会员类型',
    width: '140',
    handle: data => {
      let item = setMealTypeList.find(item => item.value == data)
      if (item) {
        return item.label
      }
      return '-'
    },
  },
  { slot: 'packageAmount', prop: 'amount', label: '套餐金额', width: '180', align: 'left' },
  { prop: 'payType', label: '支付方式', width: '180', handle: data => payTypeMapNew[data] || '-' },
  {
    prop: 'submitCredentialTime',
    label: '提交信息',
    width: '170',
    html: (data, row) => {
      return `<div>${row.createUserName}</div>
    <div>${data || ''}</div>`
    },
  },
]

// 待领取素材
const material_columns = [
  { slot: 'productPic', prop: 'productPic', label: '产品图', width: '120' },
  { slot: 'productInfo2', prop: 'productInfo2', label: '产品信息', minWidth: '200', align: 'left' },
  { slot: 'shootModel', prop: 'shootModel', label: '拍摄模特 ', minWidth: '180', align: 'left' },
  { slot: 'contact', prop: 'contact', label: '关联客服 ', width: '180' },
  { slot: 'link', prop: 'link', label: '素材链接 ', minWidth: '180' },
]

// 待处理任务单
const task_columns = [
  {
    prop: 'taskType',
    label: '任务单类型',
    width: '130',
    handle: (data, row) => {
      if (row.taskType == 1) {
        if (row.afterSaleClass == 1) {
          return afterSaleVideoTypeList.find(item => item.value == row.afterSaleVideoType)?.label || ''
        }
        return afterSalePicTypeList.find(item => item.value === row.afterSalePicType)?.label || ''
      }
      if (row.taskType == 2) {
        return workOrderTypeMap[row.workOrderType]
      }
      return '-'
    },
  },
  { prop: 'content', label: '问题描述', minWidth: '150', ellipsis: true },
  { slot: 'issuePic', prop: 'issuePic', label: '问题图片 ', width: '100' },
  { slot: 'priority', prop: 'priority', label: '优先级 ', width: '180' },
  {
    prop: 'submitBy',
    label: '提交人',
    width: '180',
    html: (data, row) => {
      return `<div>${data || ''}</div>
    <div>${row.submitTime || ''}</div>`
    },
  },
]

// 任务单被拒绝
const task_reject_columns = [
  { slot: 'taskProblem', prop: 'taskProblem', label: '任务单问题', width: '130' },
  { prop: 'refuseRemark', label: '拒绝理由', minWidth: '130', ellipsis: true },
  {
    prop: 'refuseOperateBy',
    label: '拒绝人 ',
    width: '120',
    html: (data, row) => {
      return `<div>${data || ''}</div>
    <div>${row.refuseTime || ''}</div>`
    },
  },
]

function routerNewWindow(path, query = {}) {
  const { href } = router.resolve({ path, query })
  window.open(href, '_blank')
}

function handleHeadOpenAction(title, length) {
  switch (title) {
    case '待匹配订单':
      window.open('/task/preselectionModel/list', '_blank')
      break
    case '交易关闭订单':
      if (length > 0) {
        routerNewWindow('/order/list', {
          tab: orderStatusMap['交易关闭'],
          close_os: 1,
          abm: false,
          issue: 1,
        })
      } else {
        routerNewWindow('/order/list', {
          tab: orderStatusMap['交易关闭'],
          close_os: 1,
          abm: false,
        })
      }

      break
    case '暂停匹配订单':
      routerNewWindow('/order/list', { tab: orderStatusMap['待匹配'], match_s: 2, abm: false })
      break
  }
}
// 表头查看更多
function handleHeadAction(title, length) {
  switch (title) {
    case '待审核订单':
      router.push(`/order/list?tab=${orderStatusMap['待确认']}`)
      break
    case '待匹配订单':
      router.push(`/task/preselectionModel/list`)
      break
    case '交易关闭订单':
      router.push(
        `/order/list?tabs=${orderStatusMap['交易关闭']}&close_os=1` + (length > 0 ? '&issue=1' : '')
      )
      break
    case '视频待审核订单':
      router.push(`/finance/receivableApprove?as=0`)
      break
    case '会员待审核订单':
      router.push(`/finance/receivableApprove?as=0&tab=2`)
      break
    case '待领取素材':
      router.push(`/task/clip/list?tab=${curTabStatusMap['待下载']}&s=0`)
      break
    case '待处理任务单':
      router.push(`/task/workOrder?tab=${workOrderStatusMap['待处理']}&type=1`)
      break
    case '任务单被拒绝':
      router.push(`/task/workOrder?tab=${workOrderStatusMap['已拒绝']}&type=1`)
      break
    case '暂停匹配订单':
      router.push(`/order/list?tab=${orderStatusMap['待匹配']}&match_s=2&abm=false`)
      break
    default:
      break
  }
}
// 表格操作按钮
function handleAction(row, title) {
  switch (title) {
    case '待审核订单':
      router.push(`/order/list?tab=${orderStatusMap['待确认']}&keyword=${row.videoCode || ''}`)
      break
    case '待匹配订单':
      if (auth.value === workbench_auth_map['英文部']) {
        routerNewWindow('/task/preselectionModel/list', {
          tab: 1,
          keyword: row.videoCode || '',
        })
      } else {
        router.push(`/task/preselectionModel/list?tab=1&keyword=${row.videoCode || ''}`)
      }

      break
    case '交易关闭订单':
      if (auth.value === workbench_auth_map['英文部']) {
        routerNewWindow('/order/list', {
          tab: orderStatusMap['交易关闭'],
          keyword: row.videoCode || '',
        })
      } else {
        router.push(`/order/list?tabs=${orderStatusMap['交易关闭']}&keyword=${row.videoCode || ''}`)
      }

      break
    case '视频待审核订单':
      router.push(`/finance/receivableApprove?as=0&v_search=${row.orderNum || ''}`)
      break
    case '会员待审核订单':
      router.push(`/finance/receivableApprove?as=0&tab=2&m_search=${row.orderNum || ''}`)
      break
    case '待领取素材':
      router.push(`/task/clip/list?tab=${curTabStatusMap['待下载']}&s=0&keyword=${row.videoCode || ''}`)
      break
    case '待处理任务单':
      router.push(
        `/task/workOrder?tab=${workOrderStatusMap['待处理']}&type=${row.taskType || '1'}&keyword=${
          row.taskNum || ''
        }`
      )
      break
    case '任务单被拒绝':
      router.push(
        `/task/workOrder?tab=${workOrderStatusMap['已拒绝']}&type=${row.taskType || '1'}&keyword=${
          row.taskNum || ''
        }`
      )
      break
    case '暂停匹配订单':
      if (auth.value === workbench_auth_map['英文部']) {
        routerNewWindow('/order/list', {
          tab: orderStatusMap['待匹配'],
          match_s: 2,
          abm: false,
          keyword: row.videoCode || '',
        })
      } else {
        router.push(`/order/list?tab=${orderStatusMap['待匹配']}&abm=false&keyword=${row.videoCode || ''}`)
      }
      break
    default:
      break
  }
}

onMounted(() => {
  if (userStore.workbenchRoleType) {
    auth.value = userStore.workbenchRoleType
  }
})
</script>

<style lang="scss" scoped>
.workbench-container {
  width: 100%;
}
</style>
