<template>
  <div>
    <el-dialog
      v-model="dialogVisible"
      width="750px"
      title="历史变更记录"
      align-center
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="close"
    >
      <el-table :data="changeLogList" style="width: 100%;max-height: 70vh;overflow-y: auto;">
        <el-table-column prop="createTime" label="操作时间" width="150" align="center"></el-table-column>
        <el-table-column prop="createBy" label="操作者" width="100" align="center">
          <template v-slot="{row}">
            {{ row.createBy || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="type" label="操作类型" width="150" align="center">
          <template v-slot="{row}">
            {{
              row.type == 1
                ? '新增收款主体'
                : row.type == 2
                ? '编辑主体信息'
                : row.type == 3
                ? '变更收款主体'
                : '-'
            }}
          </template>
        </el-table-column>
        <el-table-column prop="comments" label="操作详情"></el-table-column>
      </el-table>
      <template #footer>
        <div class="dialog-footer flex-center">
          <el-button v-btn @click="close" style="padding: 8px 59px">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { getChangeLogList } from '@/api/system/collection'

const dialogVisible = ref(false)
const changeLogList = ref([])
defineExpose({ open, close })

function open(type) {
  dialogVisible.value = true
  getHistoryList(type)
}

function close() {
  dialogVisible.value = false
  changeLogList.value = []
}

function getHistoryList(type) {
  getChangeLogList(type).then(res => {
    changeLogList.value = res.data || []
  })
}
</script>

<style scoped lang="scss"></style>
