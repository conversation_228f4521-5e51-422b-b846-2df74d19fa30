<template>
  <div>
    <el-dialog
      v-model="dialogVisible"
      width="800px"
      :title="dialogTitle"
      align-center
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="close"
    >
      <div class="edit-box">
        <div style="width: 48%">
          <div style="max-height: 70vh; overflow-y: auto; margin-bottom: 10px" v-loading="loading">
            <PayTypeList
              :type="dialogType"
              :isEdit="true"
              :list="payeeTypeList"
              v-model="curSelVal"
              :stopChange="stopChange"
            />
          </div>
          <el-button class="add-btn" @click="addPayee('add')">
            <el-icon size="18" color="#000"><Plus /></el-icon>
          </el-button>
        </div>
        <div class="box-right" style="width: 48%">
          <div class="link"></div>
          <div style="margin-bottom: 10px">主体信息</div>
          <el-form
            :model="formDialog"
            label-width="120px"
            ref="formDialogRef"
            :rules="dialogType == 6 ? rulesOne : rulesTwo"
          >
            <div v-if="dialogType == '6'">
              <el-form-item label="收款公司名称:" prop="accountName">
                <el-input
                  v-model="formDialog.accountName"
                  maxlength="500"
                  clearable
                  @input="handleChangeVal"
                />
              </el-form-item>
              <el-form-item label="收款银行账号:" prop="bankAccount">
                <el-input
                  v-model="formDialog.bankAccount"
                  maxlength="500"
                  clearable
                  @input="handleChangeVal"
                />
              </el-form-item>
              <el-form-item label="开户行名称:" prop="bankName">
                <el-input v-model="formDialog.bankName" maxlength="500" clearable @input="handleChangeVal" />
              </el-form-item>
            </div>
            <div v-if="dialogType == 7">
              <el-form-item label="账户名:" prop="accountName">
                <el-input
                  v-model="formDialog.accountName"
                  maxlength="500"
                  clearable
                  @input="handleChangeVal"
                />
              </el-form-item>
              <el-form-item label="收款账户类型:" prop="companyAccountType">
                <el-input
                  v-model="formDialog.companyAccountType"
                  maxlength="500"
                  clearable
                  @input="handleChangeVal"
                />
              </el-form-item>
              <el-form-item label="银行所在地:" prop="bankName">
                <el-input v-model="formDialog.bankName" maxlength="500" clearable @input="handleChangeVal" />
              </el-form-item>
              <el-form-item label="银行代码:" prop="companyBankCode">
                <el-input
                  v-model="formDialog.companyBankCode"
                  maxlength="500"
                  clearable
                  @input="handleChangeVal"
                />
              </el-form-item>
              <el-form-item label="分行代码:" prop="companyBankSubCode">
                <el-input
                  v-model="formDialog.companyBankSubCode"
                  maxlength="500"
                  clearable
                  @input="handleChangeVal"
                />
              </el-form-item>
              <el-form-item label="SWIFT代码:" prop="companyBankSwiftCode">
                <el-input
                  v-model="formDialog.companyBankSwiftCode"
                  maxlength="500"
                  clearable
                  @input="handleChangeVal"
                />
              </el-form-item>
              <el-form-item label="银行账户:" prop="bankAccount">
                <el-input
                  v-model="formDialog.bankAccount"
                  maxlength="500"
                  clearable
                  @input="handleChangeVal"
                />
              </el-form-item>
              <el-form-item label="银行名称:" prop="companyBankName">
                <el-input
                  v-model="formDialog.companyBankName"
                  maxlength="500"
                  clearable
                  @input="handleChangeVal"
                />
              </el-form-item>
              <el-form-item label="银行地址:" prop="companyBankAddress">
                <el-input
                  v-model="formDialog.companyBankAddress"
                  maxlength="500"
                  clearable
                  @input="handleChangeVal"
                />
              </el-form-item>
              <el-form-item label="收款人地址:" prop="companyBankPayeeAddress">
                <el-input
                  v-model="formDialog.companyBankPayeeAddress"
                  maxlength="500"
                  clearable
                  @input="handleChangeVal"
                />
              </el-form-item>
            </div>
          </el-form>
          <div class="dialog-footer flex-center">
            <el-button v-btn @click="close" style="padding: 8px 43px">取消</el-button>
            <el-button v-btn type="primary" :loading="loading" @click="confirmChange">
              {{ formType == 'add' ? '添加' : '保存' }}主体信息
            </el-button>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import PayTypeList from '@/views/system/collection/components/payTypeList.vue'
import { addSubjectInfo, getSubjectList, updateSubjectInfo, getPayeeInfo } from '@/api/system/collection'
import { ElMessage } from 'element-plus'

defineExpose({ open, close })
const emits = defineEmits(['success'])

const dialogVisible = ref(false)
const dialogTitle = ref('')
const dialogType = ref()
const formDialogRef = ref(null)
const formType = ref('')
const payeeTypeList = ref([])
const curSelVal = ref('')
const loading = ref(false)

const formDialog = ref({
  accountName: '',
  bankAccount: '',
  bankName: '',
  companyAccountType: '',
  companyBankCode: '',
  companyBankSubCode: '',
  companyBankSwiftCode: '',
  companyBankPayeeAddress: '',
  companyBankName: '',
  companyBankAddress: '',
})

const rulesOne = ref({
  accountName: [{ required: true, message: '请输入收款公司名称', trigger: 'blur' }],
  bankAccount: [{ required: true, message: '请输入收款银行账号', trigger: 'blur' }],
  bankName: [{ required: true, message: '请输入开户行名称', trigger: 'blur' }],
})
const rulesTwo = ref({
  accountName: [{ required: true, message: '请输入账户名', trigger: 'blur' }],
  companyAccountType: [{ required: true, message: '请输入收款账户类型', trigger: 'blur' }],
  bankName: [{ required: true, message: '请输入银行所在地', trigger: 'blur' }],
  companyBankCode: [{ required: true, message: '请输入银行代码', trigger: 'blur' }],
  companyBankSubCode: [{ required: true, message: '请输入分行代码', trigger: 'blur' }],
  companyBankSwiftCode: [{ required: true, message: '请输入SWIFT代码', trigger: 'blur' }],
  bankAccount: [{ required: true, message: '请输入银行账户', trigger: 'blur' }],
  companyBankName: [{ required: true, message: '请输入银行名称', trigger: 'blur' }],
  companyBankAddress: [{ required: true, message: '请输入银行地址', trigger: 'blur' }],
  companyBankPayeeAddress: [{ required: true, message: '请输入收款人地址', trigger: 'blur' }],
})
const curPayee = ref({})
watch(
  () => curSelVal.value,
  val => {
    if (val) {
      const index = payeeTypeList.value.findIndex(item => item.id == val)
      curPayee.value = payeeTypeList.value[index]
      formType.value = 'edit'
      stopChange.value = false
      // getNewPayee()
      getInfo()
    }
  }
)

const stopChange = ref(false)
function handleChangeVal(val) {
  stopChange.value = true
}

function getInfo() {
  getPayeeInfo(curSelVal.value).then(res => {
    formDialog.value = res.data
  })
}

function addPayee(type) {
  resetForm()
  formType.value = type
}

// function getNewPayee() {
//   getSubjectList(dialogType.value).then(res => {
//     payeeTypeList.value = res.data || []
//     formDialog.value = payeeTypeList.value[index]?.orderPayeeAccountConfigInfoDTO
//   })
// }

function getPayeeList(type) {
  getSubjectList(type)
    .then(res => {
      payeeTypeList.value = res.data || []
      if (payeeTypeList.value.length > 0) {
        if (formType.value == '') {
          const index = payeeTypeList.value.findIndex(item => item.status == 1)
          curSelVal.value = payeeTypeList.value[index].id
        } else if (formType.value == 'add') {
          curSelVal.value = payeeTypeList.value[0].id
        }
      }

      loading.value = false
    })
    .finally(() => {
      loading.value = false
    })
}

function open(data, type) {
  dialogType.value = type * 1
  if (type == '7') {
    dialogTitle.value = '全币种支付主体'
    getPayeeList(type)
  } else if (type == '6') {
    dialogTitle.value = '对公转账主体'
    getPayeeList(type)
  }
  dialogVisible.value = true
}

function close() {
  resetForm()
  dialogType.value = 0
  formType.value = ''
  dialogTitle.value = ''
  dialogVisible.value = false
}

function resetForm() {
  formDialog.value = {
    accountName: null,
    bankAccount: null,
    bankName: null,
    companyAccountType: null,
    companyBankCode: null,
    companyBankSubCode: null,
    companyBankSwiftCode: null,
    companyBankPayeeAddress: null,
    companyBankName: null,
    companyBankAddress: null,
  }
  curSelVal.value = ''
  stopChange.value = false
  formDialogRef.value.clearValidate()
}

function handleForm(data) {
  const filteredObject = Object.fromEntries(Object.entries(data).filter(([key, value]) => value != null))
  return filteredObject
}

function confirmChange() {
  formDialogRef.value.validate(valid => {
    if (valid) {
      loading.value = true
      let params = handleForm(formDialog.value)
      dialogType.value == '6' ? (params.type = 6) : (params.type = 7)
      if (formType.value == 'add') {
        addSubjectInfo(params)
          .then(res => {
            ElMessage.success('添加成功')
            stopChange.value = false
            getPayeeList(dialogType.value)
          })
          .finally(() => {
            loading.value = false
          })
      } else {
        params.id = curSelVal.value
        updateSubjectInfo(curSelVal.value, params)
          .then(res => {
            ElMessage.success('保存成功')
            stopChange.value = false
            emits('success')
            getPayeeList(dialogType.value)
          })
          .finally(() => {
            loading.value = false
          })
      }
    }
  })
}

//编辑主体相关
</script>

<style scoped lang="scss">
.edit-box {
  display: flex;
  justify-content: space-between;
  min-height: 500px;
  .add-btn {
    width: 100%;
    padding: 25px 0;
    font-size: 18px;
  }
}
.box-right {
  position: relative;
  .link::after {
    content: '';
    position: absolute;
    top: 0;
    left: -4%;
    width: 1px;
    height: 100%;
    background-color: #dcdfe6;
  }
}
</style>
