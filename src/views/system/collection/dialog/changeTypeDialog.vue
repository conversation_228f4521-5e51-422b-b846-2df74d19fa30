<template>
  <div>
    <el-dialog
      v-model="dialogVisible"
      width="450px"
      :title="dialogTitle"
      align-center
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="close"
    >
      <div style="max-height: 70vh; overflow-y: auto">
        <PayTypeList :type="dialogType" :list="payeeList" v-model="curSelVal" />
      </div>
      <template #footer>
        <div class="dialog-footer flex-center">
          <el-button v-btn @click="close" style="padding: 8px 59px">取消</el-button>
          <el-button
            v-btn
            type="primary"
            @click="confirmChange"
            v-if="payeeList.length && payeeList.length > 1"
          >
            确认更换收款主体
          </el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
      v-model="confirmDialog"
      align-center
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="450px"
      :show-close="false"
    >
      <div class="confirm-dialog">
        <div class="title">{{ curTypePay }}收款主体将要切换为</div>
        <div class="content">{{ payeeList.find(item => item.id == curSelVal)?.accountName || '' }}</div>
        <div style="margin-bottom: 15px">
          <el-button v-btn style="padding: 8px 29px; margin-right: 20px" round @click="confirmDialog = false">
            取消
          </el-button>
          <el-button v-btn type="primary" round @click="confirmChangeType">确认更换</el-button>
        </div>
        <div class="flex-center" style="color: #d9001b">
          <el-icon><WarnTriangleFilled /></el-icon>
          <span style="margin-left: 5px">请勿短时间内频繁切换，否则会影响线上商户付款</span>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import PayTypeList from '@/views/system/collection/components/payTypeList.vue'
import { getSubjectList, getActivePayee } from '@/api/system/collection'
import { ElMessage } from 'element-plus'
defineExpose({ open, close })

const emits = defineEmits(['success'])

const dialogVisible = ref(false)
const dialogTitle = ref('')
const dialogType = ref(0)
const curSelVal = ref('')
const payeeList = ref([])
const curTypePay = ref('')

function getPayTypeList() {
  getSubjectList(dialogType.value).then(res => {
    payeeList.value = res.data || []
    if (payeeList.value.length > 0) {
      const index = payeeList.value.findIndex(item => item.status == 1)
      curSelVal.value = payeeList.value[index].id
    }
  })
}

function open(data, type) {
  dialogType.value = type * 1
  if (type == '1') {
    dialogTitle.value = '微信支付主体'
    curTypePay.value = '微信'
  } else if (type == '2') {
    dialogTitle.value = '支付宝支付主体'
    curTypePay.value = '支付宝'
  } else if (type == '7') {
    dialogTitle.value = '全币种支付主体'
    curTypePay.value = '全币种'
  } else if (type == '6') {
    dialogTitle.value = '对公转账主体'
    curTypePay.value = '对公转账'
  }
  getPayTypeList()
  //
  dialogVisible.value = true
}

function close() {
  dialogType.value = 0
  dialogTitle.value = ''
  curTypePay.value = ''
  dialogVisible.value = false
}

function confirmChange() {
  confirmDialog.value = true
  // getActivePayee(curSelVal.value).then(res => {
  //   ElMessage.success('变更成功')
  //   emits('success')
  //   close()
  // })
}
function confirmChangeType() {
  getActivePayee(curSelVal.value).then(res => {
    ElMessage.success('变更成功')
    emits('success')
    confirmDialog.value = false
    close()
  })
}

const confirmDialog = ref(false)

//编辑主体相关
const formDialog = ref({})
</script>

<style scoped lang="scss">
.box-right {
  position: relative;
  .link::after {
    content: '';
    position: absolute;
    top: 0;
    left: -46px;
    width: 1px;
    height: 100%;
    background-color: #dcdfe6;
  }
}

.confirm-dialog {
  text-align: center;
  .title {
    font-size: 17px;
    color: #333;
  }
  .content {
    margin: 8px 0 20px 0;
    font-size: 21px;
    color: #409eff;
  }
}
</style>
