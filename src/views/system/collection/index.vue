<template>
  <div class="collection-page">
    <div>
      <div class="title">收款配置</div>
      <div class="title-tip">如需新增微信及支付宝收款主体，请联系技术人员</div>
    </div>
    <el-table :data="tableData" style="width: 100%">
      <template #empty>
        <el-empty description="暂无数据" :image-size="80"></el-empty>
      </template>
      <el-table-column prop="type" label="支付方式" width="200" align="center">
        <template v-slot="{ row }">
          <div>{{ payTypeList.find(item => item.value === row.type)?.label || '-' }}</div>
        </template>
      </el-table-column>
      <el-table-column
        prop="accountName"
        label="当前收款主体"
        min-width="200"
        align="center"
      ></el-table-column>
      <el-table-column prop="status" label="状态" width="200" align="center">
        <template v-slot="{ row }">
          <div>{{ row.status == 1 ? '正常' : '停用' }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="updateTime" label="最后操作时间" width="200" align="center">
        <template v-slot="{ row }">
          {{ row.updateTime || '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="updateBy" label="最后操作人" width="200" align="center">
        <template v-slot="{ row }">
          {{ row.updateBy || '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="name" label="操作" width="300" fixed="right" :align="'center'">
        <template v-slot="{ row }">
          <el-button
            v-if="row.type != 5"
            link
            type="primary"
            v-btn
            @click="changePayType(row)"
            v-hasPermi="['system:collection:change']"
          >
            变更主体
          </el-button>
          <el-button
            link
            type="primary"
            v-btn
            v-if="row.type == 6 || row.type == 7"
            @click="editPayee(row)"
            v-hasPermi="['system:collection:edit']"
          >
            编辑主体
          </el-button>
          <el-button
            link
            type="primary"
            v-btn
            @click="showHistory(row.type)"
            v-hasPermi="['system:collection:record']"
          >
            历史记录
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <ChangeTypeDialog ref="ChangeTypeDialogRef" @success="handleQuery" />
    <EditDialog ref="EditDialogRef" @success="handleQuery" />
    <HistoryDialog ref="HistoryDialogRef" />
  </div>
</template>

<script setup>
import ChangeTypeDialog from '@/views/system/collection/dialog/changeTypeDialog.vue'
import EditDialog from '@/views/system/collection/dialog/editDialog.vue'
import HistoryDialog from '@/views/system/collection/dialog/historyDialog.vue'
import { getPayeeList } from '@/api/system/collection'
import { payTypeList } from './data'
const tableData = ref([])
const ChangeTypeDialogRef = ref(null)
const EditDialogRef = ref(null)
const HistoryDialogRef = ref(null)
//变更主体
function changePayType(data) {
  ChangeTypeDialogRef.value.open(data, data.type * 1)
}
//编辑
function editPayee(data) {
  EditDialogRef.value.open(data.id, data.type)
}

function showHistory(type) {
  HistoryDialogRef.value.open(type)
}

function handleQuery() {
  getPayeeList().then(res => {
    tableData.value = res.data
  })
}

handleQuery()
</script>

<style scoped lang="scss">
.collection-page {
  padding: 20px 30px;
  .title {
    font-size: 18px;
    font-weight: 600;
  }
  .title-tip {
    margin: 10px 0;
    font-size: 14px;
    color: #aaa;
  }
}
</style>
