<template>
  <div class="pay-type-list">
    <el-radio-group :model-value="props.modelValue" size="large" @change="handleRadioChange">
      <el-radio-button v-for="item in props.list" :key="item.id" :value="item.id">
        <div class="cur-icon" v-if="item.status == 1">当前主体</div>
        <div class="info">
          <div class="info-title" style="white-space: normal">{{ item.accountName }}</div>
          <div
            class="info-content"
            style="display: flex"
            v-if="(props.type == '6' || props.type == '7') && props.isEdit"
          >
            <span>添加人: {{ item.createBy }}</span>
            <span style="margin-left: 20px">添加时间: {{ item.createTime }}</span>
          </div>
          <div v-if="!props.isEdit">
            <div class="info-content text-wrap" v-if="props.type == '1' || props.type == '2'">
              {{ item.orderPayeeAccountConfigInfoDTO?.bankAccount || '-' }}
            </div>
            <div class="info-content" v-if="props.type == '6'">
              <div class="text-wrap">{{ item.orderPayeeAccountConfigInfoDTO?.bankAccount || '-' }}</div>
              <div class="text-wrap">{{ item.orderPayeeAccountConfigInfoDTO?.bankName || '-' }}</div>
            </div>
            <div class="info-content" v-if="props.type == '7'">
              <div class="text-wrap">
                收款账户类型: {{ item.orderPayeeAccountConfigInfoDTO?.companyAccountType || '-' }}
              </div>
              <div class="text-wrap">
                银行所在地：{{ item.orderPayeeAccountConfigInfoDTO?.bankName || '-' }}
              </div>
              <div class="text-wrap">
                银行代码: {{ item.orderPayeeAccountConfigInfoDTO?.companyBankCode || '-' }}
              </div>
              <div class="text-wrap">
                分行代码: {{ item.orderPayeeAccountConfigInfoDTO?.companyBankSubCode || '-' }}
              </div>
              <div class="text-wrap">
                SWIFT代码: {{ item.orderPayeeAccountConfigInfoDTO?.companyBankSwiftCode || '-' }}
              </div>
              <div class="text-wrap">
                银行账号: {{ item.orderPayeeAccountConfigInfoDTO?.bankAccount || '-' }}
              </div>
              <div class="text-wrap">
                银行名称: {{ item.orderPayeeAccountConfigInfoDTO?.companyBankName || '-' }}
              </div>
              <div class="text-wrap">
                银行地址: {{ item.orderPayeeAccountConfigInfoDTO?.companyBankAddress || '-' }}
              </div>
              <div class="text-wrap">
                收款人地址: {{ item.orderPayeeAccountConfigInfoDTO?.companyBankPayeeAddress || '-' }}
              </div>
            </div>
          </div>
        </div>
      </el-radio-button>
    </el-radio-group>
  </div>
</template>

<script setup>
import { ElMessageBox } from 'element-plus'
const props = defineProps({
  type: {
    type: Number,
    default: 0,
  },
  list: {
    type: Array,
    default: () => [],
  },
  isEdit: {
    type: Boolean,
    default: false,
  },
  modelValue: {
    type: [Number, String],
    required: false,
  },
  stopChange: {
    type: Boolean,
    default: false,
  },
})

const emits = defineEmits(['change', 'update:modelValue'])

function handleRadioChange(val) {
  if (props.stopChange) {
    ElMessageBox.confirm('您当前编辑内容未保存，是否保存后退出该页面？', '', {
      autofocus: false,
      confirmButtonText: '去保存',
      cancelButtonText: '取消',
      showClose: false,
      closeOnPressEscape: false,
      closeOnClickModal: false,
      center: true,
    })
      .then(() => {
        return
      })
      .catch(() => {
        emits('update:modelValue', val)
      })
  } else {
    emits('update:modelValue', val)
  }
}
</script>

<style scoped lang="scss">
@import '@/assets/styles/customForm.scss';
:deep(.el-radio-group) {
  flex-direction: column;
  width: 100% !important;
  .el-radio-button {
    width: 100%;
    .cur-icon {
      position: absolute;
      right: 0;
      top: 0;
      background: #409eff;
      color: #fff;
      padding: 3px 8px;
      border-top-right-radius: 4px;
      font-size: 12px;
    }
    .el-radio-button__inner {
      padding: 20px 19px;
      text-align: left;
      width: 100%;
    }
    &.is-focus {
      --el-radio-button-radio-border-color: var(--el-border-color);

      .el-radio-button__inner {
        border-left-color: var(--el-border-color);
      }
    }
    &.is-checked {
      --el-radio-button-radio-border-color: var(--el-color-primary);
      .el-radio-button__inner {
        color: var(--el-color-primary);
        border-left-color: var(--el-color-primary);
      }
    }
    &.is-active {
      --el-radio-button-radio-border-color: var(--el-color-primary);
      .el-radio-button__inner {
        color: var(--el-color-primary);
        border-left-color: var(--el-color-primary);
        background-color: #409eff10;
      }
      .info-content {
        color: #409eff;
      }
    }
  }
}
.text-wrap {
  word-break: break-all;
  line-break: anywhere;
  white-space: normal;
}
.info {
  .info-title {
    font-weight: 600;
    word-break: break-all;
    line-break: anywhere;
  }
  .info-content {
    gap: 8px 0;
    display: grid;
    color: #aaa;
    margin-top: 10px;
    font-size: 12px;
  }
}
</style>
