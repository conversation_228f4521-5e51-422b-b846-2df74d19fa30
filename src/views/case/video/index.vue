<template>
  <div style="padding: 20px">
    <ElTablePage
      ref="tableRef"
      :columns="columns"
      :data="tableData"
      :loading="tableLoading"
      :currentPage="pageNum"
      :pageSize="pageSize"
      :total="total"
      :tableAction="{
        width: '150',
        fixed: 'right',
      }"
      :tableOptions="{
        border: false,
      }"
      @page-change="pageChange"
    >
      <template #tableHeader>
        <div>
          <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="68px" @submit.prevent>
            <el-form-item label="视频标题">
              <el-input
                v-model="queryParams.name"
                clearable
                style="width: 220px"
                placeholder="请输入视频标题"
              ></el-input>
            </el-form-item>
            <el-form-item label="平台">
              <el-select v-model="queryParams.platform" placeholder="请选择" clearable style="width: 180px">
                <el-option
                  v-for="item in biz_model_platform"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item>
              <el-button v-btn type="primary" icon="Search" native-type="submit" @click="onQuery">
                搜索
              </el-button>
              <el-button v-btn icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div style="margin-bottom: 10px">
          <el-button v-btn type="primary" @click="handleAdd" v-hasPermi="['case:video:add']">
            新增视频
          </el-button>
        </div>
      </template>
      <template #pic="{ row }">
        <el-image
          style="width: 90px; height: 90px"
          :src="row.pic"
          :preview-src-list="row.pic ? [row.pic] : []"
          fit="fill"
          preview-teleported
        >
          <template #error>
            <div class="flex-column" style="height: 100%; justify-content: center; gap: 10px">
              <el-icon :size="30" color="#ccc"><Picture /></el-icon>
            </div>
          </template>
        </el-image>
      </template>
      <template #platform="{ row }">
        <biz-model-platform :value="row.platform" size="default" :round="false" />
      </template>

      <template #tableAction="{ row }">
        <el-button v-btn link type="primary" @click="handleEdit(row)" v-hasPermi="['case:video:edit']">
          编辑
        </el-button>
        <el-button v-btn link type="primary" @click="handleDel(row)" v-hasPermi="['case:video:delete']">
          删除
        </el-button>
      </template>
    </ElTablePage>

    <el-dialog
      v-model="dialogVisible"
      :title="title"
      width="500"
      align-center
      @close="handleClose"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
    >
      <div class="flex-start form-box" v-loading="loading">
        <el-form ref="formRef" :model="form" :rules="rules" :disabled="disabled" label-width="110px">
          <el-form-item label="视频平台" prop="platform">
            <el-select v-model="form.platform" placeholder="请选择" clearable :disabled="type === 'edit'">
              <el-option
                v-for="item in biz_model_platform"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="视频名称" prop="name">
            <el-input v-model="form.name" clearable placeholder="请输入视频名称" maxlength="32"></el-input>
          </el-form-item>
          <el-form-item label="视频链接" prop="link">
            <el-input v-model="form.link" clearable placeholder="请输入视频链接"></el-input>
          </el-form-item>
          <el-form-item label="封面图" prop="pic">
            <CropperUpload
              v-model="form.pic"
              preview
              :disabled="disabled"
              :fixed-number-arr="[[2, 2]]"
              :limit="1"
            />
            <span style="color: red; font-size: 13px">*支持上传正方形比例的图片，大小不超过5M</span>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="onSubmit" :loading="disabled" :disabled="loading">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import ElTablePage from '@/components/Table/ElTablePage'
import CropperUpload from '@/components/FileUpload/cropperUpload'
import { getCaseVideoList, saveCaseVideo, updateCaseVideo, deleteCaseVideo } from '@/api/case/video'
import { beforeUpload } from '@/utils/public'
import { http_reg } from '@/utils/RegExp'

const { proxy } = getCurrentInstance()

const { biz_model_platform } = proxy.useDict('biz_model_platform')

const columns = [
  { prop: 'id', label: '视频ID', width: '160' },
  { prop: 'name', label: '视频名称', minWidth: '230' },
  { slot: 'pic', prop: 'pic', label: '视频封面图', width: '220' },
  { slot: 'platform', prop: 'platform', label: '平台', width: '160' },
  {
    prop: 'create',
    label: '创建信息',
    minWidth: '250',
    handle: (data, row) => {
      let str = ''
      if (row.createBy) {
        str += `创建人：${row.createBy}\n`
      }
      if (row.createTime) {
        str += `创建时间：${row.createTime}`
      }
      return str
    },
  },
]

const tableData = ref([])
const pageNum = ref(1)
const pageSize = ref(20)
const total = ref(0)
const tableLoading = ref(false)
const queryParams = ref({
  name: '',
  platform: '',
})

const dialogVisible = ref(false)
const title = ref('新增视频')
const type = ref('add')
const loading = ref(false)
const disabled = ref(false)
const form = ref({
  id: undefined,
  name: '',
  link: '',
  platform: '',
  pic: [],
})
const formRef = ref()

const rules = {
  platform: [{ required: true, message: '请选择视频平台', trigger: 'change' }],
  name: [{ required: true, message: '请输入视频名称', trigger: 'blur' }],
  link: [{ required: true, validator: checkLink, trigger: 'blur' }],
  pic: [{ required: true, message: '请上传视频封面图', trigger: 'change' }],
}
function checkLink(rule, value, callback) {
  if (!value) {
    return callback(new Error('请输入视频链接'))
  }
  if (!http_reg.test(value)) {
    return callback(new Error('请输入正确的视频链接'))
  }
  return callback()
}

function onQuery() {
  pageNum.value = 1
  getList()
}
function resetQuery() {
  queryParams.value = {
    name: '',
    platform: '',
  }
  pageNum.value = 1
  getList()
}
// 分页跳转
function pageChange(page) {
  pageNum.value = page.currentPage
  pageSize.value = page.pageSize
  getList()
}

function getList() {
  let params = {
    name: queryParams.value.name,
    platform: queryParams.value.platform,
    pageNum: pageNum.value,
    pageSize: pageSize.value,
  }
  tableLoading.value = true
  getCaseVideoList(params)
    .then(res => {
      if (res.data) {
        tableData.value = res.data.rows
        total.value = res.data.total
      }
    })
    .finally(() => (tableLoading.value = false))
}

function handleAdd() {
  title.value = '新增视频'
  type.value = 'add'
  dialogVisible.value = true
}
function handleEdit(row) {
  title.value = '修改视频'
  type.value = 'edit'
  form.value = {
    id: row.id,
    name: row.name,
    link: row.link,
    platform: row.platform + '',
    pic: [
      {
        url: row.pic,
      },
    ],
  }
  dialogVisible.value = true
}
function handleDel(row) {
  proxy.$modal.confirm('确认删除视频吗？', '提示', {}).then(() => {
    proxy.$modal.loading('正在删除中')
    deleteCaseVideo(row.id)
      .then(res => {
        proxy.$modal.msgSuccess('删除成功')
        pageNum.value = 1
        getList()
      })
      .finally(() => proxy.$modal.closeLoading())
  })
}

function handleClose() {
  form.value = {
    id: undefined,
    name: '',
    link: '',
    platform: '',
    pic: [],
  }
  formRef.value.clearValidate()
}

function onSubmit() {
  // console.log(form.value)
  formRef.value.validate(valid => {
    if (valid) {
      let { pic, ...params } = form.value
      if (type.value === 'edit') {
        proxy.$modal.loading('保存中...')
        updateCaseVideo({
          ...params,
          pic: pic[0].url,
        })
          .then(res => {
            proxy.$modal.msgSuccess('修改成功')
            dialogVisible.value = false
            getList()
          })
          .finally(() => proxy.$modal.closeLoading())
      } else {
        proxy.$modal.loading('保存中...')
        saveCaseVideo({
          ...params,
          pic: pic[0].url,
        })
          .then(res => {
            proxy.$modal.msgSuccess('新增成功')
            dialogVisible.value = false
            pageNum.value = 1
            getList()
          })
          .finally(() => proxy.$modal.closeLoading())
      }
    }
  })
}

getList()
</script>

<style scoped lang="scss">
.form-box {
  :deep(.el-upload-list__item) {
    width: 80px;
    height: 80px;

    img {
      object-fit: fill;
    }
  }
  :deep(.disabled) {
    .el-upload--picture-card {
      display: none;
    }
  }
  :deep(.hidden) {
    .el-upload--picture-card {
      display: none;
    }
  }
  :deep(.el-upload--picture-card) {
    width: 80px;
    height: 80px;
  }
}
</style>
