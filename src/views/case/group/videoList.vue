<template>
  <div>
    <ElTablePage
      style="padding: 20px"
      ref="tableRef"
      :columns="columns"
      :data="tableData"
      :loading="tableLoading"
      :currentPage="pageNum"
      :pageSize="pageSize"
      :total="total"
      :tableAction="{
        width: '150',
        fixed: 'right',
      }"
      :tableOptions="{
        border: false,
      }"
      @page-change="pageChange"
    >
      <template #tableHeader>
        <div>
          <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="68px" @submit.prevent>
            <el-form-item label="视频标题">
              <el-input
                v-model="queryParams.name"
                clearable
                style="width: 220px"
                placeholder="请输入视频标题"
              ></el-input>
            </el-form-item>
            <el-form-item label="视频ID">
              <el-input
                v-model="queryParams.id"
                clearable
                style="width: 220px"
                placeholder="请输入视频ID"
              ></el-input>
            </el-form-item>

            <el-form-item>
              <el-button v-btn type="primary" icon="Search" native-type="submit" @click="onQuery(0)">
                搜索
              </el-button>
              <el-button v-btn icon="Refresh" @click="resetQuery(0)">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div style="margin-bottom: 10px">
          <el-button v-btn type="primary" @click="handleAdd" v-hasPermi="['case:group:manage']">
            添加视频
          </el-button>
        </div>
      </template>
      <template #pic="{ row }">
        <div class="flex-start gap-10">
          <el-image
            style="width: 90px; height: 90px"
            :src="row.videoPic"
            :preview-src-list="row.videoPic ? [row.videoPic] : []"
            fit="fill"
            preview-teleported
          >
            <template #error>
              <div class="flex-column" style="height: 100%; justify-content: center; gap: 10px">
                <el-icon :size="30" color="#ccc"><Picture /></el-icon>
              </div>
            </template>
          </el-image>
          <div class="flex-column info-box">
            <div>{{ row.videoName }}</div>
            <div>{{ row.videoId }}</div>
          </div>
        </div>
      </template>
      <template #sort="{ row }">
        <div class="flex-center sort-box">
          {{ row.sort }}
          <el-icon v-hasPermi="['case:group:manage']" @click="changeSort(row)"><Edit /></el-icon>
        </div>
      </template>

      <template #tableAction="{ row }">
        <el-button v-btn link type="primary" v-hasPermi="['case:group:manage']" @click="handleDel(row)">
          移除分组
        </el-button>
      </template>
    </ElTablePage>

    <el-dialog v-model="dialogVisible" title="添加视频" width="650" align-center @close="handleClose">
      <div class="list-box">
        <el-form :model="dialogParams" ref="queryRef" :inline="true" label-width="68px" @submit.prevent>
          <el-form-item label="">
            <el-input
              v-model="dialogParams.val"
              clearable
              style="max-width: 300px"
              placeholder="请输入对应的值"
            >
              <template #prepend>
                <el-select v-model="dialogParams.select" placeholder="请选择" clearable style="width: 100px">
                  <el-option label="视频标题" value="name" />
                  <el-option label="视频ID" value="id" />
                </el-select>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item>
            <el-button v-btn type="primary" icon="Search" native-type="submit" @click="onQuery(1)">
              搜索
            </el-button>
            <el-button v-btn icon="Refresh" @click="resetQuery(1)">重置</el-button>
          </el-form-item>
        </el-form>

        <div class="flex-between" style="margin-bottom: -24px">
          <h3>视频列表</h3>
          <el-button
            v-btn
            round
            type="primary"
            :disabled="!checkList.length"
            v-hasPermi="['case:group:manage']"
            @click="onConfirm"
          >
            确认添加
          </el-button>
        </div>
        <el-divider />

        <el-empty v-if="!videoList.length" description="暂无数据" :image-size="80"></el-empty>

        <el-checkbox-group v-model="checkList">
          <template v-for="row in videoList" :key="row.id">
            <div class="flex-between" style="width: 100%">
              <div class="flex-start gap-10" style="width: 90%">
                <el-image
                  style="width: 60px; height: 60px"
                  :src="row.pic"
                  :preview-src-list="row.pic ? [row.pic] : []"
                  fit="fill"
                  preview-teleported
                >
                  <template #error>
                    <div class="flex-column" style="height: 100%; justify-content: center; gap: 10px">
                      <el-icon :size="30" color="#ccc"><Picture /></el-icon>
                    </div>
                  </template>
                </el-image>
                <div class="flex-column info-box">
                  <div>{{ row.name }}</div>
                  <div>{{ row.id }}</div>
                </div>
              </div>
              <div>
                <el-checkbox :value="row.id">{{ '' }}</el-checkbox>
              </div>
            </div>

            <el-divider />
          </template>
        </el-checkbox-group>
      </div>
      <div class="flex-end" style="margin: 10px 0 -10px">
        <el-pagination
          background
          @size-change="pageChange2({ pageNum: 1, pageSize: $event })"
          @current-change="pageChange2({ pageNum: $event, pageSize })"
          :current-page="dialogPageNum"
          :page-size="dialogPageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="dialogTotal"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import ElTablePage from '@/components/Table/ElTablePage'
import {
  groupsVideoList,
  removeGroupVideo,
  addGroupsVideoList,
  addGroupsVideo,
  updateGroupSort,
} from '@/api/case/group'

const { proxy } = getCurrentInstance()

const route = useRoute()

const columns = [
  { slot: 'pic', prop: 'pic', label: '视频信息', minWidth: '260' },
  { prop: 'joinTime', label: '加入时间', minWidth: '200' },
  { slot: 'sort', prop: 'sort', label: '排序', minWidth: '160' },
]

const tableData = ref([])
const pageNum = ref(1)
const pageSize = ref(20)
const total = ref(0)
const tableLoading = ref(false)
const queryParams = ref({
  name: '',
  id: '',
})
const groupId = ref('')

const videoList = ref([])
const checkList = ref([])
const dialogParams = ref({
  val: '',
  select: 'name',
})
const dialogPageNum = ref(1)
const dialogPageSize = ref(10)
const dialogTotal = ref(0)
const dialogVisible = ref(false)
const loading = ref(false)

function onQuery(n) {
  if (n) {
    dialogPageNum.value = 1
    getVideoList()
    return
  }
  pageNum.value = 1
  getList()
}

function resetQuery(n) {
  if (n) {
    dialogParams.value = {
      val: '',
      select: 'name',
    }
    dialogPageNum.value = 1
    getVideoList()
    return
  }
  queryParams.value = {
    name: '',
    id: '',
  }
  pageNum.value = 1
  getList()
}
// 分页跳转
function pageChange(page) {
  pageNum.value = page.currentPage
  pageSize.value = page.pageSize
  getList()
}

function getList() {
  let params = {
    groupId: groupId.value,
    videoName: queryParams.value.name,
    videoId: queryParams.value.id,
    pageNum: pageNum.value,
    pageSize: pageSize.value,
  }
  tableLoading.value = true
  groupsVideoList(params)
    .then(res => {
      if (res.data) {
        tableData.value = res.data.rows
        total.value = res.data.total
      }
    })
    .finally(() => (tableLoading.value = false))
}

function getVideoList() {
  let params = {
    groupId: groupId.value,
    pageNum: dialogPageNum.value,
    pageSize: dialogPageSize.value,
  }
  if (dialogParams.value.select) {
    params[dialogParams.value.select] = dialogParams.value.val
  }
  loading.value = true
  addGroupsVideoList(params)
    .then(res => {
      if (res.data) {
        videoList.value = res.data.rows
        dialogTotal.value = res.data.total
      }
    })
    .finally(() => (loading.value = false))
}
// 分页跳转
function pageChange2(page) {
  dialogPageNum.value = page.currentPage
  dialogPageSize.value = page.pageSize
  getVideoList()
}

function handleAdd() {
  dialogPageNum.value = 1
  dialogVisible.value = true
  getVideoList()
}
function handleClose() {
  queryParams.value = {
    name: '',
    id: '',
  }
  pageNum.value = 1
  dialogParams.value = {
    val: '',
    select: 'name',
  }
  dialogPageNum.value = 1
  checkList.value = []
}

function handleDel(row) {
  proxy.$modal.confirm('确认移除分组吗？', '提示', {}).then(() => {
    proxy.$modal.loading('正在移除中')
    removeGroupVideo({
      groupId: groupId.value,
      videoIds: [row.videoId],
    })
      .then(res => {
        proxy.$modal.msgSuccess('移除成功')
        pageNum.value = 1
        getList()
        // proxy.$modal.alert('该分组已关联配置页面中，请先解除关联后再删除', '无法删除', {
        //   confirmButtonText: '好的~',
        // })
      })
      .finally(() => proxy.$modal.closeLoading())
  })
}
function changeSort(row) {
  proxy.$modal
    .prompt('请输入序号', '排序', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputValue: row.sort + '',
      inputPattern: /^([1-9]\d{0,4}|0|[1-9]|1\d{0,2})$/,
      inputErrorMessage: '请输入0到99999的整数序号',
    })
    .then(({ value }) => {
      proxy.$modal.loading('正在更新中')
      updateGroupSort({
        groupId: groupId.value,
        sort: value,
        videoId: row.videoId,
      })
        .then(() => {
          proxy.$modal.msgSuccess('修改成功')
          getList()
        })
        .finally(() => proxy.$modal.closeLoading())
    })
    .catch(() => {})
}

function onConfirm() {
  proxy.$modal.loading('正在添加中')
  addGroupsVideo({
    groupId: groupId.value,
    videoIds: checkList.value,
  })
    .then(res => {
      proxy.$modal.msgSuccess('添加成功')
      dialogVisible.value = false
      getList()
    })
    .finally(() => proxy.$modal.closeLoading())
}

function init() {
  if (route.params.id) {
    groupId.value = route.params.id
  }
  getList()
}

init()
</script>

<style scoped lang="scss">
.info-box {
  align-items: flex-start;
  margin-top: -10px;
}
.sort-box {
  cursor: pointer;
}
.list-box {
  min-height: 330px;
  max-height: 500px;
  overflow-y: auto;

  .info-box {
    font-size: 13px;
    line-height: 1.6;
  }
}
</style>
