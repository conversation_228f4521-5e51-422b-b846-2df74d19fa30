<template>
  <div style="padding: 20px">
    <ElTablePage
      ref="tableRef"
      :columns="columns"
      :data="tableData"
      :loading="tableLoading"
      :currentPage="pageNum"
      :pageSize="pageSize"
      :total="total"
      :tableAction="{
        width: '200',
        fixed: 'right',
      }"
      :tableOptions="{
        border: false,
      }"
      @page-change="pageChange"
    >
      <template #tableHeader>
        <div>
          <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="68px" @submit.prevent>
            <el-form-item label="分组名称">
              <el-input
                v-model="queryParams.name"
                clearable
                style="width: 220px"
                placeholder="请输入分组名称"
              ></el-input>
            </el-form-item>
            <el-form-item label="分组ID">
              <el-input
                v-model="queryParams.id"
                clearable
                style="width: 220px"
                placeholder="请输入分组ID"
              ></el-input>
            </el-form-item>
            <el-form-item label="所属平台">
              <el-select v-model="queryParams.platform" placeholder="请选择" clearable style="width: 180px">
                <el-option
                  v-for="item in biz_model_platform"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item>
              <el-button v-btn type="primary" icon="Search" native-type="submit" @click="onQuery">
                搜索
              </el-button>
              <el-button v-btn icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div style="margin-bottom: 10px">
          <el-button v-btn type="primary" v-hasPermi="['case:group:add']" @click="handleAdd">
            新增分组
          </el-button>
        </div>
      </template>

      <template #platform="{ row }">
        <biz-model-platform :value="row.platform" size="default" :round="false" />
      </template>

      <template #tableAction="{ row }">
        <el-button v-btn link type="primary" v-hasPermi="['case:group:edit']" @click="handleEdit(row)">
          编辑
        </el-button>
        <el-button v-btn link type="primary" v-hasPermi="['case:group:delete']" @click="handleDel(row)">
          删除
        </el-button>
        <el-button v-btn link type="primary" @click="handleVideo(row)" v-hasPermi="['case:group:manage']">
          视频管理
        </el-button>
      </template>
    </ElTablePage>

    <el-dialog v-model="dialogVisible" :title="title" width="500" align-center @close="handleClose">
      <div class="flex-start form-box" v-loading="loading">
        <el-form ref="formRef" :model="form" :rules="rules" :disabled="disabled" label-width="110px">
          <el-form-item label="所属平台" prop="platform">
            <el-select v-model="form.platform" placeholder="请选择" clearable :disabled="type === 'edit'">
              <el-option
                v-for="item in biz_model_platform"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="分组名称" prop="name">
            <el-input v-model="form.name" clearable placeholder="请输入分组名称"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="onSubmit" :loading="disabled" :disabled="loading">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import ElTablePage from '@/components/Table/ElTablePage'
import { getCaseGroupList, saveCaseGroup, updateCaseGroup, deleteCaseGroup } from '@/api/case/group'

const { proxy } = getCurrentInstance()

const { biz_model_platform } = proxy.useDict('biz_model_platform')

const router = useRouter()

const columns = [
  { prop: 'id', label: '分组ID' },
  { prop: 'name', label: '分组名称' },
  { slot: 'platform', prop: 'platform', label: '所属平台' },
  { prop: 'videoCount', label: '视频数量' },
]

const tableData = ref([])
const pageNum = ref(1)
const pageSize = ref(20)
const total = ref(0)
const tableLoading = ref(false)
const queryParams = ref({
  id: '',
  name: '',
  platform: '',
})

const dialogVisible = ref(false)
const title = ref('新增视频')
const type = ref('add')
const loading = ref(false)
const disabled = ref(false)
const form = ref({
  id: undefined,
  name: '',
  platform: '',
})
const formRef = ref()

const rules = {
  platform: [{ required: true, message: '请选择所属平台', trigger: 'change' }],
  name: [{ required: true, validator: checkName, trigger: 'blur' }],
}
function checkName(rule, value, callback) {
  if (!value) {
    return callback(new Error('请输入分组名称'))
  }
  if (value.length < 2 || value.length > 30) {
    return callback(new Error('请输入2-32字符的名称'))
  }
  return callback()
}

function onQuery() {
  pageNum.value = 1
  getList()
}

function resetQuery() {
  queryParams.value = {
    id: '',
    name: '',
    platform: '',
  }
  pageNum.value = 1
  getList()
}
// 分页跳转
function pageChange(page) {
  pageNum.value = page.currentPage
  pageSize.value = page.pageSize
  getList()
}

function getList() {
  let params = {
    id: queryParams.value.id,
    name: queryParams.value.name,
    platform: queryParams.value.platform,
    pageNum: pageNum.value,
    pageSize: pageSize.value,
  }
  tableLoading.value = true
  getCaseGroupList(params)
    .then(res => {
      if (res.data) {
        tableData.value = res.data.rows
        total.value = res.data.total
      }
    })
    .finally(() => (tableLoading.value = false))
}

function handleAdd() {
  title.value = '新增分组'
  type.value = 'add'
  dialogVisible.value = true
}
function handleEdit(row) {
  title.value = '编辑分组'
  type.value = 'edit'
  form.value = {
    id: row.id,
    name: row.name,
    platform: row.platform + '',
  }
  dialogVisible.value = true
}
function handleDel(row) {
  proxy.$modal.confirm('确认删除分组吗？', '提示', {}).then(() => {
    proxy.$modal.loading('正在删除中')
    deleteCaseGroup(row.id)
      .then(res => {
        proxy.$modal.msgSuccess('删除成功')
        pageNum.value = 1
        getList()
      })
      .finally(() => proxy.$modal.closeLoading())
  })
}
function handleVideo(row) {
  // router.push(`/case/group-video/${row.id}`)
  const path = '/case/group-video/' + row.id
  const { href } = router.resolve({ path })
  window.open(href, '_blank')
}

function handleClose() {
  form.value = {
    id: undefined,
    name: '',
    platform: '',
  }
  formRef.value.clearValidate()
}

function onSubmit() {
  // console.log(form.value)
  formRef.value.validate(valid => {
    if (valid) {
      if (type.value === 'edit') {
        proxy.$modal.loading('保存中...')
        updateCaseGroup(form.value)
          .then(res => {
            proxy.$modal.msgSuccess('修改成功')
            dialogVisible.value = false
            getList()
          })
          .finally(() => proxy.$modal.closeLoading())
      } else {
        proxy.$modal.loading('保存中...')
        saveCaseGroup(form.value)
          .then(res => {
            proxy.$modal.msgSuccess('新增成功')
            dialogVisible.value = false
            pageNum.value = 1
            getList()
          })
          .finally(() => proxy.$modal.closeLoading())
      }
    }
  })
}

getList()
</script>

<style scoped lang="scss"></style>
