<template>
  <div class="sale-after-detail">
    <Title style="margin: 12px 0">工单信息</Title>

    <div class="detail-box">
      <el-row>
        <el-col :span="8">
          <div class="label">工单编号：</div>
          <div class="content">{{ workOrderData.taskNum }}</div>
        </el-col>
        <el-col :span="8">
          <div class="label">优先级：</div>
          <div class="content">
            {{
              workOrderData.priority
                ? priorityList.find(item => item.value === workOrderData.priority)?.label || ''
                : ''
            }}
          </div>
        </el-col>
        <el-col :span="8">
          <div class="label">状态：</div>
          <div class="content">
            <span
              v-if="
                workOrderData.records?.length &&
                (workOrderData.records[workOrderData.records.length - 1].completionMode ||
                  workOrderData.records[workOrderData.records.length - 1].remark)
              "
            >
              <span>{{ workOrderStatusMap[workOrderData.status] }}</span>
              <template v-if="handleTaskOperatType(
                  workOrderData.records[workOrderData.records.length - 1].operateType,
                  workOrderData.records[workOrderData.records.length - 1].completionMode ||
                    workOrderData.records[workOrderData.records.length - 1].remark,
                  workOrderData.records[workOrderData.records.length - 1].appointee?.name,
                  false
                )">
                ({{
                  handleTaskOperatType(
                    workOrderData.records[workOrderData.records.length - 1].operateType,
                    workOrderData.records[workOrderData.records.length - 1].completionMode ||
                    workOrderData.records[workOrderData.records.length - 1].remark,
                    workOrderData.records[workOrderData.records.length - 1].appointee?.name,
                    false
                  )
                }})
              </template>
            </span>
            <span v-else>{{ workOrderStatusMap[workOrderData.status] }}</span>
          </div>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <div class="label">工单类型：</div>
          <div class="content">{{ workOrderTypeMap[workOrderData.workOrderType] }}</div>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <div class="label">工单内容：</div>
          <div class="content">
            {{ workOrderData.content }}
            <div style="margin-top: 10px" v-if="workOrderData.issuePic?.length">
              <ViewerImageList :data="workOrderData.issuePic" is-preview-all :show-delete-btn="false" />
            </div>
          </div>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <div class="label">提交人：</div>
          <div class="content">{{ workOrderData.submit?.name || '' }}</div>
        </el-col>
        <el-col :span="12">
          <div class="label">处理人：</div>
          <div class="content">{{ workOrderData.assignee?.name || '' }}</div>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <div class="label">提交时间：</div>
          <div class="content">{{ workOrderData.submitTime || '' }}</div>
        </el-col>
        <el-col
          :span="12"
          v-if="
            workOrderData.status === workOrderStatusMap['已关闭'] ||
            workOrderData.status === workOrderStatusMap['已完结']
          "
        >
          <div class="label">关闭/完结时间：</div>
          <div class="content">{{ workOrderData.endTime }}</div>
        </el-col>
      </el-row>
    </div>

    <Title style="margin: 12px 0">状态流转记录</Title>
    <div class="detail-box">
      <div v-for="(item, index) in workOrderData.records" :key="item.id" class="record-item">
        <span>
          {{
            `${index + 1}. ${item.time}，由 ${
              item.operateByType == 1 ? '处理人' : item.operateByType == 2 ? '剪辑人' : '系统'
            }${item.operate?.name || ''} ` +
            handleTaskOperatType(item.operateType, item.completionMode || item.remark, item.appointee?.name)
          }}
        </span>
        <el-button v-if="item.issuePic?.length" type="primary" link @click="showViewer(item.issuePic)">
          查看图片
        </el-button>
      </div>
    </div>
    <div class="detail-btn">
      <el-button plain v-btn @click="handleBack">返回</el-button>
    </div>
  </div>
</template>

<script setup>
import ViewerImageList from '@/components/ImagePreview/viewerImageList.vue'
import Title from '@/components/Public/title.vue'
import { workOrderDetail } from '@/api/task/workOrder'
import {
  priorityList,
  workOrderStatusMap,
  workOrderTypeMap,
  handleTaskOperatType,
} from '@/views/task/data.js'
import { useViewer } from '@/hooks/useViewer'

const { showViewer } = useViewer()

const router = useRouter()
const route = useRoute()

const taskNum = ref('')
const workOrderData = ref({})
const loading = ref(false)

function handleBack() {
  router.replace('/task/workOrder?type=2')
}

function getDetail() {
  loading.value = true
  workOrderDetail(taskNum.value)
    .then(res => {
      workOrderData.value = res.data
    })
    .finally(() => {
      loading.value = false
    })
}

function init() {
  if (!route.params.id) {
    handleBack()
    return
  }
  taskNum.value = route.params.id
  getDetail()
}

init()
</script>

<style lang="scss" scoped>
.sale-after-detail {
  padding: 20px;
  min-height: calc(100vh - 84px);
  overflow-y: auto;
  position: relative;

  .detail-box {
    background-color: #fff;
    padding: 13px;
    border-radius: 4px;
    gap: 10px;
    position: relative;
    box-shadow: var(--el-box-shadow-light);

    .record-item {
      line-height: 30px;
      font-size: 15px;

      .el-button {
        margin-left: 8px;
        font-size: 15px;
        vertical-align: baseline;
      }
    }
  }

  .el-col-8,
  .el-col-12,
  .el-col-24 {
    display: flex;
    align-items: baseline;
    font-size: 15px;
    margin-bottom: 15px;
    // align-items: center;

    .label {
      color: #7f7f7f;
      flex-shrink: 0;
      width: 130px;
      text-align: right;
    }
    .content {
      flex-shrink: 0;
      width: calc(100% - 130px);
      word-break: break-all;
    }
  }
  .detail-btn {
    position: absolute;
    bottom: 0;
    right: 60px;
  }
}
</style>
