<template>
  <div class="after-sale-page">
    <el-tabs v-model="curTab" class="tabs" @tab-change="handleTabChange">
      <el-tab-pane v-for="(tab, i) in tabList" :key="tab.value" :name="tab.value">
        <template #label>
          <!-- v-if="i" -->
          <div>
            {{ tab.label }}
            {{ '(' }}
            <span style="color: red">{{ tab.number }}</span>
            {{ ')' }}
          </div>
          <!-- <div v-else>{{ tab.label }}</div> -->
        </template>
      </el-tab-pane>
    </el-tabs>
    <div class="search-box">
      <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="80px" @submit.prevent>
        <el-form-item label="搜索">
          <el-input
            placeholder="支持视频编码、产品信息进行搜索"
            v-model="queryParams.keyword"
            clearable
            style="width: 300px"
          />
        </el-form-item>
        <el-form-item label="优先级">
          <el-select v-model="queryParams.priority" placeholder="请选择" clearable style="width: 180px">
            <el-option
              v-for="item in priorityList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="拍摄模特">
          <el-select
            placeholder="请选择"
            v-model="queryParams.shootModelId"
            multiple
            filterable
            collapse-tags
            collapse-tags-tooltip
            clearable
            style="width: 180px"
          >
            <el-option v-for="item in shootModelList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="工单类型">
          <el-select
            v-model="queryParams.workOrderType"
            placeholder="请选择"
            multiple
            filterable
            :reserve-keyword="false"
            collapse-tags
            collapse-tags-tooltip
            clearable
            style="width: 180px"
          >
            <el-option
              v-for="item in workOrderTypeList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="工单状态" v-if="curTab == ''">
          <el-select
            v-model="queryParams.status"
            placeholder="请选择"
            multiple
            collapse-tags
            collapse-tags-tooltip
            clearable
            style="width: 180px"
          >
            <el-option
              v-for="item in workOrderStatusList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="当前处理人" label-width="100px">
          <el-select
            v-model="queryParams.assigneeId"
            placeholder="请选择"
            multiple
            filterable
            collapse-tags
            collapse-tags-tooltip
            clearable
            style="width: 180px"
          >
            <el-option
              v-for="item in assigneeList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="最新回复时间" label-width="110px">
          <el-checkbox-group
            v-model="queryParams.statusDays"
            @change="changeStatusDays"
            style="--el-checkbox-font-weight: 500"
          >
            <el-checkbox-button value="0">今天</el-checkbox-button>
            <el-checkbox-button value="1">昨天</el-checkbox-button>
            <el-checkbox-button value="2">前天</el-checkbox-button>
            <el-checkbox-button value="3">更早之前</el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="历史处理人" label-width="100px">
          <el-select
            v-model="queryParams.historyAssigneeId"
            placeholder="请选择"
            filterable
            clearable
            @change="changeHistoryAssigneeId"
            style="width: 180px"
          >
            <el-option
              v-for="item in historyAssigneeList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="提交人">
          <el-select
            v-model="queryParams.submitById"
            placeholder="请选择"
            multiple
            filterable
            collapse-tags
            collapse-tags-tooltip
            clearable
            style="width: 180px"
          >
            <el-option
              v-for="item in submitByIdList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="提交时间" prop="submitTime" style="width: 450px">
          <el-date-picker
            style="width: 350px"
            v-model="queryParams.submitTime"
            format="YYYY/M/D HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="datetimerange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
            :shortcuts="shortcuts"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="任务关联人" label-width="90px">
          <el-select
            v-model="queryParams.relevanceUserId"
            placeholder="请选择"
            multiple
            filterable
            collapse-tags
            collapse-tags-tooltip
            clearable
            style="width: 180px"
          >
            <el-option v-for="item in relevanceUserList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button v-btn type="primary" icon="Search" native-type="submit" @click="handleQuery">
            搜索
          </el-button>
          <el-button v-btn icon="Refresh" plain @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="table-box" v-loading="loading">
      <template v-if="!tableData?.length">
        <el-empty description="暂无数据" :image-size="80"></el-empty>
      </template>
      <WorkOrderItem v-for="item in tableData" :key="item.id" :data="item" @action="handleAction" />
    </div>
    <div class="flex-end" style="margin: 10px 0 60px">
      <PaginationFloatBar
        :current-page="pageNum"
        :page-size="pageSize"
        @update:current-page="handlePageChange"
        :total="total"
      />
      <!-- <el-pagination
        background
        @size-change="pageChange({ pageNum: 1, pageSize: $event })"
        @current-change="pageChange({ pageNum: $event, pageSize })"
        :current-page="pageNum"
        :page-size="pageSize"
        layout="total, prev, pager, next, jumper"
        :total="total"
      /> -->
    </div>
    <ConfirmDialog ref="confirmDialogRef" type="workOrder" @submit="handleConfirm" />
    <ProductMoreInfo ref="ProductMoreInfoRef" />
    <FeedbackLinkToMerchant ref="FeedbackLinkToMerchantRef" @success="handleQuery" />
    <FeedbackLinkList ref="FeedbackLinkListRef" @success="handleQuery" />
    <ConfirmDeliverGoods ref="ConfirmDeliverGoodsRef" @success="handleQuery" />
    <ApplyRefundDialog ref="ApplyRefundDialogRef" isTask @success="handleQuery" />

    <RecordsDialog ref="RecordsDialogRef" @success="handleQuery" />
  </div>
</template>

<script setup>
import ProductMoreInfo from '@/views/order/components/dialog/productMoreInfo.vue'
import FeedbackLinkToMerchant from '@/views/order/components/dialog/feedbackLinkToMerchant.vue'
import FeedbackLinkList from '@/views/order/components/dialog/feedbackLinkList.vue'
import ApplyRefundDialog from '@/views/order/components/dialog/applyRefund.vue'
import ConfirmDeliverGoods from '@/views/order/components/dialog/confirmDeliverGoods.vue'
// import { selectListShootModel } from '@/api/order/select'
import {
  assigneeSelectList,
  historyAssigneeSelectList,
  submitSelectList,
  selectRelevanceUserList,
  modelSelectList,
  workOrderCount,
  workOrderList,
  closeWorkOrder,
  reopenWorkOrder,
  finishWorkOrder,
  refuseWorkOrder,
  assignHandler,
} from '@/api/task/workOrder'
import {
  workOrderStatusList,
  workOrderStatusMap,
  priorityList,
  workOrderTypeList,
} from '@/views/task/data.js'
import ConfirmDialog from '@/views/task/components/confirmDialog.vue'
import WorkOrderItem from '@/views/task/components/workOrderItem.vue'
import RecordsDialog from '@/views/task/components/recordsDialog.vue'
import { ElMessage } from 'element-plus'
import useUserStore from '@/store/modules/user'

const { proxy } = getCurrentInstance()
const router = useRouter()
const route = useRoute()
const store = useUserStore()

const ProductMoreInfoRef = ref(null)
const confirmDialogRef = ref(null)
const FeedbackLinkToMerchantRef = ref(null)
const FeedbackLinkListRef = ref(null)
const ApplyRefundDialogRef = ref(null)
const ConfirmDeliverGoodsRef = ref(null)
const RecordsDialogRef = ref(null)

const confirmType = ref('')

const curTab = ref('')
const queryRef = ref(null)
const pageNum = ref(1)
const pageSize = 20
const total = ref(0)
const queryParams = ref({
  keyword: '',
  priority: '',
  shootModelId: [],
  workOrderType: [],
  status: [],
  assigneeId: [],
  historyAssigneeId: '',
  submitById: [],
  statusDays: [],
  submitTime: [],
  relevanceUserId: [],
})

const tabList = ref([
  { label: '所有', value: '', number: 0 },
  { label: '待处理', value: workOrderStatusMap['待处理'], number: 0 },
  { label: '已完结', value: workOrderStatusMap['已完结'], number: 0 },
  { label: '已拒绝', value: workOrderStatusMap['已拒绝'], number: 0 },
  { label: '已关闭', value: workOrderStatusMap['已关闭'], number: 0 },
])
const loading = ref(false)
const tableData = ref([])

const shootModelList = ref([])
const assigneeList = ref([])
const historyAssigneeList = ref([])
const submitByIdList = ref([])
const relevanceUserList = ref([])

const shortcuts = [
  {
    text: '今天',
    value: () => {
      const start = new Date()
      start.setHours(0, 0, 0, 0)
      const end = new Date()
      end.setHours(23, 59, 59, 59)
      return [start, end]
    },
  },
]

// tab切换
function handleTabChange(name) {
  curTab.value = name
  resetQuery()
}

function handleQuery() {
  loading.value = true
  workOrderList({
    ...handleParams(),
    pageNum: pageNum.value,
    pageSize: pageSize,
  })
    .then(res => {
      if (res.code === 200) {
        tableData.value = res.data.rows
        total.value = res.data.total
      }
    })
    .finally(() => {
      loading.value = false
    })
  workOrderCount().then(res => {
    if (res.code === 200) {
      tabList.value.forEach(item => {
        if (item.value === '') {
          item.number = res.data.totalCount
        } else if (item.value === workOrderStatusMap['待处理']) {
          item.number = res.data.unHandleCount
        } else if (item.value === workOrderStatusMap['已完结']) {
          item.number = res.data.handleCount
        } else if (item.value === workOrderStatusMap['已拒绝']) {
          item.number = res.data.rejectCount
        } else if (item.value === workOrderStatusMap['已关闭']) {
          item.number = res.data.closeCount
        }
      })
    }
  })
}

function handleParams() {
  let { statusDays, submitTime, ...params } = queryParams.value
  if (statusDays?.length > 0) {
    if (statusDays[0] == '3') {
      params.lastReplyTimeLogicalSymbol = '5'
    } else {
      params.lastReplyTimeLogicalSymbol = '3'
    }
    params.lastReplyTime = handleDate(statusDays[0])
  }
  if (submitTime?.length > 0) {
    params.submitTimeBegin = submitTime[0]
    params.submitTimeEnd = submitTime[1]
  }
  if (curTab.value != '') {
    params.status = curTab.value
  }
  return params
}

function handleDate(val) {
  if (!val) return ''
  let date = new Date()
  if (val > 0) {
    date = new Date(date.getTime() - 1000 * 60 * 60 * 24 * val)
  }
  let year = date.getFullYear()
  let month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1
  let day = date.getDate() < 10 ? '0' + date.getDate() : date.getDate()
  return `${year}-${month}-${day}`
}

function pageChange(params) {
  pageNum.value = params.pageNum
  // pageSize = params.pageSize
  handleQuery()
}

function handlePageChange(num) {
  pageNum.value = num
  handleQuery()
}

function resetQuery() {
  router.replace({ query: {} })
  queryParams.value = {
    keyword: '',
    priority: '',
    shootModelId: [],
    workOrderType: [],
    status: [],
    assigneeId: [],
    historyAssigneeId: '',
    submitById: [],
    statusDays: [],
    submitTime: [],
    relevanceUserId: [],
  }
  pageNum.value = 1
  handleQuery()
}
function routerNewWindow(path) {
  const { href } = router.resolve({ path })
  window.open(href, '_blank')
}

function handleAction(btn, row, data) {
  switch (btn) {
    case '查看详情':
      routerNewWindow('/task/workOrder/detail/' + row.taskNum)
      break
    case '关闭工单':
      proxy.$modal.confirm('确定关闭工单？', '提示', {}).then(() => {
        proxy.$modal.loading('关闭中...')
        closeWorkOrder({ taskDetailId: row.id })
          .then(res => {
            if (res.code === 200) {
              proxy.$modal.msgSuccess('操作成功')
              handleQuery()
            }
          })
          .finally(() => proxy.$modal.closeLoading())
      })
      break
    case '完结工单':
      proxy.$modal.confirm('确定完结工单？', '提示', {}).then(() => {
        proxy.$modal.loading('执行中...')
        finishWorkOrder({ taskDetailId: row.id })
          .then(res => {
            if (res.code === 200) {
              proxy.$modal.msgSuccess('操作成功')
              handleQuery()
            }
          })
          .finally(() => proxy.$modal.closeLoading())
      })
      break
    case '重新打开':
      confirmDialogRef.value.open('reset', row.id)
      confirmType.value = 'reset'
      break
    case '拒绝工单':
      confirmDialogRef.value.open('reject', row.id)
      confirmType.value = 'reject'
      break
    case '指派':
      confirmDialogRef.value.open('assign', row.id)
      confirmType.value = 'assign'
      break
    case '查看订单更多':
      ProductMoreInfoRef.value?.open(row.videoId)
      break
    case '反馈素材给商家':
      FeedbackLinkToMerchantRef.value?.open(data.videoId, data.picCount)
      break
    case '模特反馈素材':
      FeedbackLinkListRef.value?.open(data.videoId, row.id)
      break
    case '补发':
      ConfirmDeliverGoodsRef.value?.open(data.videoId, false)
      break
    case '补偿':
      ApplyRefundDialogRef.value?.open(data.videoId, data.picCount)
      break
    case '处理记录':
      RecordsDialogRef.value?.open(
        row.taskNum,
        row.status == workOrderStatusMap['已关闭'] ||
          row.status == workOrderStatusMap['已完结'] ||
          row.status == workOrderStatusMap['已拒绝']
      )
      break
    default:
      break
  }
}

function handleConfirm(params) {
  proxy.$modal.loading('执行中...')
  if (confirmType.value === 'reset') {
    // 重新打开工单
    reopenWorkOrder({
      id: params.id,
      issuePic: params.picUrl.map(item => item.picUrl),
      remark: params.reason,
    })
      .then(res => {
        if (res.code === 200) {
          proxy.$modal.msgSuccess('操作成功')
          handleQuery()
          confirmDialogRef.value.close()
        }
      })
      .finally(() => {
        proxy.$modal.closeLoading()
      })
  } else if (confirmType.value === 'reject') {
    // 拒绝工单
    refuseWorkOrder({
      id: params.id,
      issuePic: params.picUrl.map(item => item.picUrl),
      remark: params.reason,
    })
      .then(res => {
        if (res.code === 200) {
          proxy.$modal.msgSuccess('操作成功')
          handleQuery()
          confirmDialogRef.value.close()
        }
      })
      .finally(() => {
        proxy.$modal.closeLoading()
      })
  } else if (confirmType.value === 'assign') {
    // 指派
    assignHandler({
      assigneeId: params.assigneeId,
      id: params.id,
      issuePic: params.picUrl.map(item => item.picUrl),
      remark: params.reason,
    })
      .then(res => {
        if (res.code === 200) {
          proxy.$modal.msgSuccess('操作成功')
          handleQuery()
          confirmDialogRef.value.close()
        }
      })
      .finally(() => {
        proxy.$modal.closeLoading()
      })
  }
}

function changeStatusDays(value) {
  if (queryParams.value.historyAssigneeId) {
    queryParams.value.statusDays.length = 0
    ElMessage.warning('选择历史处理人后，最新回复时间将失效')
  } else if (value.length > 1) {
    queryParams.value.statusDays.splice(0, queryParams.value.statusDays.length - 1)
  }
}

function changeHistoryAssigneeId(value) {
  if (value && queryParams.value.statusDays.length > 0) {
    queryParams.value.statusDays.length = 0
    ElMessage.warning('选择历史处理人后，最新回复时间将失效')
  }
}

function init() {
  // 处理人
  assigneeSelectList({ type: 2 }).then(res => {
    if (res.data) {
      assigneeList.value = res.data.map(item => ({
        value: item.id,
        label: item.name,
      }))
    }
  })
  // 历史处理人
  historyAssigneeSelectList().then(res => {
    if (res.data) {
      historyAssigneeList.value = res.data.map(item => ({
        value: item.id,
        label: item.name,
      }))
    }
  })
  // 提交人
  submitSelectList({ type: 2 }).then(res => {
    if (res.data) {
      submitByIdList.value = res.data.map(item => ({
        value: item.id,
        label: item.name,
      }))
    }
  })
  // 拍摄模特
  modelSelectList({ type: 2 }).then(res => {
    if (res.data) {
      shootModelList.value = res.data.map(item => ({
        ...item,
        name: item.name + `(${item.account})`,
      }))
    }
  })
  // 工单关联人
  selectRelevanceUserList({ type: 2 }).then(res => {
    relevanceUserList.value = res.data || []
  })

  // 搜索
  if (route.query.type == 2) {
    if (route.query.keyword) {
      queryParams.value.keyword = route.query.keyword
    }
    if (route.query.tab) {
      curTab.value = Number(route.query.tab) || ''
    }
    if (route.query.assignee) {
      queryParams.value.assigneeId = [store.id]
    }
    if (route.query.submitBy) {
      queryParams.value.submitById = [store.id]
    }
    if (route.query.relevance) {
      queryParams.value.relevanceUserId = [store.id]
    }
    if (route.query.afterType) {
      queryParams.value.workOrderType = [route.query.afterType * 1]
    }
  }

  handleQuery()
}

init()
</script>

<style scoped lang="scss">
.table-box {
  min-height: 200px;
}
</style>
