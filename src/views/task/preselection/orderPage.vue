<template>
  <div class="order-page">
    <div class="search-box">
      <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="88px" @submit.prevent>
        <el-form-item label="搜索" prop="keyword">
          <el-input
            v-model="queryParams.keyword"
            clearable
            style="width: 250px"
            placeholder="请输入搜索内容"
          />
        </el-form-item>
        <el-form-item label="预选人数" prop="preselection">
          <el-select v-model="queryParams.preselection" placeholder="请选择" clearable style="width: 180px">
            <el-option
              v-for="item in peoplesOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="拍摄国家" prop="shootingCountries">
          <el-select
            v-model="queryParams.shootingCountries"
            placeholder="请选择"
            clearable
            multiple
            collapse-tags
            style="width: 180px"
          >
            <el-option v-for="item in biz_nation" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="模特类型" prop="modelType">
          <el-select
            v-model="queryParams.modelType"
            multiple
            collapse-tags
            collapse-tags-tooltip
            placeholder="请选择"
            clearable
            style="width: 180px"
          >
            <el-option
              v-for="item in biz_model_type"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
            <el-option label="影/素都可以" value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="匹配次数" prop="counts">
          <el-select
            v-model="queryParams.counts"
            placeholder="请选择"
            multiple
            collapse-tags
            collapse-tags-tooltip
            clearable
            style="width: 180px"
          >
            <el-option
              v-for="item in frequencyOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="开始匹配时间" label-width="100px">
          <el-checkbox-group
            v-model="queryParams.matchStartTimes"
            style="margin-left: 10px; --el-checkbox-font-weight: 500"
            @change="handleChangeMathTimes"
          >
            <el-checkbox-button v-for="item in timeOptions" :value="item.value">
              {{ item.label }}
            </el-checkbox-button>
            <el-checkbox-button :value="99">自定义</el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item v-if="handleMatchTime">
          <el-date-picker
            v-model="queryParams.beforeMatchStartTime"
            format="YYYY/M/D"
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            unlink-panels
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 250px"
          />
        </el-form-item>
        <el-form-item label="照顾单">
          <el-select v-model="queryParams.isCare" placeholder="请选择" clearable style="width: 180px">
            <el-option
              v-for="item in isCareOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="淘汰原因">
          <el-select
            v-model="queryParams.preselectModelOustType"
            placeholder="请选择"
            multiple
            collapse-tags
            collapse-tags-tooltip
            clearable
            style="width: 180px"
          >
            <el-option
              v-for="item in oustTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="英文部客服">
          <el-select
            :reserve-keyword="false"
            v-model="queryParams.englishCustomerServiceNames"
            placeholder="请选择"
            multiple
            filterable
            collapse-tags
            collapse-tags-tooltip
            clearable
            style="width: 180px"
            :loading="issueLoading"
            @visible-change="getIssueList"
          >
            <el-option v-for="item in issueList" :key="item.name" :label="item.name" :value="item.name" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button v-btn type="primary" icon="Search" native-type="submit" @click="onQuery">搜索</el-button>
          <el-button v-btn icon="Refresh" plain @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="table-box">
      <el-table ref="tableRef" :data="tableData" v-loading="loading" style="width: 100%" border row-key="id">
        <template #empty>
          <el-empty description="暂无数据" :image-size="80"></el-empty>
        </template>
        <el-table-column
          prop="productPic"
          label="产品图"
          align="center"
          width="120"
          class-name="product-img-box"
        >
          <template v-slot="{ row }">
            <div class="corner-mark-hint" v-if="row.productPicChange">调</div>
            <div class="flex-start top-tag">
              <el-tag v-if="row.isCare" type="danger" size="small" round>照顾单</el-tag>
              <el-tag
                v-if="
                  row.orderVideoRefund &&
                  (row.orderVideoRefund.refundStatus === 0 || row.orderVideoRefund.refundStatus == 1)
                "
                type="warning"
                size="small"
                round
              >
                退款中
              </el-tag>
            </div>
            <el-image
              style="width: 90px; height: 90px; cursor: pointer"
              :style="{ 'margin-top': row.isCare ? '15px' : '0' }"
              :src="
                row.productPic
                  ? $picUrl +
                    row.productPic +
                    '?imageMogr2/format/webp/interlace/1/quality/100/interlace/0/thumbnail/200x'
                  : ''
              "
              fit="scale-down"
              preview-teleported
              @click="() => row.productPic && showViewer([$picUrl + row.productPic])"
            >
              <template #error>
                <img :src="$picUrl + 'static/assets/no-img.png'" alt="" style="width: 100%; height: 100%" />
              </template>
            </el-image>
          </template>
        </el-table-column>
        <el-table-column prop="msg" label="产品信息" minWidth="320">
          <template v-slot="{ row }">
            <div class="corner-mark-hint" v-if="row.goodsInfoChange">调</div>
            <div class="product-info-box">
              <div class="flex-start gap-5">
                <span>视频编码：{{ row.videoCode }}</span>
                <!-- <CopyButton class="btn" plain size="small" :copy-content="handleCopy(row)" /> -->
                <el-tag type="warning" round size="small" v-if="row.isGund == 1">通品</el-tag>
                <el-tag type="success" round size="small" v-else-if="row.isGund == 0">非通品</el-tag>
                <el-tag type="info" round size="small" v-else-if="row.isGund == 3">暂未选择</el-tag>
              </div>
              <div class="one-ell">中文名称：{{ row.productChinese }}</div>
              <div class="one-ell">英文名称：{{ row.productEnglish }}</div>
              <div class="one-ell productLink">
                产品链接：
                <el-link :underline="false" target="_blank" type="primary" :href="row.productLink">
                  {{ row.productLink }}
                </el-link>
              </div>
              <biz-model-platform :value="row.platform" />
              <biz-model-type :value="row.modelType" />
              <biz-nation :value="row.shootingCountry" />
              <template v-for="op in videoFormatOptions" :key="op.value">
                <el-tag class="tag" v-if="op.value == row.videoFormat" type="warning" size="small" round>
                  {{ op.label }}
                </el-tag>
              </template>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="picCount" label="照片数量" align="center" width="90">
          <template v-slot="{ row }">
            <div class="corner-mark-hint" v-if="row.picCountChange">调</div>
            <div>{{ handleSelectiveAssembly(row.picCount) }}</div>
            <el-button
              v-btn
              v-if="row.referencePic && row.referencePic.length"
              link
              type="primary"
              :disabled="isRefund(row.orderVideoRefund)"
              @click="showViewer(row.referencePic)"
            >
              已选参考图
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="intentionModel" label="意向模特" align="center" width="110">
          <template v-slot="{ row }">
            <div class="corner-mark-hint" v-if="row.intentionModelChange">调</div>
            <ModelCardItem
              v-if="row.intentionModel"
              :data="row.intentionModel"
              @mouseenter="handleMouseEnter($event, row.intentionModel.id)"
              @mouseleave="handleMouseLeave($event, row.intentionModel.id)"
            />
            <div v-else>-</div>
          </template>
        </el-table-column>
        <el-table-column prop="shootRequired" label="拍摄建议" align="center" width="80">
          <template v-slot="{ row }">
            <div class="corner-mark-hint" v-if="row.shootRequiredChange">调</div>
            <!-- <div class="more-ell" style="--l: 3">
              {{ row.shootRequired }}
            </div> -->
            <el-button
              v-btn
              v-if="row.shootRequired?.length"
              v-hasPermi="['preselection:order:shootRequired']"
              link
              type="primary"
              @click="viewShootRequired(row)"
            >
              查看
            </el-button>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="sellingPointProduct" label="产品卖点" align="center" width="80">
          <template v-slot="{ row }">
            <div class="corner-mark-hint" v-if="row.sellingPointProductChange">调</div>
            <el-button
              v-btn
              v-if="row.sellingPointProduct?.length"
              v-hasPermi="['preselection:order:shootingSuggestion']"
              link
              type="primary"
              @click="viewShootRequired(row, 'shootingSuggestion')"
            >
              查看
            </el-button>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="cautions" label="匹配模特注意事项" align="center" width="180">
          <template v-slot="{ row }">
            <div
              class="corner-mark-hint"
              v-if="row.cautionsChange || row.particularEmphasisChange || row.orderSpecificationRequireChange"
            >
              调
            </div>
            <div class="one-ell" v-has-ellipsis:cautionsMore="row" :key="Math.random()">
              <span v-if="row.orderVideoCautionsVO?.cautions && row.orderVideoCautionsVO?.cautions.length">
                模特要求：
              </span>
              <template v-for="(item, i) in row.orderVideoCautionsVO?.cautions" :key="i">
                {{ handleR(item.content) }}
                <br />
              </template>
            </div>
            <div
              class="one-ell"
              v-has-ellipsis:orderSpecificationRequireMore="row"
              :key="Math.random()"
              v-if="row?.orderSpecificationRequire"
            >
              商品规格要求：{{ row?.orderSpecificationRequire }}
            </div>
            <div
              class="one-ell"
              v-has-ellipsis:particularEmphasisMore="row"
              :key="Math.random()"
              v-if="row?.particularEmphasis"
            >
              特别强调：{{ handleR(row?.particularEmphasis) }}
            </div>
            <el-button
              v-btn
              v-if="
                row.cautionsMore ||
                row.orderVideoCautionsVO?.cautionsPics?.length ||
                row.particularEmphasisMore ||
                row.particularEmphasisPic?.length ||
                row.orderSpecificationRequireMore
              "
              link
              type="primary"
              @click="handleViewRequire(row)"
            >
              更多
            </el-button>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="cautions" label="模特要求" align="center" width="250">
          <template v-slot="{ row }">
            <div class="corner-mark-hint" v-if="row.cautionsChange">调</div>
            <div class="more-ell" style="--l: 3" v-has-ellipsis:cautionsMore="row" :key="Math.random()">
              <template v-for="(item, i) in row.orderVideoCautionsVO?.cautions">
                {{ item.content }}
                <br />
              </template>
            </div>
            <el-button
              v-btn
              v-if="row.cautionsMore || row.orderVideoCautionsVO?.cautionsPics?.length"
              link
              type="primary"
              @click="handleViewCautions(row.orderVideoCautionsVO)"
            >
              更多
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="particularEmphasis" label="特别强调" align="center" width="200">
          <template v-slot="{ row }">
            <div class="corner-mark-hint" v-if="row.particularEmphasisChange">调</div>
            <div
              class="more-ell"
              style="--l: 3; white-space: pre-line"
              v-has-ellipsis:particularEmphasisMore="row"
              :key="Math.random()"
            >
              {{ row?.particularEmphasis }}
            </div>
            <el-button
              v-btn
              link
              type="primary"
              @click="handleViewCautionsDialog(row)"
              v-if="row.particularEmphasisMore || row.particularEmphasisPic?.length"
            >
              更多
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="particularEmphasis" label="商品规格要求" align="center" width="200">
          <template v-slot="{ row }">
            <div class="corner-mark-hint" v-if="row.orderSpecificationRequireChange">调</div>
            <div
              class="more-ell"
              style="--l: 3; white-space: pre-line"
              v-has-ellipsis:orderSpecificationRequireMore="row"
              :key="Math.random()"
            >
              {{ row?.orderSpecificationRequire }}
            </div>
            <el-button
              v-btn
              v-if="row.orderSpecificationRequireMore"
              link
              type="primary"
              @click="handleViewSpecificationRequire(row.orderSpecificationRequire)"
            >
              更多
            </el-button>
          </template>
        </el-table-column>-->
        <el-table-column prop="contact" label="中文部客服" align="center" width="90">
          <template v-slot="{ row }">
            <div>
              {{ row.contact?.name || '-' }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="count"
          label="匹配次数"
          align="center"
          width="115"
          class-name="product-img-box"
        >
          <template v-slot="{ row }">
            <div class="top-tag">
              <RollbackTag :row="row" reject-field="isRejectAfterSubmitModel" />
            </div>
            <div v-if="row.count === 1">首次</div>
            <div v-else>第{{ row.count }}次</div>
            <el-button
              v-btn
              v-hasPermi="['preselection:order:history']"
              link
              type="primary"
              @click="viewHistoryModel(row)"
            >
              查看历史模特
            </el-button>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="contact" label="英文部客服" align="center" width="110">
          <template v-slot="{ row }">
            <div>
              {{ row.issue?.name || '-' }}
            </div>
          </template>
        </el-table-column> -->
        <el-table-column prop="user" label="订单运营" align="center" width="100">
          <template v-slot="{ row }">
            <div>
              {{ row.merchantCode }}
            </div>
            <div>
              {{ (row.createOrderUserNickName || '-') + '/' + (row.createOrderUserName || '-') }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="preselectModelCount" label="参与预选模特" align="center" width="580">
          <template v-slot="{ row }">
            <div class="flex-start gap-5" v-if="row.preselectModels?.length">
              <!-- <SelcetModelImage :list="row.preselectModels" /> -->
              <template v-for="(item, i) in row.preselectModels">
                <ModelCardItem
                  v-if="i < 5 && item"
                  :data="item"
                  :showAccount="false"
                  @mouseenter="handleMouseEnter($event, item.id)"
                  @mouseleave="handleMouseLeave($event, item.id)"
                />
              </template>
              <div
                v-if="row.preselectModelCount > 5 && checkPermi(['preselection:order:selected'])"
                class="flex-column preselect-model-more"
                @click="viewModel(row)"
              >
                更多
              </div>
            </div>
            <!-- <div v-else class="flex-center">
              <div style="width: 30px; height: 30px; border-radius: 50%; border: 2px solid #888888">
                <el-icon style="margin-top: 3px" size="18"><Avatar /></el-icon>
              </div>
            </div> -->
            <div class="preselect-model-count">{{ row.preselectModelCount || 0 }}个</div>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" :align="'center'" width="155">
          <template v-slot="{ row }">
            <div v-if="row.startTime">{{ handleOrderStatusTime(row.startTime) }}</div>
            <el-button
              v-btn
              v-hasPermi="['preselection:order:detail']"
              link
              type="primary"
              @click="routerNewWindow('/order/details/' + row.videoId)"
            >
              订单详情
            </el-button>
            <el-button
              v-btn
              v-hasPermi="['preselection:order:add']"
              link
              type="primary"
              @click="addPreselection(row, 'add')"
            >
              添加预选
            </el-button>
            <el-button
              v-btn
              v-hasPermi="['preselection:order:distribute']"
              link
              type="primary"
              @click="addPreselection(row, 'distribute')"
            >
              添加分发
            </el-button>
            <el-button
              v-btn
              link
              type="primary"
              v-hasPermi="['preselection:order:remark']"
              @click="handleRemark(row)"
              :class="{ 'btn-red-tip': row.hasComment }"
            >
              备注
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="flex-end" style="margin: 10px 0 60px">
      <PaginationFloatBar
        :current-page="pageNum"
        :page-size="pageSize"
        @update:current-page="handlePageChange"
        :total="total"
      />
      <!-- <el-pagination
        background
        @size-change="pageChange({ pageNum: 1, pageSize: $event })"
        @current-change="pageChange({ pageNum: $event, pageSize })"
        :current-page="pageNum"
        :page-size="pageSize"
        :page-sizes="[10, 20, 30, 50]"
        layout="total, prev, pager, next, jumper"
        :total="total"
      /> -->
    </div>

    <HistoryModels ref="HistoryModelsRef" @success="handleQuery" />
    <OrderRemark ref="OrderRemarkRef" @success="handleQuery" />
    <PreselectionDialog ref="PreselectionDialogRef" @success="handleQuery" @openSchedule="openSchedule" />
    <ShootRequiredDialog ref="ShootRequiredDialogRef" />
    <CautionsDialog ref="CautionsDialogRef" />
    <ModelInfoPopover ref="ModelInfoPopoverRef" />
    <ScheduleOrderDialog ref="ScheduleOrderDialogRef" @success="handleQuery" @action="handleAction" />
  </div>
</template>

<script setup>
// import SelectModelImage from '@/views/task/preselection/components/selectModelImage.vue'
import ModelCardItem from '@/views/order/components/modelCardItem.vue'
import OrderRemark from '@/views/order/components/dialog/orderRemark.vue'
import RollbackTag from '@/views/order/components/rollbackTag.vue'
import HistoryModels from '@/views/task/preselection/components/historyModels.vue'
import PreselectionDialog from '@/views/task/preselection/components/preselectionDialog.vue'
import ShootRequiredDialog from '@/views/task/preselection/components/shootRequiredDialog.vue'
import ScheduleOrderDialog from '@/views/task/preselection/components/scheduleOrderDialog.vue'
import ModelInfoPopover from '@/views/model/ModelInfoPopover.vue'
import { checkPermi } from '@/utils/permission'
import { getOrderPoolList, poolIssueEnglishSelect } from '@/api/order/preselection'
import { videoFormatOptions } from '@/views/order/list/data.js'
import CautionsDialog from '@/views/task/preselection/components/cautionsDialog.vue'
import {
  frequencyOptions,
  peoplesOptions,
  timeOptions,
  handleCopy,
  handleSelectiveAssembly,
  isRefund,
  handleOrderStatusTime,
  handleViewEmphasis,
  handleViewRequire,
  oustTypeOptions,
} from '@/views/task/preselection/index.js'
import { useViewer } from '@/hooks/useViewer'

const router = useRouter()
const route = useRoute()
function routerNewWindow(path) {
  const { href } = router.resolve({ path })
  window.open(href, '_blank')
}

const { showViewer } = useViewer()
const { proxy } = getCurrentInstance()
const { biz_nation, biz_model_type } = proxy.useDict('biz_nation', 'biz_model_type')
const isCareOptions = [
  { value: 1, label: '是' },
  { value: 0, label: '否' },
]

const queryParams = ref({
  keyword: '',
  preselection: '',
  shootingCountries: [],
  modelType: '',
  counts: [],
  matchStartTimes: [],
  beforeMatchStartTime: [],
  isCare: '',
  preselectModelOustType: [],
  englishCustomerServiceNames: [],
})
const tableData = ref([])
const pageNum = ref(1)
const pageSize = 20
const total = ref(0)
const loading = ref(false)

const HistoryModelsRef = ref()
const OrderRemarkRef = ref()
const PreselectionDialogRef = ref()
const ShootRequiredDialogRef = ref()
const ModelInfoPopoverRef = ref()
const ScheduleOrderDialogRef = ref()

function onQuery() {
  pageNum.value = 1
  handleQuery()
}
function resetQuery() {
  router.replace({ query: {} })
  queryParams.value = {
    keyword: '',
    preselection: '',
    shootingCountries: [],
    beforeMatchStartTime: [],
    modelType: [],
    counts: [],
    matchStartTimes: [],
    isCare: '',
    preselectModelOustType: [],
    englishCustomerServiceNames: [],
  }
  pageNum.value = 1
  handleQuery()
}

const handleMatchTime = computed(() => {
  let isShow = false
  if (queryParams.value.matchStartTimes && queryParams.value.matchStartTimes.length > 0) {
    isShow = queryParams.value.matchStartTimes.includes(99)
  }
  if (!isShow) queryParams.value.beforeMatchStartTime = []
  return isShow
})

function handleChangeMathTimes(value) {
  if (value && value.length > 0) {
    if (value[value.length - 1] == 99) {
      queryParams.value.matchStartTimes = [99]
    } else {
      queryParams.value.matchStartTimes = value.filter(item => item != 99)
    }
  }
}
function handleQuery() {
  loading.value = true
  let modelTypes = ''
  if (queryParams.value.modelType.length) {
    modelTypes = queryParams.value.modelType.join(',')
  }
  let matchStartTimes = []
  if (queryParams.value.matchStartTimes && queryParams.value.matchStartTimes.length > 0) {
    matchStartTimes = queryParams.value.matchStartTimes.filter(item => item != 99)
  }
  let { beforeMatchStartTime, englishCustomerServiceNames, ...params } = queryParams.value
  if (handleMatchTime.value && beforeMatchStartTime && beforeMatchStartTime.length > 0) {
    params.beforeMatchStartTimeStart = beforeMatchStartTime[0]
    params.beforeMatchStartTimeEnd = beforeMatchStartTime[1]
  }
  if (englishCustomerServiceNames && englishCustomerServiceNames.length > 0) {
    params.englishCustomerServiceNames = englishCustomerServiceNames
    params.issueIds = issueList.value
      .filter(item => englishCustomerServiceNames.includes(item.name))
      .map(item => item.id)
  }
  getOrderPoolList({
    ...params,
    modelType: null,
    matchStartTimes,
    modelTypes,
    pageNum: pageNum.value,
    pageSize,
  })
    .then(res => {
      if (res.code === 200) {
        tableData.value = res.data.rows.map(item => ({
          ...item,
          cautionsMore: false,
          particularEmphasisMore: false,
        }))
        total.value = res.data.total
      }
    })
    .finally(() => {
      loading.value = false
    })
}
function pageChange(params) {
  pageNum.value = params.pageNum
  // pageSize = params.pageSize
  handleQuery()
}
function handlePageChange(num) {
  pageNum.value = num
  handleQuery()
}

function handleAction(btn, row, length) {
  switch (btn) {
    case '拍摄建议':
      if (row.shootRequired) {
        viewShootRequired(row)
      }
      break
    case '产品卖点':
      if (row.sellingPointProduct) {
        viewShootRequired(row, 'shootingSuggestion')
      }
      break
    case '匹配模特注意事项':
      handleViewRequire(row)
      break

    default:
      break
  }
}

// 凑单推荐
function openSchedule(list) {
  ScheduleOrderDialogRef.value?.open(list, 2)
}

// 3分钟
const selectUpdateTime = 1000 * 60 * 3
// 英文部客服
const issueList = ref([])
const issueLoading = ref(false)
let issueLastTime = 0
function getIssueList(visible) {
  if (visible && !issueLoading.value && new Date().getTime() - issueLastTime > selectUpdateTime) {
    issueLoading.value = true
    poolIssueEnglishSelect()
      .then(res => {
        if (res.data) {
          issueList.value = res.data
          issueLastTime = new Date().getTime()
        }
      })
      .finally(() => {
        issueLoading.value = false
      })
  }
}

// 拍摄建议
function viewShootRequired(row, type) {
  if (type == 'shootingSuggestion') {
    ShootRequiredDialogRef.value?.open(
      row.matchId || row.id,
      [handleR(row.sellingPointProduct)],
      row.productChinese,
      row.videoCode,
      type
    )
  } else {
    ShootRequiredDialogRef.value?.open(
      row.matchId || row.id,
      row.shootRequired?.map(item => handleR(item.content)) || [],
      row.productChinese,
      row.videoCode
    )
  }
}

function handleR(data) {
  if (data && data.length > 0) {
    return data.replace(/\r/g, '\r\n') || ''
  } else return ''
}

//拍摄要求
const CautionsDialogRef = ref()
function handleViewCautionsDialog(row) {
  handleViewEmphasis(row)
  // CautionsDialogRef.value?.open(row.id)
}
// 历史模特
function viewHistoryModel(row) {
  HistoryModelsRef.value?.open(row.videoId)
}
// 参与预选模特
function viewModel(row) {
  PreselectionDialogRef.value?.show(row.id)
}
// 添加预选
function addPreselection(row, type) {
  PreselectionDialogRef.value?.open(row.id, type, {
    nation: row.shootingCountry,
    platform: row.platform,
    type: row.modelType == '3' ? '0,1' : row.modelType,
    filterBlackListBizUserId: row.createOrderBizUserId,
    videoId: row.videoId,
    isGund: type === 'add' ? row.isGund : undefined,
    matchStartTime: type === 'add' ? row.startTime : undefined,
  })
}
// 备注
function handleRemark(row) {
  OrderRemarkRef.value?.open(row.videoId)
}

function handleMouseEnter(e, id) {
  ModelInfoPopoverRef.value?.open(e.target, id)
}
function handleMouseLeave(e, id) {
  ModelInfoPopoverRef.value?.close()
}

function init() {
  if (route.query.tab == 3) {
    if (route.query.keyword) {
      queryParams.value.keyword = route.query.keyword
    }
  }
  onQuery()
}
init()
</script>

<style scoped lang="scss">
.order-page {
  padding: 20px 0;
  .search-box {
    margin-top: 10px;
  }
  .table-box {
    .btn-red-tip {
      &::after {
        content: '';
        display: block;
        width: 6px;
        height: 6px;
        background-color: red;
        border-radius: 50%;
        margin-left: 3px;
      }
    }

    .productLink {
      position: relative;
      padding-right: 5px;

      :deep(.el-link) {
        display: contents;

        .el-link__inner {
          display: inline;
        }
      }
    }

    .preselect-model-count {
      position: absolute;
      top: 2px;
      left: 2px;
      z-index: 9;
      color: var(--el-color-primary);
    }
    .preselect-model-more {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      border: 1px solid var(--el-color-primary);
      color: var(--el-color-primary);
      cursor: pointer;
      justify-content: center;
      flex-shrink: 0;
    }

    :deep(.el-table) {
      .el-button + .el-button {
        margin: 0 3px;
      }
      .el-table__cell {
        position: relative;
      }
      .corner-mark-hint {
        z-index: 999;
        position: absolute;
        right: 0px;
        top: 0px;
        color: #00bfbf;
        // background-color: #d0efef;
        padding: 0px 4px 6px;
        background-image: url('@/assets/icons/svg/corner-mark-flag.svg');
      }
      .warning-row {
        --el-table-tr-bg-color: var(--el-color-warning-light-9);
      }
      .product-img-box {
        position: relative;

        .top-tag {
          position: absolute;
          top: 2px;
          left: 1px;
        }
      }

      .preselection-model-box {
        padding-top: 8px;
        overflow: hidden;

        .hint-box {
          z-index: 999;
          position: absolute;
          right: -6px;
          top: -2px;
          // overflow: hidden;
          width: 32px;
          height: 25px;

          .exchange {
            background: var(--el-color-warning);
            color: #fff;
            font-size: 13px;
            width: 60px;
            transform: translate(-10px, -3px) scale(0.8) rotateZ(45deg);

            .text {
              transform: rotateZ(-45deg) translate(-1px, 1px);
            }
          }
        }
      }
    }
  }
}
</style>
