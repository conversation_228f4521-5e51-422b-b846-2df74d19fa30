<template>
  <div class="preselection-page">
    <el-tabs v-if="tabType === 1" v-model="status" class="tabs" @tab-change="handleStatusChange">
      <el-tab-pane v-for="(tab, i) in tabList" :key="tab" :name="tab.value" :label="tab.label">
        <template #label>
          <div v-if="tab.value === 3">
            {{ tab.label }}
            {{ '(' }}
            <span style="color: red">{{ tab.number }}</span>
            {{ ')' }}
          </div>
          <div v-else>{{ tab.label }}</div>
        </template>
      </el-tab-pane>
    </el-tabs>

    <div class="explain-box" v-if="status === 3">说明：非自己手动淘汰的模特会需要二次确认淘汰</div>

    <div class="search-box">
      <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="88px" @submit.prevent>
        <el-form-item label="搜索" label-width="60px">
          <el-input
            v-model="queryParams.keyword"
            clearable
            style="width: 250px"
            placeholder="支持视频编码、产品信息进行搜索"
          />
        </el-form-item>
        <el-form-item label="添加预选时间" label-width="100px" v-if="status == 1">
          <el-checkbox-group
            v-model="queryParams.addPreselectTimes"
            style="margin-left: 10px; --el-checkbox-font-weight: 500"
          >
            <el-checkbox-button v-for="item in timeOptions" :value="item.value">
              {{ item.label }}
            </el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="淘汰时间" label-width="100px" v-if="status == 3">
          <el-checkbox-group
            v-model="queryParams.oustTimes"
            style="margin-left: 10px; --el-checkbox-font-weight: 500"
          >
            <el-checkbox-button v-for="item in timeOptions" :value="item.value">
              {{ item.label }}
            </el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="照顾单" v-if="status != 2">
          <el-select v-model="queryParams.isCare" placeholder="请选择" clearable style="width: 180px">
            <el-option
              v-for="item in isCareOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="匹配次数" v-if="status == 0 || status == 1">
          <el-select
            v-model="queryParams.counts"
            placeholder="请选择"
            multiple
            collapse-tags
            collapse-tags-tooltip
            clearable
            style="width: 180px"
          >
            <el-option
              v-for="item in frequencyOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="模特等级" v-if="tabType == 2">
          <el-select
            v-model="queryParams.modelCooperations"
            placeholder="请选择"
            multiple
            collapse-tags
            collapse-tags-tooltip
            clearable
            style="width: 180px"
          >
            <el-option
              v-for="item in biz_model_cooperation"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="携带情况" v-if="tabType == 2">
          <el-select v-model="queryParams.carryType" placeholder="请选择" clearable style="width: 180px">
            <el-option label="主携带" :value="1" />
            <el-option label="被携带" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="模特来源" v-if="status != 2">
          <el-select
            v-model="queryParams.addTypes"
            placeholder="请选择"
            multiple
            collapse-tags
            collapse-tags-tooltip
            clearable
            style="width: 180px"
          >
            <el-option
              v-for="item in sourceOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="预选人数" v-if="status == 1 && tabType != 2">
          <el-select v-model="queryParams.preselection" placeholder="请选择" clearable style="width: 180px">
            <el-option
              v-for="item in peoplesOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="模特意向" v-if="status == 1 || status == 3">
          <el-select
            v-model="queryParams.modelIntentions"
            placeholder="请选择"
            multiple
            collapse-tags
            collapse-tags-tooltip
            clearable
            style="width: 180px"
          >
            <el-option
              v-for="item in modelIntentionOptionsList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="沟通状态" v-if="status == 1 && tabType === 1">
          <el-select
            v-model="queryParams.status"
            placeholder="请选择"
            multiple
            collapse-tags
            collapse-tags-tooltip
            clearable
            style="width: 180px"
          >
            <el-option
              v-for="item in communicationOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="选定状态" v-if="status == 1 && tabType === 1">
          <el-select
            v-model="queryParams.modelSelectionStatus"
            placeholder="请选择"
            clearable
            style="width: 180px"
          >
            <el-option label="已选定模特" :value="1" />
            <el-option label="未选定模特" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="开始匹配时间" label-width="100px" v-if="status == 1">
          <el-checkbox-group
            v-model="queryParams.matchStartTimes"
            style="margin-left: 10px; --el-checkbox-font-weight: 500"
            @change="handleChangeMathTimes"
          >
            <el-checkbox-button v-for="item in timeOptions" :value="item.value">
              {{ item.label }}
            </el-checkbox-button>
            <el-checkbox-button :value="99">自定义</el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item v-if="handleMatchTime">
          <el-date-picker
            v-model="queryParams.beforeMatchStartTime"
            format="YYYY/M/D"
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            unlink-panels
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 250px"
          />
        </el-form-item>
        <el-form-item label="预选模特" label-width="100px" v-if="tabType === 1 && status == 1">
          <el-select
            v-model="queryParams.preselectModelIds"
            placeholder="请选择"
            multiple
            filterable
            collapse-tags
            collapse-tags-tooltip
            clearable
            style="width: 180px"
            :loading="preselectLoading"
            @visible-change="getPreselectList"
          >
            <el-option
              v-for="item in preselectModelList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="携带单" v-if="status == 0">
          <el-select v-model="queryParams.carryType" placeholder="请选择" clearable style="width: 180px">
            <el-option label="主携带" :value="1" />
            <el-option label="被携带" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="我的订单" v-if="status == 0">
          <el-select v-model="queryParams.isMyOrder" placeholder="请选择" clearable style="width: 180px">
            <el-option label="是" :value="true" />
            <el-option label="否" :value="false" />
          </el-select>
        </el-form-item>
        <el-form-item label="订单状态" v-if="status == 0">
          <el-select v-model="queryParams.orderStatus" placeholder="请选择" clearable style="width: 180px">
            <el-option
              v-for="item in orderStatusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="拍摄模特" v-if="status == 0">
          <el-select
            v-model="queryParams.shootModelIds"
            placeholder="请选择"
            multiple
            filterable
            collapse-tags
            collapse-tags-tooltip
            clearable
            style="width: 180px"
            :loading="shootLoading"
            @visible-change="getShootModelList"
          >
            <el-option v-for="item in shootModelList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="开始匹配时间" label-width="100px" v-if="status == 0">
          <el-date-picker
            v-model="queryParams.beforeMatchStartTime"
            format="YYYY/M/D"
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            unlink-panels
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 250px"
          />
        </el-form-item>
        <el-form-item label="匹配结果" v-if="status == 0">
          <el-select
            v-model="queryParams.matchingResults"
            placeholder="请选择"
            clearable
            style="width: 180px"
          >
            <el-option label="匹配成功" :value="1" />
            <el-option label="匹配失败" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="完成匹配时间" label-width="100px" v-if="status == 0">
          <el-date-picker
            v-model="queryParams.beforeMatchEndTime"
            format="YYYY/M/D"
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            unlink-panels
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 250px"
          />
        </el-form-item>
        <el-form-item label="分发模特" v-if="status == 2">
          <el-select
            :reserve-keyword="false"
            v-model="queryParams.distributeModelIds"
            placeholder="请选择"
            multiple
            filterable
            collapse-tags
            collapse-tags-tooltip
            clearable
            style="width: 180px"
            :loading="distributeLoading"
            @visible-change="getDistributeModelList"
          >
            <el-option
              v-for="item in distributeModelList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="英文部客服" v-if="status == 2">
          <el-select
            :reserve-keyword="false"
            v-model="queryParams.issueIds"
            placeholder="请选择"
            multiple
            filterable
            collapse-tags
            collapse-tags-tooltip
            clearable
            style="width: 180px"
            :loading="issueLoading"
            @visible-change="getIssueList"
          >
            <el-option v-for="item in issueList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="添加分发时间" label-width="100px" v-if="status == 2">
          <el-checkbox-group
            v-model="queryParams.distributeStartTimes"
            style="margin-left: 10px; --el-checkbox-font-weight: 500"
            @change="handleChangeMathTimes"
          >
            <el-checkbox-button v-for="item in timeOptions" :value="item.value">
              {{ item.label }}
            </el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="沟通状态" v-if="status == 2">
          <el-select
            v-model="queryParams.communicationStatus"
            placeholder="请选择"
            clearable
            style="width: 180px"
          >
            <template v-for="item in communicationOptions" :key="item.value">
              <el-option v-if="item.value < 2" :label="item.label" :value="item.value" />
            </template>
          </el-select>
        </el-form-item>
        <el-form-item label="淘汰模特" v-if="status == 3">
          <el-select
            v-model="queryParams.oustModelIds"
            placeholder="请选择"
            filterable
            multiple
            collapse-tags
            collapse-tags-tooltip
            clearable
            style="width: 180px"
          >
            <el-option
              v-for="item in oustModelOptionsList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button v-btn type="primary" icon="Search" native-type="submit" @click="onQuery()">
            搜索
          </el-button>
          <el-button v-btn icon="Refresh" plain @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="table-box" v-loading="loading">
      <template v-if="!tableData.length">
        <el-empty description="暂无数据" :image-size="80"></el-empty>
      </template>
      <template v-if="status == 2">
        <DistributeListItem v-for="item in tableData" :key="item.id" :data="item" @action="handleAction" />
      </template>
      <template v-else>
        <PreselectionListItem
          v-for="item in tableData"
          :key="item.id"
          :data="item"
          :status="status"
          :tabType="tabType"
          @action="handleAction"
          @hover="handleModelHover"
        />
      </template>
    </div>

    <div class="flex-end" style="margin: 10px 0 60px">
      <PaginationFloatBar
        :current-page="pageNum"
        :page-size="pageSize"
        @update:current-page="handlePageChange"
        :total="total"
      />
      <!-- <el-pagination
        background
        @size-change="pageChange({ pageNum: 1, pageSize: $event })"
        @current-change="pageChange({ pageNum: $event, pageSize })"
        :current-page="pageNum"
        :page-size="pageSize"
        :page-sizes="[10, 20, 30, 50]"
        layout="total, prev, pager, next, jumper"
        :total="total"
      /> -->
    </div>

    <el-dialog
      v-model="confirmDialogVisible"
      :title="confirmDialogTitle"
      width="510"
      align-center
      :close-on-press-escape="false"
      destroy-on-close
    >
      <div class="flex-column confirm-dialog-box">
        <div class="center" v-if="confirmDialogType === 1">确认已开始与模特沟通</div>
        <div class="center" v-else-if="confirmDialogType === 11">
          <span>已批量选择</span>
          <span style="color: var(--el-color-primary); margin: 0 5px">{{ confirmDialogData.count }}</span>
          <span>笔订单，确认批量标记为沟通中？</span>
        </div>
        <div class="center" v-else-if="confirmDialogType === 2">
          该模特已无剩余可接单数，是否确认选定该模特为该订单拍摄模特？
        </div>
        <div class="sub-model-info" v-else-if="confirmDialogType === 3 || confirmDialogType === 4">
          <div class="flex-start gap-5">
            <div class="sub-model-info-title">模特：</div>
            <el-avatar
              class="model-avatar"
              icon="UserFilled"
              :size="60"
              :src="$picUrl + confirmDialogData.modelPic + '!1x1compress'"
            />
            <div style="margin-left: 5px">
              <div>
                <span style="margin-right: 5px">{{ confirmDialogData.name }}</span>
                <span class="tips">{{ confirmDialogData.account }}</span>
              </div>
              <div class="flex-start gap-5">
                <model-score
                  v-if="
                    confirmDialogData.modelCooperationScore || confirmDialogData.modelCooperationScore === 0
                  "
                  :score="confirmDialogData.modelCooperationScore"
                  style="line-height: normal"
                />
                <biz-model-type-new :value="confirmDialogData.modelType" />
              </div>
            </div>
          </div>
          <div style="margin: 10px 0 0">
            <div class="sub-model-info-title" style="margin-bottom: 10px">拍摄注意事项：</div>
            <el-form-item label="注意内容" style="margin-bottom: 10px">
              <div
                class="template-pre sub-model-attention"
                v-if="confirmDialogData.shootAttention"
                v-text="confirmDialogData.shootAttention"
              ></div>
              <div v-else>-</div>
            </el-form-item>
            <el-form-item label="上传图片">
              <ViewerImageList
                v-if="confirmDialogData.shootAttentionObjectKey"
                :data="confirmDialogData.shootAttentionObjectKey"
                is-preview-all
                :show-delete-btn="false"
              />
              <div v-else>-</div>
            </el-form-item>
          </div>

          <div class="hint" v-if="commitIsTrue">该产品已有与选定模特一致的订单，是否确认选中该模特？</div>
          <div class="hint" v-else-if="confirmDialogType === 3">
            该模特已无剩余可接单数，是否确认提交该模特给商家？
          </div>
        </div>
        <template v-else-if="confirmDialogType === 5">
          <div class="center">您的模特已全部淘汰，是否继续添加预选？</div>
          <div class="center tips">如不继续添加预选，后续可在订单池中再次添加预选模特</div>
        </template>
        <template v-else-if="confirmDialogType === 7">
          <div class="center">确认取消选定该模特？</div>
        </template>
        <div style="margin: 10px 0 20px" v-else-if="confirmDialogType > 100">
          <el-form
            :model="confirmDialogData"
            ref="confirmDialogRef"
            label-width="88px"
            :rules="confirmDialogRules"
          >
            <el-form-item label="淘汰原因" prop="reason" v-if="confirmDialogType === 101">
              <el-select
                v-model="confirmDialogData.reason"
                placeholder="请选择"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="item in oustTypeModelOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="淘汰备注" prop="remark" v-if="confirmDialogType === 101">
              <el-input
                v-model="confirmDialogData.remark"
                clearable
                type="textarea"
                style="width: 100%"
                :rows="4"
                placeholder="请输入淘汰备注"
                maxlength="200"
                show-word-limit
              />
            </el-form-item>
            <el-form-item v-if="confirmDialogType === 101">
              <div
                style="margin-bottom: 10px"
                v-if="confirmDialogData.objectKeys && confirmDialogData.objectKeys?.length < 5"
              >
                <PasteUpload
                  v-model="confirmDialogData.objectKeys"
                  :limit="5"
                  :multiple="true"
                  :fileType="['jpg', 'jpeg', 'png']"
                  :show-file-list="false"
                  :size="5"
                />
                <div style="width: 100%; font-size: 12px">
                  请上传大小不超过
                  <span style="color: #d9001b">5M</span>
                  ，格式为
                  <span style="color: #d9001b">jpg/jpeg/png</span>
                  的图片，最多支持上传5张
                </div>
              </div>
              <ViewerImageList
                :data="confirmDialogData.objectKeys"
                urlName="picUrl"
                is-preview-all
                @delete="(f, i) => confirmDialogData.objectKeys.splice(i, 1)"
              />
            </el-form-item>
            <el-form-item label="模特意向" prop="modelIntention" v-if="confirmDialogType === 102">
              <el-radio-group v-model="confirmDialogData.modelIntention">
                <el-radio-button :value="2">模特想要</el-radio-button>
                <el-radio-button :value="3">模特不想要</el-radio-button>
                <el-radio-button :value="4">取消分发</el-radio-button>
              </el-radio-group>
            </el-form-item>
          </el-form>
          <div class="center tips" v-if="confirmDialogType === 102">
            <span v-if="confirmDialogData.modelIntention == 2">
              注意：确认后，该订单会流转至沟通中列表，进行其他匹配操作
            </span>
            <span v-if="confirmDialogData.modelIntention == 3">
              注意：确认后，模特不想要状态会同步至模特端
            </span>
            <span v-if="confirmDialogData.modelIntention == 4">
              注意：确认后，模特端将不展示在该模特的分发推荐页
            </span>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="flex-center gap-10">
          <el-button class="dialog-btn" v-btn round @click="confirmDialogVisible = false">取消</el-button>
          <el-button class="dialog-btn" v-btn round type="primary" @click="confirmSubmit">确认</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 发货备注 -->
    <el-dialog
      v-model="shippingDialogVisible"
      title="发货备注"
      width="600"
      align-center
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      :show-close="false"
      destroy-on-close
    >
      <div>
        <div class="text-warp">
          <div class="fs-0" style="width: 120px; text-align: end">备注：</div>
          <div style="max-height: 600px; overflow: auto; width: 100%">
            {{ selectModelInfo.shippingRemark || '无' }}
          </div>
        </div>
        <div class="text-warp" style="align-items: flex-start; margin-top: 20px">
          <div class="fs-0" style="width: 120px; text-align: end">发货图片：</div>
          <div v-if="selectModelInfo.shippingPics && selectModelInfo.shippingPics.length > 0">
            <ViewerImageList :data="selectModelInfo.shippingPics" is-preview-all :show-delete-btn="false" />
          </div>
          <span v-else>-</span>
        </div>
      </div>
      <template #footer>
        <div class="flex-end gap-10">
          <el-button class="dialog-btn" v-btn round @click="closeShippingDialogVisible">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <PreselectionRecord ref="PreselectionRecordRef" />
    <DistributeHistoryDialog ref="DistributeHistoryDialogRef" />
    <HistoryModels ref="HistoryModelsRef" :disabled="tabType === 2" @success="handleQuery" />
    <SelectionModel ref="SelectionModelRef" @success="handleQuery" />
    <ShootAttentionDialog ref="ShootAttentionDialogRef" @success="handleQuery" />
    <ShootRequiredDialog ref="ShootRequiredDialogRef" />
    <OrderRemark ref="OrderRemarkRef" @success="handleQuery" />
    <PreselectionDialog ref="PreselectionDialogRef" @success="handleQuery" @openSchedule="openSchedule" />
    <ModelInfoPopover ref="ModelInfoPopoverRef" />
    <CautionsDialog ref="CautionsDialogRef" />
    <ImageLoadDialog ref="ImageLoadDialogRef" />
    <ScheduleOrderDialog ref="ScheduleOrderDialogRef" @success="handleQuery" @action="handleAction" />
    <SameLinkDialog ref="SameLinkDialogRef" @action="handleAction" />
  </div>
</template>

<script setup>
import OrderRemark from '@/views/order/components/dialog/orderRemark.vue'
import DistributeListItem from '@/views/task/preselection/components/distributeListItem.vue'
import PreselectionListItem from '@/views/task/preselection/components/preselectionListItem.vue'
import DistributeHistoryDialog from '@/views/task/preselection/components/distributeHistoryDialog.vue'
import HistoryModels from '@/views/task/preselection/components/historyModels.vue'
import PreselectionRecord from '@/views/task/preselection/components/preselectionRecord.vue'
import SelectionModel from '@/views/task/preselection/components/selectionModel.vue'
import ShootAttentionDialog from '@/views/task/preselection/components/shootAttentionDialog.vue'
import ShootRequiredDialog from '@/views/task/preselection/components/shootRequiredDialog.vue'
import ModelInfoPopover from '@/views/model/ModelInfoPopover.vue'
import PreselectionDialog from '@/views/task/preselection/components/preselectionDialog.vue'
import CautionsDialog from '@/views/task/preselection/components/cautionsDialog.vue'
import ScheduleOrderDialog from '@/views/task/preselection/components/scheduleOrderDialog.vue'
import SameLinkDialog from '@/views/task/preselection/components/sameLinkDialog.vue'
import ViewerImageList from '@/components/ImagePreview/viewerImageList.vue'
import ImageLoadDialog from '@/views/task/preselection/components/imageLoadDialog.vue'
import PasteUpload from '@/components/FileUpload/pasteUpload.vue'
import { orderStatusMap } from '@/views/order/list/data.js'
import {
  frequencyOptions,
  peoplesOptions,
  modelIntentionOptions,
  communicationOptions,
  timeOptions,
  handleViewCautions,
  handleSelectedModelStatus,
  sourceOptions,
  oustTypeModelOptions,
  handleViewEmphasis,
  handleViewSpecificationRequire,
  handleViewRequire,
  handleRestorePreselection,
} from '@/views/task/preselection/index.js'
import {
  getMyPreselectDockingList,
  getMyPreselectFailList,
  getMyPreselectDistributionList,
  getMyPreselectEndMatchList,
  editPreselectModel,
  confirmOustPreselectModel,
  batchMarkCommunication,
  setModelDistributionResult,
  submitModel,
  getSelectModelInfo,
  checkModelAnOrder,
  myPreselectDockingModelSelect,
  myPreselectShootModelSelect,
  getPreselectModelShootAttention,
  distributionEnglishSelect,
  distributionModelSelect,
  myPreselectOustModelSelect,
  getMyPreselectFailListCount,
} from '@/api/order/preselection'
import { useRemindBox } from '@/hooks/useRemindBox'
const { initCurTab } = useRemindBox()
import { ElMessageBox } from 'element-plus'
import useUserStore from '@/store/modules/user'
import { unescapeHtmlMethod } from '@/utils/index.js'

const { proxy } = getCurrentInstance()
const router = useRouter()
const route = useRoute()
const store = useUserStore()

const props = defineProps({
  tabType: {
    type: Number,
    default: 1,
  },
})
const emits = defineEmits(['saveImg'])

const { biz_model_cooperation } = proxy.useDict('biz_model_cooperation')

const ModelInfoPopoverRef = ref()
const tabList = ref([
  { value: 1, label: '沟通中', number: 0 },
  { value: 2, label: '分发中', number: 0 },
  { value: 3, label: '失败待确认', number: 0 },
  { value: 0, label: '结束匹配', number: 0 },
])

const status = ref(1)
watchEffect(() => {
  if (initCurTab.value) {
    status.value = 1
    initCurTab.value = ''
    handleStatusChange()
    ElMessageBox.close()
  }
})

const handleMatchTime = computed(() => {
  let isShow = false
  if (queryParams.value.matchStartTimes && queryParams.value.matchStartTimes.length > 0) {
    isShow = queryParams.value.matchStartTimes.includes(99)
  }
  if (!isShow && status.value == 1) queryParams.value.beforeMatchStartTime = []
  return isShow
})

const orderStatusOptions = [
  { value: orderStatusMap['待匹配'], label: '待匹配' },
  { value: orderStatusMap['需发货'], label: '需发货' },
  { value: orderStatusMap['待完成'], label: '待完成' },
  { value: orderStatusMap['需确认'], label: '需确认' },
  { value: orderStatusMap['已完成'], label: '已完成' },
]

const queryParams = ref({})
const isCareOptions = [
  { value: 1, label: '是' },
  { value: 0, label: '否' },
]

const tableData = ref([])
const pageNum = ref(1)
const pageSize = 20
const total = ref(0)
const loading = ref(false)

const modelIntentionOptionsList = computed(() => {
  if (status.value === 3) {
    return modelIntentionOptions.filter(item => item.value !== 1 && item.value !== 5)
  }
  return modelIntentionOptions.filter(item => item.value !== 3 && item.value !== 5)
})

const oustModelOptionsList = ref([])

function resetParams() {
  return {
    keyword: '',
    isCare: '',
    addPreselectTime: [],
    addtypes: [],
    counts: [],
    preselection: '',
    modelIntentions: [],
    status: [],
    modelSelectionStatus: '',
    matchStartTimes: [],
    carryType: '',
    modelCooperations: [],
    isMyOrder: undefined,
    orderStatus: '',
    beforeMatchStartTime: [],
    beforeMatchEndTime: [],
    preselectModelIds: [],
    shootModelIds: [],
    distributeModelIds: [],
    issueIds: [],
    distributeStartTimes: [],
    communicationStatus: '',
    matchingResults: '',
    oustTimes: [],
    oustModelIds: [],
  }
}

function init() {
  queryParams.value = resetParams()
  // 搜索条件
  if (route.query.tab == props.tabType) {
    if (route.query.s) {
      let s = atob(route.query.s).split(',')
      if (s && s.length) {
        queryParams.value.status = s.map(item => parseInt(item)).filter(item => !isNaN(item))
      }
    }
    if (route.query.keyword) {
      queryParams.value.keyword = route.query.keyword
    }
  }
  onQuery()
  getFailListCount()
}

function handleChangeMathTimes(value) {
  if (value && value.length > 0) {
    if (value[value.length - 1] == 99) {
      queryParams.value.matchStartTimes = [99]
    } else {
      queryParams.value.matchStartTimes = value.filter(item => item != 99)
    }
  }
}

function handleStatusChange(val) {
  tableData.value.length = 0
  total.value = 0
  PreselectionRecordRef.value?.close()
  DistributeHistoryDialogRef.value?.close()
  HistoryModelsRef.value?.close()
  SelectionModelRef.value?.close()
  ShootAttentionDialogRef.value?.close()
  ShootRequiredDialogRef.value?.close()
  OrderRemarkRef.value?.close()
  PreselectionDialogRef.value?.close()
  ScheduleOrderDialogRef.value?.close()
  SameLinkDialogRef.value?.close()
  confirmDialogVisible.value = false
  shippingDialogVisible.vlaue = false
  if (val == 3) {
    getOustModelOptions()
  }
  resetQuery(true)
}

function getOustModelOptions() {
  myPreselectOustModelSelect().then(res => {
    oustModelOptionsList.value = res.data
  })
}
function getFailListCount() {
  getMyPreselectFailListCount().then(res => {
    tabList.value[2].number = res.data || 0
  })
}

function onQuery() {
  pageNum.value = 1
  handleQuery()
}
function resetQuery(isNReset = false) {
  router.replace({ query: {} })
  queryParams.value = resetParams()

  if (isNReset) {
    // 分发中-客服筛选项回填
    if (status.value == 2 && store.id > 1) {
      loading.value = true
      getIssueList(true, () => {
        let issue = issueList.value.find(item => item.id === store.id)
        if (issue) {
          queryParams.value.issueIds = [store.id]
        }
        onQuery()
      })
      return
    }
  }
  onQuery()
}

let randomStr = '' // 频繁请求只处理最后一次请求
function handleQuery() {
  loading.value = true
  let curRandomStr = (randomStr = Math.random().toString(36).substring(2))
  if (status.value === 1) {
    if (props.tabType === 2) {
      queryParams.value.status = [2]
    }
    let matchStartTimes = []
    if (queryParams.value.matchStartTimes && queryParams.value.matchStartTimes.length > 0) {
      matchStartTimes = queryParams.value.matchStartTimes.filter(item => item != 99)
    }
    let { beforeMatchStartTime, ...params } = queryParams.value
    if (handleMatchTime.value && beforeMatchStartTime && beforeMatchStartTime.length > 0) {
      params.beforeMatchStartTimeStart = beforeMatchStartTime[0]
      params.beforeMatchStartTimeEnd = beforeMatchStartTime[1]
    }
    getMyPreselectDockingList({
      ...params,
      status: null,
      matchStartTimes,
      statusList: queryParams.value.status,
      pageNum: pageNum.value,
      pageSize: pageSize,
      addPreselectTime: queryParams.value.addPreselectTime[0] || '',
      isPendingSubmit: props.tabType === 2,
      preselectModelIds:
        queryParams.value.preselectModelIds && queryParams.value.preselectModelIds.length > 0
          ? queryParams.value.preselectModelIds.join(',')
          : [],
      shootModelIds: [],
    })
      .then(res => {
        if (res.code === 200 && curRandomStr === randomStr) {
          tableData.value = res.data.rows
          total.value = res.data.total
          loading.value = false
        }
      })
      .catch(() => {
        if (curRandomStr === randomStr) loading.value = false
      })
  } else if (status.value === 2) {
    getMyPreselectDistributionList({
      keyword: queryParams.value.keyword.trim(),
      englishCustomerServiceIds: queryParams.value.issueIds,
      modelIds: queryParams.value.distributeModelIds,
      addPreselectTimes: queryParams.value.distributeStartTimes,
      communicationStatus: queryParams.value.communicationStatus,
      pageNum: pageNum.value,
      pageSize: pageSize,
    })
      .then(res => {
        if (res.code === 200 && curRandomStr === randomStr) {
          tableData.value = res.data.rows
          total.value = res.data.total
          loading.value = false
        }
      })
      .catch(() => {
        if (curRandomStr === randomStr) loading.value = false
      })
  } else if (status.value === 3) {
    let { isCare, ...params } = queryParams.value
    getMyPreselectFailList({
      ...params,
      isCares: isCare >= 0 ? [isCare] : [],
      pageNum: pageNum.value,
      pageSize: pageSize,
    })
      .then(res => {
        if (res.code === 200 && curRandomStr === randomStr) {
          tableData.value = res.data.rows
          total.value = res.data.total
          loading.value = false
          getFailListCount()
        }
      })
      .catch(() => {
        if (curRandomStr === randomStr) loading.value = false
      })
  } else {
    let { beforeMatchStartTime, beforeMatchEndTime, ...params } = queryParams.value
    if (beforeMatchStartTime && beforeMatchStartTime.length > 0) {
      params.beforeMatchStartTimeStart = beforeMatchStartTime[0]
      params.beforeMatchStartTimeEnd = beforeMatchStartTime[1]
    }
    if (beforeMatchEndTime && beforeMatchEndTime.length > 0) {
      params.beforeMatchEndTimeStart = beforeMatchEndTime[0]
      params.beforeMatchEndTimeEnd = beforeMatchEndTime[1]
    }
    getMyPreselectEndMatchList({
      ...params,
      pageNum: pageNum.value,
      pageSize: pageSize,
      preselectModelIds: [],
      shootModelIds:
        queryParams.value.shootModelIds && queryParams.value.shootModelIds.length > 0
          ? queryParams.value.shootModelIds.join(',')
          : [],
    })
      .then(res => {
        if (res.code === 200 && curRandomStr === randomStr) {
          tableData.value = [res.data.rows]
          total.value = res.data.total
          loading.value = false
        }
      })
      .catch(() => {
        if (curRandomStr === randomStr) loading.value = false
      })
  }
}
// function pageChange(params) {
//   pageNum.value = params.pageNum
//   // pageSize = params.pageSize
//   handleQuery()
// }
function handlePageChange(num) {
  pageNum.value = num
  handleQuery()
}

const OrderRemarkRef = ref(null)
const ShootAttentionDialogRef = ref(null)
const ShootRequiredDialogRef = ref(null)
const PreselectionDialogRef = ref(null)
const DistributeHistoryDialogRef = ref(null)
const HistoryModelsRef = ref(null)
const SelectionModelRef = ref(null)
const PreselectionRecordRef = ref(null)
const ScheduleOrderDialogRef = ref(null)
const SameLinkDialogRef = ref(null)
const confirmDialogVisible = ref(false)
const confirmDialogTitle = ref('提示')
const confirmDialogType = ref(2)
const confirmDialogData = ref({})
const confirmDialogRules = {
  reason: [{ required: true, message: '请选择淘汰原因', trigger: 'change' }],
  remark: [{ required: false, message: '请输入淘汰备注', trigger: 'change' }],
  modelIntention: [{ required: true, message: '请选择意向', trigger: 'change' }],
}
const confirmDialogRef = ref(null)
const shippingDialogVisible = ref(false)
const selectModelInfo = ref({})
const commitIsTrue = ref(false)
const CautionsDialogRef = ref(null)
const ImageLoadDialogRef = ref()
function handleR(data) {
  return data.replace(/\r/g, '\r\n') || '-'
}
function handleAction(btn, row, length) {
  switch (btn) {
    case '拍摄建议':
      if (row.shootRequired) {
        ShootRequiredDialogRef.value?.open(
          row.matchId || row.id,
          row.shootRequired?.map(item => handleR(item.content)) || [],
          row.productChinese,
          row.videoCode
        )
      }
      break
    case '产品卖点':
      if (row.sellingPointProduct) {
        ShootRequiredDialogRef.value?.open(
          row.matchId || row.id,
          [handleR(row.sellingPointProduct)],
          row.productChinese,
          row.videoCode,
          'shootingSuggestion'
        )
      }
      break
    case '拍摄注意事项':
      ShootAttentionDialogRef.value?.open(row)
      break
    case '历史预选模特':
      HistoryModelsRef.value?.open(row.videoId)
      break
    case '参与预选模特':
      PreselectionDialogRef.value?.show(row.matchId)
      break
    case '添加预选':
      PreselectionDialogRef.value?.open(row.matchId || row.id, 'add', {
        nation: row.shootingCountry,
        platform: row.platform,
        type: row.modelType == '3' ? '0,1' : row.modelType,
        filterBlackListBizUserId: row.createOrderBizUserId,
        videoId: row.videoId,
        isGund: row.isGund,
        matchStartTime: row.startTime,
      })
      break
    case '添加分发':
      PreselectionDialogRef.value?.open(row.id, 'distribute', {
        nation: row.shootingCountry,
        platform: row.platform,
        type: row.modelType == '3' ? '0,1' : row.modelType,
        filterBlackListBizUserId: row.createOrderBizUserId,
        videoId: row.videoId,
      })
      break
    case '备注':
      OrderRemarkRef.value?.open(row.videoId)
      break
    case '标记沟通':
      confirmDialogData.value = {
        id: row.preselectModelId || row.pid,
      }
      openConfirmDialog(1)
      break
    case '批量标记沟通':
      confirmDialogData.value = {
        id: row.preselectModelId || row.pid,
        selection: row.selection,
        count: length,
      }
      openConfirmDialog(11)
      break
    case '选定':
      if (row.can <= 0) {
        confirmDialogData.value = {
          matchId: row.matchId,
          id: row.id,
          pid: row.pid,
          proL: row.productLink,
        }
        openConfirmDialog(2)
      } else {
        SelectionModelRef.value?.open(row.matchId, row.id, row.pid, row.productLink)
      }
      break
    case '淘汰':
      let data = tableData.value.find(item => item.id === row.matchId)
      confirmDialogData.value = {
        matchId: row.matchId,
        id: row.pid,
        remark: '',
        addTips: length === 1,
        row: data,
        objectKeys: [],
      }
      openConfirmDialog(101)
      break
    case '修改选定说明':
      SelectionModelRef.value?.show(row.matchId, row.id, row.pid)
      break
    case '取消选定':
      confirmDialogData.value = {
        id: row.pid,
      }
      openConfirmDialog(7)
      break
    case '确定提交':
      confirmDialogData.value = row
      commitIsTrue.value = false
      if (row.productLink) {
        checkModelAnOrder({ productLink: row.productLink || undefined, modelId: row.id }).then(res => {
          commitIsTrue.value = res.data
        })
      }

      openConfirmDialog(row.can > 0 ? 4 : 3)
      break
    case '预选记录':
      PreselectionRecordRef.value?.open(row.id)
      break
    case '跳转订单':
      const { href } = router.resolve({ path: '/order/list', query: { keyword: row.videoCode } })
      window.open(href, '_blank')
      break
    case '发货备注':
      getSelectModelInfo({ matchId: row.matchId, modelId: row.id }).then(res => {
        selectModelInfo.value = res.data
      })
      shippingDialogVisible.value = true
      break
    case '拍摄要求':
      CautionsDialogRef.value?.open(row.id)
      break
    case '模特要求':
      handleViewCautions(row.orderVideoCautionsVO)
      break
    case '特别强调':
      handleViewEmphasis(row)
      // CautionsDialogRef.value?.open(row.id)
      break
    case '商品规格要求':
      handleViewSpecificationRequire(row.orderSpecificationRequire)
      break
    case '匹配模特注意事项':
      handleViewRequire(row)
      break
    case '下载图片':
      ImageLoadDialogRef.value?.open(row.videoId)
      break
    case '历史分发记录':
      DistributeHistoryDialogRef.value?.open(row.modelId)
      break
    case '模特意向':
      confirmDialogData.value = { ...row, modelIntention: 2 }
      openConfirmDialog(102)
      break
    case '排单推荐':
      ScheduleOrderDialogRef.value?.open(row, 1)
      break
    case '确认淘汰':
      handleOustModel(row)
      break
    case '同链接订单':
      SameLinkDialogRef.value?.open(row)
      break
    case '恢复预选':
      handleRestorePreselection(row, handleQuery)
      break
    default:
      break
  }
}

function closeShippingDialogVisible() {
  selectModelInfo.value = {}
  shippingDialogVisible.value = false
}

// 模特浮窗
function handleModelHover(el, id, show) {
  if (show) {
    ModelInfoPopoverRef.value?.open(el, id, 'top-start')
  } else {
    ModelInfoPopoverRef.value?.close()
  }
}

async function openConfirmDialog(type) {
  switch (type) {
    case 1:
      confirmDialogTitle.value = '沟通标记'
      break
    case 11:
      confirmDialogTitle.value = '批量沟通标记'
      break
    case 2:
      confirmDialogTitle.value = '提示'
      break
    case 3:
    case 4:
      proxy.$modal.loading('加载中')
      let res = await getPreselectModelShootAttention({ preselectModelId: confirmDialogData.value.pid })
      proxy.$modal.closeLoading()
      if (res?.data) {
        confirmDialogData.value.shootAttention = unescapeHtmlMethod(res.data.shootAttention)
        confirmDialogData.value.shootAttentionObjectKey = res.data.shootAttentionObjectKey
      }
      confirmDialogTitle.value = '请确认信息无误后提交模特！'
      break
    case 5:
      confirmDialogTitle.value = '模特淘汰'
      break
    case 7:
      confirmDialogTitle.value = '取消选定'
      break
    case 101:
      confirmDialogTitle.value = '淘汰模特'
      break
    case 102:
      confirmDialogTitle.value = '模特意向'
      break
    default:
      break
  }
  confirmDialogType.value = type
  confirmDialogVisible.value = true
}

function handleOustModel(row) {
  proxy.$modal
    .confirm(
      `<div style="margin: 5px 0 10px;line-height: 24px;">
        <div style="font-weight: bold;">确认是否淘汰该模特</div>
        <div style="color: #7f7f7f;font-size: 12px;">淘汰后若有需要可重新添加模特至预选中</div>
      </div>`,
      '确认淘汰模特',
      { dangerouslyUseHTMLString: true }
    )
    .then(() => {
      proxy.$modal.loading('正在淘汰中')
      confirmOustPreselectModel({ matchId: row.matchId, modelId: row.id })
        .then(res => {
          proxy.$modal.msgSuccess('操作成功')
          handleQuery()
        })
        .finally(() => proxy.$modal.closeLoading())
    })
}

function confirmSubmit() {
  if (commitIsTrue.value) {
    if (confirmDialogType.value == 3) {
      commitIsTrue.value = false
      return
    }
  }
  // 标记模特、淘汰模特
  if (confirmDialogType.value === 1 || confirmDialogType.value === 7 || confirmDialogType.value === 101) {
    let status = 1
    let remark = confirmDialogData.value.remark
    if (confirmDialogType.value === 101) {
      status = 3
      confirmDialogRef.value?.validateField('reason')
      if (!confirmDialogData.value.reason) return
      let r = oustTypeModelOptions.find(item => item.value === confirmDialogData.value.reason).label
      remark = r + (remark ? `-${remark}` : '')
    }
    proxy.$modal.loading('提交中')
    editPreselectModel({
      id: confirmDialogData.value.id,
      remark,
      status,
      objectKeys:
        confirmDialogType.value === 101 ? confirmDialogData.value.objectKeys.map(item => item.id) : [],
    })
      .then(res => {
        if (res.code === 200) {
          proxy.$modal.msgSuccess('操作成功')
          handleQuery()
          confirmDialogVisible.value = false
          if (confirmDialogData.value.addTips) {
            nextTick(() => {
              openConfirmDialog(5)
            })
          }
        }
      })
      .finally(() => {
        proxy.$modal.closeLoading()
      })
  } else if (confirmDialogType.value === 11) {
    if (Array.isArray(confirmDialogData.value.selection)) {
      proxy.$modal.loading('提交中')
      batchMarkCommunication(confirmDialogData.value.selection.map(item => item.preselectModelId))
        .then(res => {
          if (res.code === 200) {
            proxy.$modal.msgSuccess('操作成功')
            handleQuery()
            confirmDialogVisible.value = false
          }
        })
        .finally(() => {
          proxy.$modal.closeLoading()
        })
    }
  } else if (confirmDialogType.value === 2) {
    SelectionModelRef.value?.open(
      confirmDialogData.value.matchId,
      confirmDialogData.value.id,
      confirmDialogData.value.pid,
      confirmDialogData.value.proL
    )
    confirmDialogVisible.value = false
  } else if (confirmDialogType.value === 3 || confirmDialogType.value === 4) {
    proxy.$modal.loading('提交中')
    submitModel({
      matchId: confirmDialogData.value.matchId,
    })
      .then(res => {
        if (res.code === 200) {
          handleSelectedModelStatus(res.data)
            .then(() => {
              proxy.$modal.msgSuccess('操作成功')
              handleQuery()
              confirmDialogVisible.value = false
              commitIsTrue.value = false
            })
            .catch(msg => {
              proxy.$modal.msgError(msg)
              // proxy.$modal.alert(msg, '提示', {
              //   confirmButtonText: '知道了',
              // }).then(() => {
              //   // proxy.$modal.loading('正在删除中')
              //   // submitModel({
              //   //   isClear: 1,
              //   //   matchId: confirmDialogData.value.matchId,
              //   // })
              //   //   .then(res => {
              //   //     proxy.$modal.msgSuccess('操作成功')
              //   //     handleQuery()
              //   //   })
              //   //   .finally(() => {
              //   //     proxy.$modal.closeLoading()
              //   //   })
              // })
            })
        }
      })
      .finally(() => {
        proxy.$modal.closeLoading()
      })
  } else if (confirmDialogType.value === 5) {
    if (confirmDialogData.value.row) {
      handleAction('添加预选', confirmDialogData.value.row)
    }
    confirmDialogVisible.value = false
  } else if (confirmDialogType.value === 102) {
    confirmDialogRef.value?.validateField('modelIntention')
    if (!confirmDialogData.value.modelIntention) return
    proxy.$modal.loading('提交中')
    setModelDistributionResult({
      distributionResult: confirmDialogData.value.modelIntention,
      preselectModelId: confirmDialogData.value.preselectModelId,
    })
      .then(res => {
        if (res.code === 200) {
          proxy.$modal.msgSuccess('操作成功')
          handleQuery()
          confirmDialogVisible.value = false
        }
      })
      .finally(() => {
        proxy.$modal.closeLoading()
      })
  }
}

// 凑单推荐
function openSchedule(list) {
  ScheduleOrderDialogRef.value?.open(list, 2)
}

// 3分钟
const selectUpdateTime = 1000 * 60 * 3

// 预选模特
const preselectModelList = ref([])
const preselectLoading = ref(false)
let preselectLastTime = 0
function getPreselectList(visible) {
  if (visible && !preselectLoading.value && new Date().getTime() - preselectLastTime > selectUpdateTime) {
    preselectLoading.value = true
    myPreselectDockingModelSelect()
      .then(res => {
        if (res.data) {
          preselectModelList.value = res.data
          preselectLastTime = new Date().getTime()
        }
      })
      .finally(() => {
        preselectLoading.value = false
      })
  }
}

// 英文部客服
const issueList = ref([])
const issueLoading = ref(false)
let issueLastTime = 0
function getIssueList(visible, fun) {
  if (visible && !issueLoading.value && new Date().getTime() - issueLastTime > selectUpdateTime) {
    issueLoading.value = true
    distributionEnglishSelect()
      .then(res => {
        if (res.data) {
          issueList.value = res.data
          issueLastTime = new Date().getTime()
          if (fun) fun()
        }
      })
      .finally(() => {
        issueLoading.value = false
      })
  } else if (fun) {
    fun()
  }
}

// 分发模特
const distributeModelList = ref([])
const distributeLoading = ref(false)
let distributeLastTime = 0
function getDistributeModelList(visible) {
  if (visible && !distributeLoading.value && new Date().getTime() - distributeLastTime > selectUpdateTime) {
    distributeLoading.value = true
    distributionModelSelect()
      .then(res => {
        if (res.data) {
          distributeModelList.value = res.data
          distributeLastTime = new Date().getTime()
        }
      })
      .finally(() => {
        distributeLoading.value = false
      })
  }
}

// 拍摄模特
const shootModelList = ref([])
const shootLoading = ref(false)
let shootLastTime = 0
function getShootModelList(visible) {
  if (visible && !shootLoading.value && new Date().getTime() - shootLastTime > selectUpdateTime) {
    shootLoading.value = true
    myPreselectShootModelSelect()
      .then(res => {
        if (res.data) {
          shootModelList.value = res.data
          shootLastTime = new Date().getTime()
        }
      })
      .finally(() => {
        shootLoading.value = false
      })
  }
}

init()
</script>

<style scoped lang="scss">
.preselection-page {
  padding-top: 20px;
  position: relative;

  .explain-box {
    position: absolute;
    top: 40px;
    right: 0;
    font-size: 12px;
    color: #7a7a7a;
  }

  .table-box {
    min-height: 200px;
  }

  .confirm-dialog-box {
    min-height: 80px;
    font-size: 15px;
    justify-content: center;
    gap: 6px;

    .center {
      text-align: center;
    }
    .tips {
      font-size: 13px;
      color: #848484;
    }

    .sub-model-info {
      width: 100%;

      .model-avatar {
        --el-avatar-size: 30px !important;
      }

      .sub-model-attention {
        max-height: 300px;
        overflow-y: auto;
      }

      &-title {
        color: #333;
      }

      .hint {
        color: red;
        font-size: 14px;
        margin-top: 10px;
      }
    }
  }
  .dialog-btn {
    padding: 8px 35px;
  }
}
.text-warp {
  word-break: break-all;
  line-break: anywhere;
  white-space: break-spaces;
  display: flex;
  align-items: baseline;
}
</style>
