<template>
  <div class="preselection-container">
    <el-radio-group v-model="curTab" size="large" @change="handleTabChange()">
      <el-radio-button :value="1">我的预选</el-radio-button>
      <el-radio-button :value="2" v-if="checkPermi(['my:preselection:waitSubmit'])">待提交</el-radio-button>
      <el-radio-button :value="3">订单池</el-radio-button>
    </el-radio-group>

    <PreselectionPage v-if="curTab === 1" :tabType="1" />
    <PreselectionPage v-if="curTab === 2" :tabType="2" />
    <OrderPage v-if="curTab === 3" />
  </div>
</template>

<script setup>
import PreselectionPage from '@/views/task/preselection/preselectionPage.vue'
import OrderPage from '@/views/task/preselection/orderPage.vue'
import { checkPermi } from '@/utils/permission'
import { useRemindBox } from '@/hooks/useRemindBox'
const { initCurTab } = useRemindBox()
const router = useRouter()
const route = useRoute()

const curTab = ref(1)

watchEffect(() => {
  if (initCurTab.value && curTab.value != 1) {
    router.go(0)
    curTab.value = 1
    initCurTab.value = ''
  }
})

function handleTabChange() {
  // console.log('handleTabChange', curTab.value);
}

function init() {
  if (route.query.tab) {
    if (route.query.tab == 2 && !checkPermi(['my:preselection:waitSubmit'])) return
    curTab.value = +route.query.tab
  }
}
init()
</script>

<style scoped lang="scss">
.preselection-container {
  padding: 20px 20px 0 20px;
}
</style>
