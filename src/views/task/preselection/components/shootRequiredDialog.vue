<template>
  <el-dialog
    v-model="dialogVisible"
    :title="title"
    width="700"
    align-center
    append-to-body
    :show-close="true"
    :close-on-click-modal="true"
    :close-on-press-escape="false"
    @close="handleClose"
    style="font-weight: 600"
  >
    <div class="content-box">
      <div style="margin-bottom: 10px">
        <el-row>
          <el-col :span="8">
            <span class="">视频编码：{{ videoCode }}</span>
          </el-col>
          <el-col :span="16">
            <span style="word-break: break-all">产品名称：{{ productName }}</span>
          </el-col>
        </el-row>
      </div>
      <div class="flex-start gap-10">
        <span class="label">英</span>
        <div class="list-box template-pre">
          <ul style="list-style-type: none" class="text">
            <li v-for="(item, index) in content" :key="index">{{ item }}</li>
          </ul>
          <CopyButton
            v-if="content"
            class="copy-btn"
            type="primary"
            size="small"
            round
            :copy-content="handleCopy()"
          >
            复制
          </CopyButton>
        </div>
      </div>
      <div class="flex-start gap-10" v-loading="loading">
        <span class="label">翻</span>
        <ul style="list-style-type: none" class="text template-pre">
          <li v-for="(item, index) in translate" :key="index" class="template-pre" style="white-space: pre-wrap;">{{ item }}</li>
        </ul>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import CopyButton from '@/components/Button/CopyButton.vue'
import { translateShootRequired } from '@/api/order/preselection'

defineExpose({
  open,
  close,
})

const emits = defineEmits(['success'])

const dialogVisible = ref(false)
const matchId = ref('')
const content = ref([])
const translate = ref([])
const loading = ref(false)
const productName = ref('')
const videoCode = ref('')
const title = ref('')
function open(id, val, name, code, type) {
  title.value = type === 'shootingSuggestion' ? '产品卖点' : '拍摄建议'
  productName.value = name
  videoCode.value = code
  matchId.value = id
  content.value = val
  translateContent()
  dialogVisible.value = true
}
function close() {
  dialogVisible.value = false
}
function handleClose() {
  content.value.length = 0
  translate.value.length = 0
  dialogVisible.value = false
}

function translateContent() {
  if (!content.value?.length) return
  loading.value = true
  translateShootRequired({
    matchId: matchId.value,
    language: 1,
    wordList: content.value,
  })
    .then(res => {
      translate.value = res.data
    })
    .finally(() => {
      loading.value = false
    })
}

function handleCopy() {
  return content.value.join('\n')
}
</script>

<style scoped lang="scss">
.content-box {
  font-weight: 500;

  .flex-start {
    align-items: stretch;
    margin-bottom: 10px;
  }

  .label {
    font-size: 16px;
    color: #333;
  }
  .text {
    margin: 0 0 10px 0;
    width: 640px;
    min-height: 100px;
    max-height: 350px;
    overflow-y: auto;
    font-size: 14px;
    line-height: 18px;
    color: #000;
    background-color: #ededed;
    padding: 10px 15px 20px 30px;
    border-radius: 8px;
    list-style-type: auto;
  }

  .list-box {
    position: relative;
    line-break: anywhere;
    white-space: pre-line;
    .copy-btn {
      position: absolute;
      bottom: 18px;
      right: 10px;
      padding: 10px 20px;
    }
  }
}
</style>
