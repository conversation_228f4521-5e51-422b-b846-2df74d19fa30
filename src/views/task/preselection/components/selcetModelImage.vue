<template>
  <template v-if="list.length == 3">
    <el-image
      v-for="(item, i) in 3"
      :key="i"
      style="width: 30px; height: 30px; border-radius: 50%"
      :style="{
        marginLeft: i ? '-10px' : '0px',
        zIndex: list.length + 1 - i,
      }"
      :src="$picUrl + handle(list[i]) + '!1x1compress'"
      fit="fill"
    >
      <template #error>
        <div class="image-error">
          <el-icon :size="30"><PictureRounded /></el-icon>
        </div>
      </template>
    </el-image>
  </template>
  <template v-else-if="list.length < 3">
    <el-image
      v-for="(item, i) in list"
      :key="i"
      style="width: 30px; height: 30px; border-radius: 50%"
      :style="{
        marginLeft: i ? '-10px' : '0px',
        zIndex: list.length + 1 - i,
      }"
      :src="handleItem(item)"
      fit="fill"
    >
      <template #error>
        <div class="image-error">
          <el-icon :size="30"><PictureRounded /></el-icon>
        </div>
      </template>
    </el-image>
  </template>
  <template v-else-if="list.length > 3">
    <div class="flex-center">
      <el-image
        v-for="(item, i) in 2"
        :key="i"
        style="width: 30px; height: 30px; border-radius: 50%"
        :style="{
          marginLeft: i ? '-10px' : '0px',
          zIndex: list.length + 1 - i,
        }"
        :src="$picUrl + handle(list[i]) + '!1x1compress'"
        fit="fill"
      >
        <template #error>
          <div class="image-error">
            <el-icon :size="30"><PictureRounded /></el-icon>
          </div>
        </template>
      </el-image>
      <div class="flex-center more-box" style="">
        <el-icon><MoreFilled /></el-icon>
      </div>
    </div>
  </template>
</template>

<script setup>
defineProps({
  list: {
    type: Array,
    default: () => [],
  },
})

const { proxy } = getCurrentInstance()

const handle = data => {
  if (data.model && data.model.modelPic) {
    return data.model.modelPic
  } else if (data.modelPic) {
    return data.modelPic
  } else {
    return ''
  }
}
function handleItem(data) {
  if (data.model && data.model.modelPic) {
    return proxy.$picUrl + data.model.modelPic + '!1x1compress'
  } else if (data.modelPic) {
    return proxy.$picUrl + data.modelPic + '!1x1compress'
  } else {
    return ''
  }
}
</script>

<style lang="scss" scoped>
.image-error {
  color: #ccc;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.more-box {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: #e3e3e3;
  z-index: 1;
  margin-left: -10px;
}
</style>
