<template>
  <div
    class="model-list-item"
    :class="{ mg: data.showTitle, bg: isSelect, no: data.isBlack }"
    @click="handleClick"
  >
    <div class="error-tips" v-if="errMsg">{{ errMsg }}</div>
    <div class="title" v-if="data.showTitle">{{ data.showTitle }}</div>
    <div class="flex-start gap-10 content-box" v-else>
      <div class="percentage-img">
        <PercentageImg
          width="90px"
          :src="$picUrl + data?.modelPic"
          :moreLength="data?.lifePhoto?.length || 0"
          radius="4px"
          @more="viewLifePhoto"
        />
        <el-tag
          v-if="data.isBlack || data.isReject"
          class="tag-box"
          effect="dark"
          type="danger"
          round
          color="#A30014"
        >
          {{ data.isBlack ? '商家拉黑' : data.isReject ? '商家已驳回' : '' }}
        </el-tag>
      </div>
      <div class="info-box">
        <div class="name one-ell">
          <div>{{ data.name }}</div>
          <div class="acc">{{ data.account ? `${data.account}` : '' }}</div>
        </div>
        <div class="flex-start gap-10 tag-info">
          <el-text v-if="data.sex == 1">
            <el-icon color="#3b99fc"><Male /></el-icon>
            男
          </el-text>
          <el-text v-else>
            <el-icon color="#f69661"><Female /></el-icon>
            女
          </el-text>
          <biz-model-ageGroup :value="data.ageGroup" tag="text">
            <template v-slot="{ dict }">
              <el-text>
                <el-icon color="#3b99fc"><User /></el-icon>
                {{ dict.label }}
              </el-text>
            </template>
          </biz-model-ageGroup>
          <biz-nation :value="data.nation" tag="text">
            <template v-slot="{ dict }">
              <el-text>
                <el-icon color="#3b99fc"><LocationInformation /></el-icon>
                {{ dict.label }}
              </el-text>
            </template>
          </biz-nation>
        </div>
        <div class="tips" v-if="type === 'history'">
          <span>待完成订单数：</span>
          <span>{{ data.waits }}单</span>
        </div>
        <div class="tips" v-if="showMoney">
          <span>模特佣金：</span>
          <span v-if="data.commission >= 0">
            {{ data.commission }}{{ handleCommissionUnit(data.commissionUnit) }}
          </span>
          <span v-else>-</span>
        </div>
        <div class="flex-start tag-info">
          <model-score
            v-if="data.cooperationScore || data.cooperationScore === 0"
            :score="data.cooperationScore"
            style="margin-right: 5px"
          />
          <biz-model-type-new :value="data.modelType || data.type" />
          <!-- <div style="color: #606266" v-if="data.cooperationScore">
            模特评分：{{ data.cooperationScore }}分
          </div> -->
          <!-- <biz-model-cooperation :value="data.modelCooperation" type="primary" /> -->
        </div>
        <div class="flex-start tag-info">
          <!-- <biz-model-type :value="data.modelType || data.type" /> -->
          <!-- <biz-model-cooperation :value="data.modelCooperation" /> -->
          <biz-model-platform :value="data.modelPlatform || data.platform" />
        </div>
        <div class="flex-start tag-info">
          <el-tag type="danger" v-if="data.isSameProduct">
            <el-icon class="icon" :size="12"><WarnTriangleFilled /></el-icon>
            <span>已拍同产品</span>
          </el-tag>
          <el-tag type="danger" v-if="data.isSameProductCancel">
            <el-icon class="icon" :size="12"><WarnTriangleFilled /></el-icon>
            <span>已被同产品淘汰</span>
          </el-tag>
          <el-tag type="danger" size="small" v-if="data.isSameProductPreselect">
            <el-icon class="icon" :size="12"><WarnTriangleFilled /></el-icon>
            <span>同产品预选中</span>
          </el-tag>
        </div>
      </div>
      <div class="flex-column gap-10 fs-0 data-box" v-if="type === 'add' || type === 'distribute'">
        <div class="flex-start gap-10">
          <div class="label">可携带</div>
          <div class="content">{{ data.carryCount ? `${data.carryCount}单` : '-' }}</div>
        </div>
        <div class="flex-start gap-10">
          <div class="label">待拍数</div>
          <div class="content">{{ data.waits }}单</div>
        </div>
        <div class="flex-start gap-10">
          <div class="label">待确认</div>
          <div class="content">{{ data.toBeConfirm }}单</div>
        </div>
        <!-- <div class="flex-start gap-10">
          <div class="label">剩余可接订单</div>
          <div class="content">{{ data.can }}单</div>
        </div> -->
        <div class="flex-start gap-10">
          <div class="label">售后率</div>
          <div class="content">
            {{
              data.afterSaleRate
                ? `${math.multiply(math.bignumber(data.afterSaleRate), math.bignumber(100))}%`
                : '0%'
            }}
          </div>
        </div>
        <div class="flex-start gap-10">
          <div class="label">超时率</div>
          <div class="content">
            {{
              data.overtimeRate
                ? `${math.multiply(math.bignumber(data.overtimeRate), math.bignumber(100))}%`
                : '0%'
            }}
          </div>
        </div>
      </div>
      <div class="tags-box" v-if="data.status != 3">
        <div class="flex-start gap algin_b" style="margin-bottom: 10px">
          <div class="label-text" style="color: #015478">模特标签</div>
          <div class="flex-start gap tag-list">
            <template v-for="(item, index) in data.tags" :key="item.dictId">
              <el-tag class="one-ell tag" v-if="index < 7" round>{{ item.name }}</el-tag>
            </template>
            <template v-if="data.tags.length > 7">
              <el-tooltip placement="top" effect="light" trigger="click">
                <el-tag class="tag cur" round>. . .</el-tag>
                <template #content>
                  <div class="tooltip-tag" style="max-width: 900px">
                    <template v-for="item in data.tags" :key="item.dictId">
                      <el-tag class="tag" round>{{ item.name }}</el-tag>
                    </template>
                  </div>
                </template>
              </el-tooltip>
            </template>
          </div>
        </div>
        <div class="flex-start gap algin_b">
          <div class="label-text" style="color: #7b4d12">擅长品类</div>
          <div class="flex-start gap tag-list">
            <template v-for="(item, index) in data.specialtyCategory" :key="item.dictId">
              <el-tag class="one-ell tag" v-if="index < 7" type="warning" round>{{ item.name }}</el-tag>
            </template>
            <template v-if="data.specialtyCategory.length > 7">
              <el-tooltip placement="top" effect="light" trigger="click">
                <el-tag class="tag cur" type="warning" round>. . .</el-tag>
                <template #content>
                  <div class="tooltip-tag" style="max-width: 900px">
                    <template v-for="item in data.specialtyCategory" :key="item.dictId">
                      <el-tag class="tag" type="warning" round>{{ item.name }}</el-tag>
                    </template>
                  </div>
                </template>
              </el-tooltip>
            </template>
          </div>
        </div>
      </div>
      <div
        class="flex-column gap-10 right-box"
        style="width: 365px"
        v-if="type === 'history' && data.status === 3"
      >
        <div class="flex-start gap-5" v-if="data.status != 3">
          <div class="label" style="width: fit-content">模特来源：</div>
          <div class="content">{{ sourceOptions.find(item => item.value == data.addType)?.label }}</div>
        </div>
        <div class="flex-start gap-5">
          <div class="label" style="width: fit-content">模特意向：</div>
          <div class="content">{{ handleModelIntention(data.modelIntention) }}</div>
        </div>
        <div class="flex-start gap-5" style="align-items: baseline">
          <div class="label" style="width: fit-content">淘汰原因：</div>
          <div>
            <div class="remark-box">
              <div>{{ handleOustRemark(data.remark, data.oustType, false) }}</div>
              <div class="text-n-all remark-content" :class="{ 'max-h': data.showRemark }">
                {{ handleOustRemark(data.remark, data.oustType, true) }}
              </div>
            </div>
            <ViewerImageList
              v-if="data.objectKey"
              :data="data.objectKey.split(',')"
              :show-delete-btn="false"
              is-preview-all
              style="--image-width: 40px; --image-height: 40px"
            />
          </div>
          <el-button
            v-if="data.remark && stringLength(handleOustRemark(data.remark, data.oustType, true)) > 42"
            v-btn
            link
            size="small"
            style="font-size: 12px; transform: translateY(15px)"
            @click="() => (data.showRemark = !data.showRemark)"
          >
            {{ data.showRemark ? '收起' : '展开' }}
            <el-icon
              :style="{
                transform: data.showRemark ? 'rotate(270deg)' : 'rotate(90deg)',
              }"
            >
              <DArrowRight />
            </el-icon>
          </el-button>
        </div>
      </div>
      <div class="flex-column gap-10 right-box" v-if="type === 'view' || type === 'history'">
        <div class="flex-start gap-5">
          <div class="label">模特来源：</div>
          <div class="content">{{ sourceOptions.find(item => item.value == data.addType)?.label }}</div>
        </div>
        <div class="flex-start gap-5">
          <div class="label">英文部客服：</div>
          <div class="content">{{ data.modelPersonName }}</div>
        </div>
        <div class="flex-start gap-5" v-if="type === 'history' && data.status != 3">
          <div class="label">状态：</div>
          <div class="content">{{ handleCommunication(data.status) }}</div>
        </div>
        <div class="flex-start gap-5" v-if="data.addTime && data.status != 3">
          <div class="label">{{ data.addType == 4 ? '添加分发：' : '添加预选：' }}</div>
          <div class="content">{{ handleOrderStatusTime(data.addTime).slice(1) }}</div>
        </div>
        <div class="flex-start gap-5" v-if="data.addTime && data.status == 3">
          <div class="label">{{ data.addType == 4 ? '添加分发时间：' : '添加预选时间：' }}</div>
          <div class="content">{{ data.addTime }}</div>
        </div>
        <div class="flex-start gap-5" v-if="data.status == 3">
          <div class="label">淘汰时间：</div>
          <div class="content">{{ data.oustTime || '-' }}</div>
        </div>
      </div>
      <template v-if="type === 'add' || type === 'distribute'">
        <div class="select-btn cancel" v-if="isSelect">取消</div>
        <div class="select-btn" v-if="!isSelect && !data.isBlack">选择</div>
      </template>
    </div>
  </div>
</template>

<script setup>
import PercentageImg from '@/components/ImagePreview/percentageImg.vue'
import ViewerImageList from '@/components/ImagePreview/viewerImageList.vue'
import { bizCommissionUnit } from '@/utils/dict'
import { stringLength } from '@/utils/index'
import * as math from 'mathjs'
import {
  communicationOptions,
  handleOrderStatusTime,
  modelIntentionOptions,
  handleOustRemark,
  sourceOptions,
} from '@/views/task/preselection/index.js'

const props = defineProps({
  data: {
    type: Object,
    required: true,
  },
  type: {
    type: String,
    default: 'view',
  },
  isSelect: {
    type: Boolean,
    default: false,
  },
  errorTips: {
    type: Array,
    default: () => [],
  },
  showMoney: {
    type: Boolean,
    default: true,
  },
})

const emits = defineEmits(['openPhoto', 'select'])

const data = toRef(() => props.data)

const errMsg = computed(() => {
  if (props.errorTips.length > 0) {
    let item = props.errorTips.find(item => item.id == props.data.id)
    if (item) {
      switch (item.type) {
        case 1:
          return '*模特已被客服添加，无法重复加入'
        case 2:
          return '*模特已被添加为商家意向，无法重复加入'
        case 3:
          return '*订单匹配条件已变更，请刷新页面重试'
        case 4:
          return '*模特分数不满足订单要求，请重新选择'
      }
    }
  }
  return ''
})

function handleCommissionUnit(val) {
  let b = bizCommissionUnit.find(item => item.value == val)
  return b ? b.label : ''
}
function handleCommunication(val) {
  let str = communicationOptions.find(item => item.value == val)
  return str ? str.label : ''
}
function handleModelIntention(val) {
  let res = modelIntentionOptions.find(item => item.value == val)
  return res ? res.label : ''
}

function handleClick() {
  if (props.data.isBlack) return
  if (props.type === 'add' || props.type === 'distribute') {
    emits('select', !props.isSelect, data.value.id)
  }
}

function viewLifePhoto() {
  emits('openPhoto', data.value.lifePhoto)
}
</script>

<style scoped lang="scss">
.algin_b {
  align-items: baseline;
}
.model-list-item {
  position: relative;
  margin: 10px 0;
  padding: 15px 10px;
  border-radius: 5px;
  border: 1px solid var(--el-color-primary-light-7);

  &.mg {
    border: none;
  }
  &.bg {
    background-color: #ebf5ff80;
  }
  &.no {
    background-color: #b3b3b3;
    cursor: no-drop;
  }

  .error-tips {
    font-size: 14px;
    color: var(--el-color-danger);
  }

  .title {
    // position: absolute;
    // top: -30px;
    // left: 0;
    font-size: 16px;
    color: #333;
  }

  .status-box {
    position: absolute;
    top: 0;
    right: 0;
    font-size: 13px;
    border-bottom: 1px solid var(--el-color-primary-light-7);
    border-left: 1px solid var(--el-color-primary-light-7);
    padding: 3px 10px;
    border-radius: 0 5px;
    background: #fff;

    .el-text {
      cursor: pointer;
    }

    .primary {
      color: var(--el-color-primary);
    }
    .success {
      color: var(--el-color-success);
    }
    .warning {
      color: var(--el-color-warning);
    }
    .danger {
      color: var(--el-color-danger);
    }
  }

  .content-box {
    overflow: hidden;
  }

  .percentage-img {
    position: relative;
    flex-shrink: 0;
    margin: auto 0;
    overflow: hidden;
    .tag-box {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 90px;
    }

    .hint {
      background: var(--el-color-warning);
      color: #fff;
      font-size: 13px;
      width: 60px;
      position: absolute;
      left: -20px;
      top: -2px;
      transform: scale(0.8) rotateZ(-45deg);
      text-align: center;
      line-height: 23px;
    }
  }

  .info-box {
    font-size: 14px;
    width: 200px;
    flex-shrink: 0;

    .name {
      display: flex;
      align-items: center;
      font-size: 16px;
      font-weight: 600;
      color: #333;
      max-width: 300px;
      margin-bottom: 5px;

      .acc {
        margin-left: 5px;
        font-size: 12px;
        font-weight: 300;
        color: #7f7f7f;
      }
    }

    .tag-info {
      flex-wrap: wrap;
      gap: 5px;

      & + .tag-info {
        margin-top: 8px
      }

      .icon {
        transform: translateY(1px);
        margin-right: 3px;
      }
    }

    .tips {
      font-size: 12px;
      color: #7f7f7f;
      margin-bottom: 10px;
    }
  }

  .data-box {
    border: 1px solid #e6e6e6;
    border-top: none;
    border-bottom: none;
    width: 200px;
    margin-right: 20px;

    & > div {
      width: 100%;
    }

    .label {
      flex-shrink: 0;
      width: 50%;
      text-align: right;
      font-size: 12px;
      color: #7f7f7f;
    }
    .content {
      flex-shrink: 0;
      width: 50%;
      font-size: 12px;
      color: #333;
    }
  }

  .tags-box {
    font-size: 14px;
    flex-shrink: 0;

    .tag-list {
      flex-wrap: wrap;
      width: 344px;
    }
    .tag {
      width: 80px;
      display: block;
      line-height: 24px;
      text-align: center;

      &.cur {
        cursor: pointer;
      }
    }

    .gap {
      gap: 8px;
    }
  }

  .right-box {
    width: 250px;

    & > div {
      width: 100%;
    }

    .label {
      flex-shrink: 0;
      width: 50%;
      text-align: right;
      font-size: 12px;
      color: #6a6a6a;
    }
    .content {
      flex-shrink: 0;
      width: 50%;
      font-size: 12px;
      color: #333;
    }

    .remark-box {
      max-width: 300px;
      font-size: 12px;
      color: #333;
      margin-bottom: 5px;

      .remark-content {
        max-height: 16px;
        line-height: 16px;
        overflow: hidden;

        &.max-h {
          max-height: 200px;
          overflow-y: auto;
        }
      }
    }
  }

  .select-btn {
    position: absolute;
    top: 50%;
    right: 10px;
    z-index: 9;
    transform: translateY(-50%);
    width: 45px;
    height: 45px;
    line-height: 45px;
    border-radius: 50%;
    border: 1px solid #e6e6e6;
    text-align: center;
    cursor: pointer;

    &:hover {
      border-color: var(--el-color-primary);
      color: var(--el-color-primary);
    }

    &.cancel {
      border-color: var(--el-color-primary-light-5);

      &:hover {
        border-color: var(--el-color-danger);
        color: var(--el-color-danger);
      }
    }
  }
}
.tooltip-tag {
  max-width: 510px;
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}
</style>
