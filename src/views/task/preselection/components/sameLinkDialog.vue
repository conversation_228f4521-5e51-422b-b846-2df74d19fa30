<template>
  <el-dialog
    v-model="dialogVisible"
    title="同链接订单"
    width="700"
    align-center
    append-to-body
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <div class="content-box">
      <div class="flex-start gap-10 product-box">
        <el-image
          style="width: 80px; height: 80px; cursor: pointer"
          :src="
            orderInfo.productPic
              ? $picUrl +
                orderInfo.productPic +
                '?imageMogr2/format/webp/interlace/1/quality/100/interlace/0/thumbnail/200x'
              : ''
          "
          fit="scale-down"
          preview-teleported
          @click="() => orderInfo.productPic && showViewer([$picUrl + orderInfo.productPic])"
        >
          <template #error>
            <img :src="$picUrl + 'static/assets/no-img.png'" alt="" style="width: 100%; height: 100%" />
          </template>
        </el-image>

        <div style="width: 550px">
          <div class="one-ell" style="padding-right: 20px">中文名称：{{ orderInfo.productChinese }}</div>
          <div class="one-ell" style="padding-right: 20px">英文名称：{{ orderInfo.productEnglish }}</div>
          <div class="one-ell productLink">
            产品链接：
            <el-link :underline="false" target="_blank" type="primary" :href="orderInfo.productLink">
              {{ orderInfo.productLink }}
            </el-link>
          </div>
        </div>
      </div>

      <el-table
        ref="tableRef"
        :header-cell-style="{
          'background-color': '#e8e8ea',
        }"
        :height="500"
        :data="tableData"
        style="width: 100%"
        border
        row-key="id"
      >
        <template #empty>
          <el-empty description="暂无数据" :image-size="80"></el-empty>
        </template>
        <el-table-column prop="videoCode" label="视频编码" align="center">
          <template v-slot="{ row }">
            {{ row.videoCode || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="订单状态" align="center">
          <template v-slot="{ row }">
            {{
              orderStatusMap[row.status]
                ? orderStatusMap[row.status] + (row.matchStatus == 2 ? '-暂停匹配' : '')
                : '-'
            }}
          </template>
        </el-table-column>
        <el-table-column prop="preselectModelCount" label="参与预选模特" align="center">
          <template v-slot="{ row }">
            <div v-if="row.hasShootModel && row.status != 9">已有拍摄模特</div>
            <div v-else-if="row.hasSelectedModel && row.status != 9">待提交模特</div>
            <div v-else>{{ row.preselectModelCount || 0 }}个</div>
          </template>
        </el-table-column>
        <el-table-column label="操作" :align="'center'">
          <template v-slot="{ row }">
            <el-button
              v-if="checkPermi(['my:preselection:detail'])"
              v-btn
              link
              type="primary"
              @click="routerNewWindow('/order/details/' + row.videoId)"
            >
              订单详情
            </el-button>
            <el-button
              v-if="
                !row.hasSelectedModel &&
                !row.hasShootModel &&
                row.matchId &&
                row.matchStatus != 2 &&
                orderStatusMap['待匹配'] == row.status &&
                checkPermi(['my:preselection:add'])
              "
              v-btn
              link
              type="primary"
              @click="handleAction('添加预选', row)"
            >
              添加预选
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </el-dialog>
</template>

<script setup>
import { orderStatusMap } from '@/views/order/list/data.js'
import { useViewer } from '@/hooks/useViewer'
import { checkPermi } from '@/utils/permission'
import { getMyPreselectSameProductList } from '@/api/order/preselection.js'

const emits = defineEmits(['action'])

const { showViewer } = useViewer()

const router = useRouter()
function routerNewWindow(path) {
  const { href } = router.resolve({ path })
  window.open(href, '_blank')
}

defineExpose({
  open,
  close,
})

const dialogVisible = ref(false)
const orderInfo = ref({})

const tableData = ref([])
const tableLoading = ref(false)

function open(order) {
  dialogVisible.value = true
  orderInfo.value = order
  getList()
}
function close() {
  dialogVisible.value = false
}
function handleClose() {
  orderInfo.value = {}
  tableData.value = []
}

function getList() {
  tableLoading.value = true
  getMyPreselectSameProductList({
    productLink: orderInfo.value.productLink,
  })
    .then(res => {
      tableData.value = res.data || []
    })
    .finally(() => {
      tableLoading.value = false
    })
}

function handleAction(btn, row) {
  emits('action', btn, row)
}
</script>

<style scoped lang="scss">
.content-box {
  .product-box {
    margin-bottom: 10px;

    .productLink {
      position: relative;
      padding-right: 5px;

      :deep(.el-link) {
        display: contents;

        .el-link__inner {
          display: inline;
        }
      }
    }
  }
}
</style>
