<template>
  <div class="distribute-list-item">
    <el-table
      ref="tableRef"
      :data="modelList"
      style="width: 100%"
      :header-cell-style="{
        'background-color': '#e8e8ea !important',
      }"
      border
      row-key="modelId"
    >
      <el-table-column prop="modelPic" label="模特头像" align="center" width="130">
        <template v-slot="{ row }">
          <el-image
            style="width: 90px; height: 90px; cursor: pointer"
            :src="$picUrl + row.modelPic + '!1x1compress'"
            fit="scale-down"
            preview-teleported
          >
            <template #error>
              <img :src="$picUrl + 'static/assets/no-img.png'" alt="" style="width: 100%; height: 100%" />
            </template>
          </el-image>
        </template>
      </el-table-column>
      <el-table-column prop="info" label="模特信息" align="center" width="300">
        <template v-slot="{ row }">
          <div style="text-align: left">
            <div>模特姓名：{{ row.name }}({{ row.account }})</div>
            <div class="flex-start">
              <!-- <biz-model-cooperation :value="row.cooperation" tag="text" /> -->
              <model-score
                v-if="row.cooperationScore || row.cooperationScore === 0"
                :score="row.cooperationScore"
                style="margin-right: 5px"
              />
              <biz-model-type-new :value="row.type" />
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="waits" label="待完成" align="center" width="110">
        <template v-slot="{ row }">{{ row.waits }}单</template>
      </el-table-column>
      <el-table-column prop="commission" label="合作佣金" align="center" width="110">
        <template v-slot="{ row }">
          {{ row.commission }}{{ handleCommissionUnit(row.commissionUnit) }}
        </template>
      </el-table-column>
      <el-table-column prop="afterSaleRate" label="售后率" align="center" width="110">
        <template v-slot="{ row }">
          {{
            row.afterSaleRate
              ? `${math.multiply(math.bignumber(row.afterSaleRate), math.bignumber(100))}%`
              : '0%'
          }}
        </template>
      </el-table-column>
      <el-table-column prop="carryCount" label="可携带数" align="center" width="110">
        <template v-slot="{ row }">{{ row.carryCount ? `${row.carryCount}单` : '-' }}</template>
      </el-table-column>
      <el-table-column prop="issue" label="英文部客服" align="center" width="120">
        <template v-slot="{ row }">
          <div>
            {{ row.englishServiceName || '-' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" minWidth="170">
        <template v-slot="{ row }">
          <el-button
            v-if="checkPermi(['preselection:distribute:record'])"
            v-btn
            link
            type="primary"
            @click="handleAction('历史分发记录', row)"
          >
            历史分发记录
          </el-button>
          <CopyButton
            v-if="checkPermi(['preselection:distribute:shareLink'])"
            v-btn
            link
            type="primary"
            :async-copy-content="() => handleCopyLink(row)"
          >
            分享链接
          </CopyButton>
        </template>
      </el-table-column>
    </el-table>

    <div class="flex-start gap-10 interlayer-box">
      <span>包含分发({{ tableList.length }})</span>
      <el-button
        v-if="checkPermi(['preselection:distribute:markers'])"
        v-btn
        link
        type="primary"
        :disabled="!multipleSelection.length"
        @click="handleAction('批量标记沟通')"
      >
        批量标记沟通
      </el-button>
    </div>

    <el-table
      ref="tableRef"
      :header-cell-style="{
        'background-color': '#fff !important',
      }"
      :data="tableList"
      style="width: 100%"
      border
      row-key="videoId"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" :selectable="selectable" align="center" width="55" />
      <!-- 产品图 -->
      <TableColumns compType="productPic" prop="productPic" label="产品图" width="130" />
      <!-- 产品信息 -->
      <TableColumns compType="productInfo" prop="productInfo" label="产品信息" minWidth="320" align="left">
        <template #productInfoBtn="{ row }">
          <CopyButton class="btn" plain size="small" :copy-content="handleCopy(row)" />
          <el-button class="btn" size="small" type="primary" @click="handleAction('下载图片', row)">
            下载
          </el-button>
        </template>
      </TableColumns>
      <el-table-column prop="picCount" label="照片数量" align="center" width="110">
        <template v-slot="{ row }">
          <div>{{ handleSelectiveAssembly(row.picCount) }}</div>
          <el-button
            v-btn
            v-if="row.referencePic && row.referencePic.length"
            link
            type="primary"
            @click="handleAction('已选参考图', row)"
          >
            已选参考图
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="user" label="订单运营" align="center" width="150">
        <template v-slot="{ row }">
          <div>
            {{ (row.createOrderUserNickName || '-') + '/' + (row.createOrderUserName || '-') }}
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="shootRequired" label="拍摄建议" align="center" width="100">
        <template v-slot="{ row }">
          <el-button
            v-btn
            v-if="row.shootRequired?.length"
            v-hasPermi="['preselection:distribute:shootRequired']"
            link
            type="primary"
            @click="handleAction('拍摄建议', row)"
          >
            查看
          </el-button>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="sellingPointProduct" label="产品卖点" align="center" width="100">
        <template v-slot="{ row }">
          <el-button
            v-btn
            v-if="row.sellingPointProduct?.length"
            v-hasPermi="['preselection:distribute:shootingSuggestion']"
            link
            type="primary"
            @click="handleAction('产品卖点', row)"
          >
            查看
          </el-button>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="cautions" label="匹配模特注意事项" align="center" width="200">
        <template v-slot="{ row }">
          <div class="one-ell" v-has-ellipsis:cautionsMore="row" :key="Math.random()">
            <span v-if="row.orderVideoCautionsVO?.cautions && row.orderVideoCautionsVO?.cautions.length">
              模特要求：
            </span>
            <template v-for="(item, i) in row.orderVideoCautionsVO?.cautions" :key="i">
              {{ handleR(item.content) }}
              <br />
            </template>
          </div>
          <div
            class="one-ell"
            v-has-ellipsis:orderSpecificationRequireMore="row"
            :key="Math.random()"
            v-if="row.orderSpecificationRequire"
          >
            商品规格要求：{{ row.orderSpecificationRequire || '' }}
          </div>
          <div
            class="one-ell"
            v-has-ellipsis:particularEmphasisMore="row"
            :key="Math.random()"
            v-if="row.particularEmphasis"
          >
            特别强调：{{ handleR(row.particularEmphasis) }}
          </div>
          <el-button
            v-btn
            v-if="
              row.cautionsMore ||
              row.orderVideoCautionsVO?.cautionsPics?.length ||
              row.particularEmphasisMore ||
              row.particularEmphasisPic?.length ||
              row.orderSpecificationRequireMore
            "
            link
            type="primary"
            @click="handleAction('匹配模特注意事项', row)"
          >
            更多
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="contact" label="中文部客服" align="center" width="120">
        <template v-slot="{ row }">
          <div>
            {{ row.chineseServiceName || '-' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="historyPreselectModelCount" label="历史预选模特" align="center" width="120">
        <template v-slot="{ row }">
          <div>{{ row.historyPreselectModelCount || 0 }}个</div>
          <el-button
            v-if="row.historyPreselectModelCount"
            v-btn
            v-hasPermi="['preselection:distribute:history']"
            link
            type="primary"
            @click="handleAction('历史预选模特', row)"
          >
            查看
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="time" label="分发时间" align="center" width="170">
        <template v-slot="{ row }">
          <div>{{ handleOrderStatusTime(row.addTime) }}</div>
          <div>{{ row.addTime || '-' }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="沟通状态" align="center" width="120">
        <template v-slot="{ row }">
          <div>{{ handleStatus(row.status, 'status') }}</div>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" :align="'center'" width="170">
        <template v-slot="{ row }">
          <el-button
            v-if="row.status == 0 && checkPermi(['preselection:distribute:marker'])"
            v-btn
            link
            type="primary"
            @click="handleAction('标记沟通', row)"
          >
            标记沟通
          </el-button>
          <el-button
            v-if="checkPermi(['preselection:distribute:modelIntention'])"
            v-btn
            link
            type="primary"
            @click="handleAction('模特意向', row)"
          >
            模特意向
          </el-button>
          <el-button
            v-if="checkPermi(['preselection:distribute:detail'])"
            v-btn
            link
            type="primary"
            @click="routerNewWindow('/order/details/' + row.videoId)"
          >
            查看订单详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import TableColumns from '@/components/TableColumns/index.vue'
import * as math from 'mathjs'
import CopyButton from '@/components/Button/CopyButton.vue'
import { checkPermi } from '@/utils/permission'
import { useViewer } from '@/hooks/useViewer'
import useUserStore from '@/store/modules/user'
import { bizCommissionUnit } from '@/utils/dict'
import { videoFormatOptions } from '@/views/order/list/data.js'
import {
  modelIntentionOptions,
  communicationOptions,
  handleCopy,
  handleSelectiveAssembly,
  handleOrderStatusTime,
} from '@/views/task/preselection/index.js'
import { createBackstageLink } from '@/api/model/model'

const { proxy } = getCurrentInstance()

const router = useRouter()
function routerNewWindow(path) {
  const { href } = router.resolve({ path })
  window.open(href, '_blank')
}

const { showViewer } = useViewer()

const store = useUserStore()

const props = defineProps({
  data: {
    type: Object,
    required: true,
  },
})
const emits = defineEmits(['action'])

const multipleSelection = ref([])

const modelList = computed(() => {
  return props.data ? [props.data] : []
})
const tableList = computed(() => {
  if (props.data) {
    multipleSelection.value = []
    return Array.isArray(props.data.distributionOrderListVOS)
      ? props.data.distributionOrderListVOS.map(item => ({
          ...item,
          cautionsMore: false,
          particularEmphasisMore: false,
          orderSpecificationRequireMore: false,
        }))
      : []
  }
  return []
})

function handleCopyLink(row) {
  return new Promise((resolve, reject) => {
    if (!row.modelId) {
      proxy.$modal.msgError('复制参数错误')
      return reject()
    }
    proxy.$modal.loading('获取中')
    createBackstageLink(row.modelId)
      .then(res => {
        if (res.data) {
          return resolve(res.data + `&to=${btoa(JSON.stringify({ tab: 'recommend' }))}`)
        }
        proxy.$modal.msgWarning('获取链接错误')
        reject()
      })
      .catch(() => {
        proxy.$modal.msgWarning('获取链接错误')
        reject()
      })
      .finally(() => proxy.$modal.closeLoading())
  })
}

function handleAction(btn, row) {
  if (btn == '已选参考图' && row.referencePic && row.referencePic.length) {
    showViewer(row.referencePic.map(item => item))
  }

  emits(
    'action',
    btn,
    {
      ...row,
      pid: props.data.modelId,
      selection: multipleSelection.value,
    },
    multipleSelection.value.length
  )
}

function handleR(data) {
  if (data && data.length > 0) {
    return data.replace(/\r/g, '\r\n') || ''
  } else return ''
}

function selectable(row) {
  return row.status == 0
}
function handleSelectionChange(val) {
  multipleSelection.value = val
}

function handleStatus(val, type) {
  let str = ''
  if (type == 'modelIntention') {
    str = modelIntentionOptions.find(item => item.value == val)
  } else if (type == 'status') {
    str = communicationOptions.find(item => item.value == val)
  }
  return str ? str.label : '-'
}

function handleCommissionUnit(val) {
  let b = bizCommissionUnit.find(item => item.value == val)
  return b ? b.label : ''
}
</script>

<style scoped lang="scss">
.distribute-list-item {
  .product-info-box {
    position: relative;

    .product-info-btn {
      position: absolute;
      top: 0;
      right: -3px;

      .btn {
        padding: 2px 4px;
        height: auto;
        font-size: 12px;
      }
    }
  }

  .productLink {
    position: relative;
    padding-right: 5px;

    :deep(.el-link) {
      display: contents;

      .el-link__inner {
        display: inline;
      }
    }
  }

  .interlayer-box {
    border: 1px solid #ebeef5;
    border-top: none;
    border-bottom: none;
    padding: 10px 0 10px 10px;
    align-items: baseline;

    span {
      color: #7f7f7f;
      font-size: 14px;
    }
  }

  :deep(.el-table) {
    .el-table__cell {
      position: relative;
    }
    .el-button + .el-button {
      margin: 0 3px;
    }
  }
}
</style>
