<template>
  <div class="preselection-list-item">
    <el-table
      ref="tableRef"
      :header-cell-style="{
        'background-color': status ? '#e8e8ea !important' : '',
      }"
      :data="tableData"
      style="width: 100%"
      border
      row-key="id"
      :key="status"
    >
      <!-- 产品图 -->
      <TableColumns compType="productPic" prop="productPic" label="产品图" width="130">
        <template #imgBottom="{ row }">
          <div
            class="same-product-link"
            v-if="(status == 1 || status == 3) && row.productLink && row.sameProductLinkOrderCount > 1"
            @click="handleAction('同链接订单', row)"
          >
            同链接待匹配：{{ row.unMatchSameProductLinkOrderCount || 0 }}/{{ row.sameProductLinkOrderCount }}
          </div>
        </template>
      </TableColumns>
      <!-- 产品信息 -->
      <TableColumns compType="productInfo" prop="productInfo" label="产品信息" minWidth="320" align="left">
        <template #productInfoBtn="{ row }">
          <CopyButton class="btn" plain size="small" :copy-content="handleCopy(row)" />
          <el-button class="btn" size="small" type="primary" @click="handleAction('下载图片', row)">
            下载
          </el-button>
          <CopyButton
            v-if="tabType == 1 && status === 0 && row.shootModel && row.preselectModelList"
            class="btn"
            size="small"
            type="success"
            :async-copy-content="() => handleCopyshootModelLink(row)"
          >
            链接
          </CopyButton>
          <CopyButton
            v-else-if="tabType == 2 && modelList.length"
            class="btn"
            size="small"
            type="success"
            :async-copy-content="() => handleCopyModelLink(row, modelList[0])"
          >
            链接
          </CopyButton>
        </template>
      </TableColumns>
      <el-table-column prop="status" label="订单状态" align="center" width="120" v-if="status == 3">
        <template v-slot="{ row }">
          {{ orderStatusMap[row.status] }}
        </template>
      </el-table-column>
      <el-table-column prop="productPic" label="拍摄模特" align="center" width="130" v-if="status == 0">
        <template v-slot="{ row }">
          <el-image
            v-if="row.shootModel"
            style="width: 90px; height: 120px; cursor: pointer"
            :src="row.shootModel ? $picUrl + row.shootModel.modelPic + '!3x4compress' : ''"
            fit="fill"
            preview-teleported
            @click="() => row.shootModel?.modelPic && showViewer([row.shootModel.modelPic])"
          ></el-image>
          <span v-else>-</span>
          <div>{{ row.shootModel ? row.shootModel.name : '' }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="picCount" label="照片数量" align="center" width="110">
        <template v-slot="{ row }">
          <div class="corner-mark-hint" v-if="row.picCountChange">调</div>
          <div>{{ handleSelectiveAssembly(row.picCount) }}</div>
          <el-button
            v-btn
            v-if="row.referencePic && row.referencePic.length"
            link
            type="primary"
            @click="handleAction('已选参考图', row)"
          >
            已选参考图
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="user" label="订单运营" align="center" width="150" v-if="status != 3">
        <template v-slot="{ row }">
          <div>
            {{ row.merchantCode }}
          </div>
          <div>
            {{ (row.createOrderUserNickName || '-') + '/' + (row.createOrderUserName || '-') }}
          </div>
        </template>
      </el-table-column>

      <el-table-column
        v-if="status == 1 || status == 3"
        prop="shootRequired"
        label="拍摄建议"
        align="center"
        width="100"
      >
        <template v-slot="{ row }">
          <div class="corner-mark-hint" v-if="row.shootRequiredChange">调</div>
          <el-button
            v-btn
            v-if="row.shootRequired?.length && checkPermi(['my:preselection:shootRequired'])"
            link
            type="primary"
            @click="handleAction('拍摄建议', row)"
          >
            查看
          </el-button>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="status == 1 || status == 3"
        prop="sellingPointProduct"
        label="产品卖点"
        align="center"
        width="100"
      >
        <template v-slot="{ row }">
          <div class="corner-mark-hint" v-if="row.sellingPointProductChange">调</div>
          <el-button
            v-btn
            v-if="row.sellingPointProduct?.length && checkPermi(['my:preselection:shootingSuggestion'])"
            link
            type="primary"
            @click="handleAction('产品卖点', row)"
          >
            查看
          </el-button>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <!-- 匹配模特注意事项 -->
      <TableColumns
        v-if="status == 1 || status == 3"
        compType="cautions"
        prop="cautions"
        label="匹配模特注意事项"
        width="200"
        @action="handleAction"
      />

      <!-- <el-table-column v-if="status == 1" prop="cautions" label="模特要求" align="center" width="200">
        <template v-slot="{ row }">
          <div class="corner-mark-hint" v-if="row.cautionsChange">调</div>
          <div class="more-ell" style="--l: 3" v-has-ellipsis:cautionsMore="row" :key="Math.random()">
            <template v-for="(item, i) in row.orderVideoCautionsVO?.cautions" :key="i">
              {{ item.content }}
              <br />
            </template>
          </div>
          <el-button
            v-btn
            v-if="row.cautionsMore || row.orderVideoCautionsVO?.cautionsPics?.length"
            link
            type="primary"
            @click="handleAction('模特要求', row)"
          >
            更多
          </el-button>
        </template>
      </el-table-column>
      <el-table-column
        v-if="status == 1"
        prop="particularEmphasis"
        label="特别强调"
        align="center"
        width="200"
      >
        <template v-slot="{ row }">
          <div class="corner-mark-hint" v-if="row.particularEmphasisChange">调</div>
          <div
            class="more-ell"
            style="--l: 3; white-space: pre-line"
            v-has-ellipsis:particularEmphasisMore="row"
            :key="Math.random()"
          >
            {{ row?.particularEmphasis }}
          </div>
          <el-button
            v-btn
            v-if="row.particularEmphasisMore || row.particularEmphasisPic?.length"
            link
            type="primary"
            @click="handleAction('特别强调', row)"
          >
            更多
          </el-button>
        </template>
      </el-table-column>
      <el-table-column
        v-if="status == 1"
        prop="particularEmphasis"
        label="商品规格要求"
        align="center"
        width="200"
      >
        <template v-slot="{ row }">
          <div class="corner-mark-hint" v-if="row.orderSpecificationRequireChange">调</div>
          <div
            class="more-ell"
            style="--l: 3; white-space: pre-line"
            v-has-ellipsis:orderSpecificationRequireMore="row"
            :key="Math.random()"
          >
            {{ row?.orderSpecificationRequire }}
          </div>
          <el-button
            v-btn
            v-if="row.orderSpecificationRequireMore"
            link
            type="primary"
            @click="handleAction('商品规格要求', row)"
          >
            更多
          </el-button>
        </template>
      </el-table-column> -->
      <!-- <el-table-column prop="cautions" label="拍摄要求" align="center" width="200">
        <template v-slot="{ row }">
          <div class="corner-mark-hint" v-if="row.cautionsChange">调</div>
          <div class="more-ell" style="--l: 3" v-has-ellipsis:cautionsMore="row" :key="Math.random()">
            <template v-for="(item, i) in row.orderVideoCautionsVO?.cautions">
              {{ item.content }}
              <br />
            </template>
          </div>
          <el-button v-btn link type="primary" @click="handleAction('拍摄要求', row)">更多</el-button>
        </template>
      </el-table-column> -->
      <el-table-column prop="contact" label="中文部客服" align="center" width="120">
        <template v-slot="{ row }">
          <div>
            {{ row.contact?.name || '-' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="type" label="匹配次数" align="center" width="130">
        <template v-slot="{ row }">
          <div class="top-tag">
            <RollbackTag :row="row" reject-field="isRejectAfterSubmitModel" />
          </div>
          <div v-if="row.count === 1">首次</div>
          <div v-else>第{{ row.count }}次</div>
          <div v-if="row.matchStatus === 2" style="color: #bb0000">停止匹配</div>
        </template>
      </el-table-column>
      <el-table-column v-if="status == 0" prop="contact" label="英文部客服" align="center" width="120">
        <template v-slot="{ row }">
          <div>
            {{ row.shootModelPersonName || '-' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        v-if="status == 1"
        prop="historyPreselectModelCount"
        label="历史预选模特"
        align="center"
        width="120"
      >
        <template v-slot="{ row }">
          <div>{{ row.historyPreselectModelCount || 0 }}个</div>
          <el-button
            v-btn
            v-if="checkPermi(['my:preselection:history'])"
            link
            type="primary"
            @click="handleAction('历史预选模特', row)"
          >
            查看
          </el-button>
        </template>
      </el-table-column>
      <el-table-column
        v-if="status == 3"
        prop="preselectModelCount"
        label="预选中模特"
        align="center"
        width="120"
      >
        <template v-slot="{ row }">
          <div>{{ row.preselectModelCount || 0 }}个</div>
          <el-button
            v-btn
            v-if="checkPermi(['my:preselection:history'])"
            link
            type="primary"
            @click="handleAction('参与预选模特', { matchId: row.latestMatchId })"
          >
            查看
          </el-button>
        </template>
      </el-table-column>
      <el-table-column v-if="status == 0" prop="time" label="时间" align="center" width="170">
        <template v-slot="{ row }">
          <div>开始：{{ row.startTime || '-' }}</div>
          <div>完成：{{ row.endTime || '-' }}</div>
        </template>
      </el-table-column>
      <el-table-column v-if="status == 0" prop="carryType" label="排单方式" align="center" width="120">
        <template v-slot="{ row }">
          {{ handleCarryType(row) }}
        </template>
      </el-table-column>
      <el-table-column v-if="status == 0" prop="status" label="订单状态" align="center" width="120">
        <template v-slot="{ row }">
          {{ orderStatusMap[row.status] }}
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" :align="'center'" width="170">
        <template v-slot="{ row }">
          <div v-if="row.startTime && status == 1">{{ handleOrderStatusTime(row.startTime) }}</div>
          <el-button
            v-if="status == 0 && checkPermi(['my:preselection:record'])"
            v-btn
            link
            type="primary"
            @click="handleAction('预选记录', row)"
          >
            预选记录
          </el-button>
          <el-button
            v-if="status == 0 && checkPermi(['my:preselection:jump'])"
            v-btn
            link
            type="primary"
            @click="handleAction('跳转订单', row)"
          >
            跳转订单
          </el-button>
          <el-button
            v-if="(status == 1 || status == 3) && checkPermi(['my:preselection:detail'])"
            v-btn
            link
            type="primary"
            @click="routerNewWindow('/order/details/' + row.videoId)"
          >
            订单详情
          </el-button>
          <el-button
            v-if="
              tabType === 1 &&
              (status == 1 || (status == 3 && row.status === orderStatusMap['待匹配'])) &&
              checkPermi(['my:preselection:add'])
            "
            v-btn
            link
            type="primary"
            @click="handleAction('添加预选', row)"
          >
            添加预选
          </el-button>
          <el-button
            v-if="tabType === 1 && status == 1 && checkPermi(['my:preselection:distribute'])"
            v-btn
            link
            type="primary"
            @click="handleAction('添加分发', row)"
          >
            添加分发
          </el-button>
          <el-button
            v-if="(status == 1 || status == 3) && checkPermi(['my:preselection:remark'])"
            v-btn
            link
            type="primary"
            @click="handleAction('备注', row)"
            :class="{ 'btn-red-tip': row.hasComment }"
          >
            备注
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- <el-collapse v-if="status == 1" v-model="activeCollapse">
      <el-collapse-item name="1">
        <template #title> -->
    <div class="flex-start gap-5 more-btn" style="padding-left: 10px; border-bottom: none" v-if="status == 1">
      <span style="color: #7f7f7f">我的预选&emsp;</span>
      <div class="model-avatar-box" v-for="item in modelList" :key="item.id">
        <el-avatar icon="UserFilled" :size="26" :src="$picUrl + item.modelPic + '!1x1compress'" />
        <el-tag class="sel-tag" v-if="item.status === 2" type="warning" size="small" round>选定</el-tag>
      </div>
    </div>
    <!-- </template> -->
    <!-- <template #icon="{ isActive }">
          <el-button v-btn type="primary" link style="margin: 0 8px 0 auto">
            {{ isActive ? '收起' : '展开' }}
          </el-button>
        </template> -->
    <el-table
      v-if="status == 1 || status == 3"
      ref="tableRef"
      :data="modelList"
      style="width: 100%"
      :header-cell-style="{
        'background-color': '#fff !important',
      }"
      border
      row-key="key"
      :row-class-name="tableRowClassName"
    >
      <el-table-column
        prop="info"
        label="模特信息"
        align="center"
        minWidth="300"
        class-name="preselection-model-box"
      >
        <template v-slot="{ row }">
          <div class="flex-start gap-5" style="width: max-content">
            <div class="hint-box" style="pointer-events: none" v-if="row.persons[0].id == store.id">
              <div class="exchange">
                <div class="text">我</div>
              </div>
            </div>
            <el-avatar
              @mouseenter="handleMouseEnter($event, row.id)"
              @mouseleave="handleMouseLeave($event, row.id)"
              class="model-avatar"
              icon="UserFilled"
              :size="40"
              :src="$picUrl + row.modelPic + '!1x1compress'"
            />
            <div>{{ row.name }}</div>
            <div style="color: #999; font-size: 12px">{{ row.account }}</div>
            <model-score
              v-if="row.modelCooperationScore || row.modelCooperationScore === 0"
              :score="row.modelCooperationScore"
              style="line-height: normal"
            />
            <biz-model-type-new :value="row.modelType" />
            <!-- <biz-model-cooperation :value="row.modelCooperation" /> -->
            <!-- <biz-model-type :value="row.modelType" /> -->
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="waits" :label="status == 1 ? '待完成' : '待拍数'" align="center" width="110">
        <template v-slot="{ row }">{{ row.waits }}单</template>
      </el-table-column>
      <el-table-column v-if="status == 3" prop="scheduleType" label="排单方式" align="center" width="120">
        <template v-slot="{ row }">
          {{ handleScheduleType(row) }}
        </template>
      </el-table-column>
      <el-table-column v-else prop="commission" label="合作佣金" align="center" width="110">
        <template v-slot="{ row }">
          {{ row.commission }}{{ handleCommissionUnit(row.commissionUnit) }}
        </template>
      </el-table-column>
      <el-table-column prop="afterSaleRate" label="售后率" align="center" width="110">
        <template v-slot="{ row }">
          {{
            row.afterSaleRate
              ? `${math.multiply(math.bignumber(row.afterSaleRate), math.bignumber(100))}%`
              : '0%'
          }}
        </template>
      </el-table-column>
      <el-table-column prop="carryCount" label="可携带数" align="center" width="110">
        <template v-slot="{ row }">{{ row.carryCount ? `${row.carryCount}单` : '-' }}</template>
      </el-table-column>
      <el-table-column prop="issue" label="英文部客服" align="center" width="120" v-if="status == 1">
        <template v-slot="{ row }">
          <div>
            {{ row.modelPersonName || '-' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="orderType" label="来源" align="center" width="120">
        <template v-slot="{ row }">
          {{ handleAddType(row.addType) }}
        </template>
      </el-table-column>
      <el-table-column prop="modelIntention" label="模特意向" align="center" width="120">
        <template v-slot="{ row }">
          {{ handleStatus(row.modelIntention, 'modelIntention') }}
        </template>
      </el-table-column>
      <el-table-column prop="status" :label="status == 1 ? '沟通状态' : '状态'" align="center" width="120">
        <template v-slot="{ row }">
          <div>{{ handleStatus(row.status, 'status') }}</div>
          <div v-if="row.status === 2 && data.carryType" style="color: var(--el-color-warning)">
            {{ data.carryType == 1 ? '主携带' : '被携带' }}
          </div>
          <div v-else-if="row.status === 2 && data.commission" style="color: var(--el-color-warning)">
            {{ data.commission }}{{ handleCommissionUnit(data.commissionUnit) }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="addTime" label="添加预选时间" align="center" width="120" v-if="status == 1">
        <template v-slot="{ row }">
          <span
            :style="{
              color: handleAddTime(row),
            }"
          >
            {{ handleOrderStatusTime(row.addTime) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="remark" label="淘汰原因" align="center" width="120" v-if="status == 3">
        <template v-slot="{ row }">
          <div @mouseenter="handleShowRemark($event, row)">
            {{ handleOustRemark(row.remark, row.oustType, false) }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="oustTime" label="淘汰时间" align="center" width="120" v-if="status == 3">
        <template v-slot="{ row }">
          {{ handleOrderStatusTime(row.oustTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="selectedTime" label="选定时间" align="center" width="120" v-if="tabType == 2">
        <template v-slot="{ row }">
          <div v-if="row.selectedTime">{{ handleOrderStatusTime(row.selectedTime) }}</div>
          <div>{{ row.selectedTime }}</div>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" :align="'center'" width="170">
        <template v-slot="{ row }">
          <div v-if="tabType === 1 && (store.isAdmin || store.id === row.persons[0].id)">
            <el-button
              v-if="row.status == 0 && checkPermi(['my:preselection:markers'])"
              v-btn
              link
              type="primary"
              @click="handleAction('标记沟通', row)"
            >
              标记沟通
            </el-button>
            <el-button
              v-if="
                (row.status == 1 || row.status == 0) &&
                !curSelectModel &&
                checkPermi(['my:preselection:select'])
              "
              v-btn
              link
              type="primary"
              @click="handleAction('选定', row)"
            >
              选定
            </el-button>
            <el-button
              v-if="(row.status == 1 || row.status == 0) && checkPermi(['my:preselection:out'])"
              v-btn
              link
              type="primary"
              @click="handleAction('淘汰', row)"
            >
              淘汰
            </el-button>
            <el-button
              v-if="row.status == 2 && checkPermi(['my:preselection:update'])"
              v-btn
              link
              type="primary"
              @click="handleAction('修改选定说明', row)"
            >
              修改选定说明
            </el-button>
            <el-button
              v-if="row.status == 2 && checkPermi(['my:preselection:select'])"
              v-btn
              link
              type="primary"
              @click="handleAction('取消选定', row)"
            >
              取消选定
            </el-button>
            <CopyButton
              v-if="status != 3"
              link
              type="primary"
              :async-copy-content="() => handleCopyModelLink2(row)"
            >
              复制链接
            </CopyButton>
            <el-button
              v-btn
              v-if="status == 3 && checkPermi(['my:preselection:confirmOut'])"
              link
              type="primary"
              @click="handleAction('确认淘汰', row)"
            >
              确认淘汰
            </el-button>
            <el-button
              v-btn
              v-if="
                status == 3 &&
                tableData[0].status == orderStatusMap['待匹配'] &&
                tableData[0].latestMatchStatus == 1 &&
                checkOustType(row.oustType) &&
                checkPermi(['my:preselection:confirmOut'])
              "
              link
              type="primary"
              @click="handleAction('恢复预选', row)"
            >
              恢复预选
            </el-button>
            <el-button
              v-btn
              v-if="
                (status == 1 || status == 3) &&
                row.oustType != 7 &&
                row.oustType != 10 &&
                checkPermi(['my:preselection:recommend'])
              "
              link
              type="primary"
              @click="handleAction('排单推荐', row)"
            >
              排单推荐
            </el-button>
            <el-button
              v-if="status != 3 && checkPermi(['my:preselection:shootAttention'])"
              v-btn
              link
              type="primary"
              @click="handleAction('拍摄注意事项', row)"
            >
              拍摄注意事项
            </el-button>
          </div>
          <el-button
            v-if="tabType === 2 && checkPermi(['wait:preselection:remark'])"
            v-btn
            link
            type="primary"
            @click="handleAction('发货备注', row)"
          >
            发货备注
          </el-button>
          <el-button
            v-if="tabType === 2 && row.status == 2 && checkPermi(['my:preselection:submit'])"
            v-btn
            link
            type="primary"
            @click="handleAction('确定提交', row)"
          >
            确定提交
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- <div v-if="moreNumber > 0 && isShowMoreBtn" class="flex-center more-btn">
      <el-button v-btn link type="primary" style="width: 100%" @click="isShowAll = !isShowAll">
        {{ isShowAll ? '收起更多' : `展开更多(${moreNumber})` }}
      </el-button>
    </div> -->
    <!-- </el-collapse-item>
    </el-collapse> -->
  </div>
</template>

<script setup>
import TableColumns from '@/components/TableColumns/index.vue'
import * as math from 'mathjs'
import CopyButton from '@/components/Button/CopyButton.vue'
import RollbackTag from '@/views/order/components/rollbackTag.vue'
import useUserStore from '@/store/modules/user'
import { orderStatusMap, videoFormatOptions } from '@/views/order/list/data.js'
import { bizCommissionUnit } from '@/utils/dict'
import { checkPermi } from '@/utils/permission'
// import DownloadBtn from '@/components/Button/DownloadBtn'
import {
  modelIntentionOptions,
  communicationOptions,
  sourceOptions,
  handleCopy,
  handleSelectiveAssembly,
  isRefund,
  handleOrderStatusTime,
  handleOustRemark,
  oustTypeOptions,
} from '@/views/task/preselection/index.js'
import { createBackstageLink } from '@/api/model/model'
import { useViewer } from '@/hooks/useViewer'
import { useTooltips } from '@/hooks/useTooltips'

const router = useRouter()
function routerNewWindow(path) {
  const { href } = router.resolve({ path })
  window.open(href, '_blank')
}

const { proxy } = getCurrentInstance()

const { showViewer } = useViewer()

const { showTooltips } = useTooltips()

const store = useUserStore()

const props = defineProps({
  data: {
    type: Object,
    required: true,
  },
  status: {
    type: Number,
    default: 1,
  },
  tabType: {
    type: Number,
    default: 1,
  },
  showAll: {
    type: Boolean,
    default: false,
  },
})
const emits = defineEmits(['action', 'hover'])

// const activeCollapse = ref([])
// const isShowAll = ref(false)

const tableData = computed(() => {
  if (props.data) {
    // isShowAll.value = false
    // isShowMoreBtn.value = false
    // moreNumber.value = 0
    return props.status === 1 || props.status === 3
      ? [
          {
            ...props.data,
            cautionsMore: false,
            particularEmphasisMore: false,
            orderSpecificationRequireMore: false,
          },
        ]
      : props.data.map(item => ({
          ...item,
          cautionsMore: false,
          particularEmphasisMore: false,
          orderSpecificationRequireMore: false,
        }))
  }
  return []
})
const curSelectModel = computed(() => {
  if (props.data?.preselectModelList) {
    return props.data.preselectModelList.find(item => item.status === 2)?.id
  }
  return ''
})

// const myModelList = computed(() => {
//   if(props.showAll) return []
//   return props.data?.preselectModelList
//     ? props.data.preselectModelList
//         .map((item, i) => {
//           // addUserId
//           if (item.model.persons[0].id == store.id) {
//             let { id, model, ...rest } = item
//             return {
//               ...model,
//               ...rest,
//               pid: id,
//               key: rest.matchId + '-' + rest.id + '-' + model?.id + '-' + rest.status + '-' + i,
//             }
//           }
//         })
//         .filter(item => item !== undefined)
//     : []
// })

// const moreNumber = ref(0)
// const isShowMoreBtn = ref(false)
// const resultModelList = computed(() => {
//   if (myModelList.value && myModelList.value.length > 0) {
//     // activeCollapse.value = ['1']
//     isShowMoreBtn.value = true
//     moreNumber.value = modelList.value.length - myModelList.value.length
//     return myModelList.value
//   } else {
//     // activeCollapse.value = []
//     isShowMoreBtn.value = false
//     moreNumber.value = 0
//     return modelList.value
//   }
// })

function handleMouseEnter(e, id) {
  emits('hover', e.target, id, true)
}

function handleMouseLeave(e, id) {
  emits('hover', e.target, id, false)
}

const modelList = computed(() => {
  return props.data?.preselectModelList
    ? props.data.preselectModelList.map((item, i) => {
        let { id, model, ...rest } = item
        return {
          ...model,
          modelCommission: model.commission,
          modelCommissionUnit: model.commissionUnit,
          ...rest,
          pid: id,
          videoId: props.data.videoId || '',
          videoCode: props.data.videoCode || '',
          productChinese: props.data.productChinese || '',
          productEnglish: props.data.productEnglish || '',
          productLink: props.data.productLink || '',
          key: rest.matchId + '-' + rest.id + '-' + model?.id + '-' + rest.status + '-' + i,
        }
      })
    : []
})

function handleStatus(val, type) {
  let str = ''
  if (type == 'modelIntention') {
    str = modelIntentionOptions.find(item => item.value == val)
  } else if (type == 'status') {
    str = communicationOptions.find(item => item.value == val)
  }
  return str ? str.label : '-'
}

function handleAction(btn, row) {
  if (btn == '已选参考图' && row.referencePic && row.referencePic.length) {
    showViewer(row.referencePic.map(item => item))
  }
  if (btn == '预选管理') {
    if (isRefund(row.orderVideoRefund)) return
  }
  if (btn == '恢复预选') {
    row = {
      matchId: props.data.latestMatchId,
      modelIds: [row.id],
      isFailRecover: true,
    }
  }
  if (btn == '添加预选' && props.status == 3) {
    row.matchId = row.latestMatchId
  }
  let length = modelList.value?.filter(item => store.isAdmin || store.id === item.persons[0].id).length || 0
  emits('action', btn, row, length)
}

const tableRowClassName = ({ row, rowIndex }) => {
  let className = ''
  if (row.persons[0].id == store.id) {
    className = 'warning-row'
  }
  if (curSelectModel.value) {
    if (row.status != 2) {
      className = 'disabled-row'
    }
  }
  return className
}

function handleCarryType(row) {
  if (row.carryType == 1) {
    return '主携带'
  }
  if (row.carryType == 2) {
    return '被携带'
  }
  if (row.commission) {
    return row.commission + handleCommissionUnit(row.commissionUnit)
  }
  return '-'
}

function handleScheduleType(row) {
  if (row.scheduleType == 1) {
    return row.commission + handleCommissionUnit(row.commissionUnit)
  }
  if (row.carryType == 1) {
    return '主携带'
  }
  if (row.carryType == 2) {
    return '被携带'
  }
  return '-'
}

function handleAddType(val) {
  let b = sourceOptions.find(item => item.value == val)
  return b ? b.label : ''
}
function handleCommissionUnit(val) {
  let b = bizCommissionUnit.find(item => item.value == val)
  return b ? b.label : ''
}

function handleShowRemark(el, row) {
  if (!row.remark) return
  let str = handleOustRemark(row.remark, row.oustType, true)
  if (str) {
    showTooltips(el, `<div style="max-width: 500px;">${str}</div>`)
  }
}

function handleAddTime(row) {
  let t = handleAddType(row.addType)
  if (
    (t == '客服添加' || t == '商家意向') &&
    handleStatus(row.status, 'status') == '沟通中' &&
    handleStatus(row.modelIntention, 'modelIntention') != 'MT想要'
  ) {
    if (row.addTime) {
      let time = new Date().getTime() - new Date(row.addTime).getTime()
      if (time > 1000 * 60 * 60 * 24 * 3) {
        // 72小时
        return 'red'
      }
    }
  }
  return 'inherit'
}

// 是否可恢复预选
function checkOustType(type) {
  let item = oustTypeOptions.find(item => item.value == type)
  if (
    item?.label &&
    (item.label == 'MT不想要' ||
      item.label == '未被选中' ||
      item.label == '超时未选择意向' ||
      item.label == '暂停匹配' ||
      item.label == '订单回退')
  ) {
    return true
  }
  return false
}

function handleCopyshootModelLink(row) {
  return new Promise((resolve, reject) => {
    if (!row.shootModel.id || !row.preselectModelList?.length) {
      proxy.$modal.msgError('复制参数错误')
      return reject()
    }
    proxy.$modal.loading('获取中')
    createBackstageLink(row.shootModel.id)
      .then(res => {
        if (res.data) {
          let obj
          let pmId = row.preselectModelList[0]?.id || undefined
          if (row.videoCode && pmId) {
            obj = {
              vcode: row.videoCode,
              pmId,
              toType: 'p_detail',
            }
            return resolve(
              row.videoCode +
                '\t' +
                row.productChinese +
                '\t' +
                row.productEnglish +
                '\t\r\n' +
                res.data +
                `&to=${obj ? btoa(JSON.stringify(obj)) : ''}`
            )
          }
        }
        proxy.$modal.msgWarning('获取链接错误')
        reject()
      })
      .catch(() => {
        proxy.$modal.msgWarning('获取链接错误')
        reject()
      })
      .finally(() => proxy.$modal.closeLoading())
  })
}
function handleCopyModelLink(row, modelInfo) {
  // console.log(row)
  return new Promise((resolve, reject) => {
    if (!modelInfo.id) {
      proxy.$modal.msgError('复制参数错误')
      return reject()
    }
    proxy.$modal.loading('获取中')
    createBackstageLink(modelInfo.id)
      .then(res => {
        if (res.data) {
          let obj
          if (tableData.value.length && tableData.value[0].videoCode && modelInfo.pid) {
            obj = {
              vcode: tableData.value[0].videoCode,
              pmId: modelInfo.pid,
              toType: 'p_detail',
            }
            return resolve(
              row.videoCode +
                '\t' +
                row.productChinese +
                '\t' +
                row.productEnglish +
                '\t\r\n' +
                res.data +
                `&to=${obj ? btoa(JSON.stringify(obj)) : ''}`
            )
          }
        }
        proxy.$modal.msgWarning('获取链接错误')
        reject()
      })
      .catch(() => {
        proxy.$modal.msgWarning('获取链接错误')
        reject()
      })
      .finally(() => proxy.$modal.closeLoading())
  })
}
function handleCopyModelLink2(row) {
  return new Promise((resolve, reject) => {
    if (!row.id) {
      proxy.$modal.msgError('复制参数错误')
      return reject()
    }
    proxy.$modal.loading('获取中')
    createBackstageLink(row.id)
      .then(res => {
        if (res.data) {
          let obj
          if (tableData.value.length && tableData.value[0].videoCode && row.pid) {
            obj = {
              vcode: tableData.value[0].videoCode,
              pmId: row.pid,
              toType: 'p_detail',
            }
            return resolve(
              row.videoCode +
                '\t' +
                row.productChinese +
                '\t' +
                row.productEnglish +
                '\t\r\n' +
                res.data +
                `&to=${obj ? btoa(JSON.stringify(obj)) : ''}`
            )
          }
        }
        proxy.$modal.msgWarning('获取链接错误')
        reject()
      })
      .catch(() => {
        proxy.$modal.msgWarning('获取链接错误')
        reject()
      })
      .finally(() => proxy.$modal.closeLoading())
  })
}
</script>

<style scoped lang="scss">
@import '@/components/TableColumns/table.scss';

.preselection-list-item {
  // margin: 0 0 10px 0;

  .product-info-box {
    position: relative;

    .product-info-btn {
      position: absolute;
      top: 0;
      right: -3px;

      .btn {
        padding: 2px 4px;
        height: auto;
        font-size: 12px;
      }
    }
  }
  .more-btn {
    border: 1px solid #ebeef5;
    border-top: none;
    padding: 10px 0;
  }
  .productLink {
    position: relative;
    padding-right: 5px;

    :deep(.el-link) {
      display: contents;

      .el-link__inner {
        display: inline;
      }
    }
  }

  .model-avatar-box {
    position: relative;
    display: flex;

    .sel-tag {
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%) scale(0.8) translateY(10px);
      height: 18px;
      padding: 0 5px;
    }
  }

  .btn-red-tip {
    &::after {
      content: '';
      display: block;
      width: 6px;
      height: 6px;
      background-color: red;
      border-radius: 50%;
      margin-left: 3px;
    }
  }

  .same-product-link {
    color: var(--el-color-primary);
    font-size: 12px;
    line-height: 1.1;
    text-align: center;
    width: 100%;
    cursor: pointer;
    position: absolute;
    bottom: 5px;
    left: 0;
    z-index: 2;
  }

  :deep(.el-table) {
    .el-button + .el-button {
      margin: 0 3px;
    }

    .preselection-model-box {
      padding-top: 8px;
      overflow: hidden;

      .hint-box {
        z-index: 999;
        position: absolute;
        right: -6px;
        top: -2px;
        // overflow: hidden;
        width: 32px;
        height: 25px;

        .exchange {
          background: var(--el-color-warning);
          color: #fff;
          font-size: 13px;
          width: 60px;
          transform: translate(-10px, -3px) scale(0.8) rotateZ(45deg);

          .text {
            transform: rotateZ(-45deg) translate(-1px, 1px);
          }
        }
      }
    }
  }
  :deep(.el-collapse) {
    border-top: none;

    .el-collapse-item {
      .el-collapse-item__header {
        background-color: #f8f8f9;
        border-left: 1px solid #ebeef5;
        border-right: 1px solid #ebeef5;
      }
      &__content {
        padding: 0;
      }
    }
  }
}
</style>
