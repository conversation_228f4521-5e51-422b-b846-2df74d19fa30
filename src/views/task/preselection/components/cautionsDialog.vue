<template>
  <div>
    <el-dialog
      align-center
      append-to-body
      title="拍摄要求"
      v-model="isShow"
      width="600px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="close"
    >
      <div class="cautions-item">
        <div class="cautions-item-title fs-0">基础要求</div>
        <div class="cautions-item-tag">
          <el-tag v-for="item in list" :key="item.id" :value="item.id">
            {{ item.title }}
          </el-tag>
        </div>
      </div>
      <div class="cautions-item" style="margin: 15px 0">
        <div class="cautions-item-title fs-0">场景&emsp;&emsp;</div>
        <div class="cautions-item-tag">
          <el-tag v-for="item in list" :key="item.id" :value="item.id">
            {{ item.title }}
          </el-tag>
        </div>
      </div>
      <div class="cautions-item">
        <div class="cautions-item-title fs-0">拍摄内容</div>
        <div class="cautions-item-tag">
          <el-tag v-for="item in list" :key="item.id" :value="item.id">
            {{ item.title }}
          </el-tag>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
const list = [
  { title: '3d3d3d3d3d3d', id: 4 },
  { title: '4d', id: 5 },
  { title: '5d', id: 6 },
  { title: '6d', id: 45 },
]

const isShow = ref(false)
const open = id => {
  isShow.value = true
}

const close = () => {
  isShow.value = false
}

defineExpose({ open, close })
</script>

<style scoped lang="scss">
.cautions-item {
  display: flex;
  &-title {
    font-size: 14px;
    font-weight: 600;
    margin-right: 20px;
  }
  &-tag {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }
}
</style>
