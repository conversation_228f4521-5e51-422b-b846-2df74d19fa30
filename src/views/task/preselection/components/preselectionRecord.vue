<template>
  <el-dialog
    v-model="dialogVisible"
    title="预选记录"
    width="600"
    align-center
    append-to-body
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <template #header="{ titleId, titleClass }">
      <span :id="titleId" :class="titleClass" style="font-weight: bold">预选记录</span>
    </template>
    <div style="height: 570px" v-loading="loading">
      <el-tabs v-model="activeName">
        <el-tab-pane class="tab-box" label="拍摄模特" name="1" v-if="shootModel != null">
          <ModelInfo :item="shootModel" />
          <div class="info-box">
            <div class="title">结算方式</div>
            <div class="flex-start gap-10 item">
              <div class="label">排单类型</div>
              <div class="content" v-if="recordData.scheduleType == 1">佣金结算</div>
              <div class="content" v-else-if="recordData.scheduleType == 2">携带排单</div>
              <div class="content" v-else>-</div>
            </div>
            <div class="flex-start gap-10 item" v-if="recordData.scheduleType == 1">
              <div class="label">佣金</div>
              <div class="content">
                {{ recordData.commission }}{{ handleCommissionUnit(recordData.commissionUnit) }}
              </div>
            </div>
            <div class="flex-start gap-10 item" v-if="recordData.scheduleType == 2">
              <div class="label">携带方式</div>
              <div class="content">
                {{ recordData.carryType == 1 ? '主携带' : '被携带' }}
              </div>
            </div>
            <div class="flex-start gap-10 item" v-if="recordData.overstatement">
              <div class="label fs-0">超额说明</div>
              <div class="text-n-all content">{{ recordData.overstatement }}</div>
            </div>
          </div>
          <div class="info-box">
            <div class="title">发货备注</div>
            <div class="flex-start gap-10 item">
              <div class="label fs-0">备注</div>
              <div class="text-n-all content">{{ recordData.shippingRemark || '-' }}</div>
            </div>
            <div class="flex-start gap-10 item" style="align-items: flex-start">
              <div class="label">发货图片</div>
              <div class="content" v-if="recordData.shippingPics?.length">
                <ViewerImageList :data="recordData.shippingPics" isPreviewAll :show-delete-btn="false" />
              </div>
              <div class="content" v-else>-</div>
            </div>
          </div>
          <div class="info-box">
            <div class="title">拍摄注意事项</div>
            <div class="flex-start gap-10 item">
              <div class="label fs-0">注意内容</div>
              <div class="template-pre content">{{ recordData.shootAttention || '-' }}</div>
            </div>
            <div class="flex-start gap-10 item" style="align-items: flex-start">
              <div class="label">上传图片</div>
              <div class="content" v-if="recordData.shootAttentionObjectKey?.length">
                <ViewerImageList
                  :data="recordData.shootAttentionObjectKey"
                  isPreviewAll
                  :show-delete-btn="false"
                />
              </div>
              <div class="content" v-else>-</div>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="预选模特" name="2">
          <div class="list-box">
            <ModelInfo v-for="item in preselectModelList" :key="item.pid" :item="item" type="details" />
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </el-dialog>
</template>

<script setup>
import ViewerImageList from '@/components/ImagePreview/viewerImageList.vue'
import ModelInfo from '@/views/task/preselection/components/modelInfo.vue'
import { preselectionRecord } from '@/api/order/preselection'
import { bizCommissionUnit } from '@/utils/dict'

const dialogVisible = ref(false)

defineExpose({
  open,
  close,
})

const activeName = ref('1')
const matchId = ref('')
const loading = ref(false)
const recordData = ref({})
const shootModel = ref(null)
const preselectModelList = ref([])

function open(mId) {
  matchId.value = mId
  getInfo()
  dialogVisible.value = true
}

function close() {
  dialogVisible.value = false
}

function handleClose() {
  matchId.value = ''
  recordData.value = {}
  shootModel.value = null
  preselectModelList.value = []
  dialogVisible.value = false
}

function getInfo() {
  loading.value = true
  preselectionRecord(matchId.value)
    .then(res => {
      recordData.value = res.data
      recordData.value.shootAttentionObjectKey = res.data.shootAttentionObjectKey
        ? res.data.shootAttentionObjectKey.split(',')
        : []
      if (res.data.shootModel) {
        shootModel.value = {
          ...res.data.shootModel,
          submitTime: res.data.submitTime,
          modelType: res.data.shootModelType,
          modelPlatform: res.data.shootModelPlatform,
          modelPersonId: res.data.shootModelPersonId,
          modelPersonName: res.data.shootModelPersonName,
          modelCooperation: res.data.shootModelCooperation,
          addType: res.data.shootModelAddType,
        }
        activeName.value = '1'
      } else {
        activeName.value = '2'
        shootModel.value = null
      }
      preselectModelList.value = res.data.preselectModelList?.map(handleModelItem) || []
    })
    .finally(() => {
      loading.value = false
    })
}

function handleModelItem(item) {
  let { id, model, ...rest } = item
  return {
    ...model,
    ...rest,
    pid: id,
  }
}

function handleCommissionUnit(val) {
  let b = bizCommissionUnit.find(item => item.value == val)
  return b ? b.label : ''
}
</script>

<style scoped lang="scss">
.tab-box {
  overflow-y: auto;
  max-height: 510px;
}
.info-box {
  padding: 20px 0;
  border-bottom: 1px solid #ebebeb;

  .title {
    font-size: 16px;
    color: #000;
  }
  .item {
    margin-top: 15px;
    align-items: baseline;

    .label {
      width: 70px;
      font-size: 13px;
      color: #848484;
    }
    .content {
      font-size: 13px;
      color: #000;

      img {
        width: 100px;
        height: 100px;
        cursor: pointer;
      }
    }
  }
}
.list-box {
  padding-right: 10px;
  max-height: 500px;
  overflow-y: auto;
}
</style>
