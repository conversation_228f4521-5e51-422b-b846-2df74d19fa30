<template>
  <div class="model-info-box" :class="{ divider: divider }">
    <div class="flex-between gap-10">
      <div>
        <div class="flex-start gap-10">
          <el-avatar
            class="model-avatar"
            icon="UserFilled"
            :size="50"
            :src="$picUrl + item.modelPic + '!1x1compress'"
          />
          <div class="middle-box">
            <div class="flex-start" style="margin-bottom: 8px">
              <div class="model-name">
                <span>{{ item.name }}</span>
                <span>{{ item.account }}</span>
              </div>
              <!-- <template v-for="dict in biz_nation" :key="item.value">
                <el-text
                  class="flex-start"
                  v-if="item.nation == dict.value"
                >
                  <el-icon><Location style="margin: 1px 2px 0 0;" /></el-icon>
                  <span>{{ dict.label }}</span>
                </el-text>
              </template> -->
              <biz-nation :value="item.nation" tag="text">
                <template v-slot="{ dict }">
                  <span style="display: inline-flex; align-items: center">
                    <el-icon><Location style="margin-bottom: -1px" /></el-icon>
                    <span>{{ dict.label }}</span>
                  </span>
                </template>
              </biz-nation>
            </div>
            <div class="flex-start gap-5" style="margin-bottom: 5px">
              <model-score
                v-if="item.cooperationScore || item.cooperationScore === 0"
                :score="item.cooperationScore"
                style="margin-right: 5px"
              />
              <biz-model-type-new :value="item.modelType || item.type" />
              <!-- <div style="font-size: 12px" v-if="item.cooperationScore">
                {{ item.cooperationScore }}分
              </div>
              <biz-model-cooperation :value="item.cooperation" type="primary" /> -->
            </div>
            <div class="flex-start" style="flex-wrap: wrap; gap: 5px 0">
              <!-- <biz-model-type :value="item.modelType" /> -->
              <biz-model-platform :value="item.modelPlatform" />
              <!-- <biz-model-cooperation :value="item.modelCooperation" /> -->
            </div>
          </div>
        </div>
        <div class="flex-start gap-10 info-item">
          <div class="model-label">英文部客服</div>
          <div class="model-content">
            {{ item.persons && item.persons.length > 0 ? item.persons[0]?.name : '' }}
          </div>
          <div class="model-label" style="margin-left: 20px; width: 60px">模特来源</div>
          <div class="model-content">
            {{ sourceOptions.find(data => data.value == item.addType)?.label || '-' }}
          </div>
        </div>
        <div class="flex-start gap-10 info-item" v-if="type === 'select'">
          <div class="model-label">选定时间</div>
          <div class="model-content">{{ item.selectedTime }}</div>
        </div>
        <div class="flex-start gap-10 info-item" v-if="!type">
          <div class="model-label">提交时间</div>
          <div class="model-content">{{ item.submitTime }}</div>
        </div>
      </div>
      <div class="remark-box" v-if="type === 'details'">
        {{ item.remark || handleRemark(item.oustType) }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { oustTypeOptions, sourceOptions } from '@/views/task/preselection/index.js'

const props = defineProps({
  item: {
    type: Object,
    default: () => ({}),
  },
  type: {
    type: String,
    default: '',
  },
  divider: {
    type: Boolean,
    default: true,
  },
})

function handleRemark(val) {
  let res = oustTypeOptions.find(item => item.value == val)
  return res ? res.label : ''
}
</script>

<style scoped lang="scss">
.model-info-box {
  padding: 10px 10px 20px 0;

  &.divider {
    border-bottom: 1px solid #ebebeb;
  }

  .middle-box {
    max-width: 325px;
  }

  .model-name {
    font-size: 15px;
    color: #000000;
    margin-right: 10px;

    span:last-child {
      margin-left: 10px;
      font-size: 13px;
      color: #848484;
    }
  }
  .info-item {
    margin-top: 15px;
    align-items: baseline;

    .model-label {
      width: 70px;
      font-size: 13px;
      color: #848484;
    }
    .model-content {
      font-size: 13px;
      color: #000;
    }
  }
  .remark-box {
    max-width: 150px;
    max-height: 80px;
    overflow-y: auto;
  }
}
</style>
