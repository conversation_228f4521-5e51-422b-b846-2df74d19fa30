<template>
  <div>
    <el-dialog
      align-center
      append-to-body
      title=""
      v-model="isShow"
      width="500px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <div class="box-padding" id="imageBox" v-loading="loading">
        <div class="box-head">
          <div class="box-head-left">
            <div class="left-top">
              <div class="text-warp">
                <div class="fs-0 title">Code：</div>
                <div class="one-ell title" id="imageContent" style="font-size: 14px">
                  {{ formData.videoCode || '' }}
                </div>
              </div>
              <div class="text-warp">
                <div class="fs-0 title">Product Name：</div>
                <div class="one-ell content" id="imageContent">{{ formData.productEnglish || '' }}</div>
              </div>
              <div class="text-warp">
                <div class="fs-0 title">Video Duration：</div>
                <div class="one-ell content" id="imageContent">{{ formData.videoDuration || '' }}s</div>
              </div>
              <div class="text-warp" v-if="formData.videoStyle != 2 && formData.platform != 3">
                <div class="fs-0 title">Video Style：</div>
                <div class="one-ell content" id="imageContent">
                  {{
                    formData.videoStyle == 0 ? 'Amazon style' : formData.videoStyle == 1 ? 'TikTok style' : ''
                  }}
                </div>
              </div>
              <div class="text-warp">
                <div class="fs-0 title">Video Format：</div>
                <div class="one-ell content" id="imageContent">
                  {{ videoFormat.find(item => item.value == formData.videoFormat)?.label || '' }}
                </div>
              </div>
              <div class="text-warp" v-if="formData.picCount">
                <div class="fs-0 title">Take Photos：</div>
                <div class="one-ell content" id="imageContent">{{ formData.surplusPicCount }} photos</div>
              </div>
            </div>
            <div class="text-chunk" v-if="formData.referenceVideoLink">
              Please note, before filming, make sure to fnd and watch the reference video in our chat history
              to ensure the filming meets the requirerents.
            </div>
          </div>
          <div
            v-if="image"
            class="fs-0"
            style="
              width: 160px;
              background-repeat: no-repeat;
              background-size: contain;
              background-position: center;
            "
            :style="{ backgroundImage: 'url(' + image + ')' }"
          >
            <!-- <el-image
              style="width: 160px; height: 210px"
              :src="formData.productPic ? $picUrl + formData.productPic : ''"
            >
              <template #error>
                <div class="image-slot">
                  <img :src="$picUrl + 'static/assets/no-img.png'" alt="" style="width: 100%; height: 100%" />
                </div>
              </template>
            </el-image> -->
            <!-- <el-image style="width: 100%; height: 100%" src="../../../../../src/assets/images/111111.png">
              <template #error>
                <div class="image-slot">
                  <img :src="$picUrl + 'static/assets/no-img.png'" alt="" />
                </div>
              </template>
            </el-image> -->
          </div>
          <div v-else>
            <img :src="$picUrl + 'static/assets/no-img.png'" alt="" style="width: 160px; height: 210px;object-fit: contain;" />
          </div>

        </div>
        <div class="text-content" v-if="formData.shootRequired && formData.shootRequired.length > 0">
          <div class="text-content-title">Shooting suggerstions：</div>
          <div class="text-content-items" v-for="(item, index) in formData.shootRequired" :key="index">
            <!-- <div class="title-icon">{{ item.title }}：</div> -->
            <div class="title-content">{{ item.content }}</div>
          </div>
        </div>
        <div
          class="text-content"
          v-if="formData.sellingPointProduct && formData.sellingPointProduct.length > 0"
        >
          <div class="text-content-title">Product selling points：</div>
          <div class="text-content-items">
            <div class="title-content">{{ formData.sellingPointProduct }}</div>
          </div>
          <!-- <div class="text-content-items" v-for="(item, index) in formData.shootingSuggestion" :key="index">
            <div class="title-icon">{{ item.title }}：</div>
            <div class="title-content">{{ formData.shootingSuggestion }}</div>
          </div> -->
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="close">取 消</el-button>
          <el-button type="primary" @click="confirm">下 载</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { videoOrderDetails } from '@/api/order/order'

import { ElMessage } from 'element-plus'
import html2canvas from 'html2canvas'
const loading = ref(false)

const isShow = ref(false)

const emits = defineEmits(['success'])

const videoFormat = [
  {
    label: 'Horizontal recording',
    value: '1',
  },
  {
    label: 'Vertical shooting',
    value: '2',
  },
]
const videoStyleList = [
  {
    label: 'Amazon style',
    value: '0',
  },
  {
    label: 'TikTok styles',
    value: '1',
  },
]

const { proxy } = getCurrentInstance()
const pathHead = proxy.$picUrl
const image = computed(() => {
  return formData.value.productPic ? pathHead + formData.value.productPic : ''
})

const formData = ref({})
const open = id => {
  videoOrderDetails(id).then(res => {
    if (res.data.orderVideoSimpleVO) {
      formData.value = res.data.orderVideoSimpleVO
    }
  })
  isShow.value = true
}

const close = () => {
  formData.value = {}
  // const contentDom = document.querySelectorAll('.content')
  //   if (contentDom) {
  //     const contentArray = Array.from(contentDom)
  //     contentArray.forEach(dom => {
  //       dom.style.whiteSpace = 'nowrap'
  //     })
  //   }
  isShow.value = false
}

// 保存二维码截图
async function saveQrCode() {
  try {
    const dom = document.getElementById('imageBox')

    if (!dom) {
      ElMessage.error('保存出错了！')
      console.error('Element null')
      return
    }
    // const contentDom = document.querySelectorAll('.content')
    // if (contentDom) {
    //   const contentArray = Array.from(contentDom)
    //   contentArray.forEach(dom => {
    //     dom.style.whiteSpace = 'normal'
    //   })
    // }

    const canvas = await html2canvas(dom, {
      useCORS: true,
      scale: 2,
      allowTaint: true,
      backgroundColor: '#fff',
    })
    const img = canvas.toDataURL('image/png')
    const a = document.createElement('a')
    a.href = img
    a.download = '产品需求图.png'
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    ElMessage.success('下载成功')
    close()
    loading.value = false
  } catch (error) {
    console.error('Error capturing element:', error)
    ElMessage.error('下载出错了！')
    loading.value = false
  }
}

function confirm() {
  loading.value = true
  saveQrCode()
}

defineExpose({ open, close })
</script>

<style lang="scss" scoped>
.box-padding {
  padding: 0 4px;
}

.box-head {
  display: flex;
  justify-content: space-between;
  &-left {
    margin-right: 16px;
    display: flex;
    flex-direction: column;
  }
  .left-top {
    flex: 1;
    display: grid;
    align-items: center;
    gap: 6px 0;
  }
  :deep(.el-image__inner) {
    object-fit: contain;
  }
}

.text-warp {
  word-break: break-all;
  line-break: anywhere;
  white-space: break-spaces;
  display: flex;
  align-items: baseline;
  .title {
    font-weight: 500;
    font-size: 16px;
    color: #333333;
  }
  .content {
    max-width: 200px;
    line-height: 1.2;
    white-space: normal;
    color: #777777;
  }
}
.text-chunk {
  color: #333333b3;
  padding: 10px;
  font-weight: 500;
  font-size: 12px;
  background: #feefe4;
  border-radius: 10px;
}
.text-content {
  &-title {
    margin-top: 16px;
    font-weight: 500;
    font-size: 16px;
    color: #333333;
  }
  &-items {
    display: grid;
    margin-top: 8px;
    color: #777777;
  }
  .title-icon::before {
    content: '';
    display: inline-block;
    width: 12px;
    height: 12px;
    background-color: #333333;
    border-radius: 50%;
    margin-right: 4px;
  }
  .title-content {
    margin-top: 2px;
    line-break: anywhere;
    word-wrap: break-word;
    white-space: pre-wrap;
  }
}
</style>
