<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '选定说明' : '选定模特'"
    width="600"
    align-center
    append-to-body
    destroy-on-close
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
    style="font-weight: 600"
  >
    <div class="form-box" v-loading="loading">
      <ModelInfo v-if="isEdit" :item="modelInfo" type="select" />
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        :disabled="disabled"
        :validate-on-rule-change="false"
        label-width="120px"
      >
        <div class="title">结算方式</div>
        <el-form-item label="排单类型" prop="scheduleType">
          <el-radio-group v-model="form.scheduleType">
            <el-radio-button :value="1">佣金结算</el-radio-button>
            <el-radio-button :value="2">携带排单</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="form.scheduleType === 1" label="" prop="commission" :rules="commissionRules">
          <el-input v-model="form.commission" clearable style="width: 300px" placeholder="请输入佣金">
            <template #prepend>
              <el-select v-model="form.commissionUnit" placeholder="请选择单位" style="width: 100px">
                <el-option
                  v-for="item in bizCommissionUnit"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </template>
          </el-input>
          <span style="margin-left: 10px; color: red" v-if="form.originCommission >= 0">
            原佣金：{{ form.originCommission }}{{ handleUnit(form.originCommissionUnit) }}
          </span>
        </el-form-item>
        <el-form-item label="超额说明" prop="overstatement" v-if="showOverstatement">
          <el-input
            v-model="form.overstatement"
            type="textarea"
            :rows="4"
            placeholder="请输入超额说明"
            style="width: 80%"
            maxlength="800"
            show-word-limit
            clearable
          />
        </el-form-item>
        <template v-if="form.scheduleType === 2">
          <el-form-item label="携带类型" prop="carryType">
            <el-radio-group v-model="form.carryType">
              <el-radio-button :value="1">主携带</el-radio-button>
              <el-radio-button :value="2">被携带({{ carriedCount.leftCarryCount }}单)</el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item v-if="form.carryType === 1" label="" prop="mainCarryCount">
            <el-input-number
              title=""
              v-model="form.mainCarryCount"
              :min="0"
              :max="9999"
              controls-position="right"
              style="width: 200px"
              placeholder="请输入携带数量"
              clearable
            />
          </el-form-item>
          <el-form-item v-if="form.carryType === 2" label="" prop="mainCarryVideoId">
            <el-select v-model="form.mainCarryVideoId" placeholder="请选择主携带订单" style="width: 240px">
              <el-option
                v-for="item in mainCarryVideoList"
                :key="item.videoId"
                :label="item.videoCode"
                :value="item.videoId"
              >
                <template #default>
                  <div class="flex-between">
                    <div>{{ item.videoCode }}</div>
                    <div>{{ item.leftCarryCount }}/{{ item.mainCarryCount }}</div>
                  </div>
                </template>
              </el-option>
            </el-select>
          </el-form-item>
        </template>
        <div class="title">发货备注</div>
        <el-form-item label="备注" prop="shippingRemark">
          <el-input
            v-model="form.shippingRemark"
            type="textarea"
            :rows="4"
            placeholder="请输入发货备注"
            style="width: 100%"
            maxlength="800"
            show-word-limit
            clearable
          />
        </el-form-item>
        <div>{{ form.shippingPic }}</div>

        <el-form-item label="发货图片">
          <!-- <ViewerImageList
            v-if="form.shippingPic && form.shippingPic.length > 0"
            :data="form.shippingPic"
            is-preview-all
            urlName="picUrl"
            :show-delete-btn="true"
            @delete="handleFileDelete"
          />
          <PasteUpload
            v-if="!form.shippingPic || form.shippingPic.length < 3"
            v-model="form.shippingPic"
            :limit="3"
            :bucketType="'order'"
            :size="5"
          /> -->
          <PasteUpload
            v-model="fileList"
            v-if="fileList.length < 3"
            style="width: 100%"
            :limit="3"
            :alwaysShow="true"
            :bucketType="'order'"
            :isClear="true"
            :size="5"
          />
          <div v-if="fileList.length < 3" style="width: 100%; margin-bottom: 10px">
            请上传大小不超过
            <span style="color: #d9001b">5M</span>
            ，格式为
            <span style="color: #d9001b">png/jpg/jpeg</span>
            的图片,最多支持上传3张
          </div>
          <ViewerImageList
            urlName="picUrl"
            :data="fileList"
            is-preview-all
            @delete="handleFileDelete($event, 'file')"
          />
        </el-form-item>
        <div class="title">拍摄注意事项</div>
        <el-form-item label="注意内容" prop="shootAttention" style="position: relative">
          <el-input
            v-model="form.shootAttention"
            type="textarea"
            clearable
            :rows="4"
            placeholder="请用英文填写需要模特注意的拍摄注意事项，填写内容将会同步在模特端的产品详情中供模特查看"
          />
          <div class="textarea-limit">{{ form.shootAttention.length || 0 }}/3000</div>
        </el-form-item>
        <el-form-item label="上传图片" prop="shootAttentionObjectKey">
          <PasteUpload
            v-model="form.shootAttentionObjectKey"
            v-if="form.shootAttentionObjectKey?.length < 5"
            style="width: 100%"
            :limit="5"
            :alwaysShow="true"
            :bucketType="'order'"
            :isClear="true"
            :size="5"
            paste-disabled
          />
          <div
            v-if="form.shootAttentionObjectKey?.length < 5"
            style="width: 100%; margin: 10px 0; line-height: 1"
          >
            请上传大小不超过
            <span style="color: #d9001b">5M</span>
            ，格式为
            <span style="color: #d9001b">png/jpg/jpeg</span>
            的图片,最多支持上传5张
          </div>
          <ViewerImageList
            urlName="picUrl"
            :data="form.shootAttentionObjectKey"
            is-preview-all
            @delete="handleFileDelete($event, 'shootAttentionObjectKey')"
          />
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div class="flex-center">
        <el-button v-btn @click="close">取消</el-button>
        <el-button v-btn type="primary" :disabled="disabled" @click="onConfirm">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import ModelInfo from '@/views/task/preselection/components/modelInfo.vue'
import PasteUpload from '@/components/FileUpload/pasteUpload.vue'
import ViewerImageList from '@/components/ImagePreview/viewerImageList.vue'
import {
  selectedModel,
  selectModelMainCarry,
  getSelectModelInfo,
  editSelectedModel,
  checkModelAnOrder,
} from '@/api/order/preselection'
import { handleSelectedModelStatus } from '@/views/task/preselection/index'
import { bizCommissionUnit } from '@/utils/dict'
import { chinese_reg } from '@/utils/RegExp'
import { ElMessageBox } from 'element-plus'
import { unescapeHtmlMethod } from '@/utils/index.js'
const { proxy } = getCurrentInstance()

defineExpose({
  show,
  open,
  close,
})

const emits = defineEmits(['success', 'previews'])

const dialogVisible = ref(false)
const matchId = ref('')
const modelId = ref('')
const editId = ref('')
const loading = ref(false)
const disabled = ref(false)
const formRef = ref()
const form = ref({
  scheduleType: 1,
  commission: '',
  commissionUnit: bizCommissionUnit[0].value,
  carryType: 1,
  mainCarryCount: 0,
  mainCarryVideoId: '',
  shippingPic: [],
  overstatement: '',
  shippingRemark: '',
  shootAttention: '',
  shootAttentionObjectKey: [],
})
const fileList = ref([])
const mainCarryVideoList = ref([])
const isEdit = ref(false)
const modelInfo = ref({})

const rules = {
  scheduleType: [{ required: true, message: '请选择排单类型', trigger: 'blur' }],
  shippingRemark: [{ required: true, message: '请输入发货备注', trigger: 'blur' }],
  overstatement: [{ required: true, message: '请输入超额说明', trigger: 'blur' }],
  carryType: [{ required: true, message: '请选择携带类型', trigger: 'blur' }],
  mainCarryCount: [{ required: true, message: '请输入携带数量', trigger: 'blur' }],
  mainCarryVideoId: [{ required: true, message: '请选择主携带订单', trigger: 'blur' }],
  shootAttention: [
    { required: false, trigger: 'blur' },
    { validator: checkShootAttention, trigger: 'change' },
  ],
}
const commissionRules = [{ required: true, validator: checkCommission, trigger: 'blur' }]

function checkCommission(rule, value, callback) {
  if (!value && value != 0) {
    return callback(new Error('请输入佣金'))
  }
  if (isNaN(value)) {
    return callback(new Error('请输入数字'))
  }
  const numberReg = /^\d+(\.\d{1,2})?$/
  if (!numberReg.test(value)) {
    return callback(new Error('只能输入二位小数'))
  }
  if (value > 99999) {
    return callback(new Error('不能大于99999.00'))
  }
  if (!form.value.commissionUnit) {
    return callback(new Error('请选择佣金单位'))
  }
  return callback()
}
function checkShootAttention(rule, value, callback) {
  if (value) {
    if (value.length > 3000) {
      return callback(new Error('最多不能超过3000个字符'))
    }
    if (chinese_reg.test(value)) {
      return callback(new Error('请输入英文'))
    }
  }
  return callback()
}

const showOverstatement = computed(() => {
  if (
    form.value.scheduleType == 1 &&
    (form.value.commissionUnit !== form.value.originCommissionUnit ||
      (form.value.originCommission >= 0 && form.value.commission > form.value.originCommission))
  ) {
    return true
  }
  return false
})
const productLink = ref('')
function open(id, mid, pid, proL) {
  matchId.value = id
  modelId.value = mid
  editId.value = pid
  productLink.value = proL
  dialogVisible.value = true
  isEdit.value = false
  getInfo()
}
function show(id, mid, pid) {
  matchId.value = id
  modelId.value = mid
  editId.value = pid
  dialogVisible.value = true
  isEdit.value = true
  getInfo()
}
function close() {
  dialogVisible.value = false
}

function handleClose() {
  form.value = {
    scheduleType: 1,
    commission: '',
    commissionUnit: bizCommissionUnit[0].value,
    carryType: 1,
    mainCarryCount: 0,
    mainCarryVideoId: '',
    shippingRemark: '',
    overstatement: '',
    shippingPic: [],
    shootAttention: '',
    shootAttentionObjectKey: [],
  }
  productLink.value = ''
  modelInfo.value = {}
  fileList.value = []
  disabled.value = false
  formRef.value.clearValidate()
}

function getSelectList(id, code, cCount, mMcount) {
  selectModelMainCarry({
    matchId: matchId.value,
    modelId: modelId.value,
  }).then(res => {
    mainCarryVideoList.value = res.data || []
    if (id && code && !mainCarryVideoList.value.some(item => item.videoId === id)) {
      mainCarryVideoList.value.unshift({
        videoId: id,
        videoCode: code,
        leftCarryCount: cCount,
        mainCarryCount: mMcount,
      })
    }
    if (!isEdit.value) {
      mainCarryVideoList.value = mainCarryVideoList.value.filter(
        item => item.leftCarryCount !== null && item.leftCarryCount !== 0
      )
    }
  })
}

const carriedCount = ref({})
function getInfo() {
  loading.value = true
  getSelectModelInfo({
    matchId: matchId.value,
    modelId: modelId.value,
  })
    .then(res => {
      if (res.code === 200) {
        getSelectList(
          res.data.mainCarryVideoId,
          res.data.mainCarryVideoCode,
          res.data.leftCarryCount,
          res.data.mainCarryCount
        )
        carriedCount.value = {
          leftCarryCount: res.data.leftCarryCount,
          mainCarryCount: res.data.mainCarryCount,
        }
        modelInfo.value = {
          ...res.data.preselectModelVO,
          selectedTime: res.data.selectedTime,
          modelType: res.data.modelType,
          modelPlatform: res.data.modelPlatform,
          modelPersonId: res.data.modelPersonId,
          modelPersonName: res.data.modelPersonName,
          modelCooperation: res.data.modelCooperation,
          addType: res.data.addType,
        }
        let originCommissionUnit = ''
        if (res.data.originCommission >= 0 && res.data.originCommissionUnit) {
          originCommissionUnit = res.data.originCommissionUnit
        }
        form.value = {
          carryType: res.data.carryType,
          commission: res.data.commission,
          commissionUnit:
            res.data.commissionUnit || res.data.originCommissionUnit || bizCommissionUnit[0].value,
          originCommission: res.data.originCommission,
          originCommissionUnit,
          mainCarryCount: res.data.mainCarryCount,
          mainCarryVideoId: res.data.mainCarryVideoId,
          scheduleType: res.data.scheduleType,
          shippingRemark: res.data.shippingRemark,
          overstatement: res.data.overstatement || '',
          shootAttention: unescapeHtmlMethod(res.data.shootAttention) || '',
          shootAttentionObjectKey: res.data.shootAttentionObjectKey
            ? res.data.shootAttentionObjectKey.split(',').map(item => ({
                picUrl: item,
              }))
            : [],
        }
        if (res.data.shippingPics?.length) {
          fileList.value = res.data.shippingPics.map(item => ({
            url: item,
            picUrl: item,
          }))
        }
        disabled.value = false
      }
    })
    .catch(() => {
      disabled.value = true
    })
    .finally(() => {
      loading.value = false
    })
}

function handleUnit(val) {
  if (val) {
    return bizCommissionUnit.find(item => item.value == val)?.label || ''
  }
  return ''
}

function handleFileDelete(index, type) {
  if (type === 'file') {
    fileList.value.splice(index, 1)
  } else if (type === 'shootAttentionObjectKey') {
    form.value.shootAttentionObjectKey.splice(index, 1)
  }
}

function onConfirm() {
  formRef.value.validate(valid => {
    if (valid) {
      disabled.value = true
      let params = {
        id: matchId.value,
        scheduleType: form.value.scheduleType,
        shippingRemark: form.value.shippingRemark,
        overstatement: showOverstatement.value ? form.value.overstatement : '',
        shootAttention: form.value.shootAttention?.trim() || null,
        shootAttentionObjectKey: form.value.shootAttentionObjectKey.length
          ? form.value.shootAttentionObjectKey.map(item => item.picUrl)
          : null,
      }
      // 排单类型
      if (params.scheduleType === 1) {
        params.commission = form.value.commission
        params.commissionUnit = form.value.commissionUnit
      } else {
        // 携带类型
        params.carryType = form.value.carryType
        if (params.carryType === 1) {
          params.mainCarryCount = form.value.mainCarryCount
        } else {
          params.mainCarryVideoId = form.value.mainCarryVideoId
        }
      }
      if (fileList.value.length) {
        params.shippingPic = fileList.value.map(item => item.picUrl)
      }
      params.matchPreselectModelId = editId.value

      if (isEdit.value) {
        proxy.$modal.loading('保存中')
        editSelectedModel(params)
          .then(res => {
            proxy.$modal.msgSuccess('保存成功')
            emits('success')
            close()
          })
          .finally(() => {
            disabled.value = false
            proxy.$modal.closeLoading()
          })
        return
      }
      handleCommit(params)
    }
  })
}

async function handleCommit(params) {
  let hint = ''
  if (productLink.value) {
    let { data } = await checkModelAnOrder({ productLink: productLink.value, modelId: modelId.value })
    hint = data
  }
  if (hint) {
    ElMessageBox.confirm('该产品已有与选定模特一致的订单，是否确认选中该模特？', '提醒', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(() => {
        handleSave(params)
        disabled.value = false
      })
      .catch(() => {
        disabled.value = false
      })
  } else {
    handleSave(params)
  }
}
function handleSave(params) {
  proxy.$modal.loading('提交中')
  selectedModel(params)
    .then(res => {
      if (res.code === 200) {
        handleSelectedModelStatus(res.data)
          .then(() => {
            proxy.$modal.msgSuccess('操作成功')
            emits('success')
            close()
          })
          .catch(msg => {
            proxy.$modal.msgError(msg)
            // proxy.$modal.alert(msg, '提示', {
            //   confirmButtonText: '知道了',
            // }).then(() => {
            //   // proxy.$modal.loading('正在删除中')
            //   // selectedModel({
            //   //   isClear: 1,
            //   //   ...params,
            //   // })
            //   //   .then(res => {
            //   //     proxy.$modal.msgSuccess('操作成功')
            //   //     emits('success')
            //   //   })
            //   //   .finally(() => {
            //   //     proxy.$modal.closeLoading()
            //   //   })
            // })
          })
      }
    })
    .finally(() => {
      disabled.value = false
      proxy.$modal.closeLoading()
    })
}
</script>

<style scoped lang="scss">
:deep(.el-radio-button) {
  margin: 0 10px 10px 0;

  &.is-active {
    box-shadow: none;
  }

  .el-radio-button__inner {
    border-left: var(--el-border);
    border-radius: var(--el-border-radius-base);
  }
}
:deep(.el-radio-group) {
  .el-radio-button.is-active {
    .el-radio-button__inner {
      box-shadow: none;
    }
  }
}
.form-box {
  font-weight: 500;

  .textarea-limit {
    position: absolute;
    bottom: 5px;
    right: 10px;
    background-color: #fff;
    line-height: 1;
  }
}
.title {
  margin: 10px;
  font-size: 16px;
  color: #000;
}
</style>
