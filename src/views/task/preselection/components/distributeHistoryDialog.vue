<template>
  <el-dialog
    v-model="dialogVisible"
    title="历史分发记录"
    width="700"
    align-center
    append-to-body
    destroy-on-close
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
    style="font-weight: 600"
  >
    <div class="list-box" v-loading="loading">
      <el-empty
        v-if="!historyList.length"
        description="暂无数据"
        :image-size="80"
        style="margin-top: 50px"
      />

      <div class="flex-between gap-10 history-list" v-for="(row, index) in historyList" :key="row.id">
        <div>视频编码：{{ row.videoCode || '-' }}</div>
        <div>分发时间：{{ row.addTime || '-' }}</div>
        <div>{{ handleStatus(row.distributionResult, row.distributionResultCause) }}</div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import { distributionResultOptions, distributionResultCauseOptions } from '@/views/task/preselection/index.js'
import { distributionRecord } from '@/api/order/preselection'

defineExpose({
  open,
  close,
})

const dialogVisible = ref(false)
const loading = ref(false)
const historyList = ref([])

function open(id) {
  getList(id)
  dialogVisible.value = true
}

function close() {
  dialogVisible.value = false
}

function handleClose() {
  loading.value = false
  historyList.value = []
}

function handleStatus(val, val2) {
  let str = []
  let distr = distributionResultOptions.find(item => item.value == val)
  if (distr?.label) {
    str.push(distr.label)
  }
  let cause = distributionResultCauseOptions.find(item => item.value == val2)
  if (cause?.label) {
    str.push(cause.label)
  }
  return str.join('-') || '-'
}

function getList(id) {
  loading.value = true
  distributionRecord({
    modelId: id,
  })
    .then(res => {
      if (res.data) {
        historyList.value = res.data
      }
    })
    .finally(() => {
      loading.value = false
    })
}
</script>

<style scoped lang="scss">
.list-box {
  font-weight: 500;
  min-height: 350px;
  max-height: 500px;
  overflow-y: auto;

  .history-list {
    height: 40px;
    border-bottom: 1px solid #e4e4e4;

    div {
      min-width: 165px;
    }
  }
}
</style>
