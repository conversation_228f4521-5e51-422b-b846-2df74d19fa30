<template>
  <div style="padding: 20px">
    <ElTablePage
      ref="tableRef"
      :columns="columns"
      :data="tableData"
      :loading="tableLoading"
      :currentPage="pageNum"
      :pageSize="pageSize"
      :total="total"
      :tableAction="{
        width: '170',
        fixed: 'right',
      }"
      @page-change="pageChange"
    >
      <template #tableHeader>
        <div style="margin-bottom: 10px">
          <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="68px" @submit.prevent>
            <el-form-item label="搜索">
              <el-input
                v-model="queryParams.keyword"
                clearable
                style="width: 600px"
                placeholder="可搜索订单号、产品名、客户名、视频编码或英文部客服"
              ></el-input>
            </el-form-item>
            <el-form-item label="状态" prop="status">
              <el-select
                v-model="queryParams.status"
                placeholder="请选择"
                multiple
                clearable
                collapse-tags
                style="width: 180px"
              >
                <el-option
                  v-for="item in statusList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button v-btn type="primary" native-type="submit" @click="onQuery">搜索</el-button>
              <el-button v-btn @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
          <div>
            当前已被催单{{ reminderData.total }}次，其中有
            <span style="color: #f59a23">未处理{{ reminderData.untreatedTotal }}条</span>
          </div>
        </div>
      </template>
      <template #productPic="{ row }">
        <el-image
          style="width: 90px; height: 90px; cursor: pointer"
          :src="$picUrl + row.productPic + '!1x1compress'"
          fit="fill"
          preview-teleported
          @click="showViewer([$picUrl + row.productPic])"
        >
          <template #error>
            <div class="flex-column" style="height: 100%; justify-content: center; gap: 10px">
              <el-icon :size="30" color="#ccc"><Picture /></el-icon>
            </div>
          </template>
        </el-image>
      </template>
      <template #productChinese="{ row }">
        <div>{{ row.productChinese }}</div>
        <div>{{ row.productEnglish }}</div>
      </template>
      <template #payTime="{ row }">
        <div>{{ row.payTime }}</div>
      </template>
      <template #status="{ row }">
        <div style="color: #f59a23" v-if="row.status == '1'">未处理</div>
        <div v-if="row.status == '2'">已确认</div>
        <div v-if="row.status == '3'">已完成</div>
      </template>

      <template #tableAction="{ row }">
        <el-button
          v-btn
          link
          type="primary"
          @click="routerNewWindow('/order/details/' + row.videoId)"
          v-hasPermi="['task:reminder:detail']"
        >
          订单详情
        </el-button>
        <el-button
          v-btn
          link
          type="primary"
          @click="routerNewWindow1('/order/list', row.videoCode)"
          v-hasPermi="['task:reminder:skip']"
        >
          跳转订单
        </el-button>
        <!-- @click="goOrderList(row.videoCode)" -->
        <!-- routerNewWindow('/order/list?videoCode=' +row.videoCode) -->
      </template>
    </ElTablePage>
  </div>
</template>

<script setup>
import ElTablePage from '@/components/Table/ElTablePage'
import { Search } from '@element-plus/icons-vue'
const router = useRouter()
import { remindRecord, getRemindCount } from '@/api/task/index'
import { parseTime } from '@/utils/ruoyi'
import { useViewer } from '@/hooks/useViewer'
const { showViewer } = useViewer()

const statusList = [
  { label: '未处理', value: '1' },
  { label: '已确认', value: '2' },
  { label: '已完成', value: '3' },
]
const columns = [
  {
    prop: 'videoCode',
    label: '视频编码',
    minWidth: '150',
  },
  { slot: 'productPic', prop: 'productPic', label: '产品图', width: '150' },
  {
    slot: 'productChinese',
    prop: 'productChinese',
    label: '产品名称',
    minWidth: '230',
  },
  {
    prop: 'businessName',
    label: '客户名称',
    minWidth: '230',
    handle: (val, row) => {
      return row.createOrderBusiness ? row.createOrderBusiness.businessName : '-'
    },
  },
  {
    prop: 'issueName',
    label: '英文部客服',
    minWidth: '200',
    handle: (val, row) => {
      return row.issue ? row.issue.name : '-'
    },
  },
  {
    prop: 'reminder',
    label: '催单次数',
    minWidth: '200',
    handle: (val, row) => {
      return `第${row.reminder}次`
    },
  },
  {
    prop: 'reminderTime',
    label: '催单时间',
    minWidth: '200',
    handle: (val, row) => {
      return parseTime(row.reminderTime, '{y}-{m}-{d} {h}:{i}')
    },
  },
  {
    slot: 'status',
    prop: 'status',
    label: '状态',
    width: '120',
  },
]

const tableData = ref([])
const pageNum = ref(1)
const pageSize = ref(20)
const total = ref(0)
const tableLoading = ref(false)

const queryParams = ref({
  status: [],
  keyword: '',
})

function resetQuery() {
  queryParams.value = {
    status: [],
    keyword: '',
  }
  init()
}

function init() {
  handleQuery()
  getReminderNum()
}

const reminderData = ref({})
function getReminderNum() {
  getRemindCount().then(res => {
    reminderData.value.total = res.data.total || 0
    reminderData.value.untreatedTotal = res.data.untreatedTotal || 0
  })
}

function onQuery() {
  pageNum.value = 1
  handleQuery()
}
function handleQuery() {
  tableLoading.value = true
  remindRecord({
    pageNum: pageNum.value,
    pageSize: pageSize.value,
    ...queryParams.value,
  })
    .then(res => {
      total.value = res.data.total
      tableData.value = res.data.rows
    })
    .finally(() => {
      tableLoading.value = false
    })
}
// 分页跳转
function pageChange(page) {
  pageNum.value = page.currentPage
  pageSize.value = page.pageSize
  init()
}

function routerNewWindow(path) {
  const { href } = router.resolve({ path })
  window.open(href, '_blank')
}
function routerNewWindow1(path, data) {
  const { href } = router.resolve({ path })
  window.open(href + '?keyword=' + data, '_blank')
}

//订单详情
function goOrderDetail(id) {
  router.push(`/order/details/${id}`)
}
function goOrderList(videoCode) {
  router.push(`/order/list?keyword=${videoCode}`)
  //   router.push({
  //     path: '/order/list',
  //     query: {
  //       id,
  //     },
  //   })
}

init()
</script>

<style scoped lang="scss"></style>
