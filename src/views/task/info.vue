<template>
  <el-container>
    <el-main v-loading="loading">
      <el-card>
        <el-row>
          <el-col :span="12">
            <el-form label-width="80px">
              <el-descriptions title="工单信息" :column="1">
                <el-descriptions-item label="工单编号:">{{ taskInfo.taskNum }}</el-descriptions-item>
                <el-descriptions-item label="工单类型:">
                  {{ getTaskTypeLabel(taskInfo.type) }}
                </el-descriptions-item>
                <el-descriptions-item label="工单内容:">{{ taskInfo.content }}</el-descriptions-item>
                <el-descriptions-item>
                  <div class="flex-start">
                    <span style="margin-right: 16px">问题图片:</span>
                    <div v-if="taskInfo.issuePic?.length">
                      <ViewerImageList :data="taskInfo.issuePic" is-preview-all :show-delete-btn="false" :customized="true" />
                    </div>
                    <div v-else>无</div>
                  </div>
                </el-descriptions-item>
                <el-descriptions-item label="优先级:">
                  {{ getPriorityLabel(taskInfo.priority) }}
                </el-descriptions-item>
                <el-descriptions-item label="提交人:">{{ taskInfo.submit?.name }}</el-descriptions-item>
                <el-descriptions-item label="提交时间:">{{ taskInfo.submitTime }}</el-descriptions-item>
                <el-descriptions-item label="处理人:">{{ taskInfo.assignee?.name }}</el-descriptions-item>
                <el-descriptions-item label="关闭/完成时间:">
                  {{ taskInfo.endTime == null ? '-' : taskInfo.endTime }}
                </el-descriptions-item>
                <el-descriptions-item label="状态:">
                  {{ getStatusLabel(taskInfo.status) }}
                </el-descriptions-item>
              </el-descriptions>
              <el-descriptions title="视频信息" :column="1">
                <el-descriptions-item label="视频编码:">{{ taskInfo.videoCode }}</el-descriptions-item>
                <el-descriptions-item label="中文名称:">{{ taskInfo.productChinese }}</el-descriptions-item>
                <el-descriptions-item label="英文名称:">{{ taskInfo.productEnglish }}</el-descriptions-item>
                <el-descriptions-item label="拍摄模特:">{{ taskInfo.shootModel?.name }}</el-descriptions-item>
                <el-descriptions-item label="商家下单账号:">
                  {{ taskInfo.orderUser?.name }}({{ taskInfo.orderUser?.account }}/{{
                    taskInfo.orderUser?.nickName
                  }})
                </el-descriptions-item>
              </el-descriptions>
              <el-descriptions title="流转记录" :column="1">
                <el-descriptions-item v-for="item in taskInfo.records" :key="item.id">
                  {{ `${item.time}, 由 ${item.assignee.name}  ${getOperateType(item)}` }}
                </el-descriptions-item>
              </el-descriptions>
            </el-form>
          </el-col>
        </el-row>
        <div class="leftRightBottom">
          <el-button v-btn type="primary" @click="backTask">返回</el-button>
        </div>
      </el-card>
    </el-main>
  </el-container>
</template>
<script setup>
import ViewerImageList from '@/components/ImagePreview/viewerImageList.vue'
import { taskDetail } from '@/api/task/index.js'
import { useRoute } from 'vue-router'
import { getPriorityLabel, getStatusLabel, getTaskTypeLabel } from '@/views/task/data.js'
import router from '@/router/index.js'

const loading = ref(false)

const operateType = [
  { label: '创建工单', value: 1, tip: '' },
  { label: '取消工单', value: 2, tip: ' 取消理由: ' },
  { label: '重新打开', value: 3, tip: ' 打开理由: ' },
  { label: '拒绝工单', value: 4, tip: ' 拒绝理由: ' },
  { label: '标记处理中', value: 5, tip: '' },
  { label: '标记已处理', value: 6, tip: '' },
]

function getOperateType(type) {
  let data = operateType.find(item => item.value === type.operateType)
  if (type.remark != null) {
    return data.label + data.tip + type.remark
  }
  return data.label + data.tip
}

const taskInfo = ref({})
const route = useRoute()

function getTaskNum() {
  return route.params.taskNum
}

function backTask() {
  router.push({ path: '/task/workOrder' })
}

getTaskInfo(getTaskNum())

function getTaskInfo(num) {
  loading.value = true
  taskDetail(num)
    .then(res => {
      taskInfo.value = res.data
      loading.value = false
    })
    .catch(() => (loading.value = false))
}
</script>

<style lang="scss">
.el-descriptions__table {
  margin-left: 15px;
}

.leftRightBottom {
  display: flex;
  flex-direction: row-reverse;
}
</style>
