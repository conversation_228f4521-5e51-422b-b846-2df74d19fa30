<template>
  <el-dialog
    v-model="dialogVisible"
    title="查看"
    width="600"
    align-center
    destroy-on-close
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <div class="content-box">
      <div class="head-box">
        <div class="flex-start head-item-box">
          <div class="head-item">视频编码：{{ orderInfo.videoCode }}</div>
          <div class="one-ell head-item" v-ellipsis-tooltips="orderInfo.productChinese">
            产品名称：{{ orderInfo.productChinese }}
          </div>
          <div class="one-ell head-item" v-ellipsis-tooltips="orderInfo.shootModel?.name">
            拍摄模特：{{ orderInfo.shootModel?.name || '' }}
          </div>
        </div>
        <div class="flex-start head-item-box">
          <div class="head-item">物流状态：{{ orderInfo.latestMainStatusSketch || '-' }}</div>
          <div class="head-item" style="width: 66%">物流单号：{{ orderInfo.number || '-' }}</div>
        </div>
      </div>

      <el-tabs v-model="curTab" class="tabs-box">
        <el-tab-pane label="跟进记录" name="followUp">
          <FollowUpTimeline :id="orderInfo.id" />
        </el-tab-pane>
        <el-tab-pane label="物流记录" name="logistics" v-if="orderInfo.platform != 3">
          <LogisticsTimeline :id="orderInfo.id" />
        </el-tab-pane>
      </el-tabs>
    </div>
  </el-dialog>
</template>

<script setup>
import FollowUpTimeline from '@/views/task/logistics/components/followUpTimeline.vue'
import LogisticsTimeline from '@/views/task/logistics/components/logisticsTimeline.vue'

defineExpose({
  open,
  close,
})

const dialogVisible = ref(false)
const orderInfo = ref({})
const curTab = ref('followUp')

function open(data) {
  if (data) {
    orderInfo.value = data
    dialogVisible.value = true
  }
}

function close() {
  dialogVisible.value = false
}

function handleClose() {
  orderInfo.value = {}
  curTab.value = 'followUp'
}
</script>

<style lang="scss" scoped>
.content-box {
  .head-box {
    margin-bottom: 10px;
    background-color: #eee;
    border-radius: 4px;
    padding: 10px 15px;

    .head-item-box {
      .head-item {
        width: 33%;
        line-height: 24px;
        flex-shrink: 0;
      }
    }
  }
}
</style>
