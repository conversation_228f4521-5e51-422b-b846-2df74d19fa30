<template>
  <el-dialog
    v-model="dialogVisible"
    :title="title"
    width="600"
    align-center
    destroy-on-close
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <div class="content-box">
      <div class="head-box">
        <div class="flex-start head-item-box" v-for="(item, index) in orders" :key="index">
          <div class="head-item">视频编码：{{ item.videoCode }}</div>
          <div class="one-ell head-item" v-ellipsis-tooltips="item.productChinese">
            产品名称：{{ item.productChinese }}
          </div>
          <div class="one-ell head-item" v-ellipsis-tooltips="item.shootModel?.name">
            拍摄模特：{{ item.shootModel?.name || '' }}
          </div>
        </div>
      </div>
      <el-form ref="formRef" :model="form" label-width="110px" :rules="rules" @submit.native.prevent>
        <div class="form-box">
          <el-form-item label="标记类型" prop="logisticFlag">
            <el-radio-group v-model="form.logisticFlag" @change="handleLogisticFlagChange">
              <el-radio v-for="item in logisticFlagOptions" :key="item.value" :value="item.value">
                {{ item.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="预计发货时间" prop="date" v-if="form.logisticFlag == 4">
            <el-date-picker
              v-model="form.date"
              type="date"
              placeholder="请选择"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              :disabled="form.dateUnknown"
              :disabled-date="handleDisabledDate"
            />
            <el-checkbox style="margin-left: 10px" label="时间未知" v-model="form.dateUnknown" />
          </el-form-item>
          <el-form-item label="说明" prop="remark" :rules="remarkRules">
            <el-input
              v-model="form.remark"
              placeholder="请输入说明"
              maxlength="300"
              show-word-limit
              type="textarea"
              :rows="4"
              resize="none"
              style="width: 400px"
              clearable
            />
          </el-form-item>
          <el-form-item label="" prop="pic">
            <ViewerImageList
              :data="form.pic"
              is-preview-all
              :urlName="'picUrl'"
              :show-delete-btn="true"
              @delete="handleFileDelete"
            >
              <template #footer>
                <div class="image-upload" v-if="form.pic && form.pic.length < 5" @click="openUpload">
                  <el-icon size="28" color="#909399"><Plus /></el-icon>
                </div>
              </template>
            </ViewerImageList>
          </el-form-item>
        </div>
      </el-form>

      <DragUploadDialog
        ref="DragUploadDialogRef"
        title="上传图片"
        :limit="uploadLimit"
        :size="5"
        :before-success="uploadSuccess"
        bucketType="order"
      />
    </div>
    <template #footer>
      <div class="flex-center gap-10">
        <el-button style="width: 100px" round @click="dialogVisible = false">取消</el-button>
        <el-button style="width: 100px" type="primary" round @click="confirm">保存</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import ViewerImageList from '@/components/ImagePreview/viewerImageList.vue'
import { handleStatusOptions, handleStatusMap, followStatusMap } from '@/views/task/logistics/data.js'
import {
  logisticFollowAlertShipping,
  logisticFollowDelayShipping,
  logisticRemarkReplenish,
} from '@/api/task/logistics.js'

defineExpose({
  open,
  close,
})

const { proxy } = getCurrentInstance()
const emits = defineEmits(['success'])

const dialogVisible = ref(false)
const title = ref('标记')
const markType = ref(0)
const orders = ref([])

const logisticFlagOptions = ref([])

const DragUploadDialogRef = ref()
const uploadLimit = computed(() => {
  if (form.value.pic && form.value.pic.length < 6) {
    return 5 - form.value.pic.length
  }
  return 5
})

const formRef = ref()
const form = ref({
  logisticFlag: 1,
  date: '',
  dateUnknown: false,
  remark: '',
  pic: [],
})

const rules = {
  logisticFlag: [{ required: true, message: '请选择标记类型', trigger: 'change' }],
  date: [{ required: true, validator: validateDate, trigger: 'change' }],
}
const remarkRules = ref([{ required: false, message: '请输入说明', trigger: 'change' }])

function validateDate(rule, value, callback) {
  if (form.value.logisticFlag == 4) {
    if (form.value.dateUnknown || value) {
      return callback()
    }
  }
  return callback(new Error('请选择预计发货时间'))
}

function handleLogisticFlagChange(val) {
  remarkRules.value[0].required = val == 5
}

function open(data) {
  if (Array.isArray(data) && data.length) {
    orders.value = data
    let item = handleStatusOptions.find(item => item.value == data[0].handleStatus)
    if (item) {
      title.value = item.title
      markType.value = item.value
      initLogisticFlag()
      dialogVisible.value = true
    } else {
      console.error('find handleStatus error')
    }
  }
}

function initLogisticFlag() {
  if (markType.value < handleStatusMap['标记发货']) {
    switch (markType.value) {
      case handleStatusMap['未通知']:
        form.value.logisticFlag = 1
        logisticFlagOptions.value = [
          { label: '通知发货', value: 1 },
          { label: '延迟发货', value: 4 },
        ]
        break
      case handleStatusMap['延迟发货提醒']:
      case handleStatusMap['催发货提醒']:
        form.value.logisticFlag = 2
        logisticFlagOptions.value = [
          { label: '提醒发货', value: 2 },
          { label: '延迟发货', value: 4 },
        ]
        break
      case handleStatusMap['地址变更通知']:
        form.value.logisticFlag = 3
        logisticFlagOptions.value = [
          { label: '通知地址变更', value: 3 },
          { label: '延迟发货', value: 4 },
        ]
        break
      default:
        logisticFlagOptions.value = [
          { label: '延迟发货', value: 4 },
          { label: '补充说明', value: 5 },
        ]
        form.value.logisticFlag = 4
        handleLogisticFlagChange(4)
        break
    }
  } else {
    switch (markType.value) {
      case handleStatusMap['标记发货提醒']:
        form.value.logisticFlag = 6
        logisticFlagOptions.value = [{ label: '通知填写物流单号', value: 6 }]
        break
      case handleStatusMap['通知确认模特']:
      case handleStatusMap['催确认模特提醒']:
        form.value.logisticFlag = 7
        logisticFlagOptions.value = [{ label: '已通知确认模特', value: 7 }]
        break
      default:
        logisticFlagOptions.value = [{ label: '补充说明', value: 5 }]
        form.value.logisticFlag = 5
        handleLogisticFlagChange(5)
        break
    }
  }
}

function close() {
  dialogVisible.value = false
}

function handleClose() {
  orders.value = []
  title.value = '标记'
  form.value = {
    logisticFlag: 1,
    date: '',
    dateUnknown: false,
    remark: '',
    pic: [],
  }
  logisticFlagOptions.value = []
  handleLogisticFlagChange(1)
}

function handleFileDelete(file, i) {
  form.value.pic.splice(i, 1)
}

function openUpload() {
  DragUploadDialogRef.value.open()
}

function uploadSuccess(data, closeUp) {
  form.value.pic.push(...data)
  closeUp()
}

function handleDisabledDate(date) {
  return date < new Date() - 86400000
}

function confirm() {
  formRef.value.validate(valid => {
    if (valid) {
      proxy.$modal.confirm('确认保存？', '提示', {}).then(() => {
        handleSave()
      })
    }
  })
}
async function handleSave() {
  let saveApi
  let params = {}
  if (form.value.logisticFlag == 4) {
    // 延迟发货
    saveApi = logisticFollowDelayShipping
    params = {
      ids: orders.value.map(item => item.id),
      remark: form.value.remark,
      resourceIds: form.value.pic.map(item => item.picUrl),
      logisticStartTime: form.value.dateUnknown ? '' : form.value.date,
      isDefaultLogisticStartTime: form.value.dateUnknown ? 1 : 0,
    }
  } else if (form.value.logisticFlag == 5) {
    // 补充说明
    saveApi = logisticRemarkReplenish
    params = {
      ids: orders.value.map(item => item.id),
      remark: form.value.remark,
      resourceIds: form.value.pic.map(item => item.picUrl),
    }
  } else {
    // 标记通知
    saveApi = logisticFollowAlertShipping
    params = {
      ids: orders.value.map(item => item.id),
      remark: form.value.remark,
      resourceIds: form.value.pic.map(item => item.picUrl),
    }
  }
  if (saveApi) {
    proxy.$modal.loading('正在保存...')
    saveApi(params)
      .then(res => {
        if (res.code == 200) {
          emits('success')
          close()
        }
      })
      .finally(() => {
        proxy.$modal.closeLoading()
      })
  }
}
</script>

<style lang="scss" scoped>
.content-box {
  .head-box {
    max-height: 220px;
    overflow: hidden;
    overflow-y: auto;

    .head-item-box {
      margin-bottom: 10px;
      background-color: #eee;
      border-radius: 4px;
      padding: 5px 15px;

      .head-item {
        width: 33%;
        line-height: 32px;
        flex-shrink: 0;
      }
    }
  }

  .image-upload {
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px dashed var(--el-border-color-darker);
    width: 80px;
    height: 80px;
    border-radius: 6px;
    background-color: var(--el-fill-color-lighter);
    cursor: pointer;

    &:hover {
      border-color: #409eff;
    }
  }
}
</style>
