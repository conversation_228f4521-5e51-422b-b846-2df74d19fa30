<template>
  <el-dialog
    v-model="dialogVisible"
    :title="waitNotice ? '通知拍摄' : '模特确认'"
    width="600"
    align-center
    destroy-on-close
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <div class="content-box">
      <div class="head-box">
        <div class="flex-start head-item-box">
          <div class="head-item">视频编码：{{ orderInfo.videoCode }}</div>
          <div class="one-ell head-item" v-ellipsis-tooltips="orderInfo.productChinese">
            产品名称：{{ orderInfo.productChinese }}
          </div>
          <div class="one-ell head-item" v-ellipsis-tooltips="orderInfo.shootModel?.name">
            拍摄模特：{{ orderInfo.shootModel?.name || '' }}
          </div>
        </div>
        <div class="flex-start head-item-box">
          <div class="head-item">物流状态：{{ orderInfo.latestMainStatusSketch || '-' }}</div>
          <div class="head-item" style="width: 66%">物流单号：{{ orderInfo.number || '-' }}</div>
        </div>
      </div>
      <el-form ref="formRef" :model="form" label-width="110px" :rules="rules" @submit.native.prevent>
        <div class="form-box">
          <template v-if="waitNotice">
            <el-form-item label="确认结果" prop="result">
              <el-radio-group v-model="form.result" @change="handleResultChange">
                <el-radio :value="6">已通知</el-radio>
              </el-radio-group>
            </el-form-item>
          </template>
          <template v-else>
            <el-form-item label="确认结果" prop="result">
              <el-radio-group v-model="form.result" @change="handleResultChange">
                <el-radio :value="1">已询问</el-radio>
                <el-radio :value="2">模特已收货</el-radio>
                <el-radio :value="3">丢件</el-radio>
              </el-radio-group>
            </el-form-item>
            <template v-if="form.result == 2">
              <el-form-item
                label="签收时间"
                required
                v-if="orderInfo.latestMainStatus == 'Delivered'"
              >
                <div>
                  {{ orderInfo.logisticUpdateTime }}
                </div>
              </el-form-item>
              <el-form-item label="签收时间" prop="signTime" v-else>
                <el-date-picker
                  v-model="form.signTime"
                  type="date"
                  placeholder="请选择"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  style="width: 400px"
                  :disabled-date="handleDisabledDate"
                />
              </el-form-item>
            </template>
          </template>
          <el-form-item label="备注" prop="remark" :rules="remarkRules">
            <el-input
              v-model="form.remark"
              placeholder="请输入备注"
              maxlength="300"
              show-word-limit
              type="textarea"
              :rows="4"
              resize="none"
              style="width: 400px"
              clearable
            />
          </el-form-item>
          <el-form-item label="" prop="pic">
            <ViewerImageList
              :data="form.pic"
              is-preview-all
              :urlName="'picUrl'"
              :show-delete-btn="true"
              @delete="handleFileDelete"
            >
              <template #footer>
                <div class="image-upload" v-if="form.pic && form.pic.length < 5" @click="openUpload">
                  <el-icon size="28" color="#909399"><Plus /></el-icon>
                </div>
              </template>
            </ViewerImageList>
          </el-form-item>
        </div>
      </el-form>

      <DragUploadDialog
        ref="DragUploadDialogRef"
        title="上传图片"
        :limit="uploadLimit"
        :size="5"
        :before-success="uploadSuccess"
        bucketType="order"
      />
    </div>
    <template #footer>
      <div class="flex-center gap-10">
        <el-button style="width: 100px" round @click="close">取消</el-button>
        <el-button style="width: 100px" type="primary" round @click="confirm">保存</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import ViewerImageList from '@/components/ImagePreview/viewerImageList.vue'
import { modelConfirm } from '@/api/task/logistics.js'

defineExpose({
  open,
  close,
})

const { proxy } = getCurrentInstance()
const emits = defineEmits(['success'])

const dialogVisible = ref(false)
const orderInfo = ref({})

const waitNotice = ref(false)

const DragUploadDialogRef = ref()
const uploadLimit = computed(() => {
  if (form.value.pic && form.value.pic.length < 6) {
    return 5 - form.value.pic.length
  }
  return 5
})

const formRef = ref()
const form = ref({
  result: 1,
  signTime: '',
  remark: '',
  pic: [],
})

const rules = {
  result: [{ required: true, message: '请选择确认结果', trigger: 'change' }],
  signTime: [{ required: true, message: '请选择签收时间', trigger: 'change' }],
}
const remarkRules = ref([{ required: false, message: '请输入备注', trigger: 'change' }])

function handleResultChange(val) {
  remarkRules.value[0].required = val == 3
}

function handleDisabledDate(date) {
  return date > new Date()
}

function open(data) {
  if (data) {
    // 通知拍摄
    waitNotice.value = data.modelResult == 5
    if (waitNotice.value) {
      form.value.result = 6
    }
    orderInfo.value = data
    dialogVisible.value = true
  }
}

function close() {
  dialogVisible.value = false
}

function handleClose() {
  orderInfo.value = {}
  form.value = {
    result: 1,
    signTime: '',
    remark: '',
    pic: [],
  }
  remarkRules.value[0].required = false
}

function handleFileDelete(file, i) {
  form.value.pic.splice(i, 1)
}

function openUpload() {
  DragUploadDialogRef.value.open()
}

function uploadSuccess(data, closeUp) {
  form.value.pic.push(...data)
  closeUp()
}

function confirm() {
  formRef.value.validate(valid => {
    if (valid) {
      proxy.$modal.confirm('确认保存？', '提示', {}).then(() => {
        proxy.$modal.loading('正在保存...')
        modelConfirm({
          id: orderInfo.value.id,
          modelResult: form.value.result,
          remark: form.value.remark,
          resourceIds: form.value.pic.map(item => item.picUrl),
          signTime: form.value.result == 2 ? form.value.signTime : undefined,
        })
          .then(res => {
            if (res.code == 200) {
              emits('success')
              close()
            }
          })
          .finally(() => {
            proxy.$modal.closeLoading()
          })
      })
    }
  })
}
</script>

<style lang="scss" scoped>
.content-box {
  .head-box {
    margin-bottom: 10px;
    background-color: #eee;
    border-radius: 4px;
    padding: 10px 15px;

    .head-item-box {
      .head-item {
        width: 33%;
        line-height: 24px;
        flex-shrink: 0;
      }
    }
  }

  .image-upload {
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px dashed var(--el-border-color-darker);
    width: 80px;
    height: 80px;
    border-radius: 6px;
    background-color: var(--el-fill-color-lighter);
    cursor: pointer;

    &:hover {
      border-color: #409eff;
    }
  }
}
</style>
