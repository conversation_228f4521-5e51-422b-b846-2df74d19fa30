<template>
  <el-dialog
    v-model="dialogVisible"
    title="跟进记录"
    width="600"
    align-center
    destroy-on-close
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <div class="content-box">
      <div class="head-box" v-if="order.id">
        <div class="flex-start head-item-box">
          <div class="head-item">视频编码：{{ order.videoCode }}</div>
          <div class="one-ell head-item" v-ellipsis-tooltips="order.productChinese">
            产品名称：{{ order.productChinese }}
          </div>
          <div class="one-ell head-item" v-ellipsis-tooltips="order.shootModel?.name">
            拍摄模特：{{ order.shootModel?.name || '' }}
          </div>
        </div>
      </div>

      <FollowUpTimeline :id="order.id" />
    </div>
  </el-dialog>
</template>

<script setup>
import FollowUpTimeline from '@/views/task/logistics/components/followUpTimeline.vue'


defineExpose({
  open,
  close,
})

const dialogVisible = ref(false)
const order = ref({})

function open(row) {
  if (row) {
    order.value = row
    dialogVisible.value = true
  }
}

function close() {
  dialogVisible.value = false
}

function handleClose() {
  order.value = {}
}

</script>

<style lang="scss" scoped>
.content-box {
  .head-box {
    max-height: 220px;
    overflow: hidden;
    overflow-y: auto;

    .head-item-box {
      margin-bottom: 10px;
      background-color: #eee;
      border-radius: 4px;
      padding: 5px 15px;

      .head-item {
        width: 33%;
        line-height: 32px;
        flex-shrink: 0;
      }
    }
  }

  
}
</style>
