<template>
  <div class="logistics-container">
    <el-radio-group v-model="curTab" size="large">
      <el-radio-button :value="0" v-if="notDelivered">未发货</el-radio-button>
      <el-radio-button :value="1" v-if="delivered">已发货</el-radio-button>
    </el-radio-group>

    <TablePage v-if="notDelivered && curTab === 0" :type="0" />
    <TablePage v-if="delivered && curTab === 1" :type="1" />

  </div>
</template>

<script setup>
import TablePage from '@/views/task/logistics/components/tablePage.vue'
import { checkPermi } from '@/utils/permission'

const curTab = ref(0)

const notDelivered = computed(() => {
  return checkPermi(['task:logistics:notDelivered'])
})
const delivered = computed(() => {
  return checkPermi(['task:logistics:delivered'])
})

onMounted(() => {
  if (notDelivered.value) {
    curTab.value = 0
  } else if (delivered.value) {
    curTab.value = 1
  }
})
</script>

<style lang="scss" scoped>
.logistics-container {
  padding: 20px;
}
</style>
