<template>
  <div class="logistics-table-page">
    <el-tabs v-model="tabStatus" class="tabs" @tab-change="handleStatusChange">
      <el-tab-pane v-for="(tab, i) in tabList" :key="tab" :name="tab.value" :label="tab.label">
        <template #label>
          <div v-if="tab.number">
            {{ tab.label }}
            {{ '(' }}
            <span style="color: red">{{ tab.number }}</span>
            {{ ')' }}
          </div>
          <div v-else>{{ tab.label }}</div>
        </template>
      </el-tab-pane>
    </el-tabs>

    <div class="search-box">
      <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="88px" @submit.prevent>
        <el-form-item label="搜索" label-width="50px">
          <el-input
            v-model="queryParams.keyword"
            clearable
            style="width: 250px"
            placeholder="请输入搜索关键词"
          />
        </el-form-item>
        <el-form-item label="处理状态" v-if="type == 0">
          <el-select
            v-model="queryParams.handleStatusList"
            multiple
            collapse-tags
            collapse-tags-tooltip
            placeholder="请选择"
            clearable
            style="width: 180px"
          >
            <el-option
              v-for="item in handleStatusSelect"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="商家编码" v-if="tabStatus == followStatusMap['需处理']">
          <el-select
            v-model="queryParams.memberCodes"
            multiple
            collapse-tags
            collapse-tags-tooltip
            placeholder="请选择"
            clearable
            style="width: 180px"
          >
            <el-option
              v-for="item in memberCodeSelect"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="拍摄模特" v-if="tabStatus == followStatusMap['待模特确认']">
          <el-select
            v-model="queryParams.shootModelId"
            placeholder="请选择"
            clearable
            filterable
            style="width: 180px"
            @change="handleShootModelChange"
          >
            <el-option
              v-for="item in shootModelSelectList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <el-checkbox
            style="margin-left: 10px"
            v-model="queryParams.includeFamily"
            :true-value="1"
            :false-value="0"
            :disabled="includeFamilyDisabled"
            label="包含家庭成员"
          />
        </el-form-item>
        <el-form-item label="物流状态" v-if="type === 1">
          <el-select
            v-model="queryParams.logisticMainStatus"
            placeholder="请选择"
            multiple
            collapse-tags
            collapse-tags-tooltip
            clearable
            style="width: 180px"
          >
            <el-option
              v-for="item in logisticsStatusSelect"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="模特反馈结果"
          label-width="110px"
          v-if="tabStatus == followStatusMap['待模特确认'] || tabStatus == followStatusMap['已结束']"
        >
          <el-select
            :reserve-keyword="false"
            v-model="queryParams.modelResult"
            placeholder="请选择"
            clearable
            style="width: 180px"
          >
            <template v-if="tabStatus == followStatusMap['待模特确认']">
              <el-option label="未更新" :value="0" />
              <el-option label="已询问" :value="1" />
              <el-option label="待通知拍摄" :value="5" />
            </template>
            <template v-else>
              <el-option label="模特已收货" :value="2" />
              <el-option label="丢件" :value="3" />
              <el-option label="回退订单" :value="4" />
              <el-option label="已通知拍摄" :value="6" />
            </template>
          </el-select>
        </el-form-item>
        <el-form-item label="中文部客服" v-if="tabStatus == followStatusMap['需跟进']">
          <el-select
            :reserve-keyword="false"
            v-model="queryParams.contactIds"
            placeholder="请选择"
            multiple
            filterable
            collapse-tags
            collapse-tags-tooltip
            clearable
            style="width: 180px"
          >
            <el-option v-for="item in contactList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="英文部客服" v-if="tabStatus == followStatusMap['需跟进']">
          <el-select
            :reserve-keyword="false"
            v-model="queryParams.issueIds"
            placeholder="请选择"
            multiple
            filterable
            collapse-tags
            collapse-tags-tooltip
            clearable
            style="width: 180px"
          >
            <el-option v-for="item in issueList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item
          label="物流状态更新时间"
          label-width="130px"
          v-if="tabStatus == followStatusMap['需跟进']"
        >
          <el-date-picker
            v-model="queryParams.updateTime"
            format="YYYY/MM/DD"
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            unlink-panels
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :shortcuts="[
              {
                text: '今天',
                value: () => [new Date().setHours(0, 0, 0, 0), new Date().setHours(23, 59, 59, 59)],
              },
            ]"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button v-btn type="primary" icon="Search" native-type="submit" @click="onQuery()">
            搜索
          </el-button>
          <el-button v-btn icon="Refresh" plain style="margin-right: 12px" @click="resetQuery()">
            重置
          </el-button>
          <el-checkbox
            size="large"
            v-model="queryParams.aboutMe"
            @change="onQuery()"
            label="与我相关的订单"
          />
        </el-form-item>
      </el-form>
    </div>

    <div class="table-box" :key="tableKey">
      <ElTablePage
        ref="tableRef"
        :columns="columns"
        :data="tableData"
        :loading="tableLoading"
        :currentPage="pageNum"
        :pageSize="pageSize"
        :total="total"
        :tableAction="{
          width: '270',
          fixed: 'right',
        }"
        :tableOptions="{
          border: false,
        }"
        row-key="id"
        @page-change="pageChange"
        @selection-change="selectionChange"
        @sort-change="sortChange"
      >
        <template #tableHeader>
          <div class="flex-start gap-10" style="margin-bottom: 10px" v-if="type == 0">
            <el-button
              v-if="checkPermi(['task:logistics_0:copy'])"
              v-btn
              type="success"
              plain
              size="small"
              :disabled="!multipleSelection.length"
              @click="handleBatchCopy"
            >
              批量复制
            </el-button>
            <el-tooltip
              v-if="tabStatus == followStatusMap['需处理'] && checkPermi(['task:logistics:mark'])"
              effect="dark"
              content="包含多个商家/处理状态，无法批量标记"
              placement="top"
              :disabled="!batchMarkDisabled || !multipleSelection.length"
            >
              <div>
                <el-button
                  v-btn
                  type="primary"
                  plain
                  size="small"
                  :disabled="batchMarkDisabled"
                  @click="handleBatchMark"
                >
                  批量标记
                </el-button>
              </div>
            </el-tooltip>
          </div>
          <div class="flex-start gap-10" style="margin-bottom: 10px" v-if="type == 1">
            <el-button
              v-if="
                checkPermi(['task:logistics_1:copy_english']) && tabStatus == followStatusMap['待模特确认']
              "
              v-btn
              type="warning"
              plain
              size="small"
              :disabled="!multipleSelection.length"
              @click="handleBatchCopyEnglish"
            >
              批量COPY
            </el-button>
          </div>
        </template>
        <template #videoCode="{ row }">
          <div
            v-if="row.videoCode"
            style="color: var(--el-color-primary); cursor: pointer"
            @click="viewDetail(row)"
          >
            {{ row.videoCode }}
          </div>
          <span v-else>-</span>
        </template>
        <template #productChinese="{ row }">
          <div @mouseenter="showProductImage($event, row.productPic)">{{ row.productChinese || '-' }}</div>
        </template>
        <template #shootModel="{ row }">
          <div
            v-if="row.shootModel"
            @mouseenter="handleMouseEnter($event, row.shootModel.id)"
            @mouseleave="handleMouseLeave()"
          >
            {{ row.shootModel.name }}
          </div>
          <div v-else>-</div>
        </template>
        <template #latestMainStatusSketch="{ row }">
          <div class="flex-start">
            <el-tag :type="row.isCallBack ? 'success' : 'warning'" round size="small">
              {{ row.isCallBack ? '系统' : '手动' }}
            </el-tag>
            <span style="margin: 0 5px">
              {{ row.latestMainStatusSketch || '-' }}
            </span>
            <span>{{ handleOrderStatusTime(row.logisticUpdateTime) }}</span>
          </div>
        </template>
        <template #lastTime="{ row }">
          <div class="flex-start">
            <span>{{ parseTime(row.updateTime, '{y}-{m}-{d} {h}:{i}') }}</span>
            <span style="margin-left: 5px">{{ handleOrderStatusTime(row.updateTime) }}</span>
          </div>
        </template>
        <template #latestRemark="{ row }">
          <div
            class="more-ell"
            style="text-align: center"
            v-if="row.resources && row.resources.length"
            @mouseenter="showLatestRemarkImage($event, row.resources, row.latestRemark)"
          >
            {{ row.latestRemark || '-' }}
          </div>
          <div
            class="more-ell"
            style="text-align: center"
            v-else-if="row.latestRemark"
            v-ellipsis-tooltips="`<div class='template-pre'>${row.latestRemark}</div>`"
          >
            {{ row.latestRemark }}
          </div>
          <div v-else>-</div>
        </template>
        <template #tableAction="{ row }">
          <template v-if="type == 0">
            <el-button
              v-if="
                row.handleStatus != handleStatusMap['标记发货'] &&
                row.handleStatus != handleStatusMap['标记发货已提醒'] &&
                checkPermi(['task:logistics:mark'])
              "
              v-btn
              type="primary"
              plain
              size="small"
              @click="handleMarkDialog(row)"
            >
              标记
            </el-button>
            <el-button
              v-if="checkPermi(['task:logistics_0:copy'])"
              v-btn
              type="success"
              plain
              size="small"
              @click="handleCopy(row)"
            >
              复制
            </el-button>
            <el-button
              v-if="row.platform != 3 && checkPermi(['order:manage:shipments'])"
              v-btn
              type="primary"
              plain
              size="small"
              @click="handleDeliverGoods(row)"
            >
              去发货
            </el-button>
            <el-button
              v-if="checkPermi(['task:logistics_0:detail'])"
              v-btn
              type="info"
              plain
              size="small"
              @click="viewFollowUpRecord(row)"
            >
              查看
            </el-button>
          </template>
          <template v-else-if="type == 1">
            <el-button
              v-if="
                checkPermi(['task:logistics:followUp']) &&
                (tabStatus == followStatusMap['需跟进'] || tabStatus == followStatusMap['无需跟进'])
              "
              v-btn
              type="primary"
              plain
              size="small"
              @click="handleLogisticsFollowUpDialog(row)"
            >
              跟进
            </el-button>
            <el-button
              v-if="checkPermi(['task:logistics:modelConfirm']) && tabStatus == followStatusMap['待模特确认']"
              v-btn
              type="primary"
              plain
              size="small"
              @click="handleModelConfirmDialog(row)"
            >
              {{ row.modelResult == 5 ? '通知' : '确认' }}
            </el-button>
            <el-button
              v-if="
                checkPermi(['task:logistics_1:copy']) &&
                tabStatus != followStatusMap['已结束'] &&
                tabStatus != followStatusMap['待模特确认']
              "
              v-btn
              type="success"
              plain
              size="small"
              @click="handleCopy(row)"
            >
              复制
            </el-button>
            <el-button
              v-if="
                checkPermi(['task:logistics_1:copy_english']) && tabStatus == followStatusMap['待模特确认']
              "
              v-btn
              type="warning"
              plain
              size="small"
              @click="handleCopyEnglish(row)"
            >
              COPY
            </el-button>
            <el-button
              v-if="checkPermi(['task:logistics_1:detail'])"
              v-btn
              type="info"
              plain
              size="small"
              @click="viewLogisticsRecord(row)"
            >
              查看
            </el-button>
          </template>
        </template>
      </ElTablePage>
    </div>

    <ModelInfoPopover ref="ModelInfoPopoverRef" />
    <MarkDialog v-if="type == 0" ref="MarkDialogRef" @success="handleSuccess" />
    <FollowUpRecordDialog v-if="type == 0" ref="FollowUpRecordDialogRef" />
    <LogisticsRecordDialog v-if="type == 1" ref="LogisticsRecordDialogRef" />
    <LogisticsFollowUpDialog v-if="type == 1" ref="LogisticsFollowUpDialogRef" @success="handleSuccess" />
    <ModelConfirmDialog v-if="type == 1" ref="ModelConfirmDialogRef" @success="handleSuccess" />
    <ConfirmDeliverGoods v-if="type == 0" ref="ConfirmDeliverGoodsRef" @success="handleSuccess" />
  </div>
</template>

<script setup>
import ElTablePage from '@/components/Table/ElTablePage'
import ModelInfoPopover from '@/views/model/ModelInfoPopover.vue'
import MarkDialog from '@/views/task/logistics/dialog/markDialog.vue'
import FollowUpRecordDialog from '@/views/task/logistics/dialog/followUpRecordDialog.vue'
import LogisticsRecordDialog from '@/views/task/logistics/dialog/logisticsRecordDialog.vue'
import LogisticsFollowUpDialog from '@/views/task/logistics/dialog/logisticsFollowUpDialog.vue'
import ModelConfirmDialog from '@/views/task/logistics/dialog/modelConfirmDialog.vue'
import ConfirmDeliverGoods from '@/views/order/components/dialog/confirmDeliverGoods.vue'
import { checkPermi } from '@/utils/permission'
import { logisticsStatus } from '@/utils/dict'
import { parseTime } from '@/utils/ruoyi'
import { copy } from '@/utils/index'
import { handleStatusMap, followStatusMap } from '@/views/task/logistics/data.js'
import { useTooltips } from '@/hooks/useTooltips'
import { orderStatusMap } from '@/views/order/list/data.js'
import {
  logisticFollowList,
  logisticStatistics,
  contactSelectList,
  issueSelectList,
  modelListSelectmodelListSelect,
  memberCodeList,
} from '@/api/task/logistics.js'
import { ElMessageBox, ElIcon } from 'element-plus'
import { Warning } from '@element-plus/icons-vue'

const { showTooltips } = useTooltips()

const props = defineProps({
  type: {
    type: Number,
    default: 0,
  },
})

const { proxy } = getCurrentInstance()
const { biz_model_type, biz_nation } = proxy.useDict('biz_model_type', 'biz_nation')

const handleStatusSelect = ref([])

const columns = ref([])
const tabList = ref([])
const tabStatus = ref('')
const queryParams = ref({
  keyword: '',
  memberCodes: [],
  handleStatusList: [],
  logisticMainStatus: [],
  aboutMe: false,
  modelResult: '',
  shootModelId: '',
  includeFamily: 0,
  contactIds: [],
  issueIds: [],
  updateTime: [],
})

const sortType = ref('')
const isAsc = ref(0)

const tableRef = ref()
const tableData = ref([])
const tableKey = ref(0)
const pageNum = ref(1)
const pageSize = 20
const total = ref(0)
const tableLoading = ref(false)
const multipleSelection = ref([])

const contactList = ref([])
const issueList = ref([])
const memberCodeSelect = ref([])
const shootModelSelect = ref([])
const shootModelSelectList = computed(() => {
  if (props.type == 1) {
    if (shootModelSelect.value.length) {
      if (queryParams.value.aboutMe) {
        return shootModelSelect.value.map(item => ({
          label: `${item.name} (${item.number})`,
          value: item.value,
        }))
      }
      return shootModelSelect.value.map(item => ({
        label: item.name,
        value: item.value,
      }))
    }
  }
  return []
})

const includeFamilyDisabled = ref(true)

const ModelInfoPopoverRef = ref()
const MarkDialogRef = ref()
const FollowUpRecordDialogRef = ref()
const LogisticsRecordDialogRef = ref()
const LogisticsFollowUpDialogRef = ref()
const ModelConfirmDialogRef = ref()
const ConfirmDeliverGoodsRef = ref()

const batchMarkDisabled = computed(() => {
  if (multipleSelection.value.length) {
    let status = multipleSelection.value[0].handleStatus
    let memberCode = multipleSelection.value[0].memberCode
    return !(
      multipleSelection.value.every(item => item.handleStatus === status) &&
      multipleSelection.value.every(item => item.memberCode === memberCode)
    )
  }
  return true
})

const logisticsStatusSelect = computed(() => {
  if (tabStatus.value == followStatusMap['需跟进']) {
    return logisticsStatus.filter(item => item.value < 5 || item.value == 6 || item.value == 9)
  }
  if (tabStatus.value == followStatusMap['待模特确认'] || tabStatus.value == followStatusMap['已结束']) {
    return logisticsStatus.filter(item => item.value == 5 || item.value == 7 || item.value == 8)
  }
  if (tabStatus.value == followStatusMap['无需跟进']) {
    return logisticsStatus.filter(item => item.value == 2 || item.value == 3 || item.value == 6)
  }
  return []
})

function onQuery() {
  pageNum.value = 1
  handleQuery()
}

function handleSuccess() {
  tableRef.value?.clearSelection()
  handleQuery()
  if (props.type == 0) {
    getMemberCodeList()
  } else if (props.type == 1) {
    getModelSelectList()
  }
}

function resetQuery(isAboutMe) {
  tableRef.value?.clearSelection()
  tableKey.value++
  multipleSelection.value = []
  queryParams.value = {
    keyword: '',
    memberCodes: [],
    handleStatusList: [],
    logisticMainStatus: [],
    aboutMe: isAboutMe ? true : false,
    modelResult: '',
    shootModelId: '',
    includeFamily: 0,
    contactIds: [],
    issueIds: [],
    updateTime: [],
  }
  includeFamilyDisabled.value = true
  sortType.value = ''
  isAsc.value = 0
  onQuery()
}

function handleQuery() {
  tableLoading.value = true
  const params = handleQueryParams()
  logisticFollowList(params)
    .then(res => {
      if (tabStatus.value == params.followStatus) {
        tableData.value = res.data.rows
        total.value = res.data.total
      }
    })
    .finally(() => {
      if (tabStatus.value == params.followStatus) {
        tableLoading.value = false
      }
    })
  getStatistics()
}

function handleQueryParams() {
  let params = {}
  if (queryParams.value.keyword) {
    params.keyword = queryParams.value.keyword.trim()
  }
  if (queryParams.value.aboutMe) {
    params.aboutMe = queryParams.value.aboutMe
  }
  params.logisticStatus = props.type
  params.followStatus = tabStatus.value
  if (props.type === 0) {
    if (queryParams.value.handleStatusList && queryParams.value.handleStatusList.length) {
      params.handleStatusList = queryParams.value.handleStatusList
    }
    if (queryParams.value.memberCodes && queryParams.value.memberCodes.length) {
      params.memberCodes = queryParams.value.memberCodes
    }
  }
  if (props.type === 1) {
    if (queryParams.value.logisticMainStatus.length) {
      params.logisticMainStatus = queryParams.value.logisticMainStatus
    }
    if (tabStatus.value == followStatusMap['需跟进']) {
      if (queryParams.value.contactIds.length) {
        params.contactIds = queryParams.value.contactIds
      }
      if (queryParams.value.issueIds.length) {
        params.issueIds = queryParams.value.issueIds
      }
      if (queryParams.value.updateTime?.length) {
        params.logisticUpdateTimeStart = queryParams.value.updateTime[0]
        params.logisticUpdateTimeEnd = queryParams.value.updateTime[1]
      }
    }
    if (tabStatus.value == followStatusMap['待模特确认']) {
      params.shootModelId = queryParams.value.shootModelId
      params.includeFamily = queryParams.value.includeFamily
    }
    params.modelResult = queryParams.value.modelResult
  }
  if (sortType.value == 'latestMainStatusSketch') {
    params.orderByType = 1
    params.isAsc = isAsc.value
  }
  if (sortType.value == 'lastTime') {
    params.orderByType = 2
    params.isAsc = isAsc.value
  }
  params.pageNum = pageNum.value
  params.pageSize = pageSize
  return params
}

function handleStatusChange(name) {
  handleStatusSelectChange(name)
  tabStatus.value = name
  if (props.type == 1) {
    columns.value = handleColumns(name)
    tableKey.value++
  }
  if (props.type == 0 && name == followStatusMap['需处理']) {
    getMemberCodeList()
  } else if (props.type == 1 && name == followStatusMap['待模特确认']) {
    getModelSelectList()
  }
  resetQuery(true)
}

function handleShootModelChange(val) {
  if (val) {
    includeFamilyDisabled.value = false
  } else {
    includeFamilyDisabled.value = true
    queryParams.value.includeFamily = 0
  }
}

function pageChange(page) {
  pageNum.value = page.currentPage
  handleQuery()
}

function selectionChange(val) {
  multipleSelection.value = val
}

function sortChange(val) {
  // console.log(val)
  if (val.order) {
    sortType.value = val.prop
    isAsc.value = val.order == 'ascending' ? 1 : 0
  } else {
    sortType.value = ''
    isAsc.value = 0
  }
  handleQuery()
}

function handleStatusSelectChange(val) {
  if (val == followStatusMap['需处理']) {
    handleStatusSelect.value = [
      { label: '未通知', value: handleStatusMap['未通知'] },
      { label: '延迟发货提醒', value: handleStatusMap['延迟发货提醒'] },
      { label: '催发货提醒', value: handleStatusMap['催发货提醒'] },
      { label: '地址变更通知', value: handleStatusMap['地址变更通知'] },
      { label: '标记发货提醒', value: handleStatusMap['标记发货提醒'] },
    ]
  } else if (val == followStatusMap['暂不处理']) {
    handleStatusSelect.value = [
      { label: '已通知', value: handleStatusMap['已通知'] },
      { label: '延迟发货', value: handleStatusMap['延迟发货'] },
      { label: '延迟发货已提醒', value: handleStatusMap['延迟发货已提醒'] },
      { label: '催发货已提醒', value: handleStatusMap['催发货已提醒'] },
      { label: '变更已通知', value: handleStatusMap['变更已通知'] },
      { label: '标记发货', value: handleStatusMap['标记发货'] },
      { label: '标记发货已提醒', value: handleStatusMap['标记发货已提醒'] },
    ]
  }
}

// 查看产品图
function showProductImage(e, url) {
  if (url) {
    showTooltips(e, `<img src="${proxy.$picUrl + url}" style="width: 120px; height: 120px;" />`)
  }
}

// 查看说明图
function showLatestRemarkImage(e, urls, text) {
  let dom = `<div style="max-width: 500px;">
    <div class="template-pre">${text}</div>
  `
  if (urls) {
    dom += `<div class="flex-start gap-10" style="margin-top: 10px;">`
    urls.forEach(url => {
      dom += `<img src="${proxy.$picUrl + url}" style="width: 80px; height: 80px;" />`
    })
    dom += `</div>`
  }

  dom += `</div>`
  showTooltips(e, dom)
}

function handleMouseEnter(e, id) {
  ModelInfoPopoverRef.value?.open(e.target, id)
}
function handleMouseLeave() {
  ModelInfoPopoverRef.value?.close()
}
// 标记
function handleMarkDialog(row) {
  MarkDialogRef.value?.open([row])
}
function handleBatchMark() {
  MarkDialogRef.value?.open(multipleSelection.value)
}

// 去发货
function handleDeliverGoods(row) {
  ConfirmDeliverGoodsRef.value?.open(row.videoId, row.videoStatus === orderStatusMap['需发货'])
}

// 复制
function handleCopy(row) {
  let text = handleCopyText(row)
  if (text) copy('"----------------------------------------------\n' + text)
}
function handleBatchCopy() {
  let texts = []
  let u = []
  let s = []
  multipleSelection.value.forEach(item => {
    texts.push(handleCopyText(item))
    if (!u.includes(item.businessId)) {
      u.push(item.businessId)
    }
    if (!s.includes(item.handleStatus)) {
      s.push(item.handleStatus)
    }
  })
  let text = texts.join('\n"----------------------------------------------\n')
  if (text) {
    copy('"----------------------------------------------\n' + text)
    if (u.length <= 1 && s.length <= 1) return
    let tips = ''
    if (u.length > 1 && s.length > 1) {
      tips = '多个商家及多个处理状态'
    } else if (u.length > 1) {
      tips = '多个商家'
    } else if (s.length > 1) {
      tips = '多个处理状态'
    }
    copyTips('您复制的订单信息包含', tips, '的订单信息')
  }
}
function handleCopyText(row) {
  let text = []
  if (row.createOrderOperationUserName) {
    text.push(`运营：${row.createOrderOperationUserName}`)
  }
  if (row.videoCode && row.productChinese) {
    text.push(`▲${row.videoCode} ${row.productChinese}`)
  }
  if (row.shootModel) {
    let t = biz_model_type.value.find(item => item.value == row.shootModel.type)
    let n = biz_nation.value.find(item => item.value == row.shootModel.nation)
    text.push(`拍摄模特：${row.shootModel.name}${t ? `（${t.label}）` : ''}${n ? `${n.label}` : ''}`)
  }
  return text.join('\n')
}

// 复制英文
function handleCopyEnglish(row) {
  copy(handleCopyTextEnglish(row))
}
function handleBatchCopyEnglish() {
  let texts = []
  let modelNumber = []
  multipleSelection.value.forEach(item => {
    texts.push(handleCopyTextEnglish(item))
    if (item.shootModel && !modelNumber.includes(item.shootModel.id)) {
      modelNumber.push(item.shootModel.id)
    }
  })
  let text = texts.join('\n')
  if (text) {
    copy(text)
    if (modelNumber.length <= 1) return
    copyTips('您复制的内容包含', '多个模特', '的物流信息')
  }
}
function handleCopyTextEnglish(row) {
  let text = []
  if (row.videoCode && row.productChinese && row.productEnglish) {
    text.push(`${row.videoCode}`)
    text.push(`${row.productChinese}`)
    text.push(`${row.productEnglish}`)
  }
  if (row.productLink) {
    text.push(`${row.productLink}`)
  }
  text.push(`Tracking number:${row.number}`)
  text.push('----------------------------------------------')
  return text.join('\n')
}

function copyTips(text1, text2, text3) {
  ElMessageBox({
    title: '复制提醒',
    showCancelButton: false,
    showConfirmButton: false,
    message: () =>
      h(
        'div',
        {
          class: 'flex-column gap-10',
          style: {
            width: '396px',
          },
        },
        [
          h(
            ElIcon,
            {
              color: 'var(--el-color-warning)',
              size: 70,
            },
            () => h(Warning)
          ),
          h(
            'div',
            {
              style: {
                padding: '10px 15px 0',
                'text-align': 'center',
              },
            },
            [
              h('span', text1),
              h('span', { style: { color: 'var(--el-color-warning)' }, innerText: text2 }),
              h('span', text3),
            ]
          ),
        ]
      ),
  })
}

// 订单详情
function viewDetail(row) {
  window.open(`/order/details/${row.videoId}`)
}

// 查看跟进记录
function viewFollowUpRecord(row) {
  FollowUpRecordDialogRef.value?.open(row)
}

// 查看物流记录
function viewLogisticsRecord(row) {
  LogisticsRecordDialogRef.value?.open(row)
}

// 物流跟进
function handleLogisticsFollowUpDialog(row) {
  LogisticsFollowUpDialogRef.value?.open(row)
}

// 模特确认
function handleModelConfirmDialog(row) {
  ModelConfirmDialogRef.value?.open(row)
}

function handleOrderStatusTime(time) {
  if (!time) return ''
  let t = time.split(' ')[0]
  let date = new Date(t).getTime()
  let now = new Date().getTime()
  let day = Math.floor((now - date) / (1000 * 60 * 60 * 24))
  return '(已' + (day > 0 ? day : 0) + '天)'
}

function handleLogisticsStatus(val) {
  return logisticsStatus.find(item => item.value == val)?.label || '-'
}

function handleColumns(s) {
  if (props.type === 0) {
    return [
      { type: 'selection', 'reserve-selection': true, width: '55' },
      { prop: 'memberCode', label: '商家编码', width: '150' },
      { slot: 'videoCode', prop: 'videoCode', label: '视频编码', width: '150' },
      { slot: 'productChinese', prop: 'productChinese', label: '产品名称', minWidth: '200' },
      { slot: 'shootModel', prop: 'shootModel', label: '拍摄模特', width: '150' },
      {
        prop: 'createOrderOperationUserName',
        label: '订单运营',
        width: '150',
        handle: (data, row) => {
          return (data || '-') + '/' + (row.createOrderOperationUserNickName || '-')
        },
      },
      { prop: 'contact', label: '中文部客服', width: '130', handle: data => data?.name || '-' },
      {
        prop: 'handleStatus',
        label: '处理状态',
        width: '150',
        handle: data => {
          return handleStatusMap[data] || '-'
        },
      },
      {
        slot: 'latestRemark',
        prop: 'latestRemark',
        label: '说明',
        width: '150',
      },
    ]
  } else if (props.type === 1) {
    if (s === followStatusMap['待模特确认'] || s === followStatusMap['已结束']) {
      return [
        { type: 'selection', 'reserve-selection': true, width: '55' },
        { prop: 'number', label: '物流单号', width: '150' },
        { slot: 'videoCode', prop: 'videoCode', label: '视频编码', width: '150' },
        { slot: 'productChinese', prop: 'productChinese', label: '产品名称', minWidth: '200' },
        { slot: 'shootModel', prop: 'shootModel', label: '拍摄模特', width: '150' },
        {
          prop: 'createOrderUserName',
          label: '订单运营',
          width: '150',
          handle: (data, row) => {
            return (data || '-') + '/' + (row.createOrderUserNickName || '-')
          },
        },
        { prop: 'contact', label: '中文部客服', width: '130', handle: data => data?.name || '-' },
        { prop: 'issue', label: '英文部客服', width: '130', handle: data => data?.name || '-' },
        {
          slot: 'latestMainStatusSketch',
          prop: 'latestMainStatusSketch',
          label: '物流状态',
          width: '220',
          sortable: 'custom',
        },
        {
          prop: 'modelResult',
          label: '模特反馈结果',
          width: '150',
          handle: data => {
            if (data == 0) {
              return '未更新'
            }
            if (data == 1) {
              return '已询问'
            }
            if (data == 2) {
              return '模特已收货'
            }
            if (data == 3) {
              return '丢件'
            }
            if (data == 4) {
              return '订单回退'
            }
            if (data == 5) {
              return '待通知拍摄'
            }
            if (data == 6) {
              return '已通知拍摄'
            }
            return '-'
          },
        },
        {
          slot: 'lastTime',
          prop: 'lastTime',
          label: '最后更新时间',
          width: '220',
          sortable: 'custom',
        },
      ]
    }
    return [
      { type: 'selection', 'reserve-selection': true, width: '55' },
      { prop: 'number', label: '物流单号', width: '150' },
      { slot: 'videoCode', prop: 'videoCode', label: '视频编码', width: '150' },
      { slot: 'productChinese', prop: 'productChinese', label: '产品名称', minWidth: '200' },
      { slot: 'shootModel', prop: 'shootModel', label: '拍摄模特', width: '150' },
      {
        prop: 'createOrderUserName',
        label: '订单运营',
        width: '150',
        handle: (data, row) => {
          return (data || '-') + '/' + (row.createOrderUserNickName || '-')
        },
      },
      { prop: 'contact', label: '中文部客服', width: '130', handle: data => data?.name || '-' },
      { prop: 'issue', label: '英文部客服', width: '130', handle: data => data?.name || '-' },
      {
        slot: 'latestMainStatusSketch',
        prop: 'latestMainStatusSketch',
        label: '物流状态',
        width: '220',
        sortable: 'custom',
      },
      {
        slot: 'lastTime',
        prop: 'lastTime',
        label: '最后更新时间',
        width: '220',
        sortable: 'custom',
      },
    ]
  }
}

// 获取tab统计数据
function getStatistics() {
  logisticStatistics().then(res => {
    if (props.type === 0) {
      tabList.value[0].number = res.data.needHandleCount
      tabList.value[1].number = res.data.tempHoldCount
    } else if (props.type === 1) {
      tabList.value[0].number = res.data.needFollowCount
      tabList.value[1].number = res.data.modelConfirmPendCount
    }
  })
}

// 获取商家编码下拉列表
function getMemberCodeList() {
  memberCodeList({
    logisticStatus: props.type,
    followStatus: followStatusMap['需处理'],
  }).then(res => {
    if (res.data && Array.isArray(res.data)) {
      memberCodeSelect.value = res.data.map(item => ({
        label: `${item.memberCode} (${item.logisticFollowNum})`,
        value: item.memberCode,
      }))
    } else {
      memberCodeSelect.value = []
    }
  })
}

// 获取拍摄模特下拉列表
function getModelSelectList() {
  modelListSelectmodelListSelect({
    logisticStatus: props.type,
    followStatus: followStatusMap['待模特确认'],
  }).then(res => {
    if (res.data && Array.isArray(res.data)) {
      shootModelSelect.value = res.data.map(item => ({
        name: item.modelName,
        number: item.logisticFollowNum,
        value: item.modelId,
      }))
    } else {
      shootModelSelect.value = []
    }
  })
}

function init() {
  if (props.type === 0) {
    handleStatusSelectChange(followStatusMap['需处理'])
    tabList.value = [
      { label: '需处理', value: followStatusMap['需处理'], number: 0 },
      { label: '暂不处理', value: followStatusMap['暂不处理'], number: 0 },
    ]
    tabStatus.value = followStatusMap['需处理']
    columns.value = handleColumns()

    getMemberCodeList()
  } else if (props.type === 1) {
    tabList.value = [
      { label: '需跟进', value: followStatusMap['需跟进'], number: 0 },
      { label: '待模特确认', value: followStatusMap['待模特确认'], number: 0 },
      { label: '无需跟进', value: followStatusMap['无需跟进'], number: 0 },
      { label: '已结束', value: followStatusMap['已结束'], number: 0 },
    ]
    tabStatus.value = followStatusMap['需跟进']
    columns.value = handleColumns()

    getModelSelectList()

    contactSelectList({ followStatus: followStatusMap['需跟进'] }).then(res => {
      if (res.data) {
        contactList.value = res.data.map(item => ({
          ...item,
          name: item.name + `(${item.id})`,
        }))
      }
    })
    issueSelectList({ followStatus: followStatusMap['需跟进'] }).then(res => {
      if (res.data) {
        issueList.value = res.data.map(item => ({
          ...item,
          name: item.name + `(${item.id})`,
        }))
      }
    })
  }


  queryParams.value.aboutMe = true
  onQuery()
}
init()
</script>

<style lang="scss" scoped>
.logistics-table-page {
}
</style>
