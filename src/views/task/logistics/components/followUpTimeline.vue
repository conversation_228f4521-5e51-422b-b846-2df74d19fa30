<template>
  <div class="follow-up-record-box" v-loading="loading">
    <el-empty description="暂无记录" v-if="!recordList.length" />
    <el-timeline style="width: 100%; margin-top: 20px" v-else>
      <el-timeline-item
        v-for="(item, index) in recordList"
        :key="index"
        :color="index ? '' : '#4fcc33'"
      >
        <div class="record-item">
          <div class="record-time">{{ parseTime(item.eventExecuteTime, '{y}-{m}-{d} {h}:{i}') }}</div>
          <div class="title">{{ item.eventName }}</div>
          <div class="template-pre tip">{{ item.eventContent }}</div>
          <div v-if="item.resources">
            <ViewerImageList :data="item.resources" is-preview-all :show-delete-btn="false" />
          </div>
        </div>
      </el-timeline-item>
    </el-timeline>
  </div>
</template>

<script setup>
import ViewerImageList from '@/components/ImagePreview/viewerImageList.vue'
import { parseTime } from '@/utils/ruoyi'
import { logisticFollowDetail } from '@/api/task/logistics.js'

const props = defineProps({
  id: {
    type: [String, Number],
  },
})

const recordList = ref([])
const loading = ref(false)

function getList() {
  if (!props.id) return
  loading.value = true
  logisticFollowDetail(props.id)
    .then(res => {
      if (res.data) {
        recordList.value = res.data.orderVideoLogisticFollowRecords || []
      }
    })
    .finally(() => {
      loading.value = false
    })
}

onMounted(() => {
  getList()
})
</script>

<style scoped lang="scss">
.follow-up-record-box {
  padding-left: 88px;
  max-height: 450px;
  overflow: hidden;
  overflow-y: auto;

  .record-item {
    .record-time {
      position: absolute;
      top: 0;
      left: -125px;
      line-height: 24px;
      color: var(--el-text-color-secondary);
      font-size: var(--el-font-size-small);
    }
    .title {
      font-size: 15px;
      font-weight: 600;
    }
    .tip {
      color: var(--el-text-color-secondary);
      font-size: var(--el-font-size-small);
      margin: 3px 0 10px;
    }
  }
}
</style>
