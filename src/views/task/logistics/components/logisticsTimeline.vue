<template>
  <div class="logistics-record-box" v-loading="loading">
    <el-empty description="暂无记录" v-if="!logisticsList.length" />
    <Logistics v-else :dataArray="logisticsList" />
  </div>
</template>

<script setup>
import Logistics from '@/views/order/details/components/logistics.vue'
import { logisticFollowDetail } from '@/api/task/logistics.js'

const props = defineProps({
  id: {
    type: [String, Number],
  },
})

const logisticsList = ref([])
const loading = ref(false)

function getList() {
  if (!props.id) return
  loading.value = true
  logisticFollowDetail(props.id)
    .then(res => {
      if (res.data) {
        logisticsList.value = res.data.logisticInfo || []
      }
    })
    .finally(() => {
      loading.value = false
    })
}

getList()
</script>

<style scoped lang="scss">
.logistics-record-box {
  margin-top: 15px;
  padding-left: 20px;
  min-height: 200px;
  max-height: 450px;
  overflow: hidden;
  overflow-y: auto;

  .record-item {
    .record-time {
      position: absolute;
      top: 0;
      left: -125px;
      line-height: 24px;
      color: var(--el-text-color-secondary);
      font-size: var(--el-font-size-small);
    }
    .title {
      font-size: var(--el-font-size-large);
    }
    .tip {
      color: var(--el-text-color-secondary);
      font-size: var(--el-font-size-small);
      margin: 3px 0 10px;
    }

  }
}
</style>
