// 处理状态
const handleStatusOptions = [
  { label: '未通知', value: 0, title: '未通知标记' },
  { label: '已通知', value: 1, title: '标记' },
  { label: '延迟发货', value: 2, title: '标记' },
  { label: '延迟发货提醒', value: 3, title: '延迟发货提醒标记' },
  { label: '延迟发货已提醒', value: 4, title: '标记' },
  { label: '催发货提醒', value: 5, title: '催发货提醒标记' },
  { label: '催发货已提醒', value: 6, title: '标记' },
  { label: '地址变更通知', value: 7, title: '地址变更通知标记' },
  { label: '变更已通知', value: 8, title: '标记' },
  { label: '标记发货', value: 10 },
  { label: '标记发货提醒', value: 11, title: '标记发货提醒' },
  { label: '标记发货已提醒', value: 12 },
  { label: '通知确认模特', value: 13, title: '通知确认模特' },
  { label: '已通知确认模特', value: 14, title: '通知确认模特' },
  { label: '催确认模特提醒', value: 15, title: '催确认模特提醒' },
  { label: '已通知催确认模特', value: 16, title: '通知确认模特' },
]
const handleStatusMap = {}
handleStatusOptions.forEach(item => {
  handleStatusMap[item.value] = item.label
  handleStatusMap[item.label] = item.value
})

// 跟进状态
const followStatusOptions = [
  { label: '需处理', value: 1 },
  { label: '暂不处理', value: 2 },
  { label: '需跟进', value: 11 },
  { label: '待模特确认', value: 12 },
  { label: '无需跟进', value: 13 },
  { label: '已结束', value: 14 },
]
const followStatusMap = {}
followStatusOptions.forEach(item => {
  followStatusMap[item.value] = item.label
  followStatusMap[item.label] = item.value
})

export {
  handleStatusOptions,
  handleStatusMap,
  followStatusOptions,
  followStatusMap
}