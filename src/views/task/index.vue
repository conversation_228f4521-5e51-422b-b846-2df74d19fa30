<template>
  <div class="task-container">
    <el-radio-group v-model="curTab" size="large" @change="handleTabChange()">
      <el-radio-button :value="1">售后单</el-radio-button>
      <el-radio-button :value="2">工单</el-radio-button>
    </el-radio-group>
    <AfterSalePage v-if="curTab === 1 && checkPermi(['task:afterSale:list'])" />
    <WorkOrderPage v-if="curTab === 2 && checkPermi(['task:workOrder:list'])" />
    <el-backtop style="z-index: 1000" :bottom="100" target=".main-container">
      <div
        class="flex-center"
        style="
          height: 100%;
          width: 100%;
          border-radius: 50%;
          background-color: var(--el-bg-color-overlay);
          box-shadow: var(--el-box-shadow-lighter);
          text-align: center;
          line-height: 40px;
          color: #1989fa;
        "
      >
        <el-icon><Top /></el-icon>
      </div>
    </el-backtop>
  </div>
</template>
<script setup>
import AfterSalePage from '@/views/task/afterSale/page.vue'
import WorkOrderPage from '@/views/task/workOrder/page.vue'
import { checkPermi } from '@/utils/permission'

const route = useRoute()

const taskRef = ref(null)
const curTab = ref(1)

if (route.query.type) {
  curTab.value = Number(route.query.type) || ''
}

function handleClick(name) {
  activeName.modelValue = name
  taskRef.value.changeListInit(name)
}
function handleTabChange() {}
</script>

<style lang="scss" scoped>
.task-container {
  padding: 20px 20px 0 20px;
}
</style>
