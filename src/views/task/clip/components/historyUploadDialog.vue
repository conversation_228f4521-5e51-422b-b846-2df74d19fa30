<template>
  <div>
    <el-dialog
      align-center
      append-to-body
      title="历史上传记录"
      v-model="isShow"
      width="900px"
      :close-on-click-modal="true"
      :close-on-press-escape="false"
      @close="close"
    >
      <div class="table-box">
        <el-table :data="tableList" ref="tableRef" border height="600px">
          <el-table-column label="提交人" align="center" width="100">
            <template v-slot="{ row }">
              <div>
                <div>{{ row.object == 1 ? '商家' : '运营' }}-{{ row.userName }}</div>
                <div>{{ row.time }}</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="上传次数" align="center" width="85" prop="count">
            <template v-slot="{ row }">
              <div>{{ row.count == 1 ? '首次上传' : `第${row.count}次上传` }}</div>
            </template>
          </el-table-column>
          <el-table-column label="上传信息" align="center">
            <template v-slot="{ row }">
              <div style="text-align: start">
                <div class="one-ell productLink">
                  上传链接：
                  <el-link :underline="false" target="_blank" type="primary" :href="row.needUploadLink">
                    {{ row.needUploadLink }}
                  </el-link>
                </div>
                <div style="line-break: anywhere">上传标题：{{ row.videoTitle || '-' }}</div>
                <div class="one-ell">上传账号：{{ row.uploadAccount || '-' }}</div>
                <div class="one-ell flex-start">
                  视频封面：
                  <el-button
                    link
                    type="primary"
                    v-if="row.videoCover"
                    v-btn
                    @click="showViewer([row.videoCover], { raw: true })"
                  >
                    查看
                  </el-button>
                </div>
                <div style="line-break: anywhere; word-break: break-all">
                  上传备注：{{ row.remark || '-' }}
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="最终上传链接" align="center" width="120" class-name="link-box">
            <template v-slot="{ row }">
              <div class="tag">
                <RollbackTag :row="row" reject-field="isRejectAfterSubmitModel" />
              </div>
              <div v-if="row.uploadLink">
                <el-link :underline="false" target="_blank" type="primary" :href="row.uploadLink">
                  {{ row.uploadLink }}
                </el-link>
              </div>
              <div v-else>-</div>
            </template>
          </el-table-column>
          <el-table-column label="上传状态" align="center" width="90">
            <template v-slot="{ row }">
              <div>{{ uploadStatusList.find(item => item.value == row.status)?.label || '-' }}</div>
              <div v-if="row.status == 4 || row.status == 3">{{ row.operateRemark }}</div>
              <!-- <div
                v-if="
                  (row.status != 4 || (!row.operateRemark && row.status == 4)) &&
                  (row.status != 3 || (!row.operateRemark && row.status == 3))
                "
              >
                {{ uploadStatusList.find(item => item.value == row.status)?.label || '-' }}
              </div> -->
              <!-- <el-tooltip
                v-else
                class="box-item"
                effect="dark"
                :content="row.operateRemark"
                placement="top"
                :hide-after="0"
              >
                <template #content>
                  <div style="width: 600px; white-space: pre-wrap; max-height: 45vh; overflow-y: auto">
                    {{ row.operateRemark }}
                  </div>
                </template>
                <div>{{ row.status == 4 ? '上传失败' : '取消上传' }}</div>
              </el-tooltip> -->
            </template>
          </el-table-column>
          <el-table-column label="上传人员" align="center" width="100">
            <template v-slot="{ row }">
              <div v-if="row.uploadUserName">
                <div>{{ row.uploadUserName }}</div>
                <div>{{ row.uploadTime }}</div>
              </div>
              <div v-else>-</div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import RollbackTag from '@/views/order/components/rollbackTag.vue'

import { historyUploadRecord } from '@/api/clip'

import { useViewer } from '@/hooks/useViewer'

const { showViewer } = useViewer()
const isShow = ref(false)
const tableList = ref([])
const uploadStatusList = [
  { label: '已上传', value: 0 },
  { label: '待上传', value: 1 },
  { label: '待确认上传', value: 2 },
  { label: '取消上传', value: 3 },
  { label: '上传失败', value: 4 },
]

const emits = defineEmits(['success'])
const open = id => {
  isShow.value = true
  historyUploadRecord({ uploadLinkId: id }).then(res => {
    tableList.value = res.data || []
  })
}

const close = () => {
  isShow.value = false
}

const confirm = () => {
  close()
}

defineExpose({ open, close })
</script>

<style lang="scss" scoped>
.table-box {
  :deep(.el-table) {
    .link-box {
      position: relative;
      .tag {
        position: absolute;
        top: 0;
        right: 0;
      }
      .el-link {
        display: block;
        .el-link__inner {
          display: block;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
  }
  .productLink {
    position: relative;
    padding-right: 5px;

    :deep(.el-link) {
      display: contents;

      .el-link__inner {
        display: inline;
      }
    }
  }
}
</style>
