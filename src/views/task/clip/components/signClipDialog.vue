<template>
  <div>
    <el-dialog
      align-center
      append-to-body
      title="标记已剪辑"
      v-model="isShow"
      width="600px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <div
        class="flex-center"
        v-if="!isShowScore && finishTaskList.length == 0"
        style="padding: 20px 0 30px 0; font-size: 20px"
      >
        确认标记已剪辑？
      </div>
      <div>
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          :disabled="disabled"
          :validate-on-rule-change="false"
          label-width="120px"
          @submit.native.prevent
        >
          <el-form-item
            label="完结任务"
            prop="taskDetailIds"
            v-if="finishTaskList && finishTaskList.length > 0"
          >
            <div v-loading="taskLoading" class="task-box">
              <div>
                <span>是否完结任务单</span>
                <span style="color: #7f7f7f">（剪辑完成后，将同步关闭相应任务单）</span>
              </div>
              <TaskButton v-model="form.taskDetailIds" :checkList="finishTaskList" :width="448" />
            </div>
          </el-form-item>
          <el-form-item label="视频评分" prop="videoScore" v-if="isShowScore">
            <el-rate v-model="form.videoScore" :max="10" allow-half />
            已选{{ form.videoScore }}星
          </el-form-item>
          <el-form-item label="评价内容" prop="videoScoreContent" v-if="isShowScore">
            <el-input
              v-model="form.videoScoreContent"
              style="width: 100%"
              :rows="4"
              type="textarea"
              maxlength="300"
              show-word-limit
              placeholder="请对本次视频做出详细的评价"
              clearable
            />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="close">取 消</el-button>
          <el-button type="primary" @click="confirm">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import TaskButton from '@/components/Button/TaskButton.vue'
import { getFeedbackMaterialPendingTask } from '@/api/task/workOrder'
import { markClip, checkCanScore } from '@/api/clip'
import { copy } from '@/utils/index'
import { ElMessage } from 'element-plus'

const isShow = ref(false)
const taskLoading = ref(false)
const finishTaskList = ref([])
const isShowScore = ref(false)

const emits = defineEmits(['success'])

const disabled = ref(false)
const formRef = ref()
const form = ref({
  videoScore: null,
  videoScoreContent: '',
  taskDetailIds: [],
})

const rules = {
  taskDetailIds: [{ required: true, message: '请选择完结任务', trigger: 'change' }],
  videoScore: [
    { required: true, message: '请选择视频评分', trigger: 'blur' },
    { validator: checkMark, trigger: 'change' },
  ],
}

function checkMark(rule, value, callback) {
  if (value > 0) {
    if (value < 1) {
      return callback(new Error('请选择视频评分最少1分'))
    }
    return callback()
  }
  return callback(new Error('请选择视频评分'))
}

const baseInfoStr = ref('')

const open = (id, videoId, videoCode, productChinese, name, isToBeEdited = false) => {
  baseInfoStr.value = `${videoCode} ${productChinese} ${name}`
  form.value.id = id
  form.value.videoId = videoId
  getTaskList(videoId, isToBeEdited)
  handleCheckCanScore(videoId)
  isShow.value = true
}

const close = () => {
  form.value = {
    videoScore: null,
    taskDetailIds: [],
    videoScoreContent: '',
  }
  formRef.value.resetFields()
  isShowScore.value = false
  finishTaskList.value = []
  baseInfoStr.value = ''
  isShow.value = false
}

const confirm = () => {
  formRef.value.validate(valid => {
    if (valid) {
      markClip({
        ...form.value,
        taskDetailIds:
          form.value.taskDetailIds?.length && form.value.taskDetailIds[0] != -1
            ? form.value.taskDetailIds
            : undefined,
      })
        .then(res => {
          ElMessage.success('标记已剪辑成功')
          copy(baseInfoStr.value)
          emits('success')
          close()
        })
        .catch(() => {})
    }
  })
}

function getTaskList(videoId, isToBeEdited) {
  getFeedbackMaterialPendingTask({ videoId, afterSaleClass: '1,2', isToBeEdited }).then(res => {
    finishTaskList.value = res.data || []
  })
}

function handleCheckCanScore(videoId) {
  checkCanScore({ videoId }).then(res => {
    isShowScore.value = res.data
  })
}

defineExpose({ open, close })
</script>

<style lang="scss" scoped>
.task-box {
  width: 100%;
  max-height: 300px;
  overflow: hidden;
  overflow-y: auto;
}
</style>
