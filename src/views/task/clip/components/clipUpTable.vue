<template>
  <div class="clip-table">
    <el-table ref="tableRef" :data="list" style="width: 100%" border row-key="id" highlight-current-row>
      <el-table-column
        prop="productPic"
        label="产品图"
        align="center"
        width="130"
        class-name="product-img-box"
      >
        <template v-slot="{ row }">
          <div class="corner-mark-hint" v-if="row.productPicChange">调</div>
          <div class="flex-start top-tag">
            <el-tag v-if="row.isCare" type="danger" size="small" round>照顾单</el-tag>
          </div>
          <el-image
            style="width: 90px; height: 90px; cursor: pointer"
            :style="{ 'margin-top': row.isCare ? '15px' : '0' }"
            :src="
              row.productPic
                ? $picUrl +
                  row.productPic +
                  '?imageMogr2/format/webp/interlace/1/quality/100/interlace/0/thumbnail/200x'
                : ''
            "
            fit="scale-down"
            preview-teleported
            @click="() => row.productPic && showViewer([$picUrl + row.productPic])"
          >
            <template #error>
              <img :src="$picUrl + 'static/assets/no-img.png'" alt="" style="width: 100%; height: 100%" />
            </template>
          </el-image>
        </template>
      </el-table-column>
      <el-table-column prop="msg" label="产品信息" minWidth="320">
        <template v-slot="{ row }">
          <div class="product-info-box">
            <div class="flex-between">
              <span>视频编码：{{ row.videoCode }}</span>
              <div>
                <CopyButton class="btn" plain size="small" :copy-content="handleCopyInfo(row)" />
                <el-button class="btn" size="small" v-btn plain @click="handleAction('更多', row)">
                  更多
                </el-button>
              </div>
            </div>
            <div class="one-ell">中文名称：{{ row.productChinese }}</div>
            <div class="one-ell" style="word-break: break-all">英文名称：{{ row.productEnglish }}</div>
            <div class="one-ell productLink">
              产品链接：
              <el-link :underline="false" target="_blank" type="primary" :href="row.productLink">
                {{ row.productLink }}
              </el-link>
            </div>
            <biz-model-platform :value="row.platform" />
            <biz-model-type :value="row.modelType" />
            <biz-nation :value="row.shootingCountry" />
            <template v-for="op in videoFormatOptions" :key="op.value">
              <el-tag class="tag" v-if="op.value == row.videoFormat" type="warning" size="small" round>
                {{ op.label }}
              </el-tag>
            </template>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="model2" label="拍摄模特" align="center" width="130" class-name="shoot-model-box">
        <template v-slot="{ row }">
          <div style="padding: 5px 0">
            <div
              v-if="row.shootModel"
              @mouseenter="handleMouseEnter($event, row.shootModel.id)"
              @mouseleave="handleMouseLeave($event, row.shootModel.id)"
            >
              <div
                class="hint-box"
                style="pointer-events: none"
                v-if="row.intentionModel?.account && row.intentionModel?.account != row.shootModel?.account"
              >
                <div class="exchange">
                  <span>换</span>
                </div>
              </div>
              <el-avatar
                class="model-avatar"
                icon="UserFilled"
                :src="$picUrl + row.shootModel.modelPic + '!1x1compress'"
              />
              <div>{{ row.shootModel.name }}</div>
              <div>{{ row.shootModel?.account ? `(ID:${row.shootModel.account})` : '-' }}</div>
              <biz-model-type-new :value="row.shootModel.type" />
              <!-- <biz-model-type :value="row.shootModel.type" /> -->
            </div>
            <div v-else>-</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="contact" label="关联客服" align="center" width="110">
        <template v-slot="{ row }">
          <div>{{ row.contact?.name || '-' }} / {{ row.issue?.name || '-' }}</div>
        </template>
      </el-table-column>
      <el-table-column
        prop="needUploadLink"
        label="上传链接/ASIN"
        align="center"
        width="160"
        class-name="link-box"
      >
        <template v-slot="{ row }">
          <div v-if="row.needUploadLink || row.asin">
            <el-link v-btn :underline="false" target="_blank" type="primary" :href="row.needUploadLink">
              {{ row.needUploadLink }}
            </el-link>
            <CopyButton
              v-if="row.asin"
              class="btn"
              style="color: #606266; font-size: 14px"
              plain
              link
              size="small"
              :copy-content="row.asin"
            >
              {{ row.asin || '-' }}
            </CopyButton>
            <span v-else>-</span>
            <!-- <div>{{ row.asin || '-' }}</div> -->
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="videoTitle" label="上传标题" align="center" width="160">
        <template v-slot="{ row }">
          <div class="corner-mark-hint" v-if="row.coverAndTitleChange">调</div>
          <div>
            <!-- <CopyButton
              v-if="row.videoTitle"
              class="btn"
              style="color: #606266; font-size: 14px; white-space: pre-line;"
              plain
              type="text"
              size="small"
              :copy-content="row.videoTitle"
            >
              {{ row.videoTitle }}
            </CopyButton> -->
            <div style="cursor: pointer" @click="hanldeCopyTitle(row.videoTitle)">{{ row.videoTitle }}</div>
            <el-button
              v-if="row.videoCover"
              v-btn
              link
              type="primary"
              @click="showViewer([row.videoCover], { raw: true })"
            >
              查看视频封面
            </el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="remark" label="上传备注" align="center" width="110">
        <template v-slot="{ row }">
          <div style="display: flex" v-if="row.remark">
            <div class="one-ell" v-has-ellipsis:remarkMore="row" style="width: 95%" :key="Math.random()">
              {{ row.remark }}
            </div>
            <div>
              <el-button
                v-btn
                v-if="row.remarkMore"
                link
                type="primary"
                @click="handleAction('上传备注', row)"
              >
                更多
              </el-button>
            </div>
          </div>
          <div v-else>-</div>
        </template>
      </el-table-column>
      <el-table-column prop="uploadAccount" label="上传账号" align="center" width="110">
        <template v-slot="{ row }">
          <div>
            <div>{{ row.uploadAccount }}</div>
            <el-button
              v-btn
              link
              type="primary"
              v-if="checkPermi(['my:clip:selectAccount']) && row.status == 1"
              @click="handleAction('上传账号', row)"
            >
              {{ row.uploadAccount ? '修改' : '+选择' }}
            </el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="uploadUserName" label="上传人" align="center" width="110">
        <template v-slot="{ row }">
          <div v-if="row.uploadUserName">
            <div>{{ row.uploadUserName }}</div>
            <div>{{ row.uploadTime }}</div>
          </div>
          <div v-else>-</div>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="上传状态" align="center" width="110">
        <template v-slot="{ row }">
          <div>
            <div>{{ uploadStatus.find(item => item.value == row.status)?.label }}</div>
            <div>({{ row.count == 0 || row.count == 1 ? '首次' : '第' + row.count + '次' }})</div>
            <el-button v-btn link type="primary" @click="handleAction('上传记录', row)">查看</el-button>
          </div>
        </template>
      </el-table-column>

      <el-table-column
        fixed="right"
        label="操作"
        :align="'center'"
        width="150"
        v-if="curTab != curTabStatusMap['已关闭']"
      >
        <template v-slot="{ row }">
          <div class="flex-center gap-5 task-tag-box">
            <el-button
              type="info"
              size="small"
              link
              v-btn
              bg
              v-if="row.workOrderTaskStatus === 1"
              @click="toTaskPage('2', row)"
            >
              工单中
            </el-button>
            <el-button
              type="info"
              size="small"
              link
              bg
              v-btn
              v-else-if="row.workOrderTaskStatus === 4"
              @click="toTaskPage('2', row)"
            >
              工单已完结
            </el-button>
            <el-button
              type="info"
              size="small"
              link
              v-btn
              bg
              v-if="row.afterSaleTaskStatus === 1"
              @click="toTaskPage('1', row)"
            >
              售后中
            </el-button>
            <el-button
              type="info"
              size="small"
              link
              v-btn
              bg
              v-else-if="row.afterSaleTaskStatus === 4"
              @click="toTaskPage('1', row)"
            >
              售后已完结
            </el-button>
          </div>
          <div class="flex-center gap-5 task-button-box">
            <el-button
              v-btn
              link
              type="primary"
              v-if="row.status == 1 && checkPermi(['my:clip:sign'])"
              @click="handleAction('标记上传', row)"
            >
              标记
            </el-button>

            <template v-if="row.status == 2">
              <el-button
                v-btn
                link
                type="primary"
                v-if="checkPermi(['my:clip:uploadSuccess'])"
                @click="handleAction('视频上传', row, 'success')"
              >
                上传成功
              </el-button>
              <el-button
                v-btn
                link
                type="primary"
                v-if="checkPermi(['my:clip:uploadError'])"
                @click="handleAction('视频上传', row, 'error')"
              >
                上传失败
              </el-button>
            </template>

            <el-button
              v-btn
              link
              type="primary"
              v-if="checkPermi(['my:clip:uploadCancel'])"
              @click="handleAction('视频上传', row, 'cancel')"
            >
              取消上传
            </el-button>
            <!-- <el-button
              v-btn
              link
              type="primary"
              v-if="checkPermi(['my:clip:creatWork'])"
              @click="handleAction('创建任务单', row)"
            >
              创建任务工单
            </el-button> -->
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import CopyButton from '@/components/Button/CopyButton.vue'
import { picCountOptions, orderRefundStatusMap, videoFormatOptions } from '@/views/order/list/data.js'
import { curTabStatusMap, uploadStatus } from '@/views/task/clip/data.js'
import { copy } from '@/utils/index.js'
import { useViewer } from '@/hooks/useViewer'
import { checkPermi } from '@/utils/permission'

const emits = defineEmits(['action', 'selectionChange', 'uploadChange', 'hover'])

const { showViewer } = useViewer()

const props = defineProps({
  list: {
    type: Array,
    default: () => [],
  },
  curTab: {
    type: [String, Number],
    default: '0',
  },
  tabBoxHeight: {
    type: Number,
    default: 160,
  },
})

const checked1 = ref(false)
const tableRef = ref()

watch(
  () => [props.tabBoxHeight, props.curTab],
  async () => {
    await nextTick()
    if (tableRef.value) {
      const headerWrapper = tableRef.value.$el.querySelector('.el-table__header-wrapper')
      if (headerWrapper) {
        headerWrapper.style.top = props.tabBoxHeight + 'px'
      }
    }
  },
  { immediate: true }
)

function handleAction(btn, row, type) {
  emits('action', btn, row, type)
}

function hanldeCopyTitle(data) {
  if (!data) return
  copy(data)
}

function handleCopyInfo(data) {
  if (data.productLink && data.productLink != '') {
    return `视频编码：${data.videoCode}
中文名称：${data.productChinese}
产品链接：${data.productLink || ''}
拍摄模特：${data.shootModel?.name}`
  } else {
    return `视频编码：${data.videoCode}
中文名称：${data.productChinese}
拍摄模特：${data.shootModel?.name}`
  }
}

function handleMouseEnter(e, id) {
  emits('hover', e.target, id, true)
}
function handleMouseLeave(e, id) {
  emits('hover', e.target, id, false)
}

function toTaskPage(type, row) {
  const path = `/task/workOrder?type=${type}&keyword=${row.videoCode}`
  window.open(path, '_blank')
}
</script>

<style lang="scss" scoped>
.clip-table {
  .head-box {
    &-left {
      display: flex;
      align-items: center;
    }
    &-right {
      display: flex;
      gap: 0 5px;
    }
  }
  .productLink {
    position: relative;
    padding-right: 5px;

    :deep(.el-link) {
      display: contents;

      .el-link__inner {
        display: inline;
      }
    }
  }
  :deep(.el-table) {
    --el-table-current-row-bg-color: #cdf7fe;
    overflow: visible;
    // tr.hover-row > td {
    //   background-color: #cdf7fe !important;
    // }
    .link-box {
      position: relative;
      .tag {
        position: absolute;
        top: 0;
        right: 0;
      }
      .el-link {
        display: block;
        .el-link__inner {
          display: block;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
    .el-table__header-wrapper {
      position: sticky; // 设置粘性定位
      top: 110px; // 距离顶部的位置
      z-index: 5;
      th {
        height: 30px !important;
      }
    }
    th {
      text-align: center;
    }

    .el-button + .el-button {
      margin: 0 3px;
    }
    .el-table__header-wrapper {
      .seltAllbtnDis {
        .cell {
          visibility: hidden;
        }
      }
    }
    .corner-mark-hint {
      z-index: 999;
      position: absolute;
      right: 0px;
      top: 0px;
      color: #00bfbf;
      // background-color: #d0efef;
      padding: 0px 4px 6px;
      background-image: url('@/assets/icons/svg/corner-mark-flag.svg');
    }
    .product-info-box {
      .btn {
        padding: 2px 4px;
        height: auto;
        font-size: 12px;
      }
    }
    .product-img-box {
      position: relative;

      .top-tag {
        z-index: 9;
        position: absolute;
        top: 2px;
        left: 1px;

        .el-tag + .el-tag {
          margin-left: 5px;
        }
      }

      .productPic-img {
        width: 100%;
        height: 100%;
        position: relative;

        .productPic-btn {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }
      }
    }
    .shoot-model-box {
      // padding-top: 8px;
      position: relative;
      overflow: hidden;

      .hint-box {
        z-index: 999;
        position: absolute;
        left: -6px;
        top: -2px;
        // overflow: hidden;
        width: 32px;
        height: 25px;

        .exchange {
          background: var(--el-color-danger);
          color: #fff;
          font-size: 13px;
          width: 60px;
          transform: translate(-17px, -3px) scale(0.8) rotateZ(-45deg);
        }
      }
    }
    .task-tag-box {
      position: absolute;
      top: 0;
      right: 0;

      .el-button {
        padding: 2px 8px;
        height: 18px;
        border: 1px solid var(--el-color-info-light-7);
        margin: 0 !important;
      }
    }
    .task-button-box {
      flex-direction: column;
    }
  }
}
.btn-left {
  margin-left: 10px;
}
</style>
