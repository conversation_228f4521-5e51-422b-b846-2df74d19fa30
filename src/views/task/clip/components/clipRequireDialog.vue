<template>
  <div>
    <el-dialog
      align-center
      append-to-body
      :title="dialogTitle"
      v-model="isShow"
      width="700px"
      :close-on-click-modal="true"
      :close-on-press-escape="false"
      @close="close"
    >
      <div class="require-box">
        <template v-if="dialogTitle != '剪辑要求'">
          <div class="flex-start info-box">
            <el-image
              style="width: 90px; height: 90px; cursor: pointer"
              class="fs-0"
              :src="
                data.productPic
                  ? $picUrl +
                    data.productPic +
                    '?imageMogr2/format/webp/interlace/1/quality/100/interlace/0/thumbnail/200x'
                  : ''
              "
              fit="scale-down"
              preview-teleported
              @click="() => data.productPic && showViewer([$picUrl + data.productPic])"
            >
              <template #error>
                <img :src="$picUrl + 'static/assets/no-img.png'" alt="" style="width: 100%; height: 100%" />
              </template>
            </el-image>
            <div class="info-left">
              <div class="info-left__item">视频编码：{{ data.videoCode }}</div>
              <div class="info-left__item">中文名：{{ data.productChinese }}</div>
              <div class="info-left__item">英文名：{{ data.productEnglish }}</div>
              <div class="info-left__item">
                产品链接：
                <el-link :underline="false" target="_blank" type="primary" :href="data.productLink">
                  {{ data.productLink }}
                </el-link>
              </div>
              <biz-model-platform :value="data.platform" />
              <biz-model-type :value="data.modelType" />
              <biz-nation :value="data.shootingCountry" />
              <template v-for="op in videoFormatOptions" :key="op.value">
                <el-tag class="tag" v-if="op.value == data.videoFormat" type="warning" size="small" round>
                  {{ op.label }}
                </el-tag>
              </template>
            </div>
          </div>
          <div class="title">剪辑要求</div>
        </template>

        <el-timeline style="max-width: 650px; margin-top: 10px">
          <el-timeline-item v-for="(item, index) in activities" :key="index" color="#000">
            <template #default>
              <div class="timeline-box">
                <div class="timeline-box__title flex-between">
                  <div class="flex-center" style="align-items: baseline">
                    <span>
                      <span v-if="item.isInitial == 1">初始剪辑要求</span>
                      <el-tag type="warning" v-else>
                        {{
                          item.taskType == 2
                            ? workOrderTypeMap[item.workOrderType]
                            : item.afterSaleClass == 1
                            ? afterSaleVideoTypeMap[item.afterSaleVideoType]
                            : afterSalePicTypeMap[item.afterSalePicType]
                        }}
                      </el-tag>
                      <span v-if="item.isInitial != 1" style="margin: 0 10px">
                        {{ item.taskType == 2 ? '问题描述' : '剪辑补充需求' }}
                      </span>
                    </span>
                    <span>{{ item.submitBy }} {{ item.submitTime }}</span>
                    <RollbackTag :row="row" reject-field="isRejectAfterSubmitModel" />
                  </div>
                  <CopyButton class="btn" plain size="small" :copy-content="item.content"></CopyButton>
                </div>
                <div class="timeline-box__content">
                  {{ item.content }}
                </div>
                <ViewerImageList :data="item.issuePic" is-preview-all :show-delete-btn="false" />
              </div>
            </template>
          </el-timeline-item>
        </el-timeline>
        <div
          v-if="activities.length == 0 && dialogTitle == '剪辑要求'"
          class="flex-center"
          style="padding: 20px 0 50px 0; font-size: 20px"
        >
          暂无数据
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import CopyButton from '@/components/Button/CopyButton.vue'
import ViewerImageList from '@/components/ImagePreview/viewerImageList.vue'
import RollbackTag from '@/views/order/components/rollbackTag.vue'
import { useViewer } from '@/hooks/useViewer'
import { videoFormatOptions } from '@/views/order/list/data.js'
import { historyClipRecord } from '@/api/clip'

import { afterSalePicTypeMap, afterSaleVideoTypeMap, workOrderTypeMap } from '@/views/task/data.js'
import { row } from 'mathjs'

const { showViewer } = useViewer()

const isShow = ref(false)
const dialogTitle = ref('剪辑要求')
const activities = ref([])
const data = ref({
  productPic: '',
  productLink: '',
})

const emits = defineEmits(['success'])
const open = (type, videoId) => {
  type == 1 ? (dialogTitle.value = '剪辑要求') : (dialogTitle.value = '剪辑详情')
  getReuireDetail(videoId)
  isShow.value = true
}

const close = () => {
  activities.value = []
  isShow.value = false
}

const confirm = () => {
  close()
}

function getReuireDetail(videoId) {
  historyClipRecord({ videoId }).then(res => {
    activities.value = res.data?.historyClipRecordListVOS || []
    data.value = res.data?.orderVideoVO || {}
  })
}

defineExpose({ open, close })
</script>

<style lang="scss" scoped>
.require-box {
  max-height: 700px;
  overflow: auto;
  .title {
    font-size: 18px;
    color: #303133;
    font-weight: 600;
    margin: 20px 0;
  }
  :deep(.el-timeline-item:last-child::before) {
    content: '';
    border-left: 2px solid #000;
    left: 4px;
    position: absolute;
    height: 100%;
  }
  :deep(.el-timeline-item__tail) {
    border-left-color: #000;
  }
}
.timeline-box {
  .btn {
    padding: 2px 4px;
    height: auto;
    font-size: 12px;
  }
  &__title {
    display: flex;
    align-items: center;
  }
  &__content {
    margin-top: 10px;
    text-indent: 4ch;
    line-height: 1.7;
    white-space: pre-wrap;
    word-break: break-all;
  }
}
.info-box {
  width: 98%;
  align-items: start;
  .info-left {
    margin-left: 10px;
    &__item {
      line-break: anywhere;
      :deep(.el-link) {
        display: contents;
        .el-link__inner {
          display: inline;
        }
      }
    }
  }
}
</style>
