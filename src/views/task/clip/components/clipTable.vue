<template>
  <div class="clip-table">
    <div
      class="head-box flex-between"
      :style="{ top: tabBoxHeight + 'px' }"
      v-if="curTab == curTabStatusMap['待下载']"
    >
      <div class="head-box-left">
        <el-checkbox v-model="checked1" label="全选待领取" size="large" @click="selectAll" />
        <el-button
          class="btn-left"
          v-if="checkPermi(['my:clip:getMore'])"
          type="primary"
          size="small"
          v-btn
          :disabled="selectedRows.length == 0 && !checked1.value"
          @click="handleBulkCollection"
        >
          批量领取
        </el-button>
        <SortButton v-model="sort" @change="handleSortChange" style="font-size: 14px; margin-left: 20px">
          提交时间
        </SortButton>
      </div>
      <div class="head-box-right">
        <div>总反馈：{{ statics.totalFeedbackCount || 0 }}</div>
        <div>今日总反馈：{{ statics.todayFeedbackCount || 0 }}</div>
        <div>待领取：{{ statics.getCount || 0 }}</div>
        <div>待下载：{{ statics.downloadCount || 0 }}</div>
        <div>今日已下载：{{ statics.todayDownloadCount || 0 }}</div>
      </div>
    </div>
    <slot name="header"></slot>
    <el-table
      ref="tableRef"
      :data="list"
      style="width: 100%"
      border
      row-key="id"
      :highlight-current-row="props.curTab == curTabStatusMap['待下载'] ? true : false"
      :row-class-name="tableRowClassName"
      :header-cell-class-name="headerCheckboxStyle"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        v-if="curTab == curTabStatusMap['待下载']"
        type="selection"
        width="50"
        align="center"
        :reserve-selection="true"
        :selectable="isRowSelectable"
      />
      <el-table-column
        prop="productPic"
        label="产品图"
        align="center"
        width="130"
        class-name="product-img-box"
      >
        <template v-slot="{ row }">
          <div class="corner-mark-hint" v-if="row.productPicChange">调</div>
          <div class="flex-start top-tag">
            <el-tag v-if="row.isCare" type="danger" size="small" round>照顾单</el-tag>
          </div>
          <el-image
            style="width: 90px; height: 90px; cursor: pointer"
            :style="{ 'margin-top': row.isCare ? '15px' : '0' }"
            :src="
              row.productPic
                ? $picUrl +
                  row.productPic +
                  '?imageMogr2/format/webp/interlace/1/quality/100/interlace/0/thumbnail/200x'
                : ''
            "
            fit="scale-down"
            preview-teleported
            @click="() => row.productPic && showViewer([$picUrl + row.productPic])"
          >
            <template #error>
              <img :src="$picUrl + 'static/assets/no-img.png'" alt="" style="width: 100%; height: 100%" />
            </template>
          </el-image>
        </template>
      </el-table-column>
      <el-table-column prop="msg" label="产品信息" minWidth="320">
        <template v-slot="{ row }">
          <div class="product-info-box">
            <div class="flex-between">
              <div>
                <span>视频编码：{{ row.videoCode }}</span>
                <el-tag
                  effect="plain"
                  type="danger"
                  size="small"
                  style="margin-left: 10px"
                  v-if="row.workOrderType == 4 && curTab == curTabStatusMap['待剪辑']"
                >
                  需剪辑
                </el-tag>
              </div>
              <div>
                <CopyButton class="btn" plain size="small" :copy-content="handleCopyInfo(row)" />
                <el-button class="btn" size="small" v-btn plain @click="handleAction('更多', row)">
                  更多
                </el-button>
              </div>
            </div>
            <div class="one-ell">中文名称：{{ row.productChinese }}</div>
            <div class="one-ell" style="word-break: break-all">英文名称：{{ row.productEnglish }}</div>
            <div class="one-ell productLink">
              产品链接：
              <el-link :underline="false" target="_blank" type="primary" :href="row.productLink">
                {{ row.productLink }}
              </el-link>
            </div>
            <biz-model-platform :value="row.platform" />
            <biz-model-type :value="row.modelType" />
            <biz-nation :value="row.shootingCountry" />
            <template v-for="op in videoFormatOptions" :key="op.value">
              <el-tag class="tag" v-if="op.value == row.videoFormat" type="warning" size="small" round>
                {{ op.label }}
              </el-tag>
            </template>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="picCount" label="照片数量" align="center" width="110">
        <template v-slot="{ row }">
          <div>{{ handleSelectiveAssembly(row.picCount) }}</div>
          <el-button
            v-btn
            v-if="row.referencePic && row.referencePic?.length"
            link
            type="primary"
            @click="handleAction('查看照片', row)"
          >
            查看
          </el-button>
          <el-tag type="info" size="small" v-if="handleOrderVideoRefundList(row.orderVideoRefundList, 1)">
            已退款{{ row.refundPicCount > 0 ? `(${row.refundPicCount}张)` : '' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="status"
        label="订单状态"
        align="center"
        width="140"
        class-name="product-img-box"
        v-if="curTab != curTabStatusMap['已完成'] && curTab != curTabStatusMap['已关闭']"
      >
        <template v-slot="{ row }">
          <div class="top-tag">
            <RollbackTag :row="row" reject-field="isRejectAfterSubmitModel" />
          </div>
          <div>
            {{ orderStatusMap[row.status] }}
          </div>
          <div>
            <el-tag
              type="info"
              size="small"
              v-if="
                row.orderVideoRefund?.refundStatus == orderRefundStatusMap['退款待审核'] ||
                row.orderVideoRefund?.refundStatus == orderRefundStatusMap['退款中']
              "
            >
              {{ orderRefundStatusMap[row.orderVideoRefund.refundStatus] }}
            </el-tag>
          </div>
          <template v-if="row.orderVideoRefundList?.length">
            <div>
              <el-tag type="info" size="small" v-if="handleOrderVideoRefundList(row.orderVideoRefundList, 2)">
                有补偿订单
              </el-tag>
            </div>
          </template>
        </template>
      </el-table-column>
      <el-table-column prop="model2" label="拍摄模特" align="center" width="130" class-name="shoot-model-box">
        <template v-slot="{ row }">
          <div style="padding: 5px 0">
            <div
              v-if="row.shootModel"
              @mouseenter="handleMouseEnter($event, row.shootModel.id)"
              @mouseleave="handleMouseLeave($event, row.shootModel.id)"
            >
              <div
                class="hint-box"
                style="pointer-events: none"
                v-if="row.intentionModel?.account && row.intentionModel?.account != row.shootModel?.account"
              >
                <div class="exchange">
                  <span>换</span>
                </div>
              </div>
              <el-avatar
                class="model-avatar"
                icon="UserFilled"
                :src="$picUrl + row.shootModel.modelPic + '!1x1compress'"
              />
              <div>{{ row.shootModel.name }}</div>
              <div>{{ row.shootModel?.account ? `(ID:${row.shootModel.account})` : '-' }}</div>
              <biz-model-type-new :value="row.shootModel.type" />
              <!-- <biz-model-type :value="row.shootModel.type" /> -->
            </div>
            <div v-else>-</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="contact" label="关联客服" align="center" width="110">
        <template v-slot="{ row }">
          <div>{{ row.contact?.name || '-' }} / {{ row.issue?.name || '-' }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="historyClipRecord" label="历史剪辑记录" align="center" width="110">
        <template v-slot="{ row }">
          <div>
            <div>{{ row.historyClipRecord }}个</div>
            <el-button link type="primary" v-btn @click="handleAction('历史剪辑记录', row)">查看</el-button>
          </div>
        </template>
      </el-table-column>
      <template v-if="curTab == curTabStatusMap['待下载']">
        <el-table-column prop="link" label="素材链接" align="center" width="110">
          <template v-slot="{ row }">
            <div class="one-ell productLink">
              <el-link :underline="false" target="_blank" type="primary" :href="row.link">
                {{ row.link }}
              </el-link>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="getStatus" label="领取状态" align="center" width="110">
          <template v-slot="{ row }">
            <div>
              <div>{{ row.getStatus == 0 ? '待领取' : '已领取' }}</div>
              <div v-if="row.getCode">
                (
                <CopyButton link style="color: #606266" :copy-content="handleCopy(row)">
                  <template #default>{{ row.getCode }}</template>
                </CopyButton>
                )
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="getBy" label="领取人" align="center" width="110">
          <template v-slot="{ row }">
            <div>
              <div>{{ row.getBy || '-' }}</div>
              <div>{{ row.getTime }}</div>
            </div>
          </template>
        </el-table-column>
      </template>
      <template v-if="curTab == curTabStatusMap['待剪辑']">
        <el-table-column prop="referenceVideoLink" label="参考视频" align="center" width="110">
          <template v-slot="{ row }">
            <div>
              <el-button
                v-if="row.referenceVideoLink"
                v-btn
                link
                type="primary"
                @click="goPath(row.referenceVideoLink)"
              >
                查看
              </el-button>
              <span v-else>-</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="videoDuration" label="剪辑要求" align="center" width="110">
          <template v-slot="{ row }">
            <div>
              <div>剪辑时长:{{ row.videoDuration }}S</div>
              <el-button v-btn link type="primary" @click="handleAction('剪辑要求', row)">查看具体</el-button>
            </div>
          </template>
        </el-table-column>
      </template>
      <template v-if="curTab == curTabStatusMap['待反馈'] || curTab == curTabStatusMap['需确认']">
        <el-table-column prop="contact" label="参考视频" align="center" width="110">
          <template v-slot="{ row }">
            <div>
              <el-button
                v-if="row.referenceVideoLink"
                v-btn
                link
                type="primary"
                @click="goPath(row.referenceVideoLink)"
              >
                查看
              </el-button>
              <span v-else>-</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="contact" label="影响者素材" align="center" width="110">
          <template v-slot="{ row }">
            <div class="one-ell productLink" v-if="row.shootModel?.type == 0 && row.link">
              <el-link :underline="false" target="_blank" type="primary" :href="row.link">
                {{ row.link }}
              </el-link>
            </div>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="contact" label="剪辑要求" align="center" width="110">
          <template v-slot="{ row }">
            <div>
              <div>剪辑时长:{{ row.videoDuration }}S</div>
              <el-button v-btn link type="primary" @click="handleAction('剪辑要求', row)">查看具体</el-button>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="editBy" label="剪辑人" align="center" width="110">
          <template v-slot="{ row }">
            <div v-if="row.editBy">
              <div>{{ row.editBy }}</div>
              <div>{{ row.editTime }}</div>
            </div>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="feedbackBy"
          label="反馈人"
          align="center"
          width="110"
          v-if="curTab == curTabStatusMap['需确认']"
        >
          <template v-slot="{ row }">
            <div>
              <div>{{ row.feedbackBy }}</div>
              <div>{{ row.feedbackTime }}</div>
            </div>
          </template>
        </el-table-column>
      </template>
      <template v-if="curTab == curTabStatusMap['已完成'] || curTab == curTabStatusMap['已关闭']">
        <el-table-column prop="count" label="上传记录" align="center" width="110">
          <template v-slot="{ row }">
            <div>
              <div>{{ row.count }}个</div>
              <el-button
                v-btn
                link
                type="primary"
                v-if="row.type != 1"
                @click="handleAction('上传记录', row)"
              >
                查看
              </el-button>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="feedbackStatus"
          label="关闭原因"
          align="center"
          width="100"
          v-if="curTab == curTabStatusMap['已关闭']"
        >
          <template v-slot="{ row }">
            <div>
              {{ closeStatusV1.find(item => item.value == row.closeReason)?.label || '-' }}
              <!-- <div v-if="row.type == 1">
                {{ closeStatusV1.find(item => item.value == row.feedbackStatus)?.label || '-' }}
              </div>
              <div v-if="row.type == 2">
                {{ uploadStatusV1.find(item => item.value == row.uploadStatus)?.label || '-' }}
              </div> -->
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="closeBy"
          label="关闭人"
          align="center"
          width="110"
          v-if="curTab == curTabStatusMap['已关闭']"
        >
          <template v-slot="{ row }">
            <div>
              <div>{{ row.closeBy }}</div>
              <div>{{ row.closeTime }}</div>
            </div>
          </template>
        </el-table-column>
      </template>
      <el-table-column
        fixed="right"
        label="操作"
        :align="'center'"
        width="150"
        v-if="curTab != curTabStatusMap['已关闭']"
      >
        <template v-slot="{ row }">
          <div class="flex-center gap-5 task-tag-box">
            <el-button
              type="info"
              size="small"
              text
              bg
              v-if="row.workOrderTaskStatus === 1"
              @click="toTaskPage('2', row)"
            >
              工单中
            </el-button>
            <el-button
              type="info"
              size="small"
              text
              bg
              v-else-if="row.workOrderTaskStatus === 4"
              @click="toTaskPage('2', row)"
            >
              工单已完结
            </el-button>
            <el-button
              type="info"
              size="small"
              text
              bg
              v-if="row.afterSaleTaskStatus === 1"
              @click="toTaskPage('1', row)"
            >
              售后中
            </el-button>
            <el-button
              type="info"
              size="small"
              text
              bg
              v-else-if="row.afterSaleTaskStatus === 4"
              @click="toTaskPage('1', row)"
            >
              售后已完结
            </el-button>
          </div>
          <div class="flex-center gap-5 task-button-box">
            <template v-if="curTab == curTabStatusMap['待下载']">
              <template v-if="row.getStatus == 1">
                <el-button
                  v-if="checkPermi(['my:clip:tagDownload'])"
                  v-btn
                  link
                  type="primary"
                  @click="handleAction('标记下载', row)"
                >
                  标记下载
                </el-button>
              </template>
              <template v-else>
                <el-button
                  v-btn
                  link
                  type="primary"
                  v-if="checkPermi(['my:clip:get'])"
                  @click="handleAction('领取', row)"
                >
                  领取
                </el-button>
              </template>
            </template>
            <template
              v-if="
                curTab == curTabStatusMap['待下载'] ||
                curTab == curTabStatusMap['待剪辑'] ||
                curTab == curTabStatusMap['待反馈'] ||
                curTab == curTabStatusMap['需确认']
              "
            >
              <template v-if="curTab == curTabStatusMap['待剪辑']">
                <el-button
                  v-btn
                  link
                  type="primary"
                  v-if="checkPermi(['my:clip:signEdit'])"
                  @click="handleAction('标记已剪辑', row)"
                >
                  标记已剪辑
                </el-button>
              </template>
              <template v-else>
                <el-button
                  v-btn
                  link
                  type="primary"
                  v-if="checkPermi(['my:clip:feedback']) && curTab != curTabStatusMap['待下载']"
                  @click="handleAction('反馈素材给商家', row)"
                >
                  反馈素材给商家
                </el-button>
              </template>
              <el-button
                v-btn
                link
                type="primary"
                v-if="curTab != curTabStatusMap['需确认'] && checkPermi(['my:clip:noFeedback'])"
                @click="handleAction('不反馈给商家', row)"
              >
                不反馈给商家
              </el-button>
              <el-button
                v-btn
                link
                type="primary"
                v-if="checkPermi(['my:clip:creatWork'])"
                @click="handleAction('创建任务单', row)"
              >
                创建任务单
              </el-button>
            </template>
            <template v-if="curTab == curTabStatusMap['已完成']">
              <el-button
                v-btn
                link
                type="primary"
                v-if="checkPermi(['my:clip:detail'])"
                @click="handleAction('查看详情', row)"
              >
                查看详情
              </el-button>
            </template>
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import CopyButton from '@/components/Button/CopyButton.vue'
import RollbackTag from '@/views/order/components/rollbackTag.vue'
import {
  picCountOptions,
  orderStatusMap,
  orderRefundStatusMap,
  videoFormatOptions,
} from '@/views/order/list/data.js'
import { curTabStatusMap, closeStatusV1 } from '@/views/task/clip/data.js'
import { useViewer } from '@/hooks/useViewer'
import { checkPermi } from '@/utils/permission'
import SortButton from '@/components/Button/SortButton.vue'
const router = useRouter()

const emits = defineEmits(['action', 'selectionChange', 'uploadChange', 'hover', 'getAll', 'sortChange'])

const { showViewer } = useViewer()

const props = defineProps({
  list: {
    type: Array,
    default: () => [],
  },
  statics: {
    type: Object,
    default: () => {},
  },
  curTab: {
    type: [String, Number],
    default: '0',
  },
  tabBoxHeight: {
    type: Number,
    default: 160,
  },
})

const tableRef = ref()
const selectedRows = ref([])
const sort = ref('')

defineExpose({ clearSelectAll, clearRowSelection })

watch(
  () => [props.tabBoxHeight, props.curTab],
  async () => {
    await nextTick()
    if (tableRef.value) {
      const headerWrapper = tableRef.value.$el.querySelector('.el-table__header-wrapper')
      if (headerWrapper) {
        if (props.curTab == 1) {
          headerWrapper.style.top = props.tabBoxHeight + 40 + 'px'
          // headerWrapper.style.top = props.tabBoxHeight + 40 + 42 + 'px'
        } else {
          headerWrapper.style.top = props.tabBoxHeight + 'px'
          // headerWrapper.style.top = props.tabBoxHeight + 42 + 'px'
        }
      }
    }
  },
  { immediate: true }
)

function headerCheckboxStyle({ row, column, rowIndex, columnIndex }) {
  if (columnIndex === 0 && props.curTab == curTabStatusMap['待下载']) {
    return 'seltAllbtnDis'
  }
}

function tableRowClassName({ row, rowIndex }) {
  if (props.curTab == curTabStatusMap['待下载']) {
    return 'primary-row'
  }
}

function handleSortChange() {
  emits('sortChange', sort.value)
}

function routerNewWindow(path) {
  const { href } = router.resolve({ path })
  window.open(href, '_blank')
}

function goPath(path) {
  window.open(path, '_blank')
}
const checked1 = ref(false)

watch(
  () => props.list,
  () => {
    tableRef.value.clearSelection()
    selectedRows.value = []
    if (checked1.value) {
      props.list.forEach(item => {
        if (item.getStatus == 0) {
          tableRef.value.toggleRowSelection(item, true)
        }
      })
    }
  }
)

function clearSelectAll() {
  tableRef.value.clearSelection()
  checked1.value = false
}

function clearRowSelection(data) {
  // tableRef.value.toggleRowSelection(data, false)
  // if (selectedRows.value.length === 0) checked1.value = false
}

function handleCopy(data) {
  return `${data.getCode} ${data.shootModel?.name || ''} ${data.videoCode}`
}

function handleCopyInfo(data) {
  if (data.productLink && data.productLink != '') {
    return `视频编码：${data.videoCode}
中文名称：${data.productChinese}
产品链接：${data.productLink || ''}
拍摄模特：${data.shootModel?.name}`
  } else {
    return `视频编码：${data.videoCode}
中文名称：${data.productChinese}
拍摄模特：${data.shootModel?.name}`
  }
}

function selectAll() {
  tableRef.value.toggleAllSelection()
}

function handleBulkCollection() {
  // if (selectedRows.value.length == 0 && !checked1.value) return ElMessage.warning('请先选择素材')
  emits('getAll', selectedRows.value, checked1.value)
}

function isRowSelectable(row, index) {
  return row.getStatus == 0
}

function handleSelectionChange(val) {
  if (val.length != props.statics.getCount) {
    checked1.value = false
  }
  if (val && val.length > 0) {
    selectedRows.value = val.map(item => item.id)
    if (val.length == props.statics.getCount) {
      checked1.value = true
    }
  } else {
    selectedRows.value = []
  }
}

function handleSelectiveAssembly(val) {
  let str = picCountOptions.find(item => item.value == val)
  return str ? str.label.substring(0, 2) : '-'
}

function handleAction(btn, row) {
  emits('action', btn, row)
}

function handleOrderVideoRefundList(list, type) {
  let s = false
  if (type == '2' && list && list.length > 0) {
    list.find(item => {
      if (item.refundStatus == orderRefundStatusMap['退款成功'] && item.refundType == 1) {
        s = true
      }
    })
  }
  if (type == '1' && list && list.length > 0) {
    list.find(item => {
      if (item.refundStatus == orderRefundStatusMap['退款成功'] && item.refundType == 3) {
        s = true
      }
    })
  }
  return s
}

function toTaskPage(type, row) {
  // sessionStorage.setItem('taskQuery', JSON.stringify({ type: type, vc: row.videoCode }))
  // const path = '/task/workOrder'
  const path = `/task/workOrder?type=${type}&keyword=${row.videoCode}`
  window.open(path, '_blank')
}

function handleMouseEnter(e, id) {
  emits('hover', e.target, id, true)
}
function handleMouseLeave(e, id) {
  emits('hover', e.target, id, false)
}
</script>

<style lang="scss" scoped>
.clip-table {
  .head-box {
    position: sticky;
    top: 160px;
    z-index: 998;
    background: #fff;
    &-left {
      display: flex;
      align-items: center;
    }
    &-right {
      display: flex;
      gap: 0 5px;
    }
  }
  .productLink {
    position: relative;
    padding-right: 5px;

    :deep(.el-link) {
      display: contents;

      .el-link__inner {
        display: inline;
      }
    }
  }
  :deep(.el-table) {
    overflow: visible;
    .primary-row {
      --el-table-current-row-bg-color: #cdf7fe;
      // &:hover > td {
      //   background-color: #cdf7fe !important; // 鼠标悬停时的背景色
      // }
    }
    .el-table__header-wrapper {
      position: sticky; // 设置粘性定位
      top: 200px; // 距离顶部的位置
      z-index: 5;
      th {
        height: 30px !important;
      }
    }
    th {
      text-align: center;
    }

    .el-button + .el-button {
      margin: 0 3px;
    }

    .el-table__header-wrapper {
      .seltAllbtnDis {
        .cell {
          visibility: hidden;
        }
      }
    }
    .product-info-box {
      .btn {
        padding: 2px 4px;
        height: auto;
        font-size: 12px;
      }
    }
    .product-img-box {
      position: relative;

      .top-tag {
        z-index: 9;
        position: absolute;
        top: 2px;
        left: 1px;

        .el-tag + .el-tag {
          margin-left: 5px;
        }
      }

      .productPic-img {
        width: 100%;
        height: 100%;
        position: relative;

        .productPic-btn {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }
      }
    }
    .shoot-model-box {
      // padding-top: 8px;
      position: relative;
      overflow: hidden;

      .hint-box {
        z-index: 999;
        position: absolute;
        left: -6px;
        top: -2px;
        // overflow: hidden;
        width: 32px;
        height: 25px;

        .exchange {
          background: var(--el-color-danger);
          color: #fff;
          font-size: 13px;
          width: 60px;
          transform: translate(-17px, -3px) scale(0.8) rotateZ(-45deg);
        }
      }
    }
    .task-tag-box {
      position: absolute;
      top: 0;
      right: 0;

      .el-button {
        padding: 2px 8px;
        height: 18px;
        border: 1px solid var(--el-color-info-light-7);
        margin: 0 !important;
      }
    }
    .task-button-box {
      flex-direction: column;
    }
  }
}
.btn-left {
  margin-left: 10px;
}
</style>
