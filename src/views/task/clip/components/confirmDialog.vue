<template>
  <div>
    <el-dialog
      align-center
      append-to-body
      title=""
      v-model="isShow"
      width="500px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <div class="hint-box">
        <template v-if="type == 'get' || type == 'getMore'">
          <div class="confirm-title" :style="{ padding: type == 'get' ? '20px 0 40px 0' : '' }">
            确认领取素材吗？
          </div>
          <div class="confirm-text" v-if="type == 'getMore'">
            已选{{ isSelectAll ? getNum : curId.length }}条素材
          </div>
        </template>
        <template v-if="type == 'signDown'">
          <div class="confirm-title">确认标记为已下载？</div>
          <div class="confirm-text">确认后该任务将流转至待剪辑中</div>
          <div class="confirm-err" v-if="isErrHint">注意：该视频订单在待剪辑/待反馈中已存进行中的任务</div>
        </template>
        <template v-if="type == 'signUpload'">
          <div class="confirm-title">标记待确认上传</div>
          <div class="confirm-text" style="font-size: 16px">
            已上传至亚马逊相应位置，标记成功后请及时跟进上传结果
          </div>
        </template>
      </div>
      <template #footer>
        <span class="dialog-footer flex-center">
          <el-button v-btn class="btn" round @click="close">取 消</el-button>
          <el-button v-btn class="btn" round type="primary" @click="confirm">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ElMessage, ElMessageBox } from 'element-plus'
import { signMarkDownload, getEdit, markUploadConfirm, checkDownload } from '@/api/clip'

const isShow = ref(false)

const emits = defineEmits(['success'])
const type = ref('')
const curId = ref(null)
const isSelectAll = ref(false)
const getNum = ref(0)
const isErrHint = ref(false)
const open = (id, typeString, isAll = false, total = 0, videoId) => {
  isSelectAll.value = isAll
  getNum.value = total
  type.value = typeString
  curId.value = id
  isShow.value = true
  if (typeString == 'signDown') {
    checkDownload({ videoId: videoId }).then(res => {
      isErrHint.value = res.data
    })
  }
}

const close = () => {
  type.value = ''
  curId.value = null
  isSelectAll.value = false
  getNum.value = 0
  isShow.value = false
  isErrHint.value = false
}

const confirm = () => {
  if (type.value == 'signDown') {
    signMarkDownload({ id: curId.value }).then(res => {
      if (res.code == 200) {
        ElMessage.success('标记下载成功')
        emits('success')
      }
    })
  } else if (type.value == 'get' || type.value == 'getMore') {
    let data = {}
    if (isSelectAll.value) {
      data.selectAll = true
    } else {
      data.ids = type.value == 'get' ? [curId.value] : curId.value
    }
    getEdit(data).then(res => {
      if (res.code == 200) {
        ElMessage.success('领取成功')
        emits('success')
      }
    })
  } else if (type.value == 'signUpload') {
    markUploadConfirm({ uploadLinkId: curId.value }).then(res => {
      if (res.code == 200) {
        ElMessage.success('标记成功')
        emits('success')
      }
    })
  }
  close()
}

defineExpose({ open, close })
</script>

<style lang="scss" scoped>
.btn {
  padding: 8px 25px;
}
.hint-box {
  text-align: center;
  color: #333;
  .confirm-title {
    font-size: 30px;
    font-weight: 600;
  }
  .confirm-text {
    font-size: 22px;
    margin: 20px 0;
  }
  .confirm-err {
    font-size: 18px;
    color: #d9001b;
    margin-bottom: 10px;
  }
}
</style>
