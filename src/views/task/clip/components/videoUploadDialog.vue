<template>
  <div>
    <el-dialog
      align-center
      append-to-body
      :title="dialogTitle"
      v-model="isShow"
      width="600px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="close"
    >
      <div class="dialog-form">
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          :validate-on-rule-change="false"
          label-width="120px"
          @submit.native.prevent
        >
          <el-form-item label="视频地址" prop="uploadLink" v-if="dialogType == 'success'">
            <el-input
              v-model="form.uploadLink"
              style="width: 100%"
              :rows="4"
              type="textarea"
              maxlength="1000"
              show-word-limit
              placeholder="请输入视频的上传地址"
              clearable
            />
            <div>请填入视频上传最终地址，确保商家查阅正常</div>
          </el-form-item>
          <el-form-item label="备注" v-if="dialogType == 'error'">
            <el-input
              v-model="form.operateRemark"
              style="width: 100%"
              :rows="4"
              type="textarea"
              maxlength="300"
              show-word-limit
              placeholder="请输入备注"
              clearable
            />
            <div>失败后，会再次发起上传任务</div>
          </el-form-item>
          <el-form-item label="取消原因" prop="operateRemark" v-if="dialogType == 'cancel'">
            <el-input
              v-model="form.operateRemark"
              style="width: 100%"
              :rows="4"
              type="textarea"
              maxlength="300"
              show-word-limit
              placeholder="请输入取消原因"
              clearable
            />
            <div>取消后，上传任务不再支持发起</div>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer flex-center">
          <el-button round style="padding: 8px 50px" @click="close">取消</el-button>
          <el-button round style="padding: 8px 36px" type="primary" @click="confirm">确认提交</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ElMessage, ElMessageBox } from 'element-plus'
import { cancelUploadLink, uploadLink,uploadLinkFail } from '@/api/clip'

const isShow = ref(false)
const formRef = ref()

const emits = defineEmits(['success'])

const form = ref({
  id: '',
  operateRemark: '',
  uploadLink: '',
})

const rules = {
  uploadLink: [{ required: true, validator: validateLink, trigger: 'change' }],
  operateRemark: [{ required: true, message: '请输入不反馈原因', trigger: 'blur' }],
}

function validateLink(rule, value, callback) {
  const regLink = 'https://www.amazon.com'
  if (value && value.startsWith(regLink)) {
    return callback()
  } else {
    return callback(new Error('视频链接有格式错误'))
  }
}

const dialogTitle = ref('')
const dialogType = ref('')
const open = (type, id, link) => {
  form.value.id = id
  // form.value.uploadLink = link
  dialogType.value = type
  dialogTitle.value =
    type == 'success' ? '视频上传成功？' : type == 'error' ? '视频上传失败？' : '视频取消上传？'
  isShow.value = true
}

const close = () => {
  dialogType.value = ''
  dialogTitle.value = ''
  form.value = {
    id: '',
    operateRemark: '',
    uploadLink: '',
  }
  formRef.value.resetFields()
  isShow.value = false
}

const confirm = () => {
  formRef.value.validate(valid => {
    if (valid) {
      if (dialogType.value == 'cancel') {
        cancelUploadLink(form.value).then(res => {
          ElMessage.success('取消成功')
          emits('success')
        })
      }
      if (dialogType.value == 'success') {
        uploadLink(form.value).then(res => {
          ElMessage.success('上传成功')
          emits('success')
        })
      }
      if (dialogType.value == 'error') {
        uploadLinkFail(form.value).then(res => {
          ElMessage.success('上传失败')
          emits('success')
        })
      }
      close()
    }
  })
}

defineExpose({ open, close })
</script>

<style lang="scss" scoped>
.dialog-form {
  :deep(.el-form) {
    .el-form-item {
      flex-direction: column;
      .el-form-item__label {
        justify-content: start;
      }
    }
  }
}
</style>
