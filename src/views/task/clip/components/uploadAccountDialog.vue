<template>
  <div>
    <el-dialog
      align-center
      append-to-body
      title="标记上传账号"
      v-model="isShow"
      width="500px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="close"
    >
      <div>
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          :validate-on-rule-change="false"
          label-width="120px"
          @submit.native.prevent
        >
          <el-form-item label="上传账号" prop="uploadAccount">
            <el-select
              :reserve-keyword="false"
              v-model="form.uploadAccount"
              placeholder="请选择上传账号"
              filterable
              clearable
              style="width: 300px"
            >
              <el-option
                v-for="(item, index) in accountList"
                :key="index"
                :label="item.uploadAccount"
                :value="item.uploadAccount"
                :disabled="item.isSelect"
              />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer flex-center">
          <el-button @click="close">取 消</el-button>
          <el-button type="primary" @click="confirm">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ElMessage, ElMessageBox } from 'element-plus'
import { markUploadAccountSelect, markUploadAccount } from '@/api/clip'

const isShow = ref(false)

const formRef = ref()
const form = ref({
  uploadAccount: '',
  uploadLinkId: '',
})

const accountList = ref([])

const rules = {
  uploadAccount: [{ required: true, message: '请选择上传账号', trigger: 'change' }],
}

const emits = defineEmits(['success'])

const open = (id, uploadAccount) => {
  form.value.uploadLinkId = id
  form.value.uploadAccount = uploadAccount
  isShow.value = true
  markUploadAccountSelect({ uploadLinkId: id }).then(res => {
    // accountList.value = res.data || []
    if (res.data && res.data.length > 0) {
      // accountList.value = res.data.map(item =>
      //   item.isSelect ? item.uploadAccount + '(已选)' : item.uploadAccount
      // )
      accountList.value = res.data.map(item => ({
        ...item,
        uploadAccount: item.isSelect ? item.uploadAccount + '(已选)' : item.uploadAccount,
      }))
    }
  })
}

const close = () => {
  form.value.uploadAccount = ''
  accountList.value = []
  formRef.value.resetFields()
  isShow.value = false
}

const confirm = () => {
  formRef.value.validate(valid => {
    if (valid) {
      markUploadAccount(form.value)
        .then(res => {
          if (res.code == 200) {
            ElMessage.success('标记上传账号成功')
            close()
            emits('success')
          }
        })
        .catch(() => {})
    }
  })
}

defineExpose({ open, close })
</script>

<style lang="scss" scoped></style>
