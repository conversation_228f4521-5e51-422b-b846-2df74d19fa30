<template>
  <div>
    <el-dialog
      align-center
      append-to-body
      title="不反馈素材给商家"
      v-model="isShow"
      width="600px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <div>
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          :validate-on-rule-change="false"
          label-width="120px"
          @submit.native.prevent
        >
          <el-form-item label="不反馈原因" prop="feedbackRemark">
            <el-input
              v-model="form.feedbackRemark"
              style="width: 100%"
              :rows="4"
              type="textarea"
              maxlength="500"
              show-word-limit
              placeholder="请输入具体不反馈原因"
              clearable
            />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="close">取 消</el-button>
          <el-button type="primary" @click="confirm">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ElMessage } from 'element-plus'
import { markNoFeedback } from '@/api/clip'

const isShow = ref(false)
const formRef = ref()
const form = ref({
  feedbackRemark: '',
})

const rules = {
  feedbackRemark: [{ required: true, message: '请输入不反馈原因', trigger: 'blur' }],
}

const emits = defineEmits(['success'])
const open = (id) => {
  form.value.id = id
  isShow.value = true
}

const close = () => {
  form.value.feedbackRemark = ''
  formRef.value.resetFields()
  isShow.value = false
}

const confirm = () => {
  formRef.value.validate(valid => {
    if (valid) {
      markNoFeedback(form.value).then(res => {
        ElMessage.success('操作成功')
        emits('success')
        close()
      })
    }
  })
}

defineExpose({ open, close })
</script>

<style lang="scss" scoped></style>
