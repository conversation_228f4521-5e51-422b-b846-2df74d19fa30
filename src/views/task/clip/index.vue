<template>
  <div class="clip-box">
    <div class="tab-box">
      <el-radio-group v-model="curTab" size="large" @change="handleTabChange">
        <el-radio-button v-for="item in statusTab" :key="item.value" :value="item.value">
          {{ item.lable }}
        </el-radio-button>
      </el-radio-group>
      <div class="search-box">
        <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="88px" @submit.prevent>
          <el-form-item label="搜索">
            <el-input
              v-model="queryParams.keyword"
              :placeholder="keywordPlaceholder"
              clearable
              style="width: 330px"
            />
          </el-form-item>
          <el-form-item label="拍摄模特" v-if="curTab != curTabStatusMap['已关闭']">
            <el-select
              :reserve-keyword="false"
              v-model="queryParams.shootModelIds"
              placeholder="请选择"
              multiple
              filterable
              collapse-tags
              collapse-tags-tooltip
              clearable
              style="width: 220px"
            >
              <el-option
                v-for="item in selectShootModelList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <template v-if="curTab == curTabStatusMap['待下载']">
            <el-form-item label="素材领取编码" label-width="100px">
              <el-input v-model="queryParams.getCode" placeholder="输入" clearable style="width: 180px" />
            </el-form-item>
            <el-form-item label="领取人">
              <el-select
                :reserve-keyword="false"
                v-model="queryParams.getByIds"
                placeholder="请选择"
                multiple
                filterable
                collapse-tags
                collapse-tags-tooltip
                clearable
                style="width: 180px"
              >
                <el-option
                  v-for="item in selectReceivePersonList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="领取状态">
              <el-select v-model="queryParams.getStatus" placeholder="请选择" clearable style="width: 180px">
                <el-option
                  v-for="item in drawStatus"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="领取时间">
              <el-date-picker
                style="width: 350px"
                v-model="queryParams.submitTime"
                format="YYYY/M/D HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="datetimerange"
                range-separator="-"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
                :shortcuts="shortcuts"
              ></el-date-picker>
            </el-form-item>
          </template>
          <template v-if="curTab == curTabStatusMap['待反馈'] || curTab == curTabStatusMap['需确认']">
            <el-form-item label="剪辑人">
              <el-select
                :reserve-keyword="false"
                v-model="queryParams.editByIds"
                placeholder="请选择"
                multiple
                filterable
                collapse-tags
                collapse-tags-tooltip
                clearable
                style="width: 190px"
              >
                <el-option
                  v-for="item in selectEditPersonList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="模特类型">
              <el-select v-model="queryParams.modelType" placeholder="请选择" clearable style="width: 180px">
                <el-option
                  v-for="item in biz_model_type"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="反馈时间" v-if="curTab == curTabStatusMap['需确认']">
              <el-date-picker
                style="width: 350px"
                v-model="queryParams.feedbackTime"
                format="YYYY/M/D HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="datetimerange"
                range-separator="-"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
                :shortcuts="shortcuts"
              ></el-date-picker>
            </el-form-item>
          </template>
          <template v-if="curTab == curTabStatusMap['待上传']">
            <el-form-item label="上传账号">
              <el-select
                :reserve-keyword="false"
                v-model="queryParams.uploadAccounts"
                placeholder="请选择"
                multiple
                filterable
                collapse-tags
                collapse-tags-tooltip
                clearable
                style="width: 190px"
              >
                <el-option
                  v-for="(item, index) in uploadAccountList"
                  :key="index"
                  :label="item"
                  :value="item"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="上传状态">
              <el-select v-model="queryParams.status" placeholder="请选择" clearable style="width: 180px">
                <el-option
                  v-for="item in uploadStatus"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="提交需求上传时间" label-width="150">
              <el-date-picker
                style="width: 350px"
                v-model="queryParams.uploadLinkTimes"
                format="YYYY/M/D HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="datetimerange"
                range-separator="-"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
                :shortcuts="shortcuts"
              ></el-date-picker>
            </el-form-item>
            <el-form-item label="是否有封面">
              <el-select v-model="queryParams.existCover" placeholder="请选择" clearable style="width: 180px">
                <el-option
                  v-for="item in coverStatus"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="上传次数">
              <el-select
                v-model="queryParams.uploadLinkCount"
                placeholder="请选择"
                clearable
                style="width: 180px"
              >
                <el-option
                  v-for="item in uploadCountList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </template>
          <template v-if="curTab == curTabStatusMap['已关闭']">
            <el-form-item label="关闭原因">
              <el-select
                v-model="queryParams.closeReason"
                placeholder="请选择"
                clearable
                style="width: 180px"
              >
                <el-option
                  v-for="item in closeStatusV1"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="关闭时间">
              <el-date-picker
                style="width: 350px"
                v-model="queryParams.closeTime"
                format="YYYY/M/D HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="datetimerange"
                range-separator="-"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
                :shortcuts="shortcuts"
              ></el-date-picker>
            </el-form-item>
          </template>
          <el-form-item>
            <el-button v-btn type="primary" icon="Search" native-type="submit" @click="onQuery">
              搜索
            </el-button>
            <el-button v-btn icon="Refresh" plain @click="resetQuery">重置</el-button>
            <!-- <div style="margin-left: 10px" v-if="curTab == curTabStatusMap['需确认']">
              <DownloadBtn
                type="success"
                plain
                icon="Download"
                url="/order/edit/export-need-confirm-list"
                :params="handleQueryParams()"
                fileName="剪辑管理需确认列表.xlsx"
              />
            </div> -->
          </el-form-item>
        </el-form>
      </div>
      <!-- <div class="flex-end" v-if="curTab == '4' && total">
        <el-pagination
          background
          @size-change="pageChange({ pageNum: 1, pageSize: $event })"
          @current-change="pageChange({ pageNum: $event, pageSize })"
          :current-page="pageNum"
          :page-size="pageSize"
          :page-sizes="[10, 20, 30, 50]"
          layout="total, prev, pager, next, jumper"
          :total="total"
        />
      </div> -->
    </div>
    <div class="table-box" v-loading="loading">
      <!-- <template v-if="!tableData.length">
        <el-empty description="暂无数据" :image-size="80"></el-empty>
      </template> -->
      <!-- <template v-else> -->
      <ClipUpTable
        :list="tableData"
        v-if="curTab == '4'"
        :tabBoxHeight="tabBoxHeight"
        :curTab="curTab"
        @action="handleAction"
        @hover="handleModelHover"
      />
      <ClipTable
        :list="tableData"
        :statics="statics"
        :curTab="curTab"
        :tabBoxHeight="tabBoxHeight"
        ref="ClipTableRef"
        v-else
        @action="handleAction"
        @hover="handleModelHover"
        @getAll="handleGetAll"
        @sortChange="handleSortChange"
      >
        <!-- <template #header>
          <div
            class="flex-end"
            style="padding: 0 0 10px; position: sticky; top: 160px; z-index: 990; background: #fff"
            :style="curTab == '1' ? { top: tabBoxHeight + 40 + 'px' } : { top: tabBoxHeight + 'px' }"
          >
            <el-pagination
              background
              @size-change="pageChange({ pageNum: 1, pageSize: $event })"
              @current-change="pageChange({ pageNum: $event, pageSize })"
              :current-page="pageNum"
              :page-size="pageSize"
              :page-sizes="[10, 20, 30, 50]"
              layout="total, prev, pager, next, jumper"
              :total="total"
            />
          </div>
        </template> -->
      </ClipTable>
      <!-- </template> -->
      <!-- <PreselectionListItem
        v-for="item in tableData"
        :key="item.id"
        :data="item"
        :status="status"
        :tabType="tabType"
        @action="handleAction"
        @hover="handleModelHover"
      /> -->
    </div>
    <div class="flex-end" style="margin: 10px 0 60px">
      <PaginationFloatBar :current-page="pageNum" :page-size="pageSize" @update:current-page="handlePageChange" :total="total">
        <DownloadBtn
          v-if="curTab == curTabStatusMap['需确认']"
          type="success"
          plain
          icon="Download"
          url="/order/edit/export-need-confirm-list"
          :params="handleQueryParams()"
          fileName="剪辑管理需确认列表.xlsx"
        />
      </PaginationFloatBar>
      <!-- <el-pagination
        background
        @size-change="pageChange({ pageNum: 1, pageSize: $event })"
        @current-change="pageChange({ pageNum: $event, pageSize })"
        :current-page="pageNum"
        :page-size="pageSize"
        :page-sizes="[10, 20, 30, 50]"
        layout="total, prev, pager, next, jumper"
        :total="total"
      /> -->
    </div>
    <ProductMoreInfo ref="ProductMoreInfoRef" />

    <ModelInfoPopover ref="ModelInfoPopoverRef" />

    <HistoryClipRecord ref="HistoryClipRecordRef" />

    <ConfirmDialog ref="ConfirmDialogRef" @success="handleQuery" />

    <SignClipDialog ref="SignClipDialogRef" @success="handleQuery" />

    <NoFeedbackDialog ref="NoFeedbackDialogRef" @success="handleQuery" />

    <ClipRequireDialog ref="ClipRequireDialogRef" />

    <CreateWorkOrder ref="CreateWorkOrderRef" @success="handleQuery" />

    <HistoryUploadDialog ref="HistoryUploadDialogRef" />

    <FeedbackLinkToMerchant ref="FeedbackLinkToMerchantRef" @success="handleQuery" />

    <VideoUploadDialog ref="VideoUploadDialogRef" @success="handleQuery" />

    <UploadAccountDialog ref="UploadAccountDialogRef" @success="handleQuery" />
  </div>
</template>

<script setup>
import DownloadBtn from '@/components/Button/DownloadBtn'
import ClipTable from '@/views/task/clip/components/clipTable.vue'
import ClipUpTable from '@/views/task/clip/components/clipUpTable.vue'
import ProductMoreInfo from '@/views/order/components/dialog/productMoreInfo.vue'
import HistoryClipRecord from '@/views/task/clip/components/historyClipRecord.vue'
import ConfirmDialog from '@/views/task/clip/components/confirmDialog.vue'
import SignClipDialog from '@/views/task/clip/components/signClipDialog.vue'
import NoFeedbackDialog from '@/views/task/clip/components/noFeedbackDialog.vue'
import ClipRequireDialog from '@/views/task/clip/components/clipRequireDialog.vue'
import CreateWorkOrder from '@/views/order/components/dialog/createWorkOrder.vue'
import HistoryUploadDialog from '@/views/task/clip/components/historyUploadDialog.vue'
import VideoUploadDialog from '@/views/task/clip/components/videoUploadDialog.vue'
import FeedbackLinkToMerchant from '@/views/order/components/dialog/feedbackLinkToMerchant.vue'
import UploadAccountDialog from '@/views/task/clip/components/uploadAccountDialog.vue'
import ModelInfoPopover from '@/views/model/ModelInfoPopover.vue'
const { scrollToTop } = inject('appMainScroll')

import {
  getMaterialInfoList,
  getSelectShootModel,
  getSelectReceivePerson,
  getMaterialInfoStatistics,
  selectEditPerson,
  uploadLinkList,
  uploadLinkListSelectShootModel,
  closedList,
  markUploadAccountSelect,
  uploadAccountSelect,
} from '@/api/clip'

import {
  curTabStatusMap,
  drawStatus,
  uploadStatus,
  coverStatus,
  uploadCountList,
  closeStatusV1,
} from '@/views/task/clip/data.js'

import { ElMessage, ElMessageBox } from 'element-plus'
import { useViewer } from '@/hooks/useViewer'

const { showViewer } = useViewer()
const { proxy } = getCurrentInstance()
const { biz_model_type } = proxy.useDict('biz_model_type')

const route = useRoute()
const router = useRouter()

const curTab = ref(1)

const statusTab = ref([
  {
    lable: '待下载',
    value: 1,
  },
  {
    lable: '待剪辑',
    value: 2,
  },
  {
    lable: '待反馈',
    value: 3,
  },
  {
    lable: '需确认',
    value: 5,
  },
  {
    lable: '待上传',
    value: 4,
  },
  {
    lable: '已完成',
    value: 6,
  },
  {
    lable: '已关闭',
    value: 7,
  },
])
const tableData = ref([])
const pageNum = ref(1)
const pageSize = 20
const total = ref(0)
const loading = ref(false)
const sortValue = ref('')

const shortcuts = [
  {
    text: '今天',
    value: () => {
      const start = new Date()
      start.setHours(0, 0, 0, 0)
      const end = new Date()
      end.setHours(23, 59, 59, 59)
      return [start, end]
    },
  },
]

const queryParams = ref({
  keyword: '',
  shootModelIds: [],
  getCode: '',
  getByIds: [],
  getStatus: '',
  editByIds: [],
  submitTime: [],
  feedbackTime: [],
  status: '',
  existCover: '',
  uploadLinkCount: '',
  modelType: '',
  closeReason: [],
  closeTime: [],
  uploadAccounts: [],
  uploadLinkTimes: [],
})

const keywordPlaceholder = ref('支持视频编码、产品信息、素材链接等进行搜索')
function handleTabChange() {
  handleSizeChange()
  resetQuery()
  if (curTab.value == 5 || curTab.value == 6) {
    keywordPlaceholder.value = '支持视频编码、产品信息等进行搜索'
  } else {
    keywordPlaceholder.value = '支持视频编码、产品信息、素材链接等进行搜索'
  }
}

function onQuery() {
  pageNum.value = 1
  handleQuery()
}
function resetQuery() {
  router.replace({ query: {} })
  queryParams.value = {
    keyword: '',
    shootModelIds: [],
    getCode: '',
    getByIds: [],
    getStatus: '',
    submitTime: [],
    feedbackTime: [],
    status: '',
    existCover: '',
    uploadLinkCount: '',
    modelType: '',
    closeReason: [],
    closeTime: [],
    uploadAccounts: [],
    uploadLinkTimes: [],
  }
  onQuery()
}

function pageChange(params) {
  pageNum.value = params.pageNum
  // pageSize = params.pageSize
  handleQuery()
}
function handlePageChange(num,size) {
  pageNum.value = num
  handleQuery()
}

function handleQueryParams() {
  let { submitTime, feedbackTime, closeTime, uploadLinkTimes, ...params } = queryParams.value
  if (curTab.value == '1' || curTab.value == '2' || curTab.value == '3' || curTab.value == '5') {
    params.status = curTab.value
    if (curTab.value == 1 && sortValue.value && sortValue.value != '') {
      params.createTimeSort = sortValue.value
    }
    if (curTab.value == 5) params.status = 4
    if (submitTime && submitTime.length > 0) {
      params.getTimeBegin = submitTime[0]
      params.getTimeEnd = submitTime[1]
    }
    if (feedbackTime && feedbackTime.length > 0) {
      params.feedbackTimeBegin = feedbackTime[0]
      params.feedbackTimeEnd = feedbackTime[1]
    }
  }
  if (curTab.value == '4' || curTab.value == '6') {
    if (curTab.value == '4') {
      if (uploadLinkTimes && uploadLinkTimes.length > 0) {
        params.uploadLinkTimeBegin = uploadLinkTimes[0]
        params.uploadLinkTimeEnd = uploadLinkTimes[1]
      }
      // params.status = 1
    }
    if (curTab.value == '6') {
      params.status = 0
    }
  }
  if (curTab.value == '7') {
    if (closeTime && closeTime.length > 0) {
      params.closeTimeBegin = closeTime[0]
      params.closeTimeEnd = closeTime[1]
    }
  }

  return params
}

const ClipTableRef = ref()
function handleQuery() {
  initQuery()
  loading.value = true
  let params = handleQueryParams()
  if (curTab.value == '1' || curTab.value == '2' || curTab.value == '3' || curTab.value == '5') {
    getMaterialInfoList({ ...params, pageNum: pageNum.value, pageSize: pageSize })
      .then(res => {
        tableData.value = res.data.rows || []
        total.value = res.data.total
        // if (curTab.value == '1') {
        //   if (tabOneIsAll.value) {
        //     ClipTableRef.value?.clearSelectAll()
        //     tabOneIsAll.value = false
        //   }
        // }
      })
      .finally(() => {
        loading.value = false
      })
  }
  if (curTab.value == '4' || curTab.value == '6') {
    uploadLinkList({ ...params, pageNum: pageNum.value, pageSize: pageSize })
      .then(res => {
        tableData.value = res.data.rows || []
        total.value = res.data.total
      })
      .finally(() => {
        loading.value = false
      })
  }
  if (curTab.value == '7') {
    closedList({ ...params, pageNum: pageNum.value, pageSize: pageSize })
      .then(res => {
        tableData.value = res.data.rows || []
        total.value = res.data.total
      })
      .finally(() => {
        loading.value = false
      })
  }
}

const ProductMoreInfoRef = ref()
const HistoryClipRecordRef = ref()
const ConfirmDialogRef = ref()
const SignClipDialogRef = ref()
const NoFeedbackDialogRef = ref()
const ClipRequireDialogRef = ref()
const CreateWorkOrderRef = ref()
const HistoryUploadDialogRef = ref()
const FeedbackLinkToMerchantRef = ref()
const VideoUploadDialogRef = ref()
const UploadAccountDialogRef = ref()

const curRowSelect = ref({})
function handleAction(btn, row, type) {
  switch (btn) {
    case '更多':
      ProductMoreInfoRef.value?.open(row.videoId)
      break
    case '查看照片':
      if (row.referencePic && row.referencePic.length) {
        showViewer(row.referencePic.map(item => item))
      }
      break
    case '历史剪辑记录':
      HistoryClipRecordRef.value?.open(row.videoId)
      break
    case '标记下载':
      ConfirmDialogRef.value?.open(row.id, 'signDown', false, '', row.videoId)
      break
    case '领取':
      curRowSelect.value = row
      ConfirmDialogRef.value?.open(row.id, 'get')
      break
    case '标记上传':
      ConfirmDialogRef.value?.open(row.id, 'signUpload')
      break
    case '标记已剪辑':
      let isToBeEdited = false
      curTab.value == 2 ? (isToBeEdited = true) : (isToBeEdited = false)
      SignClipDialogRef.value?.open(
        row.id,
        row.videoId,
        row.videoCode,
        row.productChinese,
        row.shootModel?.name,
        isToBeEdited
      )
      break
    case '不反馈给商家':
      NoFeedbackDialogRef.value?.open(row.id)
      break
    case '剪辑要求':
      ClipRequireDialogRef.value?.open('1', row.videoId)
      break
    case '查看详情':
      ClipRequireDialogRef.value?.open('', row.videoId)
      break
    case '创建任务单':
      CreateWorkOrderRef.value?.open(row, row.videoId)
      break
    case '上传记录':
      HistoryUploadDialogRef.value?.open(row.id)
      break
    case '反馈素材给商家':
      // if (row.orderVideoRefundList && row.orderVideoRefundList.length > 0) {
      //     row.orderVideoRefundList.find(item => {
      //       if (item.refundType == '3') {
      //         s = '1'
      //       }
      //     })
      //   }
      let link = ''
      if (row.shootModel && row.shootModel.type == 0) {
        link = row.link
      }
      let isClose = false
      if (curTab.value == '3') {
        isClose = true
      }
      let picCount = null
      if (row.picCount) {
        let s = row.picCount == 1 ? 2 : 5
        picCount = s - row.refundPicCount
      }
      FeedbackLinkToMerchantRef.value?.open(row.videoId, picCount, '', '', row.id, true, link, isClose)
      break
    case '视频上传':
      VideoUploadDialogRef.value.open(type, row.id, row.needUploadLink)
      break
    case '上传备注':
      handleRemarkInfo(row.remark)
      break
    case '上传账号':
      UploadAccountDialogRef.value.open(row.id, row.uploadAccount)
      break
    default:
      break
  }
}
const tabOneIsAll = ref(false)
function handleGetAll(list, isAll) {
  tabOneIsAll.value = true
  ConfirmDialogRef.value?.open(list, 'getMore', isAll, statics.value.getCount)
}

function handleSortChange(value) {
  pageNum.value = 1
  sortValue.value = value
  handleQuery()
}

function handleRemarkInfo(str) {
  let cc = str
  let dom = h('div', [
    h(
      'div',
      {
        style: {
          'max-height': '500px',
          'overflow-y': 'auto',
          margin: '10px',
          'font-weight': '500',
          'line-break': 'anywhere',
          'white-space': 'pre-wrap',
        },
      },
      cc
    ),
  ])
  ElMessageBox.alert(dom, '', {
    customStyle: {
      '--el-messagebox-width': '680px',
      'font-weight': '600',
    },
    dangerouslyUseHTMLString: true,
    showConfirmButton: false,
    showCancelButton: false,
    closeOnClickModal: true,
  })
}

const ModelInfoPopoverRef = ref()
// 模特浮窗
function handleModelHover(el, id, show) {
  if (show) {
    ModelInfoPopoverRef.value?.open(el, id)
  } else {
    ModelInfoPopoverRef.value?.close()
  }
}

const selectShootModelList = ref([])
const selectReceivePersonList = ref([])
const selectEditPersonList = ref([])
const statics = ref({
  downloadCount: 0,
  getCount: 0,
  todayDownloadCount: 0,
  todayFeedbackCount: 0,
  totalFeedbackCount: 0,
})
function resetInitQuery() {
  selectShootModelList.value = []
  selectReceivePersonList.value = []
  selectEditPersonList.value = []
}
function hanldeMapList(list) {
  if (list && list.length > 0) {
    return list.map(item => {
      return {
        label: item.name + '(' + item.account + ')',
        value: item.id,
      }
    })
  } else {
    return []
  }
}

function getNumerStatistice() {
  getMaterialInfoStatistics().then(res => {
    if (res.data) {
      statics.value = res.data
    }
  })
  getSelectReceivePerson().then(res => {
    selectReceivePersonList.value = res.data || []
  })
}

const uploadAccountList = ref([])
function initQuery() {
  resetInitQuery()
  if (curTab.value == '1') {
    getNumerStatistice()
  }
  if (curTab.value == '1' || curTab.value == '2' || curTab.value == '3' || curTab.value == '5') {
    let status = curTab.value
    if (curTab.value == '5') status = '4'
    getSelectShootModel({ status }).then(res => {
      let tempList = res.data || []
      if (tempList.length > 0) {
        selectShootModelList.value = tempList.map(item => {
          return {
            label: item.name + '(' + item.account + ')',
            value: item.id,
          }
        })
      }
    })
  }
  if (curTab.value == '3' || curTab.value == '5') {
    let status = curTab.value
    if (curTab.value == '5') status = '4'
    selectEditPerson({ status }).then(res => {
      selectEditPersonList.value = res.data || []
    })
  }
  if (curTab.value == '4' || curTab.value == '6') {
    let status = ''
    if (curTab.value == '4') {
      status = '1,2'
      // markUploadAccountSelect().then(res => {
      //   uploadAccountList.value = res.data || []
      // })
      uploadAccountSelect().then(res => {
        uploadAccountList.value = res.data || []
      })
    }
    if (curTab.value == '6') status = '0'
    uploadLinkListSelectShootModel({ status: status }).then(res => {
      selectShootModelList.value = hanldeMapList(res.data) || []
    })
  }
}
const tabBoxHeight = ref(160)

function handleSizeChange() {
  tabBoxHeight.value = document.querySelector('.tab-box')?.clientHeight || 160
  scrollToTop()
}

onMounted(() => {
  if (route.query.tab) {
    curTab.value = Number(route.query.tab) || ''
  }
  if (route.query.s) {
    queryParams.value.getStatus = Number(route.query.s) || ''
  }
  if (route.query.keyword) {
    queryParams.value.keyword = route.query.keyword
  }
  handleQuery()
  tabBoxHeight.value = document.querySelector('.tab-box')?.clientHeight || 160
  window.onresize = () => {
    tabBoxHeight.value = document.querySelector('.tab-box')?.clientHeight || 160
  }
})
</script>

<style lang="scss" scoped>
.clip-box {
  padding: 0 20px;
}
.search-box {
  margin-top: 20px;
}

.tab-box {
  padding: 20px 0 10px 0;
  position: sticky;
  top: 0px;
  z-index: 999;
  background: #fff;
}
:deep(.el-select) {
  .el-tag.is-closable {
    flex-shrink: 0;
    max-width: 120px !important; /* 设置标签最大宽度 */
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
</style>
