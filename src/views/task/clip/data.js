/**
 * 订单状态
 */
const curTabStatusList = [
  { label: '待下载', value: 1 },
  { label: '待剪辑', value: 2 },
  { label: '待反馈', value: 3 },
  { label: '需确认', value: 5 },
  { label: '待上传', value: 4 },
  { label: '已完成', value: 6 },
  { label: '已关闭', value: 7 },
]

/**
 * 领取状态
 */
const drawStatus = [
  { label: '待领取', value: 0 },
  { label: '已领取', value: 1 },
]

/**
 * 上传状态
 */
const uploadStatus = [
  // { label: '已上传', value: 0 },
  { label: '待上传', value: 1 },
  { label: '待确认上传', value: 2 },
  // { label: '取消上传', value: 3 },
]

/**
 * 上传状态
 */
const uploadStatusV1 = [
  { label: '上传成功', value: 0 },
  { label: '待上传', value: 1 },
  { label: '待确认上传', value: 2 },
  { label: '取消上传', value: 3 },
  { label: '上传失败', value: 4 },
  { label: '无需上传', value: 5 },
]


/**
 * 是否有封面状态
 */
const coverStatus = [
  { label: '是', value: 1 },
  { label: '否', value: 0 },
]

/**
 * 上传次数
 */
const uploadCountList = [
  { label: '首次上传', value: 1 },
  { label: '2次', value: 2 },
  { label: '3次', value: 3 },
  { label: '4次', value: 4 },
  { label: '4次以上', value: 5 },
]

/**
 * 关闭原因
 */
const closeStatus = [
  { label: '不反馈给商家', value: 2 },
  { label: '取消上传', value: 4 },
  { label: '订单回退', value: 3 },
  {label: '联动关闭', value: 5}
]
/**
 * 关闭原因
 */
const closeStatusV1 = [
  { label: '取消上传', value: 1 },
  { label: '不反馈给商家', value: 2 },
  { label: '订单回退', value: 3 },
  {label: '联动关闭', value: 4}
]

/**
 * 订单状态
 */
const curTabStatusMap = {}
curTabStatusList.forEach(item => {
  curTabStatusMap[item.label] = item.value
  curTabStatusMap[item.value] = item.label
})

export { curTabStatusList, curTabStatusMap, drawStatus, uploadStatus, coverStatus, uploadCountList, closeStatus,uploadStatusV1,closeStatusV1 }
