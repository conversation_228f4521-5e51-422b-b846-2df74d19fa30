<template>
  <el-dialog
    v-model="dialogVisible"
    width="700px"
    title="处理记录"
    align-center
    destroy-on-close
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <div class="form-box" v-if="!isDetailDialog">
      <el-form-item label="处理内容" ref="formItemRef" label-width="100px">
        <el-input
          v-model="remark"
          style="width: 100%"
          :rows="4"
          type="textarea"
          maxlength="1000"
          show-word-limit
          placeholder="请输入处理情况"
          :disabled="disabled"
          clearable
          @input="handleInput"
        />
      </el-form-item>
      <el-form-item label="处理图片" label-width="100px">
        <PasteUpload
          v-model="objectKeys"
          :disabled="objectKeys.length >= 5"
          style="width: 100%"
          :limit="5"
          :alwaysShow="true"
          :bucketType="'afterSale'"
          :size="5"
        />
        <div style="width: 100%; margin-bottom: 10px; color: #ababb2">
          <!-- <span style="color: #d9001b">*</span> -->
          *最多支持上传5张png/jpeg/jpg图片，大小不超过5M
        </div>
        <ViewerImageList urlName="picUrl" :data="objectKeys" is-preview-all @delete="deleteImg" />
      </el-form-item>
      <div class="flex-center">
        <el-button v-btn type="primary"  round :loading="disabled" @click="submit">
          记录
        </el-button>
      </div>
      <h2>历史处理记录</h2>
    </div>
    <div class="case-box" v-loading="loading">
      <el-steps direction="vertical" process-status="wait" finish-status="success">
        <el-step v-for="item in steps" :key="item.id" :title="item.more ? '' : item.time" status="wait">
          <template #title>
            <div v-if="!item.more">
              <span v-if="!item.operateType" style="color: #000">{{ item.operate.name }}</span>
              {{ item.time }}
            </div>
          </template>
          <template #icon>
            <div class="icon-box"></div>
          </template>
          <template #description>
            <div class="description" v-if="!item.more">
              <div class="curColor" v-if="item.operateType">
                由 {{ item.operateByType == 1 ? '处理人' : item.operateByType == 2 ? '剪辑人' : '系统'
                }}{{ item.operate?.name || '' }}
                {{ handleTaskOperatType(item.operateType, item.completionMode || item.content) }}
              </div>
              <div class="curColor" v-else>
                <!-- {{ item.operate.name }}： -->
                <div>处理内容：{{ item.content }}</div>
                <div class="flex-start" style="align-items: normal; margin-top: 5px">
                  处理图片：
                  <ViewerImageList
                    v-if="item.objectKeys && item.objectKeys.length"
                    :data="item.objectKeys"
                    is-preview-all
                    :show-delete-btn="false"
                    suffix=''
                  />
                  <div v-else>无</div>
                </div>
              </div>
            </div>
            <el-button v-else type="primary" link size="small" @click="isAllSteps = true">
              展开更多记录
              <el-icon><ArrowDown /></el-icon>
            </el-button>
          </template>
        </el-step>
      </el-steps>
      <div v-if="!steps.length" class="flex-center no-data">暂无记录</div>
    </div>
  </el-dialog>
</template>

<script setup>
import { addWorkOrderProcessRecord, getWorkOrderProcessRecord } from '@/api/task/workOrder'
import PasteUpload from '@/components/FileUpload/pasteUpload.vue'
import ViewerImageList from '@/components/ImagePreview/viewerImageList.vue'
import { handleTaskOperatType } from '@/views/task/data.js'

const { proxy } = getCurrentInstance()

const dialogVisible = ref(false)
const taskNum = ref('')
const stepsData = ref([])
const objectKeys = ref([])
const isAllSteps = ref(false)
const steps = computed(() => {
  if (stepsData.value.length) {
    if (stepsData.value.length > 3 && !isAllSteps.value) {
      return [
        ...stepsData.value.slice(0, 3),
        {
          id: Math.random(),
          more: true,
        },
      ]
    }
    return stepsData.value
  }
  return []
})
const loading = ref(false)
const disabled = ref(false)
const remark = ref('')

const formItemRef = ref()

defineExpose({
  open,
  close,
})

const emits = defineEmits(['success'])
const isDetailDialog = ref(false)

function open(id, isDetail = false) {
  isDetailDialog.value = isDetail
  taskNum.value = id
  dialogVisible.value = true
  getList()
}
function close() {
  dialogVisible.value = false
}
function handleClose() {
  remark.value = ''
  stepsData.value = []
  objectKeys.value = []
  disabled.value = false
  isAllSteps.value = false
}

function getList() {
  loading.value = true
  getWorkOrderProcessRecord(taskNum.value)
    .then(res => {
      stepsData.value = res.data
    })
    .finally(() => (loading.value = false))
}

function handleInput() {
  formItemRef.value.validateState = 'success'
}

function deleteImg(data, i) {
  objectKeys.value.splice(i, 1)
}

function submit() {
  if (!remark.value) {
    formItemRef.value.validateMessage = '*你还没有输入任何处理情况'
    formItemRef.value.validateState = 'error'
    return
  }
  proxy.$modal
    .confirm('确认记录处理情况？', '温馨提示', {
      autofocus: false,
      confirmButtonText: '确定',
      cancelButtonText: '取消',
    })
    .then(() => {
      disabled.value = true
      addWorkOrderProcessRecord({
        content: remark.value,
        taskNum: taskNum.value,
        objectKeys: objectKeys.value.map(item => item.picUrl),
      })
        .then(res => {
          proxy.$modal.msgSuccess('操作成功')
          remark.value = ''
          objectKeys.value = []
          emits('success')
          getList()
        })
        .finally(() => (disabled.value = false))
    })
    .catch(() => {})
}
</script>

<style scoped lang="scss">
.form-box {
  h2 {
    color: #000;
    font-weight: 600;
  }
  :deep(.el-form-item__label) {
    font-weight: 600;
  }
}
.case-box {
  // min-height: 400px;
  max-height: 350px;
  overflow-y: auto;

  .icon-box {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #d7d7d7;
  }
  .icon-active {
    background: var(--el-color-primary-light-2);
  }
  .curColor {
    color: #000;
  }
  .description {
    position: relative;
    font-size: 15px;

    .edit-btn {
      position: absolute;
      top: -26px;
      right: 0;
    }
  }
  .reply {
    margin-top: 10px;
    align-items: baseline;

    span {
      flex-shrink: 0;
    }

    .reply-content {
      padding: 1px 10px 1px 0px;
      font-size: 15px;
      color: var(--el-color-danger);
      // border-radius: var(--el-border-radius-round);
      // background-color: var(--el-color-info);

      &.success {
        color: var(--el-color-success);
      }
    }
    .wait {
      color: var(--el-color-primary-light-2);
    }
  }

  .no-data {
    width: 100%;
    height: 250px;
    font-size: 16px;
    color: #7f7f7f;
  }

  :deep(.el-step__main) {
    margin-bottom: 15px;
  }
  :deep(.el-step__description) {
    padding: 0;
  }
}
</style>
