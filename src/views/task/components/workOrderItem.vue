<template>
  <div class="work-order-list-item">
    <el-table
      ref="tableRef"
      :header-cell-style="{
        'background-color': '#e8e8ea !important',
      }"
      :data="tableData"
      style="width: 100%"
      border
      row-key="id"
    >
      <el-table-column
        prop="productPic"
        label="产品图"
        align="center"
        width="130"
        class-name="product-img-box"
      >
        <template v-slot="{ row }">
          <div class="flex-start top-tag">
            <el-tag v-if="row.isCare" type="danger" size="small" round>照顾单</el-tag>
          </div>
          <el-image
            style="width: 90px; height: 90px; cursor: pointer"
            :style="{ 'margin-top': row.isCare ? '15px' : '0' }"
            :src="
              row.productPic
                ? $picUrl +
                  row.productPic +
                  '?imageMogr2/format/webp/interlace/1/quality/100/interlace/0/thumbnail/200x'
                : ''
            "
            fit="scale-down"
            preview-teleported
            @click="() => row.productPic && showViewer([$picUrl + row.productPic])"
          >
            <template #error>
              <img src="@/assets/images/no-img.png" alt="" style="width: 100%; height: 100%" />
            </template>
          </el-image>
        </template>
      </el-table-column>
      <el-table-column prop="msg" label="产品信息" minWidth="320">
        <template v-slot="{ row }">
          <div class="product-info-box">
            <div class="flex-between">
              <span>视频编码：{{ row.videoCode }}</span>
              <el-button
                class="btn"
                v-if="checkPermi(['task:workOrder:more'])"
                v-btn
                plain
                size="small"
                @click="handleAction('查看订单更多', row)"
              >
                更多
              </el-button>
              <!-- <CopyButton class="btn" plain size="small" :copy-content="handleCopy(row)" /> -->
            </div>
            <div>中文名称：{{ row.productChinese }}</div>
            <div>英文名称：{{ row.productEnglish }}</div>
            <div class="one-ell productLink">
              产品链接：
              <el-link :underline="false" target="_blank" type="primary" :href="row.productLink">
                {{ row.productLink }}
              </el-link>
            </div>
            <!-- <biz-model-platform :value="row.platform" />
            <biz-model-type :value="row.modelType" />
            <biz-nation :value="row.nation" />
            <template v-for="op in videoFormatOptions" :key="op.value">
              <el-tag class="tag" v-if="op.value == row.videoFormat" type="warning" size="small" round>
                {{ op.label }}
              </el-tag>
            </template> -->
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="picCount" label="照片数量" align="center" width="150">
        <template v-slot="{ row }">
          <div>{{ handleSelectiveAssembly(row.picCount) }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="user" label="订单运营" align="center" width="300">
        <template v-slot="{ row }">
          <div>微信名：{{ row.createOrderUserNickName }}</div>
          <div>姓名：{{ row.createOrderUserName || '-' }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="productPic" label="拍摄模特" align="center" width="200">
        <template v-slot="{ row }">
          <div class="flex-center" v-if="row.shootModel?.modelPic">
            <el-avatar
              class="model-avatar fs-0"
              icon="UserFilled"
              :src="$picUrl + row.shootModel?.modelPic + '!1x1compress'"
            />
            <div style="margin-left: 10px">
              <div>{{ row.shootModel ? row.shootModel.name : '' }}</div>
              <BizModelTypeNew :value="row.shootModel?.type" />
              <!-- <biz-model-type :value="row.shootModel?.type" /> -->
            </div>
          </div>
          <div v-else>-</div>
        </template>
        <!-- <el-image
            v-if="row.shootModel"
            style="width: 90px; height: 120px; cursor: pointer"
            :src="row.shootModel ? $picUrl + row.shootModel.modelPic + '!3x4compress' : ''"
            fit="fill"
            preview-teleported
            @click="() => row.shootModel?.modelPic && showViewer([row.shootModel.modelPic])"
          ></el-image>
          <span v-else>-</span>
          <div>{{ row.shootModel ? row.shootModel.name : '' }}</div> -->
        <!-- </template> -->
      </el-table-column>
      <el-table-column fixed="right" label="操作" :align="'center'" width="170">
        <template v-slot="{ row }">
          <!-- <el-button
            v-if="checkPermi(['task:workOrder:more'])"
            v-btn
            link
            type="primary"
            @click="handleAction('查看订单更多', row)"
          >
            查看订单更多
          </el-button> -->
          <el-button
            v-if="checkPermi(['task:workOrder:orderDetail'])"
            v-btn
            link
            type="primary"
            @click="routerNewWindow('/order/details/' + row.videoId)"
          >
            跳转至订单详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- <el-collapse v-model="activeCollapse">
      <el-collapse-item name="1" disabled>
        <template #icon="{ isActive }">
          <span class="icon-ele"></span>
        </template> -->
    <!-- <template #title> -->
    <div
      class="flex-start gap-5"
      style="border: #ebeef5 1px solid; border-top: none; border-bottom: none; padding: 10px"
    >
      <span style="color: #7f7f7f">
        包含工单({{ props.data.workOrderTaskDetailListVOS?.length || 0 }})&emsp;
      </span>
      <template v-for="item in workOrderTaskTagList" :key="item.id">
        <el-tag type="warning">
          {{ workOrderTypeMap[item] }}
        </el-tag>
      </template>
    </div>
    <!-- </template> -->
    <el-table
      ref="tableRef"
      :data="workOrderList"
      style="width: 100%"
      :header-cell-style="{
        'background-color': '#fff !important',
      }"
      border
    >
      <el-table-column prop="taskNum" label="工单编号" align="center" width="200">
        <template v-slot="{ row }">
          <div>{{ row.taskNum }}</div>
          <div>{{ row.submitTime }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="workOrderType" label="工单类型" align="center" width="110">
        <template v-slot="{ row }">{{ workOrderTypeMap[row.workOrderType] }}</template>
      </el-table-column>
      <el-table-column prop="content" label="问题描述" align="center" minWidth="200">
        <template v-slot="{ row }">
          <div class="more-ell" v-has-ellipsis:contentMore="row" :key="Math.random()">
            {{ row.content }}
          </div>
          <el-button
            v-btn
            v-show="row.contentMore"
            link
            type="primary"
            @click="handleViewContent(row.content)"
          >
            更多
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="pic" label="问题图片" align="center" width="110">
        <template v-slot="{ row }">
          <div v-if="row.issuePic && row.issuePic.length > 0">
            <el-image
              style="width: 90px; height: 90px; cursor: pointer"
              :src="row.issuePic && row.issuePic.length > 0 ? $picUrl + row.issuePic[0] + '!1x1compress' : ''"
              fit="fill"
              preview-teleported
              @click="() => row.issuePic && row.issuePic.length > 0 && showViewer(row.issuePic)"
            >
              <template #error>
                <img src="@/assets/images/no-img.png" alt="" style="width: 100%; height: 100%" />
              </template>
            </el-image>
            <div v-if="row.issuePic && row.issuePic.length > 0">(共{{ row.issuePic?.length }}张)</div>
          </div>
          <div v-else>-</div>
        </template>
      </el-table-column>
      <el-table-column prop="priority" label="优先级" align="center" width="110">
        <template v-slot="{ row }">
          <el-tag :type="row.priority === 1 ? 'danger' : 'primary'">
            {{ priorityList.find(item => item.value === row.priority)?.label }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="submit" label="提交人" align="center" width="130">
        <template v-slot="{ row }">
          <div>
            <div>{{ row.submit?.name }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="assignee" label="当前处理人" align="center" width="130">
        <template v-slot="{ row }">
          <div>
            <div>{{ row.assignee?.name }}</div>
            <div v-if="row.lastReplyTime">最新回复：{{ handleTime(row.lastReplyTime) }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="isNewProcess" label="最新处理记录" align="center" width="160">
        <template v-slot="{ row }">
          <div v-if="row.isNewProcess">
            <!-- <div>{{ row.time }}</div> -->
            <div class="more-ell" style="--l: 3; line-break: anywhere; word-break: break-all">
              {{ row.isNewProcess }}
            </div>
            <div class="flex-end">
              <el-button v-btn link type="primary" @click="handleAction('处理记录', row)">更多</el-button>
            </div>
          </div>
          <div v-else>
            <div>暂无记录</div>
            <el-button
              v-btn
              link
              type="primary"
              v-if="checkPermi(['task:workOrder:records'])"
              @click="handleAction('处理记录', row)"
            >
              去记录
            </el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="工单状态" align="center" width="120">
        <template v-slot="{ row }">
          {{ workOrderStatusMap[row.status] || '-' }}
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" :align="'center'" width="170">
        <template v-slot="{ row }">
          <el-button
            v-if="
              (row.assignee?.id == store.id || store.isAdmin) &&
              data.status != orderStatusMap['需确认'] &&
              data.status != orderStatusMap['已完成'] &&
              data.status != orderStatusMap['交易关闭'] &&
              row.status == workOrderStatusMap['待处理'] &&
              row.workOrderType == workOrderTypeMap['模特没收到货'] &&
              checkPermi(['task:workOrder:reissue'])
            "
            v-btn
            link
            type="primary"
            @click="handleAction('补发', row)"
          >
            补发
          </el-button>
          <el-button
            v-if="
              (row.assignee?.id == store.id || row.contactId == store.id || store.isAdmin) &&
              data.status != orderStatusMap['交易关闭'] &&
              row.status == workOrderStatusMap['待处理'] &&
              row.workOrderType == workOrderTypeMap['模特没收到货'] &&
              checkPermi(['task:workOrder:compensate'])
            "
            v-btn
            link
            type="primary"
            @click="handleAction('补偿', row)"
          >
            补偿
          </el-button>
          <!-- 处理人 -->
          <template v-if="row.assignee?.id == store.id || store.isAdmin">
            <template v-if="row.status == workOrderStatusMap['待处理']">
              <el-button
                v-if="
                  (row.workOrderType == workOrderTypeMap['模特没收到货'] ||
                    row.workOrderType == workOrderTypeMap['催素材'] ||
                    row.workOrderType == workOrderTypeMap['素材链接问题']) &&
                  checkPermi(['task:workOrder:assign'])
                "
                v-btn
                link
                type="primary"
                @click="handleAction('指派', row)"
              >
                指派
              </el-button>
              <el-button
                v-if="
                  data.status != orderStatusMap['已完成'] &&
                  data.status != orderStatusMap['交易关闭'] &&
                  (row.workOrderType == workOrderTypeMap['催素材'] ||
                    row.workOrderType == workOrderTypeMap['素材链接问题']) &&
                  checkPermi(['task:workOrder:modelFeedback'])
                "
                v-btn
                link
                type="primary"
                @click="handleAction('模特反馈素材', row)"
              >
                模特反馈素材
              </el-button>
              <!-- row.workOrderType == workOrderTypeMap['需剪辑'] && -->
              <!-- <el-button
                v-if="
                  data.status != orderStatusMap['已完成'] &&
                  data.status != orderStatusMap['交易关闭'] &&
                  checkPermi(['task:workOrder:feedbackMaterial'])
                "
                v-btn
                link
                type="primary"
                @click="handleAction('反馈素材给商家', row)"
              >
                反馈素材给商家
              </el-button> -->
              <el-button
                v-if="
                  (row.workOrderType == workOrderTypeMap['下架视频'] ||
                    row.workOrderType == workOrderTypeMap['需剪辑'] ||
                    row.workOrderType == workOrderTypeMap['其他'] ||
                    row.workOrderType == workOrderTypeMap['上传异常']) &&
                  checkPermi(['task:workOrder:completed'])
                "
                v-btn
                link
                type="primary"
                @click="handleAction('完结工单', row)"
              >
                完结工单
              </el-button>
              <el-button
                v-if="checkPermi(['task:workOrder:reject'])"
                v-btn
                link
                type="primary"
                @click="handleAction('拒绝工单', row)"
              >
                拒绝工单
              </el-button>
            </template>
          </template>
          <!-- 提交人 -->
          <template v-if="row.submit?.id == store.id || store.isAdmin">
            <template
              v-if="row.status == workOrderStatusMap['待处理'] && checkPermi(['task:workOrder:close'])"
            >
              <el-button v-btn link type="primary" @click="handleAction('关闭工单', row)">关闭工单</el-button>
            </template>
            <template
              v-if="row.status == workOrderStatusMap['已拒绝'] && checkPermi(['task:workOrder:open'])"
            >
              <el-button v-btn link type="primary" @click="handleAction('重新打开', row)">重新打开</el-button>
            </template>
          </template>
          <el-button
            v-if="checkPermi(['task:workOrder:records'])"
            v-btn
            link
            type="primary"
            @click="handleAction('处理记录', row)"
          >
            处理记录
          </el-button>
          <el-button
            v-if="checkPermi(['task:workOrder:detail'])"
            v-btn
            link
            type="primary"
            @click="handleAction('查看详情', row)"
          >
            查看详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <div
      v-if="props.data?.workOrderTaskDetailListVOS?.length > 2"
      @click="isShowAll = !isShowAll"
      class="more-btn"
    >
      <span v-if="!isShowAll">展开更多({{ props.data.workOrderTaskDetailListVOS.length - 2 }})</span>
      <span v-else>收起更多</span>
    </div>
    <!-- </el-collapse-item>
    </el-collapse> -->
  </div>
</template>

<script setup>
// import CopyButton from '@/components/Button/CopyButton.vue'
import useUserStore from '@/store/modules/user'
import { checkPermi } from '@/utils/permission'
import { handleSelectiveAssembly } from '@/views/task/preselection/index.js'
import { priorityList, workOrderStatusMap, workOrderTypeMap } from '@/views/task/data.js'
import { orderStatusMap } from '@/views/order/list/data.js'
import { useViewer } from '@/hooks/useViewer'

const { proxy } = getCurrentInstance()
const router = useRouter()
function routerNewWindow(path) {
  const { href } = router.resolve({ path })
  window.open(href, '_blank')
}

const { showViewer } = useViewer()

const store = useUserStore()

const props = defineProps({
  data: {
    type: Object,
    required: true,
  },
})
const emits = defineEmits(['action'])

const isShowAll = ref(false)

const tableData = computed(() => {
  if (props.data) {
    return [props.data]
  }
  return []
})

const workOrderList = computed(() => {
  if (props.data?.workOrderTaskDetailListVOS?.length) {
    return isShowAll.value
      ? props.data.workOrderTaskDetailListVOS.map(item => ({ ...item, contentMore: false }))
      : props.data.workOrderTaskDetailListVOS.slice(0, 2).map(item => ({ ...item, contentMore: false }))
  }
  return []
})
const workOrderTaskTagList = computed(() => {
  if (props.data?.workOrderTaskDetailListVOS?.length) {
    let tagList = props.data.workOrderTaskDetailListVOS.map(item => item.workOrderType)
    return [...new Set(tagList)]
  }
  return []
})

function handleViewContent(content) {
  if (!content) return
  let dom = `<div style="max-height: 500px; overflow-y: auto;">
      ${content}
    </div>`
  proxy.$modal.alert(dom, '问题描述', {
    customStyle: {
      '--el-messagebox-width': '550px',
    },
    dangerouslyUseHTMLString: true,
    showConfirmButton: false,
    showCancelButton: false,
    closeOnClickModal: true,
  })
}

function handleTime(time) {
  if (!time) return ''
  let t = time.split(' ')[0]
  let date = new Date(t).getTime()
  let now = new Date().getTime()
  let day = Math.floor((now - date) / (1000 * 60 * 60 * 24))
  if (day <= 0) {
    return '今天'
  } else if (day == 1) {
    return '昨天'
  } else if (day == 2) {
    return '前天'
  }
  return '更早之前'
}

function handleAction(btn, row) {
  emits('action', btn, row, props.data)
}
</script>

<style scoped lang="scss">
.work-order-list-item {
  margin: 0 0 10px 0;

  .product-info-box {
    .btn {
      padding: 2px 4px;
      height: auto;
      font-size: 12px;
    }
  }
  .productLink {
    position: relative;
    padding-right: 5px;

    :deep(.el-link) {
      display: contents;

      .el-link__inner {
        display: inline;
      }
    }
  }

  .more-btn {
    border: #ebeef5 1px solid;
    border-top: none;
    text-align: center;
    padding: 10px 0;
    cursor: pointer;
    color: #027db4;
  }

  :deep(.el-table) {
    .el-table__cell {
      position: relative;
    }
    .el-button + .el-button {
      margin: 0 3px;
    }
    .product-img-box {
      position: relative;

      .top-tag {
        position: absolute;
        top: 2px;
        left: 1px;
      }
    }
  }
}
.tooltip-tag {
  max-width: 510px;
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}
</style>
