<template>
  <div>
    <el-dialog
      v-model="showDialog"
      :title="title"
      align-center
      @close="handleClose"
      width="600px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form ref="ruleFormRef" :model="dialogForm" :rules="rules" label-width="120px">
        <el-form-item label="指派处理人" prop="assigneeId" v-if="dialogType === 'assign'">
          <el-select v-model="dialogForm.assigneeId" placeholder="请选择" clearable style="width: 220px">
            <el-option
              v-for="item in assigneeList"
              :key="item.userId"
              :label="item.userName"
              :value="item.userId"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="reasonLabel" prop="reason">
          <el-input
            v-model="dialogForm.reason"
            maxlength="300"
            type="textarea"
            :rows="5"
            show-word-limit
            placeholder="请输入理由"
          />
        </el-form-item>
        <el-form-item label="补充图片说明：">
          <PasteUpload
            v-model="dialogForm.picUrl"
            v-if="dialogForm.picUrl.length < 5"
            style="margin-bottom: 10px; width: 100%"
            :limit="5"
            :alwaysShow="true"
            :bucketType="'afterSale'"
            :isClear="true"
            :size="5"
            @success="pasteUploadChange"
          />
          <div v-if="dialogForm.picUrl.length < 5" style="margin-top: -10px; font-size: 12px; color: #7f7f7f; width: 100%">
            *支持最多上传5张格式为png/jpeg/jpg图片，大小不超过5M
          </div>
          <div>
            <ViewerImageList urlName="picUrl" :data="dialogForm.picUrl" is-preview-all @delete="deleteImg" />
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="close">取消</el-button>
          <el-button type="primary" @click="handleSubmit">{{ confirmBtn }}</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import PasteUpload from '@/components/FileUpload/pasteUpload.vue'
import ViewerImageList from '@/components/ImagePreview/viewerImageList.vue'
import { listUser } from '@/api/system/user'

const props = defineProps({
  type: {
    type: String,
    default: 'afterSale',
  },
})

const showDialog = ref(false)
const title = ref('')
const reasonLabel = ref('理由')
const ruleFormRef = ref(null)
const confirmBtn = ref('确认')
const dialogType = ref('')

const assigneeList = ref([])
const dialogForm = ref({
  assigneeId: '',
  reason: '',
  picUrl: [],
  operateType: '',
})

const rules = {
  assigneeId: [{ required: true, message: '请选择处理人', trigger: 'blur' }],
  reason: [{ required: true, message: '请输入理由', trigger: 'change' }],
}

const emits = defineEmits(['submit'])

defineExpose({ open, close })

function open(type, id, taskNum) {
  dialogForm.value.id = id
  let tit = props.type === 'afterSale' ? '售后' : '工单'
  switch (type) {
    case 'reject':
      dialogType.value = 'reject'
      title.value = '确认拒绝' + tit + '?'
      reasonLabel.value = '拒绝理由：'
      break
    case 'applyCancel':
      dialogType.value = 'applyCancel'
      title.value = '申请取消' + tit + '?'
      reasonLabel.value = '取消理由：'
      break
    case 'confirmCancel':
      dialogType.value = 'confirmCancel'
      title.value = '确认取消' + tit + '?'
      reasonLabel.value = '取消理由：'
      break
    case 'reset':
      dialogType.value = 'reset'
      title.value = '重新打开' + tit + '?'
      reasonLabel.value = '打开理由：'
      break
    case 'assign':
      dialogType.value = 'assign'
      title.value = '指派他人处理'
      reasonLabel.value = '转移理由：'
      getAssigneeList()
      break
  }
  showDialog.value = true
}

function close() {
  showDialog.value = false
}
function handleClose() {
  dialogForm.value = {
    assigneeId: '',
    reason: '',
    picUrl: [],
  }
  ruleFormRef.value.resetFields()
  assigneeList.value.length = 0
  showDialog.value = false
}

function handleSubmit() {
  ruleFormRef.value.validate(vaild => {
    if (vaild) {
      emits('submit', dialogForm.value)
    }
  })
}
// 获取指派处理人列表
function getAssigneeList() {
  listUser().then(res => {
    if (res.code === 200) {
      assigneeList.value = res.data || []
    }
  })
}

function pasteUploadChange(data) {
  //   if (data && data.length > 0) {
  //     const list = data.map(item => item.data.picUrl)
  //     dialogForm.value.picUrl = list
  //   }
}

function deleteImg(data, i) {
  dialogForm.value.picUrl.splice(i, 1)
}
</script>

<style scoped lang="scss">
.upload-img {
  position: relative;
  .img-icon {
    cursor: pointer;
    position: absolute;
    bottom: 15px;
    right: 5px;
    z-index: 9;
  }
  .img-error {
    width: 80px;
    height: 80px;
    background: var(--el-fill-color-light);
    text-align: center;
    line-height: 80px;
    color: #ababb2;
  }
}
</style>
