<template>
  <div class="preselection-list-item">
    <el-table
      ref="tableRef"
      :header-cell-style="{
        'background-color': status ? '#e8e8ea !important' : '',
      }"
      :data="tableData"
      style="width: 100%"
      border
      row-key="id"
      :key="status"
    >
      <el-table-column
        prop="productPic"
        label="产品图"
        align="center"
        width="130"
        class-name="product-img-box"
      >
        <template v-slot="{ row }">
          <div class="flex-start top-tag">
            <el-tag v-if="row.isCare" type="danger" size="small" round>照顾单</el-tag>
          </div>
          <el-image
            style="width: 90px; height: 90px; cursor: pointer"
            :style="{ 'margin-top': row.isCare ? '15px' : '0' }"
            :src="
              row.productPic
                ? $picUrl +
                  row.productPic +
                  '?imageMogr2/format/webp/interlace/1/quality/100/interlace/0/thumbnail/200x'
                : ''
            "
            fit="scale-down"
            preview-teleported
            @click="() => row.productPic && showViewer([$picUrl + row.productPic])"
          >
            <template #error>
              <img src="@/assets/images/no-img.png" alt="" style="width: 100%; height: 100%" />
            </template>
          </el-image>
        </template>
      </el-table-column>
      <el-table-column prop="msg" label="产品信息" minWidth="320">
        <template v-slot="{ row }">
          <div class="product-info-box">
            <div class="flex-between">
              <span>视频编码：{{ row.videoCode }}</span>
              <el-button
                class="btn"
                size="small"
                v-if="checkPermi(['task:afterSale:more'])"
                v-btn
                plain
                @click="handleAction('查看订单更多', row)"
              >
                更多
              </el-button>
              <!-- <CopyButton class="btn" plain size="small" :copy-content="handleCopy(row)" /> -->
            </div>
            <div>中文名称：{{ row.productChinese }}</div>
            <div>英文名称：{{ row.productEnglish }}</div>
            <div class="one-ell productLink">
              产品链接：
              <el-link :underline="false" target="_blank" type="primary" :href="row.productLink">
                {{ row.productLink }}
              </el-link>
            </div>
            <biz-model-platform :value="row.platform" />
            <biz-model-type :value="row.modelType" />
            <biz-nation :value="row.nation" />
            <template v-for="op in videoFormatOptions" :key="op.value">
              <el-tag class="tag" v-if="op.value == row.videoFormat" type="warning" size="small" round>
                {{ op.label }}
              </el-tag>
            </template>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="picCount" label="照片数量" align="center" width="150">
        <template v-slot="{ row }">
          <div>{{ handleSelectiveAssembly(row.picCount) }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="user" label="订单运营" align="center" width="300">
        <template v-slot="{ row }">
          <div>微信名：{{ row.createOrderUserNickName }}</div>
          <div>姓名：{{ row.createOrderUserName || '-' }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="productPic" label="拍摄模特" align="center" width="200">
        <template v-slot="{ row }">
          <div class="flex-center" v-if="row.shootModel?.modelPic">
            <el-avatar
              class="model-avatar fs-0"
              icon="UserFilled"
              :src="$picUrl + row.shootModel?.modelPic + '!1x1compress'"
            />
            <div style="margin-left: 10px">
              <div>{{ row.shootModel ? row.shootModel.name : '' }}</div>
              <BizModelTypeNew :value="row.shootModel?.type" />
              <!-- <biz-model-type :value="row.shootModel.type" /> -->
            </div>
          </div>
          <div v-else>-</div>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" :align="'center'" width="170">
        <template v-slot="{ row }">
          <div class="flex-column">
            <!-- <el-button
              v-if="checkPermi(['task:afterSale:more'])"
              v-btn
              link
              type="primary"
              @click="handleAction('查看订单更多', row)"
            >
              查看订单更多
            </el-button> -->
            <el-button
              v-if="checkPermi(['task:afterSale:orderDetail'])"
              v-btn
              link
              type="primary"
              @click="routerNewWindow('/order/details/' + row.videoId)"
            >
              跳转至订单详情
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <div class="flex-start gap-5 after-sale-box">
      <span style="color: #7f7f7f">包含售后({{ data.orderVideoTaskDetailList?.length || 0 }})&emsp;</span>
      <el-tag type="warning" v-for="item in data.afterSaleTypes" :key="item">
        {{ afterSaleTypeList.find(data => data.value == item)?.label }}
      </el-tag>
    </div>
    <el-table
      ref="tableRef"
      :data="isShowAll ? data.orderVideoTaskDetailList : modelList"
      style="width: 100%"
      :header-cell-style="{
        'background-color': '#fff !important',
      }"
      border
    >
      <el-table-column prop="taskNum" label="售后编号" align="center" width="200">
        <template v-slot="{ row }">
          {{ row.taskNum }}
          <div>{{ row.submitTime }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="afterSaleClass" label="售后分类" align="center" width="110">
        <template v-slot="{ row }">
          {{ row.afterSaleClass == 1 ? '视频' : '照片' }}
        </template>
      </el-table-column>
      <el-table-column prop="afterSaleClass" label="售后类型" align="center" width="110">
        <template v-slot="{ row }">
          <template v-if="row.afterSaleClass == 1">
            {{ afterSaleVideoTypeList.find(item => item.value == row.afterSaleVideoType)?.label }}
          </template>
          <template v-else>
            {{ afterSalePicTypeList.find(item => item.value === row.afterSalePicType)?.label }}
          </template>
        </template>
      </el-table-column>
      <el-table-column prop="content" label="问题描述" align="center">
        <template v-slot="{ row }">
          <div style="display: flex">
            <div class="one-ell" v-has-ellipsis:remarkMore="row" style="width: 95%" :key="Math.random()">
              {{ row.content }}
            </div>
            <div>
              <el-button v-btn v-if="row.remarkMore" link type="primary" @click="handleAction('更多', row)">
                更多
              </el-button>
            </div>
          </div>
          <!-- <div>{{ row.remarkMore }}</div>
          <el-button v-btn v-if="row.remarkMore" link type="primary" @click="handleAction('更多', row)">
            更多
          </el-button> -->
        </template>
      </el-table-column>
      <el-table-column prop="issuePic" label="问题图片" align="center" width="110">
        <template v-slot="{ row }">
          <div v-if="row.issuePic && row.issuePic.length > 0">
            <el-image
              style="width: 90px; height: 90px; cursor: pointer"
              :src="row.issuePic && row.issuePic.length > 0 ? $picUrl + row.issuePic[0] + '!1x1compress' : ''"
              fit="fill"
              preview-teleported
              @click="() => row.issuePic && row.issuePic.length > 0 && showViewer(row.issuePic)"
            >
              <template #error>
                <img src="@/assets/images/no-img.png" alt="" style="width: 100%; height: 100%" />
              </template>
            </el-image>
            <div v-if="row.issuePic && row.issuePic.length > 0">(共{{ row.issuePic?.length }}张)</div>
          </div>
          <div v-else>-</div>
        </template>
      </el-table-column>
      <el-table-column prop="priority" label="优先级" align="center" width="110">
        <template v-slot="{ row }">
          <el-tag :type="row.priority === 1 ? 'danger' : 'primary'">
            {{ priorityList.find(item => item.value === row.priority)?.label }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="submitBy" label="提交人" align="center" width="120"></el-table-column>
      <el-table-column prop="assignee" label="处理人" align="center" width="120">
        <template v-slot="{ row }">
          <div>{{ row.assignee?.name || '' }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="售后状态" align="center" width="120">
        <template v-slot="{ row }">
          {{ afterSaleStatusList.find(item => item.value === row.status)?.label }}
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" :align="'center'" width="170">
        <template v-slot="{ row }">
          <div class="flex-column">
            <template v-if="row.status == afterSaleStatusMap['待处理']">
              <!-- v-if="checkPermi(['my:preselection:out']) && (row.status == 1 || row.status == 0)" -->
              <div class="flex-column" v-if="userStore.id == row.assignee?.id || userStore.isAdmin">
                <el-button
                  v-btn
                  v-if="checkPermi(['task:afterSale:confirm'])"
                  link
                  type="primary"
                  @click="handleAction('确认售后', row, tableData[0])"
                >
                  确认售后
                </el-button>
                <el-button
                  v-btn
                  v-if="checkPermi(['task:afterSale:refuse'])"
                  link
                  type="primary"
                  @click="handleAction('拒绝售后', row)"
                >
                  拒绝售后
                </el-button>
              </div>
              <div v-if="userStore.id == row.submit?.id || userStore.isAdmin">
                <el-button
                  v-if="checkPermi(['task:afterSale:close'])"
                  v-btn
                  link
                  type="primary"
                  @click="handleAction('取消售后', row)"
                >
                  取消售后
                </el-button>
              </div>
            </template>
            <template v-if="row.status == afterSaleStatusMap['处理中']">
              <div class="flex-column" v-if="userStore.id == row.assignee?.id || userStore.isAdmin">
                <!-- <el-button
                  v-btn
                  link
                  type="primary"
                  v-if="
                    checkPermi(['task:afterSale:feedbackMaterial']) &&
                    tableData[0].status != '8' &&
                    tableData[0].status != '9' && !handleShow(row)
                  "
                  @click="handleAction('反馈素材给商家', row, tableData[0])"
                >
                  反馈素材给商家
                </el-button> -->
                <el-button
                  v-if="handleShow(row)"
                  v-btn
                  link
                  type="primary"
                  @click="handleAction('模特反馈素材', row,tableData[0])"
                >
                  模特反馈素材
                </el-button>
                <el-button
                  v-btn
                  link
                  type="primary"
                  v-if="checkPermi(['task:afterSale:finish'])"
                  @click="handleAction('完结工单', row)"
                >
                  完结售后
                </el-button>
                <el-button
                  v-btn
                  link
                  type="primary"
                  v-if="checkPermi(['task:afterSale:applicationForCancellation'])"
                  @click="handleAction('申请取消', row)"
                >
                  申请取消
                </el-button>
              </div>
              <div v-if="userStore.id == row.submit?.id || userStore.isAdmin">
                <el-button
                  v-btn
                  link
                  type="primary"
                  v-if="checkPermi(['task:afterSale:close'])"
                  @click="handleAction('取消售后', row)"
                >
                  取消售后
                </el-button>
              </div>
            </template>
            <template v-if="row.status == afterSaleStatusMap['申请取消中']">
              <div v-if="userStore.id == row.assignee?.id || userStore.isAdmin">
                <el-button
                  v-btn
                  link
                  type="primary"
                  v-if="checkPermi(['task:afterSale:cancelApplication'])"
                  @click="handleAction('撤销申请', row)"
                >
                  撤销申请
                </el-button>
              </div>
              <div v-if="userStore.id == row.submit?.id || userStore.isAdmin">
                <el-button
                  v-if="checkPermi(['task:afterSale:operateCancel'])"
                  v-btn
                  link
                  type="primary"
                  @click="handleAction('处理申请', row)"
                >
                  处理申请
                </el-button>
              </div>
            </template>
            <template v-if="row.status == afterSaleStatusMap['已拒绝']">
              <div class="flex-column" v-if="userStore.id == row.submit?.id || userStore.isAdmin">
                <el-button
                  v-btn
                  link
                  type="primary"
                  v-if="checkPermi(['task:afterSale:reopen'])"
                  @click="handleAction('重新打开', row)"
                >
                  重新打开
                </el-button>
              </div>
            </template>
            <el-button
              v-if="checkPermi(['task:afterSale:detail'])"
              v-btn
              link
              type="primary"
              @click="routerNewWindow('/task/afterSale/detail/' + row.taskNum)"
            >
              查看详情
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <div
      v-if="moreNum > 0"
      @click="isShowAll = !isShowAll"
      style="
        border: #ebeef5 1px solid;
        border-top: none;
        text-align: center;
        padding: 10px 0;
        cursor: pointer;
        color: #027db4;
      "
    >
      <span v-if="!isShowAll">展开更多({{ moreNum }})</span>
      <span v-else>收起更多</span>
    </div>
    <!-- </el-collapse-item>
    </el-collapse> -->
  </div>
</template>

<script setup>
import CopyButton from '@/components/Button/CopyButton.vue'
import useUserStore from '@/store/modules/user'
import { orderStatusMap, videoFormatOptions } from '@/views/order/list/data.js'
import { bizCommissionUnit } from '@/utils/dict'
import { checkPermi } from '@/utils/permission'
import {
  modelIntentionOptions,
  communicationOptions,
  sourceOptions,
  handleCopy,
  handleSelectiveAssembly,
  isRefund,
  handleOrderStatusTime,
} from '@/views/task/preselection/index.js'
import {
  afterSaleStatusMap,
  priorityList,
  afterSaleTypeList,
  afterSaleStatusList,
  afterSaleVideoTypeList,
  afterSalePicTypeList,
} from '@/views/task/data.js'
import { useViewer } from '@/hooks/useViewer'

const userStore = useUserStore()

const router = useRouter()
function routerNewWindow(path) {
  const { href } = router.resolve({ path })
  window.open(href, '_blank')
}

const { showViewer } = useViewer()

const props = defineProps({
  data: {
    type: Object,
    required: true,
  },
  status: {
    type: Number,
    default: 1,
  },
})
const emits = defineEmits(['action'])

const isShowAll = ref(false)

const tableData = computed(() => {
  isShowAll.value = false
  if (props.data) {
    return props.status === 1
      ? [
          {
            ...props.data,
            cautionsMore: false,
          },
        ]
      : props.data.map(item => ({
          ...item,
          cautionsMore: false,
        }))
  }
  return []
})
const curSelectModel = computed(() => {
  if (props.data?.preselectModelList) {
    return props.data.preselectModelList.find(item => item.status === 2)?.id
  }
  return ''
})
const moreNum = ref(0)
const modelList = computed(() => {
  if (props.data?.orderVideoTaskDetailList && props.data.orderVideoTaskDetailList.length > 2) {
    moreNum.value = props.data.orderVideoTaskDetailList.length - 2
    isShowAll.value = false
    return props.data.orderVideoTaskDetailList.slice(0, 2)
  } else if (props.data?.orderVideoTaskDetailList && props.data.orderVideoTaskDetailList.length <= 2) {
    moreNum.value = 0
    isShowAll.value = false
    return props.data.orderVideoTaskDetailList
  }
})

function handleStatus(val, type) {
  let str = ''
  if (type == 'modelIntention') {
    str = modelIntentionOptions.find(item => item.value == val)
  } else if (type == 'status') {
    str = communicationOptions.find(item => item.value == val)
  }
  return str ? str.label : '-'
}

function handleAction(btn, row, data = {}) {
  emits('action', btn, row, data)
}

function handleShow(row) {
  let isShow = false
  if(row.afterSaleClass == 1) {
    if(row.afterSaleVideoType == 1|| row.afterSaleVideoType == 2 || row.afterSaleVideoType == 5) {
      isShow = true
    }
  }
  if(row.afterSaleClass == 2) {
    if(row.afterSalePicType == 1|| row.afterSalePicType == 3) {
      isShow = true
    }
  }
  return isShow
}

const tableRowClassName = ({ row, rowIndex }) => {
  // if (row.persons[0].id == store.id) {
  //   return 'warning-row'
  // }
  // return ''
}

function handleCarryType(row) {
  if (row.carryType == 1) {
    return '主携带'
  }
  if (row.carryType == 2) {
    return '被携带'
  }
  if (row.commission) {
    return row.commission + handleCommissionUnit(row.commissionUnit)
  }
  return '-'
}

function handleAddType(val) {
  let b = sourceOptions.find(item => item.value == val)
  return b ? b.label : ''
}
function handleCommissionUnit(val) {
  let b = bizCommissionUnit.find(item => item.value == val)
  return b ? b.label : ''
}

function handleHasEllipsis(val, row) {
  if (val) {
    row.cautionsMore = true
  }
}
</script>

<style scoped lang="scss">
.preselection-list-item {
  margin: 0 0 10px 0;

  .product-info-box {
    .btn {
      padding: 2px 4px;
      height: auto;
      font-size: 12px;
    }
  }
  .productLink {
    position: relative;
    padding-right: 5px;

    :deep(.el-link) {
      display: contents;

      .el-link__inner {
        display: inline;
      }
    }
  }

  .after-sale-box {
    border: #ebeef5 1px solid;
    border-top: none;
    border-bottom: none;
    padding: 10px;
    align-items: baseline;
    flex-wrap: wrap;
  }

  .model-avatar-box {
    position: relative;
    display: flex;

    .sel-tag {
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%) scale(0.8) translateY(10px);
      height: 18px;
      padding: 0 5px;
    }
  }

  .btn-red-tip {
    &::after {
      content: '';
      display: block;
      width: 6px;
      height: 6px;
      background-color: red;
      border-radius: 50%;
      margin-left: 3px;
    }
  }

  :deep(.el-table) {
    .el-table__cell {
      position: relative;
    }
    .el-button + .el-button {
      margin: 0 3px;
    }
    .product-img-box {
      position: relative;

      .top-tag {
        position: absolute;
        top: 2px;
        left: 1px;
      }
    }
    .corner-mark-hint {
      z-index: 999;
      position: absolute;
      right: 0px;
      top: 0px;
      color: #00bfbf;
      // background-color: #d0efef;
      padding: 0px 4px 6px;
      background-image: url('@/assets/icons/svg/corner-mark-flag.svg');
    }
    .warning-row {
      --el-table-tr-bg-color: var(--el-color-warning-light-9);
    }

    .preselection-model-box {
      padding-top: 8px;
      overflow: hidden;

      .hint-box {
        z-index: 999;
        position: absolute;
        right: -6px;
        top: -2px;
        // overflow: hidden;
        width: 32px;
        height: 25px;

        .exchange {
          background: var(--el-color-warning);
          color: #fff;
          font-size: 13px;
          width: 60px;
          transform: translate(-10px, -3px) scale(0.8) rotateZ(45deg);

          .text {
            transform: rotateZ(-45deg) translate(-1px, 1px);
          }
        }
      }
    }
  }
  :deep(.el-collapse) {
    border-top: none;

    .el-collapse-item {
      .el-collapse-item__header {
        background-color: #f8f8f9;
        border-left: 1px solid #ebeef5;
        border-right: 1px solid #ebeef5;
      }
      &__content {
        padding: 0;
      }
    }
  }
}
</style>
