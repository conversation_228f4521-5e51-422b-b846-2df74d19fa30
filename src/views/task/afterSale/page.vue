<template>
  <div class="after-sale-page">
    <el-tabs v-model="curTab" class="tabs" @tab-change="handleTabChange">
      <el-tab-pane v-for="(tab, i) in tabList" :key="tab.value" :name="tab.value">
        <template #label>
          <!-- v-if="i" -->
          <div>
            {{ tab.label }}
            {{ '(' }}
            <span style="color: red">{{ tab.number }}</span>
            {{ ')' }}
          </div>
          <!-- <div v-else>{{ tab.label }}</div> -->
        </template>
      </el-tab-pane>
    </el-tabs>
    <div class="search-box">
      <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="80px" @submit.prevent>
        <el-form-item label="搜索">
          <el-input
            placeholder="支持视频编码、产品信息进行搜索"
            v-model="queryParams.keyword"
            style="width: 300px"
            @keyup.enter.native="handleQuery"
            clearable
          />
        </el-form-item>
        <el-form-item label="优先级">
          <el-select v-model="queryParams.priority" placeholder="请选择优先级" clearable style="width: 180px">
            <el-option
              v-for="item in priorityList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="拍摄模特">
          <el-select
            v-model="queryParams.shootModelId"
            placeholder="请选择拍摄模特"
            multiple
            filterable
            collapse-tags
            :reserve-keyword="false"
            collapse-tags-tooltip
            clearable
            style="width: 180px"
          >
            <el-option v-for="item in shootModelList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="售后类型">
          <el-select
            v-model="queryParams.afterSaleType"
            placeholder="请选择售后类型"
            clearable
            filterable
            :reserve-keyword="false"
            multiple
            collapse-tags
            style="width: 180px"
          >
            <el-option
              v-for="item in afterSaleTypeList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" v-if="curTab == ''">
          <el-select
            v-model="queryParams.status"
            placeholder="请选择状态"
            clearable
            multiple
            collapse-tags
            style="width: 180px"
          >
            <el-option
              v-for="item in afterSaleStatusList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="处理人">
          <el-select
            v-model="queryParams.assigneeId"
            placeholder="请选择处理人"
            clearable
            filterable
            :reserve-keyword="false"
            collapse-tags
            style="width: 180px"
          >
            <el-option v-for="item in assigneeList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="提交人">
          <el-select
            v-model="queryParams.submitById"
            placeholder="请选择提交人"
            clearable
            filterable
            collapse-tags
            style="width: 180px"
          >
            <el-option v-for="item in submitList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="任务关联人" label-width="90px">
          <el-select
            v-model="queryParams.relevanceUserId"
            placeholder="请选择任务关联人"
            clearable
            style="width: 180px"
          >
            <el-option v-for="item in relevanceUserList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="售后时间">
          <el-checkbox-group
            v-model="statusDays"
            @change="changeStatusDays"
            style="--el-checkbox-font-weight: 500"
          >
            <el-checkbox-button value="1">5天内</el-checkbox-button>
            <el-checkbox-button value="2">5-15天</el-checkbox-button>
            <el-checkbox-button value="3">大于15天</el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item>
          <el-button v-btn type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button v-btn icon="Refresh" plain @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="table-box" v-loading="loading">
      <template v-if="!tableData.length">
        <el-empty description="暂无数据" :image-size="80"></el-empty>
      </template>
      <AfterSaleItem
        ref="AfterSaleItemRef"
        v-for="item in tableData"
        :key="item.id"
        :data="item"
        @action="handleAction"
      />
    </div>
    <div class="flex-end" style="margin: 10px 0 60px">
      <PaginationFloatBar :current-page="pageNum" :page-size="pageSize" @update:current-page="handlePageChange" :total="total" />
      <!-- <el-pagination
        background
        @size-change="pageChange({ pageNum: 1, pageSize: $event })"
        @current-change="pageChange({ pageNum: $event, pageSize })"
        :current-page="pageNum"
        :page-size="pageSize"
        :page-sizes="[10, 20, 30, 50]"
        layout="total, prev, pager, next, jumper"
        :total="total"
      /> -->
    </div>
    <ConfirmDialog ref="confirmDialogRef" @submit="hanleConfirmSubmit" />
    <ProductMoreInfo ref="ProductMoreInfoRef" />
    <FeedbackLinkToMerchant ref="FeedbackLinkToMerchantRef" @success="handleQuery" />
    <el-dialog
      v-model="showSaleDialog"
      title="确认售后"
      align-center
      @close="closeSaleDialog"
      width="600px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form
        label-position="left"
        ref="saleFormRef"
        :model="saleForm"
        :rules="saleFormRules"
        label-width="130px"
      >
        <el-form-item style="display: block" label="售后内容：">
          <div style="word-break: break-all; padding-left: 30px; width: 100%">
            <div
              style="
                max-height: 50vh;
                overflow: auto;
                margin-bottom: 10px;
                line-break: anywhere;
                white-space: pre-wrap;
              "
            >
              {{ saleForm.content }}
            </div>
            <ViewerImageList :data="saleForm.issuePic" is-preview-all :show-delete-btn="false" />
          </div>
        </el-form-item>
        <el-form-item style="display: block" prop="remark" label="售后内容(英文)：">
          <el-input
            type="textarea"
            v-model="saleForm.remark"
            :rows="6"
            show-word-limit
            :maxlength="5000"
            placeholder="请输入提供给模特的售后内容"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeSaleDialog">取消</el-button>
          <el-button type="primary" @click="handleSaleSubmit">确认售后</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
      v-model="showMoreInfoDialog"
      title="更多"
      align-center
      @close="closeMoreInfoDialog"
      width="500px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <!-- <div>问题描述：</div> -->
      <div style="line-break: anywhere; max-height: 70vh; overflow: auto; white-space: pre-wrap">
        {{ moreInfoContent }}
      </div>
    </el-dialog>
    <el-dialog
      v-model="showConfirmSaleDialog"
      title=""
      align-center
      @close="closeConfirmSaleDialog"
      width="400px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div class="confirm-sale-dialog">
        <div style="margin: 15px 0; font-weight: 600">是否同意取消售后？</div>
        <div style="margin: 15px 0">同意后，售后单将无需处理</div>
      </div>
      <template #footer>
        <div class="dialog-footer flex-center">
          <el-button @click="rejectConfirmSale">拒绝</el-button>
          <el-button type="primary" @click="agreeCancelSale">同意</el-button>
        </div>
      </template>
    </el-dialog>
    <FeedbackLinkList ref="FeedbackLinkListRef" @success="handleQuery" />
  </div>
</template>

<script setup>
import FeedbackLinkList from '@/views/order/components/dialog/feedbackLinkList.vue'
import ViewerImageList from '@/components/ImagePreview/viewerImageList.vue'
import ProductMoreInfo from '@/views/order/components/dialog/productMoreInfo.vue'
import FeedbackLinkToMerchant from '@/views/order/components/dialog/feedbackLinkToMerchant.vue'
import SelectLoad from '@/components/Select/SelectLoad.vue'
import { ElMessage } from 'element-plus'
import { englishCharacter_d_reg, chinese_reg } from '@/utils/RegExp'
import {
  afterSaleStatusMap,
  priorityList,
  afterSaleTypeList,
  afterSaleStatusList,
} from '@/views/task/data.js'
import ConfirmDialog from '@/views/task/components/confirmDialog.vue'
import AfterSaleItem from '@/views/task/components/afterSaleItem.vue'
import { copy } from '@/utils/index'
const { proxy } = getCurrentInstance()
import {
  getAfterSaleList,
  getAfterSaleCount,
  confirmAfterSale,
  refuseAfterSale,
  cancelAfterSale,
  reopenAfterSale,
  cancelApplication,
  finishAfterSale,
  selectAssigneeList,
  selectModelList,
  selectSubmitList,
  selectRelevanceUserList,
  refuseCancelApplication,
  agreeCancelApplication,
  applicationForCancellation,
} from '@/api/task/afterSale'
import { getBeforeDate } from '@/utils/index'
import useUserStore from '@/store/modules/user'

const router = useRouter()
const route = useRoute()
const store = useUserStore()

const ProductMoreInfoRef = ref(null)
const confirmDialogRef = ref(null)
const FeedbackLinkToMerchantRef = ref(null)
const showMoreInfoDialog = ref(false)
const AfterSaleItemRef = ref(null)
const taskDetailId = ref('')

const curTab = ref('')
const queryRef = ref(null)
const pageNum = ref(1)
const pageSize = 20
const total = ref(0)
const showConfirmSaleDialog = ref(false)
const FeedbackLinkListRef = ref()
const queryParams = ref({
  keyword: '',
  afterSaleTimeBegin: '',
  afterSaleTimeEnd: '',
  afterSaleType: [],
  assigneeId: '',
  priority: [],
  shootModelId: [],
  status: [],
  submitById: '',
  relevanceUserId: '',
})
const statusDays = ref([])
const saleFormRef = ref(null)
const saleFormRules = {
  remark: [
    { required: true, message: '请输入提供给模特的售后内容(英文)', trigger: 'change' },
    { validator: checkVal, trigger: 'change' },
  ],
}
const saleForm = ref({
  id: '',
  issuePic: [],
  content: '',
  remark: '',
  operateType: 104,
})
const showSaleDialog = ref(false)
const tabList = ref([
  { label: '所有', value: '', number: 0 },
  { label: '待处理', value: afterSaleStatusMap['待处理'], number: 0 },
  { label: '处理中', value: afterSaleStatusMap['处理中'], number: 0 },
  { label: '申请取消中', value: afterSaleStatusMap['申请取消中'], number: 0 },
  { label: '已完结', value: afterSaleStatusMap['已完结'], number: 0 },
  { label: '已拒绝', value: afterSaleStatusMap['已拒绝'], number: 0 },
  { label: '已关闭', value: afterSaleStatusMap['已关闭'], number: 0 },
])
const loading = ref(false)
const tableData = ref([])

// tab切换
function handleTabChange(name) {
  curTab.value = name
  resetQuery()
}

function handleQuery() {
  loading.value = true
  let status = ''
  if (curTab.value == '') {
    status = queryParams.value.status
  } else {
    status = curTab.value
  }

  getAfterSaleList({ ...queryParams.value, status: status, pageNum: pageNum.value, pageSize: pageSize })
    .then(res => {
      tableData.value = res.data.rows
      total.value = res.data.total
    })
    .finally(() => {
      loading.value = false
    })
  getAfterSaleCount().then(res => {
    if (res.data) {
      tabList.value.forEach(item => {
        if (item.label == '所有') {
          item.number = res.data.totalCount
        } else if (item.label == '待处理') {
          item.number = res.data.unHandleCount
        } else if (item.label == '处理中') {
          item.number = res.data.handleIngCount
        } else if (item.label == '申请取消中') {
          item.number = res.data.applicationForCancellationCount
        } else if (item.label == '已完结') {
          item.number = res.data.handleCount
        } else if (item.label == '已拒绝') {
          item.number = res.data.rejectCount
        } else if (item.label == '已关闭') {
          item.number = res.data.closeCount
        }
      })
    }
  })
}

function pageChange(params) {
  pageNum.value = params.pageNum
  // pageSize = params.pageSize
  handleQuery()
}
function handlePageChange(num) {
  pageNum.value = num
  handleQuery()
}

function resetQuery() {
  router.replace({ query: {} })
  queryParams.value = {
    keyword: '',
    afterSaleTimeBegin: '',
    afterSaleTimeEnd: '',
    afterSaleType: [],
    assigneeId: '',
    priority: [],
    shootModelId: [],
    status: '',
    submitById: '',
    relevanceUserId: '',
  }
  statusDays.value = []
  taskDetailId.value = ''
  handleQuery()
}
function routerNewWindow(path) {
  const { href } = router.resolve({ path })
  window.open(href, '_blank')
}
const confirmDialogType = ref('')
const afterSaleData = ref({})
function handleAction(btn, row, data = {}) {
  switch (btn) {
    case '拒绝售后':
      confirmDialogType.value = 'reject'
      confirmDialogRef.value.open('reject', row.id, row.taskNum)
      break
    case '确认售后':
      afterSaleData.value = data
      openSaleDialog(row)
      break
    case '反馈素材给商家':
      FeedbackLinkToMerchantRef.value?.open(data.videoId, null, row.afterSaleClass)
      break
    case '完结工单':
      proxy.$modal
        .confirm(
          '<div style="margin: 25px 0;text-align: center;font-size: 16px;font-weight: 600">确认完结售后吗？</div>',
          '',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            center: true,
            dangerouslyUseHTMLString: true,
            closeOnClickModal: false,
            closeOnPressEscape: false,
          }
        )
        .then(() => {
          finishAfterSale({ taskDetailId: row.id }).then(res => {
            ElMessage.success('完结售后成功')
            handleQuery()
          })
        })
        .catch(() => {
          // console.log(7777)
        })
      break
    case '申请取消':
      confirmDialogType.value = 'applyCancel'
      confirmDialogRef.value.open('applyCancel', row.id)
      break
    case '撤销申请':
      proxy.$modal
        .confirm(
          '<div style="margin: 25px 0;text-align: center;font-size: 16px;font-weight: 600">确认撤销申请吗？</div>',
          '',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            center: true,
            dangerouslyUseHTMLString: true,
            closeOnClickModal: false,
            closeOnPressEscape: false,
          }
        )
        .then(() => {
          cancelApplication({ taskDetailId: row.id }).then(res => {
            ElMessage.success('撤销申请成功')
            handleQuery()
          })
        })
        .catch(() => {})
      break
    case '取消售后':
      confirmDialogType.value = 'confirmCancel'
      confirmDialogRef.value.open('confirmCancel', row.id)
      break
    case '重新打开':
      confirmDialogType.value = 'reset'
      confirmDialogRef.value.open('reset', row.id)
      break
    case '处理申请':
      showConfirmSaleDialog.value = true
      taskDetailId.value = row.id
      break
    case '更多':
      openMoreInfoDialog(row.content)
      break
    case '查看订单更多':
      ProductMoreInfoRef.value?.open(row.videoId)
      break
    case '模特反馈素材':
      FeedbackLinkListRef.value?.open(data.videoId, row.id)
      break
    default:
      break
  }
}

function hanleConfirmSubmit(data) {
  const params = {
    id: data.id,
    remark: data.reason,
    operateType: '',
    issuePic: [],
  }
  if (data.picUrl && data.picUrl.length > 0) {
    params.issuePic = data.picUrl.map(item => item.picUrl)
  }
  if (confirmDialogType.value == 'applyCancel') {
    params.operateType = 105
    applicationForCancellation(params).then(res => {
      ElMessage.success('申请取消成功')
      handleQuery()
      confirmDialogRef.value.close()
    })
  } else if (confirmDialogType.value == 'reject') {
    params.operateType = 101
    refuseAfterSale(params).then(res => {
      ElMessage.success('拒绝售后成功')
      handleQuery()
      confirmDialogRef.value.close()
    })
  } else if (confirmDialogType.value == 'confirmCancel') {
    params.operateType = 107
    cancelAfterSale(params).then(res => {
      ElMessage.success('取消售后成功')
      handleQuery()
      confirmDialogRef.value.close()
    })
  } else if (confirmDialogType.value == 'reset') {
    params.operateType = 109
    reopenAfterSale(params).then(res => {
      ElMessage.success('重新打开成功')
      handleQuery()
      confirmDialogRef.value.close()
    })
  }
}

//
const moreInfoContent = ref('')
function openMoreInfoDialog(data) {
  moreInfoContent.value = data
  showMoreInfoDialog.value = true
}

function closeMoreInfoDialog() {
  showMoreInfoDialog.value = false
}
function closeConfirmSaleDialog() {
  showConfirmSaleDialog.value = false
}
//拒绝售后申请
function rejectConfirmSale() {
  refuseCancelApplication({ taskDetailId: taskDetailId.value }).then(res => {
    ElMessage.success('拒绝取消售后成功')
    closeConfirmSaleDialog()
    handleQuery()
  })
}
//同意取消售后
function agreeCancelSale() {
  agreeCancelApplication({ taskDetailId: taskDetailId.value }).then(res => {
    ElMessage.success('同意取消售后成功')
    closeConfirmSaleDialog()
    handleQuery()
  })
}

//处理人
const assigneeList = ref([])
function getAssgeneeList() {
  selectAssigneeList({ type: 1 }).then(res => {
    assigneeList.value = res.data || []
  })
}
//拍摄模特
const shootModelList = ref([])
function getModelList() {
  selectModelList({ type: 1 }).then(res => {
    shootModelList.value = res.data || []
  })
}
//提交人
const submitList = ref([])
function getSubmitList() {
  selectSubmitList({ type: 1 }).then(res => {
    submitList.value = res.data || []
  })
}
//任务关联人
const relevanceUserList = ref([])
function getRelevanceUserList() {
  selectRelevanceUserList({ type: 1 }).then(res => {
    relevanceUserList.value = res.data || []
  })
}

//确认售后弹窗
function closeSaleDialog() {
  saleForm.value = {
    id: '',
    issuePic: [],
    content: '',
    remark: '',
    operateType: 104,
  }
  saleFormRef.value.resetFields()
  showSaleDialog.value = false
}
function handleSaleSubmit() {
  const copyText = `${afterSaleData.value.videoCode} ${afterSaleData.value.productChinese} ${
    afterSaleData.value.productEnglish
  }
${afterSaleData.value.productLink || ''}
售后内容：${saleForm.value.remark}`
  saleFormRef.value.validate(valid => {
    if (valid) {
      confirmAfterSale(saleForm.value).then(res => {
        ElMessage.success('确认售后成功')
        copy(copyText)
        handleQuery()
        closeSaleDialog()
      })
    }
  })
}
function openSaleDialog(row) {
  saleForm.value = {
    id: row.id,
    issuePic: row.issuePic,
    content: row.content,
    remark: '',
    operateType: 104,
  }
  showSaleDialog.value = true
}

function checkVal(rule, value, callback) {
  // !englishCharacter_d_reg.test(value)
  if (chinese_reg.test(value)) {
    callback(new Error('售后内容必须为英文'))
  } else {
    callback()
  }
}

function changeStatusDays(value) {
  if (value.length > 1) {
    statusDays.value.splice(0, statusDays.value.length - 1)
  }
  let timeList = []
  if (statusDays.value && statusDays.value.length > 0) {
    if (statusDays.value[0] == 1) {
      timeList = [getBeforeDate(4, 'one'), getBeforeDate(0, 'two')]
    } else if (statusDays.value[0] == 2) {
      timeList = [getBeforeDate(14, 'one'), getBeforeDate(4, 'two')]
    } else if (statusDays.value[0] == 3) {
      timeList = [null, getBeforeDate(15, 'two')]
    }
  }
  queryParams.value.afterSaleTimeBegin = timeList[0] || null
  queryParams.value.afterSaleTimeEnd = timeList[1] || null
}
// handleQuery()
function init() {
  // 搜索
  if (route.query.type == 1) {
    if (route.query.keyword) {
      queryParams.value.keyword = route.query.keyword
    }
    if (route.query.tab) {
      curTab.value = Number(route.query.tab) || ''
    }
    if (route.query.s) {
      let s = atob(route.query.s).split(',')
      if (s && s.length) {
        queryParams.value.status = s.map(item => parseInt(item)).filter(item => !isNaN(item))
      }
    }
    if (route.query.assignee) {
      queryParams.value.assigneeId = store.id
    }
    if (route.query.submitBy) {
      queryParams.value.submitById = store.id
    }
    if (route.query.relevance) {
      queryParams.value.relevanceUserId = store.id
    }
    if (route.query.afterType) {
      queryParams.value.afterSaleType = [route.query.afterType * 1]
    }
  }
  handleQuery()
  getAssgeneeList()
  getModelList()
  getSubmitList()
  getRelevanceUserList()
}

init()
</script>

<style scoped lang="scss">
.table-box {
  min-height: 200px;
}
.confirm-sale-dialog {
  text-align: center;
  font-size: 18px;
}
</style>
