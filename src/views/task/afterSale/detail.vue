<template>
  <div class="sale-after-detail">
    <Title style="margin: 12px 0">售后信息</Title>

    <div class="detail-box">
      <el-row>
        <el-col :span="8">
          <div class="label">售后编号：</div>
          <div class="content">{{ afterSaleData.taskNum }}</div>
        </el-col>
        <el-col :span="8">
          <div class="label">优先级：</div>
          <div class="content">
            {{ priorityList.find(item => item.value == afterSaleData.priority)?.label }}
          </div>
        </el-col>
        <el-col :span="8">
          <div class="label">状态：</div>
          <div class="content">
            {{ afterSaleStatusList.find(item => item.value == afterSaleData.status)?.label }}
            <span
              style="word-break: break-word; line-break: anywhere; white-space: pre-wrap"
              v-if="
                afterSaleData.records &&
                afterSaleData.records.length > 0 &&
                afterSaleData.records[afterSaleData.records.length - 1].remark
              "
            >
              ({{
                handleRemark(afterSaleData.records[afterSaleData.records.length - 1].operateType) + '理由：'
              }}{{ afterSaleData.records[afterSaleData.records.length - 1].remark }})
            </span>
          </div>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <div class="label">售后分类：</div>
          <div class="content">
            {{ afterSaleCategoryList.find(item => item.value == afterSaleData.afterSaleClass)?.label }}
          </div>
        </el-col>
        <el-col :span="8">
          <div class="label">售后类型：</div>
          <div class="content">
            <span v-if="afterSaleData.afterSaleClass == '1'">
              {{ afterSaleVideoTypeList.find(item => item.value == afterSaleData.afterSaleVideoType)?.label }}
            </span>
            <span v-if="afterSaleData.afterSaleClass == '2'">
              {{ afterSalePicTypeList.find(item => item.value == afterSaleData.afterSalePicType)?.label }}
            </span>
          </div>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <div class="label">问题描述：</div>
          <div class="content" style="white-space: pre-wrap; line-break: anywhere">
            {{ afterSaleData.content }}
            <div style="margin-top: 10px">
              <ViewerImageList :data="afterSaleData.issuePic" is-preview-all :show-delete-btn="false" />
            </div>
          </div>
        </el-col>
      </el-row>
      <el-row>
        <el-col
          :span="24"
          v-if="
            afterSaleData.afterSaleVideoType == '1' ||
            afterSaleData.afterSaleVideoType == '2' ||
            afterSaleData.afterSalePicType == '1' ||
            afterSaleData.afterSalePicType == '3'
          "
        >
          <div class="label">补充剪辑要求：</div>
          <div class="content" style="white-space: pre-wrap; line-break: anywhere">
            {{ afterSaleData.clipRecord }}
          </div>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <div class="label">提交人：</div>
          <div class="content">{{ afterSaleData?.submit?.name || '' }}</div>
        </el-col>
        <el-col :span="12">
          <div class="label">处理人：</div>
          <div class="content">{{ afterSaleData?.assignee?.name || '' }}</div>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <div class="label">提交时间：</div>
          <div class="content">{{ afterSaleData.submitTime }}</div>
        </el-col>
        <el-col :span="12" v-if="afterSaleData.endTime">
          <div class="label">关闭/完结时间：</div>
          <div class="content">{{ afterSaleData.endTime }}</div>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24" v-if="afterSaleData.contentEnglish">
          <div class="label">售后内容：</div>
          <div class="content" style="line-break: anywhere; white-space: pre-wrap">
            {{ afterSaleData.contentEnglish }}
            <CopyButton
              link
              style="background: #fff; color: #409eff; font-size: 16px"
              :copy-content="copyValue"
            >
              <template #default>复制</template>
            </CopyButton>
          </div>
        </el-col>
      </el-row>
    </div>

    <Title style="margin: 12px 0">状态流转记录</Title>
    <div class="detail-box">
      <template v-for="(item, index) in afterSaleData.records" :key="index">
        <div>
          {{ index + 1 + '. ' + item.time }}，由{{
            item.operateByType == 1 ? '处理人' : item.operateByType == 2 ? '剪辑人' : '系统'
          }}
          {{ item.operate?.name || '' }}
          {{
            taskOperateTypeList.find(data => data.value == item.operateType)?.label == '关闭工单'
              ? '关闭售后'
              : taskOperateTypeList.find(data => data.value == item.operateType)?.label
          }}。
          <span
            v-if="item.remark"
            style="word-break: break-word; line-break: anywhere; white-space: pre-wrap"
          >
            {{ handleRemark(item.operateType) + '理由：' }}{{ item.remark }}
          </span>
          <el-button
            v-if="item.issuePic && item.issuePic.length > 0"
            link
            v-btn
            type="primary"
            @click="showViewer(item.issuePic)"
            style="font-size: 16px"
          >
            查看图片
          </el-button>
        </div>
      </template>
    </div>
    <div class="detail-btn">
      <el-button plain v-btn @click="handleBack">返回</el-button>
    </div>
  </div>
</template>

<script setup>
import ViewerImageList from '@/components/ImagePreview/viewerImageList.vue'
import Title from '@/components/Public/title.vue'
import { useRouter } from 'vue-router'
import { getAfterSaleDetail } from '@/api/task/afterSale'
import CopyButton from '@/components/Button/CopyButton.vue'
import { useViewer } from '@/hooks/useViewer'
const { showViewer } = useViewer()
import {
  afterSaleStatusMap,
  priorityList,
  afterSaleTypeList,
  afterSaleCategoryList,
  afterSaleStatusList,
  afterSaleVideoTypeList,
  afterSalePicTypeList,
  taskOperateTypeList,
} from '@/views/task/data.js'
const router = useRouter()
const route = useRoute()

const afterSaleId = ref('')
const loading = ref(false)
const afterSaleData = ref({})
const copyValue = ref('')

function handleBack() {
  router.replace('/task/workOrder')
}

function getDetail() {
  if (!route.params.id) {
    window.location.href = '/index'
    return
  }
  loading.value = true
  afterSaleId.value = route.params.id
  getAfterSaleDetail(afterSaleId.value)
    .then(res => {
      afterSaleData.value = res.data
      if (res.data) {
        copyValue.value = `视频编码: ${res.data.videoCode || '-'}
产品中文名: ${res.data.productChinese || '-'}
产品英文名: ${res.data.productEnglish || '-'}
产品链接: ${res.data.productLink || '-'}
售后内容: ${res.data.contentEnglish || '-'}`
      }
    })
    .finally(() => {
      loading.value = false
    })
}

//处理扭转记录理由
function handleRemark(operateType) {
  let text = taskOperateTypeList.find(data => data.value == operateType)?.label.substr(0, 2)
  if (text && text == '重新') {
    text = '打开'
  }
  if (text && text == '订单') {
    text = '回退'
  }
  return text
}

getDetail()
</script>

<style lang="scss" scoped>
.sale-after-detail {
  padding: 20px 20px 100px 20px;
  min-height: calc(100vh - 84px);
  overflow-y: auto;
  position: relative;
  .detail-box {
    word-break: break-all;
    background-color: #fff;
    padding: 13px;
    border-radius: 4px;
    gap: 10px;
    position: relative;
    box-shadow: var(--el-box-shadow-light);
  }
  .el-col-8,
  .el-col-12,
  .el-col-24 {
    display: flex;
    align-items: baseline;
    font-size: 15px;
    margin-bottom: 15px;
    // align-items: center;

    .label {
      color: #7f7f7f;
      flex-shrink: 0;
      width: 130px;
      text-align: right;
    }
    .content {
      flex-shrink: 0;
      width: calc(100% - 130px);
      word-break: break-all;
    }
  }
  .detail-btn {
    position: absolute;
    bottom: 30px;
    right: 60px;
  }
}
</style>
