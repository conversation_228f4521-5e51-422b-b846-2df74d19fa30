<template>
  <div class="distribution-page">
    <div class="page-btn flex-between">
      <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="68px" @submit.prevent>
        <el-form-item label="搜索">
          <el-input
            v-model="queryParams.searchNameMemberCodeAccount"
            clearable
            style="width: 260px"
            placeholder="请输入"
          ></el-input>
        </el-form-item>
        <el-form-item label="状态" prop="noticeStatus">
          <el-select v-model="queryParams.noticeStatus" placeholder="请选择" clearable style="width: 180px">
            <el-option
              v-for="item in isNoticeList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button v-btn type="primary" icon="Search" native-type="submit" @click="handleQuery">
            搜索
          </el-button>
          <el-button v-btn icon="Refresh" plain @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="page-table">
      <el-table v-loading="loading" :data="tableList">
        <el-table-column
          label="提现单号"
          prop="withdrawNumber"
          width="100px"
          align="center"
        ></el-table-column>
        <el-table-column label="商家信息" prop="businessAccountDetailVO" minWidth="200px" align="center">
          <template v-slot="{ row }">
            <div>
              <div>
                {{
                  row.businessAccountDetailVO?.name
                    ? row.businessAccountDetailVO?.name
                    : row.businessAccountDetailVO?.nickName
                }}
                &nbsp;
                <el-tag type="warning" round size="small" v-if="row.businessAccountDetailVO?.isProxy == 1">
                  代理
                </el-tag>
              </div>
              <div v-if="row.businessAccountDetailVO?.businessName">
                公司名称：{{ row.businessAccountDetailVO?.businessName }}
              </div>
              <div v-if="row.businessAccountDetailVO?.businessCreateTime">
                会员开通时间：{{ row.businessAccountDetailVO?.businessCreateTime }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="会员编码"
          show-overflow-tooltip
          prop="memberCode"
          align="center"
          width="180px"
        >
          <template v-slot="{ row }">
            <div>
              {{ row.businessAccountDetailVO?.memberCode }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="提现金额" prop="amount" align="center"></el-table-column>
        <el-table-column label="提现方式" prop="withdrawWay" align="center">
          <template v-slot="{ row }">
            <div>
              {{ withdrawTypeList.find(item => item.value === row.withdrawWay)?.label || '-' }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="客服" prop="businessServiceName" align="center"></el-table-column>
        <el-table-column label="发起人" prop="createUserName" align="center"></el-table-column>
        <el-table-column label="审核人" prop="auditUserName" align="center"></el-table-column>
        <el-table-column label="状态" prop="notifyStatus" align="center">
          <template v-slot="{ row }">
            {{ isNoticeList.find(item => item.value === row.notifyStatus)?.label || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="支付时间" prop="payTime" align="center" width="160px"></el-table-column>
        <el-table-column
          label="标记通知时间"
          prop="notifyTime"
          width="160px"
          align="center"
        ></el-table-column>
        <el-table-column label="操作" fixed="right" width="160px" align="center">
          <template v-slot="{ row }">
            <el-button
              v-if="row.notifyStatus == 0"
              v-btn
              link
              type="primary"
              v-hasPermi="['task:inform:notify']"
              @click="handleInform(row.id)"
            >
              标记通知
            </el-button>
            <el-button
              v-btn
              link
              type="primary"
              v-hasPermi="['task:inform:view']"
              @click="showViewer([row.resourceUrl], { raw: true })"
            >
              查看凭证
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="flex-end" style="margin: 10px 0 60px">
      <PaginationFloatBar :current-page="pageNum" :page-size="pageSize" @update:current-page="handlePageChange" :total="total" />
      <!-- <el-pagination
        background
        @size-change="pageChange({ pageNum: 1, pageSize: $event })"
        @current-change="pageChange({ pageNum: $event, pageSize })"
        :current-page="pageNum"
        :page-size="pageSize"
        :page-sizes="pageSizes"
        layout="total, prev, pager, next, jumper"
        :total="total"
      /> -->
    </div>
  </div>
</template>

<script setup>
import { businessBalanceAuditFlow, businessBalanceAuditFlowMarkNotice } from '@/api/finance/balence.js'
import { isNoticeList, withdrawTypeList } from '@/views/task/data.js'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useViewer } from '@/hooks/useViewer'
const { showViewer } = useViewer()

const loading = ref(false)
const pageNum = ref(1)
const pageSize = ref(10)
const pageSizes = [10, 20, 30, 50]
const total = ref(0)
const queryParams = ref({
  searchNameMemberCodeAccount: '',
  noticeStatus: '',
  filterNotice: 1,
})
const tableList = ref([])

function handleInform(id) {
  //   ElMessageBox.confirm('', {
  //     message: h('div', null, [h('div', { style: 'text-align: center;' }, `是否已确认通知客户转账完成？`)]),
  //   })
  ElMessageBox.confirm('是否已确认通知客户转账完成？')
    .then(() => {
      businessBalanceAuditFlowMarkNotice(id).then(() => {
        ElMessage.success('标记通知成功')
        handleQuery()
      })
    })
    .catch(() => {})
}

// 分页跳转
function pageChange(page) {
  pageNum.value = page.pageNum
  pageSize.value = page.pageSize
  handleQuery()
}
function handlePageChange(num) {
  pageNum.value = num
  handleQuery()
}
function handleQuery() {
  loading.value = true
  businessBalanceAuditFlow({ ...queryParams.value, pageNum: pageNum.value, pageSize: pageSize.value })
    .then(res => {
      tableList.value = res.data.rows || []
      total.value = res.data.total || 0
    })
    .finally(() => {
      loading.value = false
    })
}

function resetQuery() {
  queryParams.value = { noticeStatus: '', searchNameMemberCodeAccount: '', filterNotice: 1 }
  pageNum.value = 1
  pageSize.value = 10
  handleQuery()
}

handleQuery()
</script>

<style scoped lang="scss">
.distribution-page {
  padding: 20px 20px 0 20px;
}
.page-btn {
  .input-box {
    margin-right: 15px;
    position: relative;

    :deep(.el-input) {
      .el-input__wrapper {
        padding-right: 32px;
      }
    }

    .el-icon {
      position: absolute;
      top: 0;
      right: 0;
      cursor: pointer;
      width: 32px;
      height: 32px;
    }
  }
}
.page-table {
  margin-top: 15px;
}
</style>
