// taskType 工单类型（1:要原视频,2:修改视频,3:重新上传,4:重拍视频,5:没拍照片,6:没拍视频,7:补拍视频,8:要剪辑）
export const taskTypeList = [
  { label: '要原视频', value: 1 },
  { label: '修改视频', value: 2 },
  { label: '重新上传', value: 3 },
  { label: '重拍视频', value: 4 },
  { label: '没拍照片', value: 5 },
  { label: '没拍视频', value: 6 },
  { label: '补拍视频', value: 7 },
  { label: '要剪辑', value: 8 },
]

export const afterSaleTypeList = [
  { label: '重拍视频', value: 1 },
  { label: '补拍视频', value: 2 },
  // { label: '没拍视频', value: 3 },
  { label: '要高清视频', value: 3 },
  { label: '视频原素材', value: 4 },
  { label: '重新上传', value: 5 },
  { label: '重新剪辑', value: 6 },
  { label: '重拍照片', value: 11 },
  // { label: '没拍照片', value: 9 },
  { label: '要高清照片', value: 12 },
  { label: '补拍照片', value: 13 },
  { label: '照片原素材', value: 14 },
]

export const afterSaleVideoTypeList = [
  { label: '重拍视频', value: 1 },
  { label: '补拍视频', value: 2 },
  { label: '要高清视频', value: 3 },
  { label: '原素材', value: 4 },
  { label: '重新上传', value: 5 },
  { label: '重新剪辑', value: 6 },
]
export const afterSalePicTypeList = [
  { label: '重拍照片', value: 1 },
  { label: '要高清照片', value: 2 },
  { label: '补拍照片', value: 3 },
  { label: '原素材', value: 4 },
]

export const afterSaleVideoTypeMap = {}
afterSaleVideoTypeList.forEach(item => {
  afterSaleVideoTypeMap[item.label] = item.value
  afterSaleVideoTypeMap[item.value] = item.label
})
export const afterSalePicTypeMap = {}
afterSalePicTypeList.forEach(item => {
  afterSalePicTypeMap[item.label] = item.value
  afterSalePicTypeMap[item.value] = item.label
})


export function getTaskTypeLabel(value) {
  const item = taskTypeList.find(item => item.value == value)
  return item ? item.label : '-'
}

export const workOperateTypeList = [
  { label: '创建工单', value: 201 },
  { label: '关闭工单', value: 202 },
  { label: '转处理人处理', value: 203 },
  { label: '重新打开', value: 204 },
  { label: '补发', value: 205 },
  { label: '补偿', value: 206 },
  { label: '转提交人处理', value: 207 },
  { label: '拒绝工单', value: 208 },
  { label: '拒绝降要求', value: 209 },
  { label: '同意降要求', value: 210 },
  { label: '完结工单', value: 211 },
  { label: '反馈素材给商家', value: 212 },
]

//售后分类
export const afterSaleCategoryList = [
  { label: '视频', value: 1 },
  { label: '照片', value: 2 },
]

// 优先级（1:紧急,2:一般）
export const priorityList = [
  { label: '紧急', value: 1, type: 'danger' },
  { label: '一般', value: 2, type: 'primary' },
]
export function getPriorityType(value) {
  const item = priorityList.find(item => item.value == value)
  return item ? item.type : 'primary'
}
export function getPriorityLabel(value) {
  const item = priorityList.find(item => item.value == value)
  return item ? item.label : '-'
}

// 工单状态（1:待处理,2:处理中,3:已处理,4:已拒绝,5:已关闭）
export const statusList = [
  { label: '待处理', value: 1, type: 'danger' },
  { label: '处理中', value: 2, type: 'success' },
  { label: '已处理', value: 3, type: 'info' },
  { label: '已拒绝', value: 4, type: 'info' },
  { label: '已关闭', value: 5, type: 'info' },
]
//售后单状态
export const afterSaleStatusList = [
  { label: '待处理', value: 1 },
  { label: '处理中', value: 2 },
  { label: '申请取消中', value: 3 },
  { label: '已完结', value: 4 },
  { label: '已拒绝', value: 5 },
  { label: '已关闭', value: 6 },
]

export const afterSaleStatusMap = {}
afterSaleStatusList.forEach(item => {
  afterSaleStatusMap[item.label] = item.value
  afterSaleStatusMap[item.value] = item.label
})

// 工单类型
export const workOrderTypeList = [
  { label: '模特没收到货', value: 1 },
  { label: '催素材', value: 2 },
  { label: '下架视频', value: 3 },
  { label: '需剪辑', value: 4 },
  { label: '上传异常', value: 6 },
  { label: '素材链接问题', value: 7 },
  { label: '其他', value: 5 },
]
export const workOrderTypeMap = {}
workOrderTypeList.forEach(item => {
  workOrderTypeMap[item.label] = item.value
  workOrderTypeMap[item.value] = item.label
})

// 工单状态
export const workOrderStatusList = [
  { label: '待处理', value: 1 },
  // { label: '处理中', value: 2 },
  // { label: '申请取消中', value: 3 },
  { label: '已完结', value: 4 },
  { label: '已拒绝', value: 5 },
  { label: '已关闭', value: 6 },
]
export const workOrderStatusMap = {}
workOrderStatusList.forEach(item => {
  workOrderStatusMap[item.label] = item.value
  workOrderStatusMap[item.value] = item.label
})

// 任务单操作类型
export const taskOperateTypeList = [
  // 售后
  { label: '创建售后', value: 101 },
  { label: '拒绝售后', value: 102 },
  { label: '确认售后', value: 103 },
  { label: '反馈素材给商家', value: 104 },
  { label: '申请取消', value: 105 },
  { label: '撤销申请', value: 106 },
  { label: '取消售后', value: 107 },
  { label: '拒绝取消售后', value: 108 },
  { label: '重新打开', value: 109 },
  { label: '取消工单', value: 110 },
  { label: '完成售后单', value: 111 },
  { label: '同意取消', value: 112 },
  // 工单
  { label: '创建工单', value: 201 },
  { label: '关闭工单', value: 202, handleRemark: val => `关闭理由：${val}` },
  { label: '转处理人处理', value: 203 },
  { label: '重新打开', showLabel: '重新打开工单', value: 204, handleRemark: val => `打开理由：${val}` },
  { label: '补发', value: 205, handleRemark: val => `补发理由：${val}` },
  { label: '补偿', showLabel: '发起补偿', value: 206, handleRemark: val => `补偿理由：${val}` },
  { label: '转提交人处理', value: 207 },
  { label: '拒绝工单', value: 208, handleRemark: val => `拒绝理由：${val}` },
  { label: '拒绝降要求', value: 209, handleRemark: val => `拒绝理由：${val}` },
  { label: '同意降要求', value: 210, handleRemark: val => `同意理由：${val}` },
  { label: '关闭工单', value: 601, handleRemark: val => `关闭理由：${val}` },
  {
    label: '完结工单',
    value: 211,
    handleRemark: val => {
      switch (val) {
        case 2:
          return '（已补发）'
        case 3:
          return '（已补偿）'
        case 4:
          return '（已反馈素材给商家）'
        case 5:
          return '（模特已反馈素材）'
        default:
          return ''
      }
    },
  },
  { label: '反馈素材给商家', value: 212 },
]
// 处理工单操作类型
export function handleTaskOperatType(value, remark, name, showLabel = true) {
  const item = taskOperateTypeList.find(item => item.value == value)
  if (item) {
    if (item.value === 203 || item.value === 207) {
      return `指派${name}处理` + (remark ? ` 转移理由：${remark}` : '')
    }
    if (remark && item.handleRemark) {
      if (showLabel) {
        return (item.showLabel || item.label) + ' ' + item.handleRemark(remark)
      } else {
        return item.handleRemark(remark)
      }
    }
    return item.showLabel || item.label
  }
  return ''
}

//是否通知
export const isNoticeList = [
  { label: '已通知', value: 1 },
  { label: '未通知', value: 0 },
]
//提现方式 3-银行卡，4-境外汇款，5-其他
export const withdrawTypeList = [
  { label: '微信', value: 1 },
  { label: '支付宝', value: 2 },
  { label: '银行卡', value: 3 },
  { label: '境外汇款', value: 4 },
  { label: '对公转账', value: 6 },
  { label: '其他', value: 5 },
]

export function getStatusLabel(value) {
  const item = statusList.find(item => item.value == value)
  return item ? item.label : '-'
}
export function getStatusType(value) {
  const item = statusList.find(item => item.value == value)
  return item ? item.type : 'primary'
}
