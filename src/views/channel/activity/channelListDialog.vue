<template>
  <el-dialog
    v-model="dialogVisible"
    title="选择渠道"
    width="800"
    align-center
    :close-on-click-modal="false"
    destroy-on-close
  >
    <div>
      <el-form ref="formRef" style="max-width: 550px" :model="form" inline label-width="auto" @submit.prevent>
        <el-form-item label="搜索" prop="name">
          <el-input v-model="form.name" placeholder="请输入渠道名称" clearable style="width: 220px" />
        </el-form-item>
        <el-form-item label="">
          <el-button v-btn type="primary" icon="Search" native-type="submit" @click="onQuery">搜索</el-button>
          <el-button v-btn icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <div style="font-size: 13px;color: #999;margin-bottom: 10px;">说明：如果渠道同时参加多个活动时，按最低会员折扣计算</div>
      <ElTablePage
        ref="tableRef"
        :columns="columns"
        :data="tableData"
        :loading="tableLoading"
        :currentPage="currentPage"
        :total="total"
        :pageSize="pageSize"
        :tableOptions="{
          border: true,
        }"
        row-key="id"
        @page-change="handlePageChange"
        @selection-change="handleSelectionChange"
      ></ElTablePage>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" :disabled="!multipleSelection.length" @click="confirm">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import ElTablePage from '@/components/Table/ElTablePage.vue'
import { channelInfoList } from '@/api/channel/activity'

const emits = defineEmits(['select'])

const dialogVisible = ref(false)
const form = ref({
  name: '',
})

const columns = [
  { type: 'selection', width: '55', 'reserve-selection': true },
  { prop: 'id', label: '账号ID', width: '110' },
  { prop: 'channelName', label: '渠道名称', width: '200' },
  { prop: 'brokeRage', label: '结算比例', width: '110', handle: data => `${data}%` },
  {
    prop: 'activityList',
    label: '已参与活动',
    minWidth: '200',
    ellipsis: true,
    tooltip: {
      rawContent: true
    },
    handle: data => {
      if (data?.length) {
        return data.map(item => item.activityName + ` (${item.startTime} ~ ${item.endTime})`).join('<br/>')
      }
      return ''
    },
  },
]
const queryParams = ref({})
const tableRef = ref(null)
const tableData = ref([])
const tableLoading = ref(false)
const total = ref(0)
const currentPage = ref(1)
const pageSize = 10
const multipleSelection = ref([])
const selectList = ref([])

function selectable(row, index) {
  return true
}

function handleSelectionChange(val) {
  // console.log('handleSelectionChange', val)
  multipleSelection.value = val
}

function handlePageChange(page) {
  currentPage.value = page.currentPage
  getList()
}

function onQuery() {
  currentPage.value = 1
  getList()
}

function resetQuery() {
  form.value.name = ''
  onQuery()
}

function getList() {
  tableLoading.value = true
  channelInfoList({
    ...queryParams.value,
    channelName: form.value.name,
    pageNum: currentPage.value,
    pageSize,
  })
    .then(res => {
      tableData.value = res.data.rows
      total.value = res.data.total
      if (selectList.value.length) {
        tableRef.value?.toggleRowSelection(selectList.value.filter(Boolean))
        selectList.value.length = 0
        getList()
      }
    })
    .finally(() => {
      tableLoading.value = false
    })
}

function confirm() {
  emits(
    'select',
    multipleSelection.value.map(item => unref(item))
  )
  dialogVisible.value = false
}

defineExpose({
  open: (arr, params) => {
    selectList.value = arr || []
    queryParams.value = params || {}
    resetQuery()
    dialogVisible.value = true
  },
  close: () => {
    dialogVisible.value = false
  },
})
</script>

<style scoped lang="scss"></style>
