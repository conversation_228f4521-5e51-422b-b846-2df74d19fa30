<template>
  <div class="activity-data">
    <Title v-if="type === 'add'">新增活动</Title>
    <Title v-if="type === 'edit'">编辑活动</Title>
    <Title v-if="type === 'detail'">活动详情</Title>

    <div class="form-box" v-loading="loading">
      <el-form
        ref="formRef"
        style="max-width: 550px"
        :model="form"
        :rules="rules"
        label-width="auto"
        :disabled="disabled"
      >
        <el-form-item label="活动名称" prop="activityName">
          <el-input
            v-model="form.activityName"
            placeholder="请输入活动名称"
            maxlength="20"
            @input="e => (form.activityName = e.trim())"
          />
        </el-form-item>
        <el-form-item label="活动时间" prop="times">
          <el-date-picker
            v-model="form.times"
            format="YYYY/M/D HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="datetimerange"
            range-separator="-"
            unlink-panels
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
            :shortcuts="shortcuts"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="会员折扣" prop="discount">
          <el-input
            v-model.number="form.discount"
            style="width: 200px"
            placeholder="请输入会员折扣"
            min="1"
            max="99"
          >
            <template #append>%</template>
          </el-input>
          <!-- <el-input-number v-model="form.discount" :min="1" :max="99" style="width: 200px">
            <template #suffix>
              <span>%</span>
            </template>
          </el-input-number> -->
        </el-form-item>
        <el-form-item label="活动状态" prop="status" v-if="type != 'add'">
          <div v-if="type === 'detail'">{{ handleStatus(form.status) }}</div>
          <el-radio-group v-model="form.status" :disabled="disabled || statusEdit" v-else>
            <el-radio label="进行中" :value="3" />
            <el-radio label="暂停" :value="1" />
          </el-radio-group>
        </el-form-item>
        <el-form-item label="参与渠道" prop="channelType">
          <el-radio-group v-model="form.channelType">
            <el-radio-button
              v-for="item in channelTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-radio-group>
        </el-form-item>
      </el-form>
      <el-form-item label="" ref="selectChannelRef">
        <div class="flex-start gap-10" v-if="form.channelType > 0">
          <el-button type="primary" @click="openDialog" v-if="type != 'detail'">
            选择渠道
          </el-button>
          <span>已选渠道：{{ tableData.length }}</span>
        </div>
      </el-form-item>
      <ElTablePage
        v-show="form.channelType > 0"
        ref="tableRef"
        :columns="columns"
        :data="tableData"
        :loading="tableLoading"
        :isTablePage="false"
        :tableOptions="{
          border: true,
        }"
        :tableAction="
          type != 'detail'
            ? {
                width: '100',
                fixed: 'right',
              }
            : null
        "
        row-key="id"
      >
        <template #tableAction="{ row }">
          <div class="column-flex-button">
            <el-button
              v-if="type != 'detail'"
              v-btn
              link
              size="small"
              type="primary"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </div>
        </template>
      </ElTablePage>

      <div class="flex-start btn-box" v-if="type != 'detail'">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit">提交</el-button>
      </div>
      <div class="flex-start btn-box" v-else>
        <el-button @click="handleCancel">返回</el-button>
      </div>
    </div>

    <ChannelListDialog ref="ChannelListDialogRef" @select="handleSelect" />
  </div>
</template>

<script setup>
import Title from '@/components/Public/title.vue'
import ElTablePage from '@/components/Table/ElTablePage.vue'
import ChannelListDialog from '@/views/channel/activity/channelListDialog.vue'
import { activityStatusOptions, channelTypeOptions } from '@/views/channel/activity/index.js'
import { addChannelActivity, editChannelActivity, channelActivityDetail } from '@/api/channel/activity'
import { ElMessage } from 'element-plus'

const { proxy } = getCurrentInstance()

const route = useRoute()
const router = useRouter()

const type = ref('add')

const loading = ref(false)

const formRef = ref(null)
const selectChannelRef = ref(null)
const form = ref({
  activityName: '',
  times: [],
  status: 1,
  discount: 88,
  channelType: 0,
})

const shortcuts = [
  {
    text: '今天',
    value: () => {
      const start = new Date()
      start.setHours(0, 0, 0, 0)
      const end = new Date()
      end.setHours(23, 59, 59, 59)
      return [start, end]
    },
  },
]

const disabled = ref(false)
const rules = {
  activityName: [{ required: true, message: '请输入活动名称', trigger: 'change' }],
  times: [{ required: true, message: '请选择活动时间', trigger: 'change' }],
  discount: [
    { required: true, message: '请输入会员折扣', trigger: 'change' },
    { type: 'number', message: '请输入数字', trigger: 'change' },
    { validator: (rule, value) => value >= 1 && value <= 99, message: '请输入1-99的数字', trigger: 'change' },
  ],
  status: [{ validator: checkStatus, trigger: 'blur' }],
  channelType: [{ required: true, message: '请选择参与渠道', trigger: 'change' }],
}

const statusEdit = computed(() => {
  if (type.value === 'edit' && form.value.times?.length === 2) {
    let st = new Date(form.value.times[0]).getTime()
    let et = new Date(form.value.times[1]).getTime()
    let now = new Date().getTime()
    if (st > now) {
      form.value.status = 2
    } else if (et <= now) {
      form.value.status = 0
    } else {
      if (form.value.status != 1 && form.value.status != 3) form.value.status = 3
      return false
    }
  } else {
    form.value.status = void 0
  }
  return true
})

function checkStatus(rule, value, callback) {
  if (!statusEdit.value) {
    if (value != 1 && value != 3) {
      return callback(new Error('请选择活动状态'))
    }
  }
  return callback()
}

const columns = [
  { prop: 'id', label: '账号ID', width: '120' },
  { prop: 'channelName', label: '渠道名称', width: '200' },
  { prop: 'brokeRage', label: '结算比例', width: '120', handle: data => `${data}%` },
  { prop: 'status', label: '分销状态', width: '120', handle: data => (data == 0 ? '开启' : '关闭') },
  {
    prop: 'activityList',
    label: '已参与有时间交叉的活动',
    minWidth: '200',
    ellipsis: true,
    tooltip: {
      rawContent: true,
    },
    handle: data => {
      if (data?.length) {
        return data.map(item => item.activityName + ` (${item.startTime} ~ ${item.endTime})`).join('<br/>')
      }
      return '-'
    },
  },
]
const tableData = ref([])
const tableLoading = ref(false)
const ChannelListDialogRef = ref(null)

function init() {
  if (route.name === 'ChannelActivityEdit') {
    type.value = 'edit'
  } else if (route.name === 'ChannelActivityDetail') {
    type.value = 'detail'
    disabled.value = true
  } else {
    type.value = 'add'
  }
  if (route.params.id) {
    getActivityDetail()
  }
}

function getActivityDetail() {
  loading.value = true
  channelActivityDetail(route.params.id)
    .then(res => {
      form.value = {
        activityName: res.data.activityName,
        times: [res.data.startTime, res.data.endTime],
        status: res.data.status,
        discount: res.data.discount,
        channelType: res.data.type,
      }
      tableData.value = res.data.channelActivityInfoList || []
      setTimeout(() => {
        formRef.value.clearValidate(['activityName', 'times', 'discount', 'status', 'channelType'])
      })
    })
    .finally(() => (loading.value = false))
}

function handleStatus(val) {
  let s = activityStatusOptions.find(item => item.value === val)
  return s ? s.label : ''
}

function openDialog() {
  if (!form.value.times?.length) {
    ElMessage.error('请先选择活动时间')
    return
  }
  let params = {}
  if (type.value === 'edit') {
    params.id = route.params.id
  }
  if (form.value.times?.length === 2) {
    params.startTime = form.value.times[0]
    params.endTime = form.value.times[1]
  }
  ChannelListDialogRef.value?.open(
    tableData.value.map(item => unref(item)),
    params
  )
}

function handleDelete(row) {
  tableData.value = tableData.value.filter(item => item.id !== row.id)
}

function handleCancel() {
  router.replace('/channel/activity/list')
}

function handleSelect(data) {
  tableData.value = data
  selectChannelRef.value.validateState = 'success'
}

function handleSubmit() {
  formRef.value.validate(valid => {
    if (valid) {
      if (form.value.channelType > 0 && !tableData.value.length) {
        selectChannelRef.value.validateMessage = '请选择渠道'
        selectChannelRef.value.validateState = 'error'
        return
      }
      proxy.$modal.confirm('确认提交保存？', '提示').then(() => {
        let params = {
          activityName: form.value.activityName,
          startTime: form.value.times[0],
          endTime: form.value.times[1],
          discount: form.value.discount,
          status: form.value.status,
          type: form.value.channelType,
          channelIds: [],
        }
        if (form.value.channelType > 0) {
          params.channelIds = tableData.value.map(item => item.id)
        }
        proxy.$modal.loading('正在提交中')
        if (type.value === 'add') {
          params.status = 3
          addChannelActivity(params)
            .then(() => {
              proxy.$modal.msgSuccess('添加成功')
              router.replace('/channel/activity/list')
            })
            .finally(() => {
              proxy.$modal.closeLoading()
            })
        } else if (type.value === 'edit') {
          params.id = route.params.id
          editChannelActivity(params)
            .then(() => {
              proxy.$modal.msgSuccess('保存成功')
              router.replace('/channel/activity/list')
            })
            .finally(() => {
              proxy.$modal.closeLoading()
            })
        }
      })
    }
  })
}

init()
</script>

<style scoped lang="scss">
@import '@/assets/styles/form-disabled.scss';

.activity-data {
  padding: 20px 0 0 20px;

  .form-box {
    margin: 60px 0 0 100px;
    width: 900px;
  }

  .btn-box {
    margin: 20px 0 0 90px;

    .el-button {
      padding: 16px 30px;
    }
  }
}
</style>
