<template>
  <div class="activity-page">
    <ElTablePage
      ref="tableRef"
      :columns="columns"
      :data="tableData"
      :loading="tableLoading"
      :currentPage="currentPage"
      :total="total"
      :tableAction="{
        width: '230',
        fixed: 'right',
      }"
      @page-change="pageChange"
      row-key="id"
    >
      <template #tableHeader>
        <div class="flex-between" style="margin: 20px 0;">
          <el-button
            v-btn
            v-hasPermi="['channel:activity:add']"
            icon="Plus"
            type="primary"
            @click="handleAction('add')"
          >
            新增活动
          </el-button>
          <el-form class="form-box" :inline="true" :model="queryParams" @submit.prevent>
            <el-form-item label="搜索" prop="keyword">
              <el-input
                v-model="queryParams.keyword"
                placeholder="请输入活动名称"
                clearable
                style="width: 220px"
              ></el-input>
            </el-form-item>
            <el-form-item label="活动状态" prop="status">
              <el-select
                v-model="queryParams.status"
                placeholder="请选择"
                clearable
                style="width: 170px"
              >
                <el-option
                  v-for="dict in activityStatusOptions"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item style="margin-right: 0;">
              <el-button v-btn type="primary" icon="Search" native-type="submit" @click="onQuery">
                搜索
              </el-button>
              <el-button v-btn icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </template>
      <template #tableAction="{ row }">
        <div>
          <el-button v-btn plain type="primary" size="small" v-hasPermi="['channel:activity:view']" @click="handleAction('view', row)">
            查看
          </el-button>
          <el-button
            v-btn
            v-hasPermi="['channel:activity:edit']"
            plain
            size="small"
            type="primary"
            @click="handleAction('edit', row)"
          >
            编辑
          </el-button>
          <DownloadBtn
            v-if="(row.status == 3 || row.status == 2) && checkPermi(['channel:activity:download'])"
            type="primary"
            plain
            size="small"
            :url="`/biz/marketing/center/distribution/activity/${row.id}/download`"
            is-asnyc
            text="下载"
            loadingText="下载中"
            message="确认下载"
            :config="{
              timeout: 30000, // 请求超时时间 30s
            }"
          />
        </div>
      </template>
    </ElTablePage>
  </div>
</template>

<script setup>
import ElTablePage from '@/components/Table/ElTablePage.vue'
import DownloadBtn from '@/components/Button/DownloadBtn'
import { activityStatusOptions, channelTypeOptions } from '@/views/channel/activity/index.js'
import { channelActivityList } from '@/api/channel/activity'
import { checkPermi } from '@/utils/permission'

const router = useRouter()

const columns = [
  { prop: 'activityName', label: '活动名称', minWidth: '200' },
  { prop: 'time', label: '活动时间', minWidth: '350', handle: (_, row) => {
    let day = calculateDaysDiff(row.startTime, row.endTime)
    return `${row.startTime} - ${row.endTime}` + (day > 0 ? ` (${day}天)` : '')
  } },
  { prop: 'discount', label: '会员折扣', minWidth: '120', handle: (data) => `${data}%` },
  { prop: 'type', label: '渠道类型', minWidth: '130', handle: (data) => {
    let s = channelTypeOptions.find(item => item.value === data)
    return s ? s.label : ''
  }},
  { prop: 'channelCount', label: '生效渠道数', minWidth: '120' },
  { prop: 'status', label: '活动状态', minWidth: '130', handle: (data) => {
    let s = activityStatusOptions.find(item => item.value === data)
    return s ? s.label : ''
  }},
]

const tableData = ref([])
const tableLoading = ref(false)
const currentPage = ref(1)
const total = ref(0)
const queryParams = ref({
  keyword: '',
  status: '',
})

function calculateDaysDiff(date1, date2) {
  const d1 = new Date(date1);
  const d2 = new Date(date2);
  
  const differenceInTime = Math.abs(d2 - d1);
  const differenceInDays = Math.ceil(differenceInTime / (1000 * 60 * 60 * 24));
  
  return differenceInDays;
}


function onQuery() {
  currentPage.value = 1
  getList()
}

function resetQuery() {
  queryParams.value = {
    keyword: '',
    status: '',
  }
  onQuery()
}

function pageChange(page) {
  currentPage.value = page.currentPage
  getList()
}


function getList() {
  tableLoading.value = true
  channelActivityList({
    activityName: queryParams.value.keyword,
    status: queryParams.value.status,
    pageNum: currentPage.value,
    pageSize: 20,
  }).then(res => {
    if (res.code === 200) {
      tableData.value = res.data.rows
      total.value = res.data.total
    }
  }).finally(() => {
    tableLoading.value = false
  })
}

function handleAction(action, row) {
  if (action === 'add') {
    router.push(`/channel/activity/add`)
    return
  }
  if (action === 'edit' && row.id) {
    router.push(`/channel/activity/edit/${row.id}`)
    return
  }
  if (action === 'view' && row.id) {
    router.push(`/channel/activity/view/${row.id}`)
    return
  }
}

resetQuery()
</script>

<style scoped lang="scss">
.activity-page {
  padding: 20px;

  .form-box {
    .el-form-item {
      margin-bottom: 0;
    }
  }
}
</style>