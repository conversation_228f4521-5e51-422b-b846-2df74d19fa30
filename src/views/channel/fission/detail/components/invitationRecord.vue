<template>
  <div class="invitation-record">
    <div class="record-title flex-between">
      <div>邀请记录</div>
      <div class="flex-center">
        <!-- <el-select
          v-model="queryParams.memberPackageType"
          clearable
          style="width: 150px; margin-right: 15px"
          @change="handleQuery"
        >
          <el-option v-for="item in memberTypeList" :label="item.label" :value="item.value" />
        </el-select> -->
        <!-- <div class="felx-center input-box">
          <el-input
            v-model="queryParams.keyword"
            @keyup.enter="handleQuery"
            style="width: 350px"
            placeholder="搜索"
            clearable
          />
          <el-icon color="#aaa" size="16" @click="handleQuery"><Search /></el-icon>
        </div>
        <el-button type="primary" @click="resetQuery" v-btn>重置</el-button> -->
      </div>
    </div>
    <div class="record-list">
      <el-table v-loading="loading" :data="tableList" max-height="400">
        <el-table-column label="账号ID" prop="bizUserId"></el-table-column>
        <el-table-column label="手机号" prop="phone"></el-table-column>
        <el-table-column label="微信名" prop="nickName"></el-table-column>
        <el-table-column label="企微添加时间" prop="addWechatTime">
          <template v-slot="{ row }">
            <span v-if="row.isWechatChannel">
              {{ row.addWechatTime ? row.addWechatTime : '' }}
            </span>
            <span v-else>非该渠道邀请</span>
          </template>
        </el-table-column>
        <el-table-column label="注册时间" prop="registerTime">
          <template v-slot="{ row }">
            <span v-if="row.isRegisterChannel">
              {{ row.registerTime }}
            </span>
            <span v-else>非该渠道邀请</span>
          </template>
        </el-table-column>
        <el-table-column label="会员类型" prop="memberType">
          <template v-slot="{ row }">
            <div>
              <div v-if="row.isChannelMember == 1">
                {{
                  row.memberType === 0
                    ? '季度会员'
                    : row.memberType === 1
                    ? '年度会员'
                    : row.memberType === 2
                    ? '三年会员'
                    : ''
                }}
              </div>
              <div v-else>
                {{ row.memberType ? '非该渠道邀请' : '' }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="120px">
          <template v-slot="{ row }">
            <el-button v-btn link type="primary" v-hasPermi="['channel:fission:account']" @click="handleDetail(row.bizUserId)">查看账号信息</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="flex-end" style="margin: 10px">
      <el-pagination
        background
        @size-change="pageChange({ pageNum: 1, pageSize: $event })"
        @current-change="pageChange({ pageNum: $event, pageSize })"
        :current-page="pageNum"
        :page-size="pageSize"
        :page-sizes="pageSizes"
        layout="total, prev, pager, next, jumper"
        :total="total"
      />
    </div>
  </div>
</template>

<script setup>
import { memberTypeList } from '@/views/channel/distribution/data.js'
import { getBackendInviteList } from '@/api/channel/fission.js'
import { useRouter } from 'vue-router'
const router = useRouter()

const props = defineProps({
  distriId: {
    type: String,
    default: '',
  },
})

defineExpose({ handleQuery })
const emits = defineEmits(['handleDialogDetail'])

const queryParams = ref({
  memberPackageType: '',
  keyword: '',
})
const searchText = ref('')
const pageNum = ref(1)
const pageSize = ref(50)
const pageSizes = [10, 20, 30, 50]
const total = ref(0)
const tableList = ref([])
const loading = ref(false)

function resetQuery() {
  queryParams.value = {
    memberPackageType: '',
    keyword: '',
  }
  pageNum.value = 1
  pageSize.value = 50
  handleQuery()
}

function handleQuery() {
  loading.value = true
  getBackendInviteList({
    channelId: props.distriId,
    // ...queryParams.value,
    pageNum: pageNum.value,
    pageSize: pageSize.value,
  })
    .then(res => {
      tableList.value = res.data.rows || []
      total.value = res.data.total || 0
    })
    .finally(() => {
      loading.value = false
    })
}

function handleDetail(userId) {
  emits('handleDialogDetail',userId)
}
</script>

<style scoped lang="scss">
.invitation-record {
  background-color: #fff;
  //   padding: 13px;
  border-radius: 4px;
  gap: 10px;
  position: relative;
  box-shadow: var(--el-box-shadow-light);
  padding-bottom: 10px;
}
.record-title {
  padding: 13px;
  border-bottom: 1px solid #e8e8e8;
  .input-box {
    margin-right: 15px;
    position: relative;

    :deep(.el-input) {
      .el-input__wrapper {
        padding-right: 32px;
      }
    }

    .el-icon {
      position: absolute;
      top: 0;
      right: 0;
      cursor: pointer;
      width: 32px;
      height: 32px;
    }
  }
}
.record-list {
  padding: 10px;
}
</style>
