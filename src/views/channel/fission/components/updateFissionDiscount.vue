<template>
  <el-dialog
    v-model="discountDialogVisible"
    title="修改裂变折扣"
    width="550"
    append-to-body
    align-center
    :close-on-click-modal="false"
    @close="handleDiscountDialogVisible"
  >
    <el-form label-width="100px" :model="form" :rules="rules" ref="formRef">
      <el-form-item label="会员折扣" prop="memberDiscount">
        <el-radio-group v-model="form.memberDiscountType">
          <el-radio-button :value="1">固定金额</el-radio-button>
          <el-radio-button :value="2">固定比例</el-radio-button>
        </el-radio-group>
        <el-input-number
          title=""
          placeholder="请输入会员折扣"
          v-model="form.memberDiscount"
          :min="0"
          :max="form.memberDiscountType == 1 ? 999 : 99"
          :precision="0"
          :step="1"
          :controls="false"
          style="width: 250px"
          @keydown="channelInputLimit"
        >
          <template #suffix>{{ form.memberDiscountType == 1 ? '元' : '%' }}</template>
        </el-input-number>
      </el-form-item>
      <el-form-item label="结算佣金" prop="settleDiscount">
        <el-radio-group v-model="form.settleDiscountType">
          <el-radio-button :value="1">固定金额</el-radio-button>
          <el-radio-button :value="2">固定比例</el-radio-button>
        </el-radio-group>
        <el-input-number
          title=""
          placeholder="请输入结算佣金"
          v-model="form.settleDiscount"
          :min="0"
          :max="form.settleDiscountType == 1 ? 999 : 99"
          :precision="0"
          :step="1"
          :controls="false"
          style="width: 250px"
          @keydown="channelInputLimit"
        >
          <template #suffix>{{ form.settleDiscountType == 1 ? '元' : '%' }}</template>
        </el-input-number>
      </el-form-item>
      <el-form-item label="活动时间" prop="activityTime" style="width: 450px">
        <el-date-picker
          v-model="form.activityTime"
          format="YYYY/MM/DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="datetimerange"
          range-separator="-"
          unlink-panels
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <div>
          <div style="margin-top: 30px">
            <el-button v-btn style="width: 110px" round @click="handleDiscountDialogVisible">取消</el-button>
            <el-button v-btn style="width: 110px" round type="primary" @click="handleUpdateDiscount">
              确定提交
            </el-button>
          </div>
          <div style="color: #f56c6c; margin-top: 10px">注意，需同步修改裂变活动规则。</div>
        </div>
      </el-form-item>
    </el-form>

    <div class="record-box">
      <Title>历史修改记录</Title>

      <el-timeline
        style="width: 100%; min-height: 250px; max-height: 300px; overflow-y: auto"
        v-loading="recordsLoading"
      >
        <div v-if="!recordsData.length" style="position: relative; left: -40px">无</div>
        <el-timeline-item placement="top" v-for="(item, index) in recordsData" :key="index" hide-timestamp>
          <div>
            <div class="item-name">{{ item.createBy }}</div>
            <div class="item-content">修改时间：{{ item.createTime }}</div>
            <div class="item-content">活动时间：{{ item.startTime }}至{{ item.endTime }}</div>
            <div class="item-content">
              会员折扣：{{ item.discountType == 1 ? '固定金额' : '固定比例' }}-{{ item.amount
              }}{{ item.discountType == 1 ? '元' : '%' }}
            </div>
            <div class="item-content">
              结算佣金：{{ item.settleDiscountType == 1 ? '固定金额' : '固定比例' }}-{{ item.settleDiscount
              }}{{ item.settleDiscountType == 1 ? '元' : '%' }}
            </div>
          </div>
        </el-timeline-item>
      </el-timeline>
    </div>
  </el-dialog>
</template>

<script setup>
import Title from '@/components/Public/title.vue'
import {
  editFissionChannelDiscount,
  getDistributionChannelDiscountLogList,
  getFissionDiscountV1,
} from '@/api/channel/fission'

defineExpose({
  open,
  close,
})

const { proxy } = getCurrentInstance()

const emits = defineEmits(['success'])

const discountDialogVisible = ref(false)
const form = ref({
  memberDiscountType: 1,
  memberDiscount: null,
  settleDiscountType: 1,
  settleDiscount: null,
  activityTime: [],
})
const rules = {
  memberDiscount: [{ required: true, validator: validateMemberDiscount, trigger: 'change' }],
  settleDiscount: [{ required: true, validator: validateSettleDiscount, trigger: 'change' }],
  activityTime: [{ required: true, type: 'array', message: '请选择活动时间', trigger: 'change' }],
}
const formRef = ref()

const recordsData = ref([])
const recordsLoading = ref(false)

function validateMemberDiscount(rule, value, callback) {
  if (form.value.memberDiscountType == 1) {
    if (value == null || value < 0 || value > 999) {
      return callback(new Error('请输入正确的金额'))
    }
  } else if (form.value.memberDiscountType == 2) {
    if (value == null || value < 0 || value > 99) {
      return callback(new Error('请输入正确的比例'))
    }
  }
  return callback()
}
function validateSettleDiscount(rule, value, callback) {
  if (form.value.settleDiscountType == 1) {
    if (value == null || value < 0 || value > 999) {
      return callback(new Error('请输入正确的金额'))
    }
  } else if (form.value.settleDiscountType == 2) {
    if (value == null || value < 0 || value > 99) {
      return callback(new Error('请输入正确的比例'))
    }
  }
  return callback()
}

function open() {
  getFissionDiscountV1().then(res => {
    if (res.data) {
      form.value = {
        memberDiscountType: res.data.memberDiscountType || 1,
        memberDiscount: res.data.memberDiscount >= 0 ? res.data.memberDiscount : 200,
        settleDiscountType: res.data.settleDiscountType || 1,
        settleDiscount: res.data.settleDiscount >= 0 ? res.data.settleDiscount : 200,
        activityTime: ['2025/1/21 18:00:00', '2025/12/31 23:59:00'],
      }
      if (res.data.startTime && res.data.endTime) {
        form.value.activityTime = [res.data.startTime, res.data.endTime]
      }
    }
  })
  getDistributionChannelDiscountLogList({ type: 5 }).then(res => {
    if (res.data) {
      recordsData.value = res.data || []
    }
  })
  discountDialogVisible.value = true
}

function close() {
  discountDialogVisible.value = false
}

const channelInputLimit = e => {
  const key = e.key
  if (key === 'Enter') e.preventDefault()
  const notAllowList = ['e', '+', '-']
  if (notAllowList.includes(key)) {
    e.returnValue = false
    return false
  }
  return true
}

function handleUpdateDiscount() {
  formRef.value.validate(valid => {
    if (valid) {
      proxy.$modal.confirm('确认提交吗？', '提示', {}).then(() => {
        proxy.$modal.loading('正在提交中...')
        let params = {
          memberDiscountType: form.value.memberDiscountType,
          memberDiscount: form.value.memberDiscount,
          settleDiscountType: form.value.settleDiscountType,
          settleDiscount: form.value.settleDiscount,
        }
        if (form.value.activityTime.length == 2) {
          params.startTime = form.value.activityTime[0]
          params.endTime = form.value.activityTime[1]
        }
        editFissionChannelDiscount(params)
          .then(res => {
            proxy.$modal.msgSuccess('修改成功')
            close()
            emits('success')
          })
          .finally(() => {
            proxy.$modal.closeLoading()
          })
      })
    }
  })
}

function handleDiscountDialogVisible() {
  discountDialogVisible.value = false
  formRef.value.resetFields()
  form.value = {
    memberDiscountType: 1,
    memberDiscount: null,
    settleDiscountType: 1,
    settleDiscount: null,
    activityTime: [],
  }
  recordsData.value = []
}
</script>

<style scoped lang="scss">
@import '@/assets/styles/customForm.scss';

.record-box {
  margin-top: 30px;

  .item-name {
    color: #333;
    font-weight: bold;
    font-size: 15px;
    margin-bottom: 10px;
  }
  .item-content {
    color: #777;
    font-size: 14px;
    margin-bottom: 10px;
  }
}
</style>
