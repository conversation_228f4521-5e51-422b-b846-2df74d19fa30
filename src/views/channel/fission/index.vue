<template>
  <div class="distribution-page">
    <div class="page-header">
      <div class="page-header__item">
        <div class="item-number">{{ statisticsData.channelNum }}</div>
        <div class="item-text">种草官 (个)</div>
      </div>
      <div class="page-header__item">
        <div class="item-number">{{ statisticsData.registerNum }}</div>
        <div class="item-text">邀请注册 (个)</div>
      </div>
      <div class="page-header__item">
        <div class="item-number">{{ statisticsData.memberNum }}</div>
        <div class="item-text">会员成交 (个)</div>
      </div>
      <div class="page-header__item">
        <div class="item-number">{{ statisticsData.unSettleAmount }}</div>
        <div class="item-text">待结算金额 (元)</div>
      </div>
    </div>
    <el-form
      class="flex-start"
      style="flex-wrap: wrap"
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      label-width="68px"
      @submit.prevent
    >
      <div style="width: 100%">
        <el-form-item label="时间范围" label-width="70px">
          <el-radio-group v-model="statusDays" style="margin-right: 10px" @change="changeStatusDays">
            <el-radio-button value="1">今天</el-radio-button>
            <el-radio-button value="2">近7天</el-radio-button>
            <el-radio-button value="3">近30天</el-radio-button>
            <el-radio-button value="4">全部</el-radio-button>
          </el-radio-group>
          <el-date-picker
            v-model="placeOrderTime"
            format="YYYY/M/D HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="datetimerange"
            range-separator="-"
            unlink-panels
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
            @change="changeTime"
            :shortcuts="shortcuts"
          ></el-date-picker>
        </el-form-item>
      </div>
      <el-form-item label="搜索" label-width="40px">
        <el-input
          v-model="queryParams.keyword"
          style="width: 280px"
          placeholder="请输入微信名/姓名/公司名称/会员编码"
          clearable
        />
      </el-form-item>
      <el-form-item label="账号类型">
        <el-select
          v-model="queryParams.accountType"
          clearable
          placeholder="请选择"
          style="min-width: 250px"
          @change="onQuery"
        >
          <el-option
            v-for="item in accountTypeList"
            :value="item.value"
            :label="item.label"
            :key="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="会员状态">
        <el-select
          v-model="queryParams.memberStatus"
          clearable
          placeholder="请选择"
          style="min-width: 250px"
          @change="onQuery"
        >
          <el-option
            v-for="item in memberStatusList"
            :value="item.value"
            :label="item.label"
            :key="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="裂变状态">
        <el-select
          v-model="queryParams.channelStatus"
          clearable
          placeholder="请选择"
          style="min-width: 250px"
          @change="onQuery"
        >
          <el-option
            v-for="item in fissionStatusList"
            :value="item.value"
            :label="item.label"
            :key="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button v-btn type="primary" icon="Search" native-type="submit" @click="onQuery">搜索</el-button>
        <el-button v-btn icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <div class="page-btn flex-between">
      <div class="flex-start">
        <el-button
          type="primary"
          plain
          v-btn
          @click="handleDiscountDialog"
          v-hasPermi="['channel:fission:editMemberDiscount']"
        >
          修改裂变折扣
        </el-button>
        <!-- <DownloadBtn
          :exportDisabled="selectIds.length == 0"
          v-hasPermi="['channel:fission:download-all']"
          type="success"
          plain
          :url="`/biz/fission-channel/download/poster/allByIds`"
          text="批量下载海报"
          loadingText="下载中"
          message="确认下载"
          :params="selectIds"
          :isAsnyc="true"
          :config="{
            timeout: 30000, // 请求超时时间 30s
          }"
        /> -->
        <div class="number-box">
          <span style="margin-right: 20px">邀请企微数：{{ listStatistics.addWeChatNum }}</span>
          <span style="margin-right: 20px">邀请注册数：{{ listStatistics.registerNum }}</span>
          <span>会员成交数：{{ listStatistics.memberNum }}</span>
        </div>
      </div>
      <CopyButton v-hasPermi="['channel:fission:promotionLink']" v-btn :copy-content="customerWoniuVideoPath">
        推广链接
      </CopyButton>
    </div>
    <div class="page-table">
      <el-table
        v-loading="loading"
        row-key="id"
        ref="tableRef"
        :data="tableList"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
      >
        <el-table-column
          type="selection"
          width="50"
          align="center"
          :reserve-selection="true"
          :selectable="isRowSelectable"
        />
        <el-table-column label="种草官ID" prop="seedId" align="center" width="90px"></el-table-column>
        <el-table-column label="账号信息" prop="nickName" minWidth="220px" align="center">
          <template v-slot="{ row }">
            <div>
              <div>微信：{{ row.nickName || '-' }}&nbsp;/&nbsp;姓名：{{ row.accountName || '-' }}</div>
              <div>{{ row.businessName || '-' }}({{ row.memberCode || '-' }})</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="会员状态" prop="memberStatus" width="100px" align="center">
          <template v-slot="{ row }">
            <div>{{ memberStatusList.find(item => item.value === row.memberStatus)?.label || '-' }}</div>
          </template>
        </el-table-column>
        <el-table-column label="账号类型" prop="accountType" width="90px" align="center">
          <template v-slot="{ row }">
            <div>{{ accountTypeList.find(item => item.value === row.accountType)?.label || '-' }}</div>
          </template>
        </el-table-column>
        <el-table-column
          label="种草码"
          show-overflow-tooltip
          prop="seedCode"
          align="center"
          width="80px"
        ></el-table-column>
        <!-- <el-table-column
          label="种草码状态"
          show-overflow-tooltip
          prop="seedCodeStatus"
          align="center"
          width="170px"
        >
          <template v-slot="{ row }">
            <div>{{ row.seedCodeStatus == '0' ? '有效' : row.seedCodeStatus == '1' ? '失效' : '-' }}</div>
            <div v-if="row.seedCodeStatus == 1">({{ row.failureTime || '-' }})</div>
          </template>
        </el-table-column> -->
        <el-table-column
          label="邀请企微数"
          prop="addWeChatNum"
          align="center"
          sortable="custom"
          width="100px"
        ></el-table-column>
        <el-table-column
          label="邀请注册数"
          prop="registerNum"
          align="center"
          sortable="custom"
          width="100px"
        ></el-table-column>
        <el-table-column
          label="会员成交数"
          prop="memberNum"
          align="center"
          sortable="custom"
          width="100px"
        ></el-table-column>
        <!-- <el-table-column label="结算比例" prop="brokeRage" align="center" width="110px" sortable="custom">
          <template v-slot="{ row }">
            {{ row.brokeRage }}
            <span v-if="row.brokeRage">%</span>
          </template>
        </el-table-column> -->
        <el-table-column
          label="订单成交额(元)"
          prop="realPayAmount"
          align="center"
          sortable="custom"
          width="120px"
        ></el-table-column>
        <el-table-column
          label="待结算金额(元)"
          prop="unSettleAmount"
          align="center"
          sortable="custom"
          width="120px"
        ></el-table-column>
        <el-table-column
          label="已结算金额(元)"
          prop="realSettleAmount"
          align="center"
          sortable="custom"
          width="120px"
        ></el-table-column>
        <el-table-column label="裂变状态" prop="status" align="center" width="80px">
          <template v-slot="{ row }">
            <el-switch
              v-model="row.status"
              :active-value="0"
              :inactive-value="1"
              :disabled="fissionStatusSwitch"
              @change="handleStatusChange($event, row)"
            />
          </template>
        </el-table-column>
        <el-table-column label="参与裂变时间" prop="createTime" align="center" width="140px">
          <template v-slot="{ row }">
            {{ row.createTime || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="120px" align="center">
          <template v-slot="{ row }">
            <el-button
              v-btn
              link
              type="primary"
              @click="handleDetail('/channel/fission/detail/' + row.id)"
              v-if="checkPermi(['channel:fission:detail'])"
            >
              查看详情
            </el-button>
            <div v-if="row.status == 0 && checkPermi(['channel:fission:download'])">
              <DownloadBtn
                v-hasPermi="['channel:fission:download']"
                type="primary"
                link
                :url="`/biz/fission-channel/download/poster/${row.id}`"
                text="下载海报"
                loadingText="下载中"
                message="确认下载"
              />
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="flex-end" style="margin: 10px 0 60px">
      <PaginationFloatBar :current-page="pageNum" :page-size="pageSize" @update:current-page="handlePageChange" :total="total">
        <DownloadBtn
          :exportDisabled="selectIds.length == 0"
          v-hasPermi="['channel:fission:download-all']"
          type="success"
          plain
          :url="`/biz/fission-channel/download/poster/allByIds`"
          text="批量下载海报"
          loadingText="下载中"
          message="确认下载"
          :params="selectIds"
          :isAsnyc="true"
          :config="{
            timeout: 30000, // 请求超时时间 30s
          }"
        />
      </PaginationFloatBar>
      <!-- <el-pagination
        background
        @size-change="pageChange({ pageNum: 1, pageSize: $event })"
        @current-change="pageChange({ pageNum: $event, pageSize })"
        :current-page="pageNum"
        :page-size="pageSize"
        :page-sizes="pageSizes"
        layout="total, prev, pager, next, jumper"
        :total="total"
      /> -->
    </div>
    <!-- 修改裂变折扣 -->
    <UpdateFissionDiscount ref="UpdateFissionDiscountRef" @success="onQuery" />
  </div>
</template>

<script setup>
import DownloadBtn from '@/components/Button/DownloadBtn'
import CopyButton from '@/components/Button/CopyButton.vue'
import UpdateFissionDiscount from '@/views/channel/fission/components/updateFissionDiscount.vue'
import {
  getBackendList,
  getBackendStatistics,
  getBackendListStatistics,
  updateFissionChannelStatus,
} from '@/api/channel/fission'
import { accountTypeList, fissionStatusList, memberStatusList } from '@/views/channel/fission/data'
import { checkPermi } from '@/utils/permission'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
const { proxy } = getCurrentInstance()

const customerWoniuVideoPath = import.meta.env.VITE_APP_CUSTOMER_WONIU_VIDEO_PATH + '/activity/fission'

const router = useRouter()

const loading = ref(false)
const pageNum = ref(1)
const pageSize = ref(20)
const pageSizes = [10, 20, 30, 50]
const total = ref(0)
const searchText = ref('')
const statisticsData = ref({
  channelNum: 0,
  registerNum: 0,
  memberNum: 0,
  unSettleAmount: 0,
})
const shortcuts = [
  {
    text: '今天',
    value: () => {
      const start = new Date()
      start.setHours(0, 0, 0, 0)
      const end = new Date()
      end.setHours(23, 59, 59, 59)
      return [start, end]
    },
  },
]
const queryParams = ref({
  memberStatus: '',
  channelStatus: '',
  keyword: '',
  createId: '',
  orderByType: null,
  accountType: '',
  isAsc: null,
  endTime: '',
  startTime: '',
})
const listStatistics = ref({
  channelNum: 0,
  registerNum: 0,
  memberNum: 0,
  unSettleAmount: 0,
  addWeChatNum: 0,
})

const statusDays = ref('4')
const placeOrderTime = ref([])
const tableList = ref([])
const tableRef = ref(null)

const fissionStatusSwitch = computed(() => {
  if (checkPermi(['channel:fission:switch'])) {
    return false
  }
  return true
})

const UpdateFissionDiscountRef = ref()

function handleDiscountDialog() {
  UpdateFissionDiscountRef.value?.open()
}
// 详情
// function handleDetail(id) {
//   router.push(`/channel/distribution/detail/${id}`)
// }
function handleDetail(path) {
  const { href } = router.resolve({ path })
  window.open(href, '_blank')
}

// 分页跳转
function pageChange(page) {
  pageNum.value = page.pageNum
  pageSize.value = page.pageSize
  onQuery()
}
function handlePageChange(num) {
  pageNum.value = num
  onQuery()
}
function handleQuery() {
  loading.value = true
  getBackendList({ ...queryParams.value, pageNum: pageNum.value, pageSize: pageSize.value })
    .then(res => {
      tableList.value = res.data.rows || []
      total.value = res.data.total || 0
    })
    .finally(() => {
      loading.value = false
    })
}

//统计
function handleDistributionStatistics() {
  getBackendStatistics().then(res => {
    statisticsData.value = res.data
  })
}

function resetQuery() {
  queryParams.value = {
    memberStatus: '',
    channelStatus: '',
    keyword: '',
    createId: '',
    orderByType: null,
    accountType: '',
    isAsc: null,
    endTime: '',
    startTime: '',
  }
  searchText.value = ''
  pageNum.value = 1
  pageSize.value = 20
  statusDays.value = '4'
  placeOrderTime.value = []
  selectIds.value = []
  tableRef.value?.clearSort()
  tableRef.value?.clearSelection()
  onQuery()
}
//表格排序
function handleSortChange(data) {
  if (data.prop == 'addWeChatNum') {
    queryParams.value.orderByType = '1'
  } else if (data.prop == 'registerNum') {
    queryParams.value.orderByType = '2'
  } else if (data.prop == 'memberNum') {
    queryParams.value.orderByType = '3'
  } else if (data.prop == 'brokeRage') {
    queryParams.value.orderByType = '4'
  } else if (data.prop == 'realPayAmount') {
    queryParams.value.orderByType = '5'
  } else if (data.prop == 'unSettleAmount') {
    queryParams.value.orderByType = '6'
  } else if (data.prop == 'settleAmount') {
    queryParams.value.orderByType = '7'
  } else if (data.prop == 'realSettleAmount') {
    queryParams.value.orderByType = '8'
  }
  data.order == 'ascending' ? (queryParams.value.isAsc = '1') : (queryParams.value.isAsc = '')
  if (!data.order) queryParams.value.orderByType = null
  handleQuery()
}

function isRowSelectable(row, index) {
  if (selectIds.value && selectIds.value.length >= 20) {
    let s = selectIds.value.find(item => item == row.id)
    if (s) {
      return true
    } else {
      return false
    }
  } else {
    return true
  }
}

//表格勾选
const selectIds = ref([])
function handleSelectionChange(val) {
  if (val && val.length > 20) {
    ElMessage.warning('最多只能选择20条数据')
    tableRef.value.toggleRowSelection(val[20], false)
    return
  }
  if (val && val.length > 0) {
    selectIds.value = val.map(item => item.id)
  } else {
    selectIds.value = []
  }
}

function changeStatusDays(val) {
  let timeList = []
  if (val == 1) {
    timeList = [getBeforeDate(0, 'one'), getBeforeDate(0, 'two')]
  } else if (val == 2) {
    timeList = [getBeforeDate(7, 'one'), getBeforeDate(0, 'two')]
  } else if (val == 3) {
    timeList = [getBeforeDate(30, 'one'), getBeforeDate(0, 'two')]
  } else {
    timeList = []
  }
  placeOrderTime.value = []
  queryParams.value.startTime = timeList[0] ? timeList[0] : ''
  queryParams.value.endTime = timeList[0] ? timeList[1] : ''
  onQuery()
}
function changeTime() {
  if (placeOrderTime.value) {
    statusDays.value = null
    queryParams.value.startTime = placeOrderTime.value[0]
    queryParams.value.endTime = placeOrderTime.value[1]
  } else {
    statusDays.value = '4'
    queryParams.value.startTime = ''
    queryParams.value.endTime = ''
  }
  onQuery()
}

function handleStatusChange(value, row) {
  proxy.$modal.loading('执行中')
  updateFissionChannelStatus({
    id: row.id,
    status: value,
  })
    .then(() => {
      proxy.$modal.closeLoading()
      proxy.$modal.msgSuccess('操作成功')
    })
    .catch(() => {
      if (value == 0) {
        row.status = 1
      } else {
        row.status = 0
      }
    })
}

function handleListStatistics() {
  getBackendListStatistics({
    ...queryParams.value,
    pageNum: pageNum.value,
    pageSize: pageSize.value,
  }).then(res => {
    listStatistics.value = res.data
  })
}
function onQuery() {
  handleQuery()
  handleDistributionStatistics()
  handleListStatistics()
}

//获取范围日期
function getBeforeDate(n, type) {
  var n = n
  var d = new Date()
  var year = d.getFullYear()
  var mon = d.getMonth() + 1
  var day = d.getDate()
  if (day <= n) {
    if (mon > 1) {
      mon = mon - 1
    } else {
      year = year - 1
      mon = 12
    }
  }
  d.setDate(d.getDate() - n)
  year = d.getFullYear()
  mon = d.getMonth() + 1
  day = d.getDate()
  var s = year + '-' + (mon < 10 ? '0' + mon : mon) + '-' + (day < 10 ? '0' + day : day)
  return type == 'one' ? s + ' 00:00:00' : s + ' 23:59:59'
}

onQuery()
</script>

<style scoped lang="scss">
:deep(.el-input-number) {
  .el-input__inner {
    text-align: left;
  }
}
// :deep(.el-table__header-wrapper) {
//   .el-checkbox {
//     display: none;
//   }
// }
.distribution-page {
  padding: 20px 20px 0 20px;
}
.margin-bottom-20 {
  margin-bottom: 20px;
}
.page-search {
  display: flex;
  align-items: center;
}
.page-header {
  display: flex;
  // justify-content: space-between;
  margin-bottom: 20px;
  gap: 0 50px;

  &__item {
    border-radius: 9px;
    padding: 20px 0;
    text-align: center;
    width: calc(100% / 4);
    box-shadow: var(--el-box-shadow-light);
    .item-number {
      font-weight: 600;
      color: #666;
      font-size: 28px;
    }
    .item-text {
      margin-top: 10px;
      font-size: 14px;
      color: #999;
    }
  }
}
.page-btn {
  flex: 1;

  .input-box {
    margin: 15px;
    position: relative;

    :deep(.el-input) {
      .el-input__wrapper {
        padding-right: 32px;
      }
    }

    .el-icon {
      position: absolute;
      top: 0;
      right: 0;
      cursor: pointer;
      width: 32px;
      height: 32px;
    }
  }
  .number-box {
    margin-left: 50px;
  }
}
.page-table {
  margin-top: 15px;
}
</style>
