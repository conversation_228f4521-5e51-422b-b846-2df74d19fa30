<template>
  <div class="market-page">
    <div class="page-header">
      <div class="page-header__item">
        <div class="item-number">{{ statisticsData.uniqueVisitor }}</div>
        <div class="item-text">独立访客</div>
      </div>
      <div class="page-header__item">
        <div class="item-number">{{ statisticsData.wechatCount }}</div>
        <div class="item-text">添加企微数</div>
      </div>
      <div class="page-header__item">
        <div class="item-number">{{ statisticsData.newRegistrationCount }}</div>
        <div class="item-text">注册用户数</div>
      </div>
      <div class="page-header__item">
        <div class="item-number">{{ statisticsData.memberNum }}</div>
        <div class="item-text">会员成交数</div>
      </div>
      <div class="page-header__item">
        <div class="item-number">{{ statisticsData.realPayAmount }}</div>
        <div class="item-text">会员金额</div>
      </div>
    </div>
    <div class="market-serach">
      <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="88px" @submit.prevent>
        <el-form-item label="时间范围">
          <el-radio-group v-model="statusDays" @change="changeStatusDays">
            <el-radio-button value="1">今天</el-radio-button>
            <el-radio-button value="2">近7天</el-radio-button>
            <el-radio-button value="3">近30天</el-radio-button>
            <el-radio-button value="4">全部</el-radio-button>
          </el-radio-group>
          <el-date-picker
            style="margin-left: 10px; width: 350px"
            v-model="placeOrderTime"
            format="YYYY/M/D HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="datetimerange"
            range-separator="-"
            unlink-panels
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
            @change="changeTime"
            :shortcuts="shortcuts"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="">
          <el-input
            style="width: 200px"
            placeholder="请输入渠道名称搜索"
            clearable
            v-model="queryParams.marketingChannelName"
            @blur="() => queryParams.marketingChannelName && init()"
            @clear="init"
          ></el-input>
        </el-form-item>
        <!-- <el-form-item label="">
          <el-input placeholder="请输入ID" v-model="queryParams.id"></el-input>
        </el-form-item> -->
        <el-form-item label="" prop="platform">
          <el-select
            v-model="queryParams.marketingPlatform"
            placeholder="请选择投放平台"
            clearable
            multiple
            collapse-tags
            style="width: 180px"
            @change="init"
          >
            <el-option
              v-for="item in platformList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="" prop="platform">
          <SelectLoad
            v-model="queryParams.userIds"
            :request="getCreateUserList"
            :multiple="true"
            :collapseTags="true"
            :requestCallback="res => res.data.rows"
            :totalCallback="res => res.data.rows.length"
            keyValue="userId"
            keyLabel="userName"
            keyWord="userName"
            :reserveKeyword="false"
            placeholder="请选择创建人"
            style="width: 250px"
            @change="init"
          />
          <!-- <el-select
            v-model="queryParams.userId"
            placeholder="请选择创建人"
            clearable
            multiple
            collapse-tags
            style="width: 180px"
          >
            <el-option
              v-for="item in createUserList"
              :key="item.userId"
              :label="item.userName"
              :value="item.userId"
            />
          </el-select> -->
        </el-form-item>
        <el-form-item>
          <el-button v-btn type="primary" icon="Search" native-type="submit" @click="init">搜索</el-button>
          <el-button v-btn icon="Refresh" plain @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="market-btn">
      <el-button v-btn type="primary" @click="handleAdd('add')" v-hasPermi="['channel:market:add']">
        新增渠道
      </el-button>
      <!-- <el-button v-btn type="success" plain @click="handleExport" v-hasPermi="['channel:market:export']">
        导出
      </el-button> -->
    </div>
    <div class="market-table">
      <el-table v-loading="loading" :data="tableList" ref="tableRef" @sort-change="handleSortChange">
        <el-table-column label="ID" prop="id" width="100px" align="center"></el-table-column>
        <el-table-column label="投放平台" prop="marketingPlatform" align="center">
          <template v-slot="{ row }">
            {{ platformList.find(item => item.value == row.marketingPlatform)?.label || '-' }}
          </template>
        </el-table-column>
        <el-table-column
          label="渠道名称"
          prop="marketingChannelName"
          align="center"
          show-overflow-tooltip
          width="180px"
        ></el-table-column>
        <el-table-column label="落地形式" prop="landingForm" align="center">
          <template v-slot="{ row }">
            {{ landingList.find(item => item.value == row.landingForm)?.label || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="创建信息" prop="createBy" align="center">
          <template v-slot="{ row }">
            <div>{{ row.createBy }}</div>
            <div>{{ row.createTime }}</div>
          </template>
        </el-table-column>
        <!-- <el-table-column label="访问量(PV)" prop="fw" align="center"></el-table-column> -->
        <el-table-column
          label="独立访客(UV)"
          sortable="custom"
          width="150px"
          prop="uniqueVisitor"
          align="center"
        ></el-table-column>
        <el-table-column label="添加企微数" sortable="custom" width="150px" prop="wechatCount" align="center">
          <template v-slot="{ row }">
            {{ row.wechatCount || '0' }}
          </template>
        </el-table-column>
        <!-- <el-table-column label="跳出率" prop="tc" align="center"></el-table-column> -->
        <el-table-column
          label="注册用户"
          sortable="custom"
          width="150px"
          prop="newRegistrationCount"
          align="center"
        ></el-table-column>
        <el-table-column
          sortable="custom"
          label="会员成交数"
          width="150px"
          prop="memberNum"
          align="center"
        ></el-table-column>
        <el-table-column
          label="会员总金额"
          sortable="custom"
          width="150px"
          prop="realPayAmount"
          align="center"
        ></el-table-column>
        <!-- <el-table-column label="新增注册数" prop="newRegistrationCount" align="center"></el-table-column>
        <el-table-column label="新增激活数" prop="newActivationCount" align="center"></el-table-column> -->
        <el-table-column label="操作" fixed="right" width="200px" align="center">
          <template v-slot="{ row }">
            <el-button
              v-if="checkPermi(['channel:market:detail'])"
              v-btn
              link
              type="primary"
              @click="handleDetail('/channel/market/detail/' + row.id)"
            >
              查看
            </el-button>
            <el-button
              v-if="checkPermi(['channel:market:edit'])"
              v-btn
              link
              type="primary"
              @click="handleAdd('edit', row.id)"
            >
              修改
            </el-button>
            <!-- <el-button
              v-if="checkPermi(['channel:market:code'])"
              v-btn
              link
              type="primary"
              @click="openDialog(row.id)"
              v-if="row.landingForm == 1"
            >
              复制链接
            </el-button> -->
            <el-button
              v-btn
              v-if="row.landingForm == 1 && row.marketingPlatform != 9 && checkPermi(['channel:market:copy'])"
              link
              type="primary"
              @click="openDialog(row.id)"
            >
              下载物料
            </el-button>
            <!-- <CopyButton v-if="row.landingForm == 1" link type="primary" :copy-content="row.dedicatedLinkCode">
              复制链接
            </CopyButton> -->
            <el-button
              v-if="row.marketingPlatform != 9 && checkPermi(['channel:market:download'])"
              v-btn
              link
              type="primary"
              @click="downUrlCode(row.id)"
            >
              下载二维码
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="flex-end" style="margin: 10px 0 60px">
      <PaginationFloatBar
        :current-page="pageNum"
        :page-size="pageSize"
        @update:current-page="handlePageChange"
        :total="total"
      >
        <el-button v-btn type="success" plain @click="handleExport" v-hasPermi="['channel:market:export']">
          导出
        </el-button>
      </PaginationFloatBar>
      <!-- <el-pagination
        background
        @size-change="pageChange({ pageNum: 1, pageSize: $event })"
        @current-change="pageChange({ pageNum: $event, pageSize })"
        :current-page="pageNum"
        :page-size="pageSize"
        :page-sizes="pageSizes"
        layout="total, prev, pager, next, jumper"
        :total="total"
      /> -->
    </div>
    <marketDialog ref="marketDialogRef" @success="init" />
    <el-dialog
      title="投流二维码"
      align-center
      v-model="dialogVisible"
      width="550px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="closeDialog"
    >
      <div class="dialog-box" v-loading="dialogLoading">
        <div class="box-title more-ell">{{ dialogData.marketingChannelName }}</div>
        <div class="box-img" id="qrcode">
          <img style="width: 200px; height: 100%" :src="qrCodeDataUrl" alt="" />
        </div>
        <div class="box-link">投流链接：{{ dialogData.dedicatedLinkCode }}</div>
      </div>
      <template #footer>
        <div class="flex-center btn">
          <slot name="button">
            <el-button
              v-btn
              type="primary"
              @click="downUrlFile(qrCodeDataUrl, `${dialogData.marketingChannelName}.png`)"
            >
              下载二维码
            </el-button>
            <CopyButton type="primary" style="padding: 0 22px" :copy-content="dialogData.dedicatedLinkCode">
              复制链接
            </CopyButton>
          </slot>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import QRCode from 'qrcode'
import marketDialog from '@/views/channel/market/dialog/marketDialog.vue'
import { downUrlFile } from '@/utils/download'
import CopyButton from '@/components/Button/CopyButton.vue'
import SelectLoad from '@/components/Select/SelectLoad.vue'
import { checkPermi } from '@/utils/permission'
import { platformList, landingList } from '@/views/channel/market/data'
import {
  getMarketList,
  downloadMaterial,
  exportMarket,
  getCreateUserList,
  getMarketStatistics,
} from '@/api/channel/market'
import { getBeforeDate, isSafari, isIOS, copy } from '@/utils/index'

const { proxy } = getCurrentInstance()
import { useRouter } from 'vue-router'
const router = useRouter()

const createUserList = ref([])
const loading = ref(false)
const queryRef = ref(null)
const clickType = ref('')
const marketDialogRef = ref(null)
const pageNum = ref(1)
const pageSize = ref(50)
const pageSizes = [10, 20, 30, 50]
const total = ref(0)
const tableRef = ref(null)
const queryParams = ref({
  userIds: [],
  marketingChannelName: '',
  id: '',
  marketingPlatform: [],
  startTime: '',
  endTime: '',
  orderByType: null,
  isAsc: null,
})

const shortcuts = [
  {
    text: '今天',
    value: () => {
      const start = new Date()
      start.setHours(0, 0, 0, 0)
      const end = new Date()
      end.setHours(23, 59, 59, 59)
      return [start, end]
    },
  },
]

const statusDays = ref('4')
const placeOrderTime = ref([])
const tableList = ref([])
const statisticsData = ref({
  uniqueVisitor: 0,
  wechatCount: 0,
  realPayAmount: 0,
  memberNum: 0,
  newRegistrationCount: 0,
})

function changeStatusDays(val) {
  let timeList = []
  if (val == 1) {
    timeList = [getBeforeDate(0, 'one'), getBeforeDate(0, 'two')]
  } else if (val == 2) {
    timeList = [getBeforeDate(7, 'one'), getBeforeDate(0, 'two')]
  } else if (val == 3) {
    timeList = [getBeforeDate(30, 'one'), getBeforeDate(0, 'two')]
  } else {
    timeList = []
  }
  placeOrderTime.value = []
  queryParams.value.startTime = timeList[0] ? timeList[0] : ''
  queryParams.value.endTime = timeList[0] ? timeList[1] : ''
  init()
}

function changeTime() {
  if (placeOrderTime.value) {
    statusDays.value = null
    queryParams.value.startTime = placeOrderTime.value[0]
    queryParams.value.endTime = placeOrderTime.value[1]
  } else {
    statusDays.value = '4'
    queryParams.value.startTime = ''
    queryParams.value.endTime = ''
  }
  init()
}

//search
function handleQuery() {
  loading.value = true
  getMarketList({
    pageNum: pageNum.value,
    pageSize: pageSize.value,
    ...queryParams.value,
  })
    .then(res => {
      tableList.value = res.data.rows || []
      total.value = res.data.total || 0
    })
    .finally(() => {
      loading.value = false
    })
}
//reset
function resetQuery() {
  queryParams.value = {
    userIds: [],
    marketingChannelName: '',
    id: '',
    marketingPlatform: [],
    startTime: '',
    endTime: '',
    orderByType: null,
    isAsc: null,
  }
  statusDays.value = '4'
  tableRef.value?.clearSort()
  placeOrderTime.value = []
  init()
}
/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    'biz/marketing-channel/export',
    {
      ...queryParams.value,
    },
    `市场渠道列表导出${new Date().getTime()}.xlsx`
  )
}

//新增
function handleAdd(type, id = '') {
  marketDialogRef.value.open(type, id)
}

function handleDetail(path) {
  const { href } = router.resolve({ path })
  window.open(href, '_blank')
  // router.push(path)
}

function handleSortChange(data) {
  if (data.prop == 'uniqueVisitor') {
    queryParams.value.orderByType = '1'
  } else if (data.prop == 'newRegistrationCount') {
    queryParams.value.orderByType = '2'
  } else if (data.prop == 'memberNum') {
    queryParams.value.orderByType = '3'
  } else if (data.prop == 'realPayAmount') {
    queryParams.value.orderByType = '4'
  } else if (data.prop == 'wechatCount') {
    queryParams.value.orderByType = '5'
  }
  data.order == 'ascending' ? (queryParams.value.isAsc = '1') : (queryParams.value.isAsc = '')
  if (!data.order) queryParams.value.orderByType = null
  handleQuery()
}

// 分页跳转
function pageChange(page) {
  pageNum.value = page.pageNum
  pageSize.value = page.pageSize
  init()
}
function handlePageChange(num) {
  pageNum.value = num
  init()
}

//下载物料
const dialogVisible = ref(false)
const dialogData = ref({})
const qrCodeDataUrl = ref('')
const dialogLoading = ref(false)

//关闭弹窗
function closeDialog() {
  dialogVisible.value = false
  dialogData.value = {}
  qrCodeDataUrl.value = ''
  dialogLoading.value = false
}

function downUrlCode(id) {
  downloadMaterial(id).then(async res => {
    dialogData.value = res.data
    if (res.data.landingForm == 1) {
      downUrlFile(res.data.wechatCode, `${dialogData.value.marketingChannelName}.png`)
    } else {
      downUrlFile(res.data.dedicatedLinkCode, `${dialogData.value.marketingChannelName}.png`)
    }
  })
}

//打开弹窗
function openDialog(id) {
  dialogLoading.value = true
  downloadMaterial(id)
    .then(async res => {
      dialogData.value = res.data
      const dataUrl = await QRCode.toDataURL(res.data.dedicatedLinkCode, {
        errorCorrectionLevel: 'H',
        width: '100%',
      })
      qrCodeDataUrl.value = dataUrl
      // downUrlFile(res.data.dedicatedLinkCode, `${dialogData.value.marketingChannelName}.png`)
    })
    .finally(() => {
      dialogLoading.value = false
    })
  dialogVisible.value = true
}

function getDownloadMaterial(id) {
  downloadMaterial(id)
    .then(async res => {
      dialogData.value = res.data
      const dataUrl = await QRCode.toDataURL(res.data.dedicatedLinkCode, {
        errorCorrectionLevel: 'H',
        width: '100%',
      })
      qrCodeDataUrl.value = dataUrl
      proxy.$modal
        .alert(`<div style="work-break: break-all;">${res.data.dedicatedLinkCode}</div>`, '', {
          confirmButtonText: '确定复制',
          dangerouslyUseHTMLString: true,
        })
        .then(res => {
          copy(dialogData.value.dedicatedLinkCode)
        })
    })
    .finally(() => {
      dialogLoading.value = false
    })
}

function getStatistics() {
  getMarketStatistics(queryParams.value).then(res => {
    statisticsData.value = res.data
  })
}

function init() {
  handleQuery()
  getStatistics()
}

init()
</script>

<style scoped lang="scss">
// :deep(.el-form--inline) {
//   .el-form-item{
//     margin-right: 15px;
//   }
// }
.market-page {
  padding: 20px 20px 0 20px;
}
.page-header {
  display: flex;
  // justify-content: space-between;
  margin-bottom: 20px;
  gap: 0 50px;

  &__item {
    border-radius: 9px;
    padding: 20px 0;
    text-align: center;
    width: calc(100% / 4);
    box-shadow: var(--el-box-shadow-light);
    .item-number {
      font-weight: 600;
      color: #666;
      font-size: 28px;
    }
    .item-text {
      margin-top: 10px;
      font-size: 14px;
      color: #999;
    }
  }
}
.market-table {
  margin-top: 10px;
  :deep(.el-table) {
    th {
      text-align: center;
    }
  }
}
.dialog-box {
  text-align: center;
  font-size: 22px;
  .box-title {
    word-break: break-word;
    text-align: center;
    color: #70b603;
  }
  .box-img {
    margin-top: 10px;
    // width: 200px;
    // height: 200px;
  }
  .box-link {
    margin-top: 20px;
    color: #000;
    word-break: break-all;
  }
}
</style>
