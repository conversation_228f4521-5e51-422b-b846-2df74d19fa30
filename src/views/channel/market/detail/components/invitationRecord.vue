<template>
  <div>
    <div style="margin-top: 20px">
      <el-form :model="queryParams" ref="queryRef" :inline="true">
        <el-form-item>
          <el-input
            style="width: 300px"
            v-model="queryParams.keyword"
            placeholder="请输入手机号、微信名搜索"
            @keyup.enter.native="init"
            clearable
          />
        </el-form-item>
        <el-form-item>
          <el-date-picker
            style="margin-left: 10px"
            v-model="queryParams.registerTime"
            format="YYYY/M/D HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="datetimerange"
            range-separator="-"
            unlink-panels
            start-placeholder="请选择注册日期范围"
            end-placeholder="请选择注册日期范围"
            :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-date-picker
            style="margin-left: 10px"
            v-model="queryParams.memberTime"
            format="YYYY/M/D HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="datetimerange"
            range-separator="-"
            unlink-panels
            start-placeholder="请选择会员购买时间"
            end-placeholder="请选择会员购买时间"
            :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button v-btn type="primary" icon="Search" @click="init">搜索</el-button>
          <el-button v-btn icon="Refresh" plain @click="resetQuery">重置</el-button>
          <DownloadBtn
            type="success"
            plain
            icon="Download"
            url="/biz/marketing-channel/getChannelInviteList/export"
            :params="handleQueryParams()"
            v-hasPermi="['channel:market:channelInviteList:export']"
            fileName="邀请记录列表.xlsx"
          />
        </el-form-item>
      </el-form>
    </div>
    <div class="invitation-record">
      <div class="record-title flex-between">
        <div>邀请记录</div>
        <div class="flex-center" style="margin-right: 20px">
          <span>注册用户数:{{ statisticsData.registerNum || 0 }}</span>
          <span style="margin: 0 30px">会员总数:{{ statisticsData.memberNum || 0 }}</span>
          <span>会员总金额:￥{{ statisticsData.realPayAmount || 0 }}</span>
        </div>
      </div>
      <div class="record-list">
        <el-table v-loading="loading" :data="tableList" max-height="400">
          <el-table-column label="账号ID" prop="bizUserId" align="center"></el-table-column>
          <el-table-column label="手机号" prop="phone" align="center">
            <template v-slot="{ row }">
              <div>{{ row.phone || '-' }}</div>
            </template>
          </el-table-column>
          <el-table-column label="微信名" prop="nickName" align="center">
            <template v-slot="{ row }">
              <div>{{ row.nickName || '-' }}</div>
            </template>
          </el-table-column>
          <el-table-column label="注册时间" prop="registerTime" align="center">
            <!-- <template v-slot="{ row }">
              <span v-if="row.isRegisterChannel">
                {{ row.addWechatTime }}
              </span>
              <span v-else>非该渠道邀请</span>
            </template> -->
          </el-table-column>
          <el-table-column label="会员类型" prop="memberType" align="center">
            <template v-slot="{ row }">
              <div>
                <div v-if="row.isChannelMember == 1">
                  {{
                    row.memberType === 0
                      ? '季度会员'
                      : row.memberType === 1
                      ? '年度会员'
                      : row.memberType === 2
                      ? '三年会员'
                      : '-'
                  }}
                </div>
                <div v-else>
                  {{ row.isChannelMember == 0 && row.accountType != 0 ? '非该渠道邀请' : '-' }}
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="购买会员时间" prop="createTime" align="center">
            <template v-slot="{ row }">
              <div>{{ row.createTime || '-' }}</div>
            </template>
          </el-table-column>
          <el-table-column label="会员金额" prop="realPayAmount" align="center">
            <template v-slot="{ row }">
              <div>{{ row.realPayAmount || '-' }}</div>
            </template>
          </el-table-column>
          <el-table-column label="操作" fixed="right" width="120px">
            <template v-slot="{ row }">
              <el-button
                v-btn
                link
                type="primary"
                v-hasPermi="['channel:market:account']"
                @click="handleDetail(row.bizUserId)"
              >
                查看账号信息
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="flex-end" style="margin: 10px">
        <el-pagination
          background
          @size-change="pageChange({ pageNum: 1, pageSize: $event })"
          @current-change="pageChange({ pageNum: $event, pageSize })"
          :current-page="pageNum"
          :page-size="pageSize"
          :page-sizes="pageSizes"
          layout="total, prev, pager, next, jumper"
          :total="total"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { memberTypeList } from '@/views/channel/distribution/data.js'
import { useRouter } from 'vue-router'
import { getChannelInviteList, getInviteStatistics } from '@/api/channel/market'
import DownloadBtn from '@/components/Button/DownloadBtn'

const router = useRouter()

const props = defineProps({
  distriId: {
    type: String,
    default: '',
  },
})

defineExpose({ init })
const emits = defineEmits(['handleDialogDetail'])

const statisticsData = ref({
  memberNum: 0,
  realPayAmount: 0,
  registerNum: 0,
})

function handleQueryParams() {
  let { memberTime, registerTime, ...params } = queryParams.value
  if (registerTime && registerTime.length > 0) {
    params.registerStartTime = registerTime[0]
    params.registerEndTime = registerTime[1]
  }
  if (memberTime && memberTime.length > 0) {
    params.memberStartTime = memberTime[0]
    params.memberEndTime = memberTime[1]
  }
  params.channelId = props.distriId
  return params
}

const queryParams = ref({
  channelId: props.distriId,
  keyword: '',
  registerTime: [],
  memberTime: [],
  registerStartTime: '',
  registerEndTime: '',
  memberEndTime: '',
  memberStartTime: '',
})
const searchText = ref('')
const pageNum = ref(1)
const pageSize = ref(50)
const pageSizes = [10, 20, 30, 50]
const total = ref(0)
const tableList = ref([])
const loading = ref(false)

function resetQuery() {
  queryParams.value = {
    keyword: '',
    registerTime: [],
    memberTime: [],
    registerStartTime: '',
    registerEndTime: '',
    memberEndTime: '',
    memberStartTime: '',
  }
  pageNum.value = 1
  pageSize.value = 50
  init()
}

function handleQuery() {
  loading.value = true
  const params = handleQueryParams()
  getChannelInviteList({
    channelId: props.distriId,
    ...params,
    pageNum: pageNum.value,
    pageSize: pageSize.value,
  })
    .then(res => {
      tableList.value = res.data.rows || []
      total.value = res.data.total || 0
    })
    .finally(() => {
      loading.value = false
    })
}

function pageChange(params) {
  pageNum.value = params.pageNum
  pageSize.value = params.pageSize
  handleQuery()
}

function handleDetail(userId) {
  emits('handleDialogDetail', userId)
}

function getStatistics() {
  const params = handleQueryParams()
  getInviteStatistics(params).then(res => {
    statisticsData.value = res.data
  })
}

function init() {
  getStatistics()
  handleQuery()
}
</script>

<style scoped lang="scss">
.invitation-record {
  background-color: #fff;
  //   padding: 13px;
  border-radius: 4px;
  gap: 10px;
  position: relative;
  box-shadow: var(--el-box-shadow-light);
  padding-bottom: 10px;
}
.record-title {
  padding: 13px;
  border-bottom: 1px solid #e8e8e8;
  .input-box {
    margin-right: 15px;
    position: relative;

    :deep(.el-input) {
      .el-input__wrapper {
        padding-right: 32px;
      }
    }

    .el-icon {
      position: absolute;
      top: 0;
      right: 0;
      cursor: pointer;
      width: 32px;
      height: 32px;
    }
  }
}
.record-list {
  padding: 10px;
}
</style>
