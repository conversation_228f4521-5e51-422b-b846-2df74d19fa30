<template>
  <div class="market-dialog">
    <el-dialog
      v-model="dialogVisible"
      :title="title"
      align-center
      width="650px"
      destroy-on-close
      :close-on-click-modal="false"
      @close="close"
    >
      <el-form
        ref="formRef"
        :model="form"
        :validate-on-rule-change="false"
        :rules="rules"
        label-width="110px"
        :disabled="dialogType === 'detail'"
      >
        <el-form-item label="投放平台" prop="marketingPlatform">
          <el-select
            v-model="form.marketingPlatform"
            placeholder="请选择"
            clearable
            style="width: 180px"
            :disabled="dialogType == 'edit'"
          >
            <el-option
              v-for="item in platformList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="投流名称" prop="marketingChannelName">
          <el-input
            v-model="form.marketingChannelName"
            placeholder="请输入投流名称"
            maxlength="27"
            show-word-limit
            type="text"
          />
        </el-form-item>
        <el-form-item label="落地形式" prop="landingForm">
          <el-radio-group v-model="form.landingForm" :disabled="dialogType == 'edit'">
            <el-radio :value="'1'">官网首页</el-radio>
            <el-radio :value="'2'">添加企微客服</el-radio>
          </el-radio-group>
        </el-form-item>
        <!-- <el-form-item label="客服名称" prop="kfmc">
          <el-input v-model="form.kfmc" placeholder="请输入" />
        </el-form-item> -->
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入" maxlength="64" show-word-limit type="textarea" resize="none" :rows="2"/>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="flex-end btn" v-if="dialogType != 'detail'">
          <slot name="button">
            <el-button v-btn plain @click="close">取消</el-button>
            <el-button v-btn type="primary" @click="onConfirm">保存</el-button>
          </slot>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { platformList } from '@/views/channel/market/data'
import { getMarketDetail, editMarket,addMarket } from '@/api/channel/market'
import { ElMessage } from 'element-plus'

const dialogVisible = ref(false)

defineExpose({ open, close })
const emits = defineEmits(['success'])

const props = defineProps({})

const title = ref('新增市场渠道')
const formRef = ref(null)
const form = ref({
  marketingPlatform: '',
  marketingChannelName: '',
  landingForm: '1',
  remark: '',
})

const rules = {
  marketingPlatform: [{ required: true, message: '请选择投放平台', trigger: 'change' }],
  marketingChannelName: [{ required: true, message: '请输入投流名称', trigger: 'blur' }],
  landingForm: [{ required: true, message: '请选择落地形式', trigger: 'change' }],
  // qrCode: [{ required: true, message: '请上传企微二维码', trigger: 'blur' }],
}

const detailId = ref('')

const dialogType = ref('')
function open(type, id) {
  dialogType.value = type
  title.value = type === 'add' ? '新增市场渠道' : type === 'edit' ? '修改渠道' : '查看详情'
  if (type === 'edit' || type === 'detail') {
    // 获取渠道信息
    detailId.value = id
    getDetail(detailId.value)
  }
  dialogVisible.value = true
}

function getDetail(id) {
  getMarketDetail(id).then(res => {
    form.value = res.data
    form.value.marketingPlatform = form.value.marketingPlatform + ''
    form.value.landingForm = form.value.landingForm + ''
  })
}

function close() {
  // formRef.value
  formRef.value.resetFields()
  form.value = {
    marketingPlatform: '',
    marketingChannelName: '',
    landingForm: '1',
    remark: '',
  }
  detailId.value = ''
  dialogVisible.value = false
}

function onConfirm() {
  formRef.value.validate(valid => {
    if (valid) {
      if (dialogType.value === 'add') {
        addMarket(form.value).then(res => {
          if (res.code === 200) {
            ElMessage.success('新增成功')
            close()
            emits('success')
          }
        })
      } else {
        const { marketingPlatform, landingForm, ...params } = form.value
        editMarket(params).then(res => {
          if (res.code === 200) {
            ElMessage.success('修改成功')
            close()
            emits('success')
          }
        })
      }
    }
  })
}
</script>

<style scoped lang="scss"></style>
