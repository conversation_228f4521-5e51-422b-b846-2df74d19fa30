<template>
  <div class="activity-data">
    <Title v-if="type === 'add'">新增活动</Title>
    <Title v-if="type === 'edit'">编辑活动</Title>
    <Title v-if="type === 'detail'">活动详情</Title>

    <div class="form-box" v-loading="loading">
      <el-form
        ref="formRef"
        style="max-width: 800px"
        :model="form"
        :rules="rules"
        label-width="auto"
        :disabled="disabled"
      >
        <el-form-item label="活动时间" prop="startTime">
          <div class="time-box flex-center" :style="{ background: disabled ? '#f5f7fa' : '#fff' }">
            <el-input-number
              style="width: 195px"
              v-model="form.startTime"
              placeholder="开始日期"
              :controls="false"
              :min="1"
              :max="31"
              @keydown="channelInputLimit"
              @change="handleStartTime"
            >
              <template #prefix>
                <span>每月</span>
              </template>
            </el-input-number>
            <div>~</div>
            <el-input-number
              style="width: 195px"
              v-model="form.endTime"
              placeholder="结束日期"
              :controls="false"
              :min="form.startTime"
              :max="31"
              @keydown="channelInputLimit"
            />
          </div>
          <span style="margin-left: 5px; color: #999999; font-size: 12px">仅支持输入1~31范围数字</span>
        </el-form-item>

        <el-form-item label="会员类型" prop="memberPackageType">
          <el-select
            v-model="form.memberPackageType"
            placeholder="请选择会员类型"
            clearable
            style="width: 400px"
          >
            <el-option
              v-for="item in memberTypeList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="加赠时间类型" prop="presentedTimeType">
          <el-radio-group v-model="form.presentedTimeType">
            <el-radio-button
              v-for="item in presentedTypeList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-radio-group>
        </el-form-item>

        <el-form-item label="加赠时间" prop="presentedTime">
          <el-input
            v-model.number="form.presentedTime"
            style="width: 400px"
            placeholder="请输入加赠时间"
            min="1"
            max="99"
          >
            <template #append>
              {{ form.presentedTimeType == '2' ? '月' : form.presentedTimeType == '3' ? '年' : '天' }}
            </template>
          </el-input>
        </el-form-item>
      </el-form>
      <div class="flex-start btn-box" v-if="type != 'detail'">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit">提交</el-button>
      </div>
      <div class="flex-start btn-box" v-else>
        <el-button @click="handleCancel">返回</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import Title from '@/components/Public/title.vue'
import { memberTypeList, presentedTypeList } from '@/views/channel/memberActivity/index.js'

import { saveMemberActivity, updateMemberActivity, getMemberActivityInfo } from '@/api/channel/member.js'

import { ElMessage } from 'element-plus'

const { proxy } = getCurrentInstance()

const route = useRoute()
const router = useRouter()

const type = ref('add')

const loading = ref(false)

const formRef = ref(null)
const selectChannelRef = ref(null)
const form = ref({
  startTime: null,
  endTime: null,
  presentedTimeType: 2,
  memberPackageType: null,
  presentedTime: null,
})
const disabled = ref(false)
const rules = {
  presentedTimeType: [{ required: true, message: '请选择加赠时间类型', trigger: 'change' }],
  memberPackageType: [{ required: true, message: '请选择会员类型', trigger: 'change' }],
  // status: [{ validator: checkStatus, trigger: 'blur' }],
  presentedTime: [{ required: true, message: '请输入加赠时间', trigger: 'blur' }],
  startTime: [
    { required: true, message: '请输入开始日期', trigger: 'change' },
    { validator: validateTime, trigger: 'change' },
  ],
}

function validateTime(rule, value, callback) {
  if (!form.value.startTime) {
    return callback(new Error('请输入开始日期'))
  } else if (!form.value.endTime) {
    return callback(new Error('请输入结束日期'))
  }
  return callback()
}

const statusEdit = computed(() => {
  if (type.value === 'edit' && form.value.times?.length === 2) {
    let st = new Date(form.value.times[0]).getTime()
    let et = new Date(form.value.times[1]).getTime()
    let now = new Date().getTime()
    if (st > now) {
      form.value.status = 2
    } else if (et <= now) {
      form.value.status = 0
    } else {
      if (form.value.status != 1 && form.value.status != 3) form.value.status = 3
      return false
    }
  } else {
    form.value.status = void 0
  }
  return true
})

function checkStatus(rule, value, callback) {
  if (!statusEdit.value) {
    if (value != 1 && value != 3) {
      return callback(new Error('请选择活动状态'))
    }
  }
  return callback()
}

const tableData = ref([])
const tableLoading = ref(false)
const ChannelListDialogRef = ref(null)

function init() {
  if (route.name === 'ChannelMemberActivityEdit') {
    type.value = 'edit'
  } else if (route.name === 'ChannelMemberActivityDetail') {
    type.value = 'detail'
    disabled.value = true
  } else {
    type.value = 'add'
  }
  if (route.params.id) {
    getActivityDetail()
  }
}

function getActivityDetail() {
  loading.value = true
  getMemberActivityInfo(route.params.id)
    .then(res => {
      form.value = res.data
    })
    .finally(() => (loading.value = false))
}

function handleCancel() {
  router.replace('/channel/memberActivity/list')
}

function handleSelect(data) {
  tableData.value = data
  selectChannelRef.value.validateState = 'success'
}

function handleSubmit() {
  formRef.value.validate(valid => {
    if (valid) {
      proxy.$modal.confirm('确认提交保存？', '提示').then(() => {
        // let params = {
        //   activityName: form.value.activityName,
        //   startTime: form.value.times[0],
        //   endTime: form.value.times[1],
        //   discount: form.value.discount,
        //   status: form.value.status,
        //   type: form.value.channelType,
        //   channelIds: [],
        // }
        // if (form.value.channelType > 0) {
        //   params.channelIds = tableData.value.map(item => item.id)
        // }
        proxy.$modal.loading('正在提交中')
        if (type.value === 'add') {
          // params.status = 3
          saveMemberActivity(form.value)
            .then(() => {
              proxy.$modal.msgSuccess('添加成功')
              router.replace('/channel/memberActivity/list')
            })
            .finally(() => {
              proxy.$modal.closeLoading()
            })
        } else if (type.value === 'edit') {
          updateMemberActivity(form.value)
            .then(res => {
              proxy.$modal.msgSuccess('保存成功')
              router.replace('/channel/memberActivity/list')
            })
            .finally(() => {
              proxy.$modal.closeLoading()
            })
        }
      })
    }
  })
}

function handleStartTime() {
  if (form.value.endTime < form.value.startTime) {
    form.value.endTime = form.value.startTime
  }
}

const channelInputLimit = e => {
  const key = e.key
  if (key === 'Enter') e.preventDefault()
  const notAllowList = ['e', '+', '-']
  if (notAllowList.includes(key)) {
    e.returnValue = false
    return false
  }
  return true
}

init()
</script>

<style scoped lang="scss">
@import '@/assets/styles/customForm.scss';
@import '@/assets/styles/form-disabled.scss';

:deep(.el-radio-button__inner) {
  padding: 8px 35px;
}
.activity-data {
  padding: 20px 0 0 20px;

  .form-box {
    margin: 60px 0 0 100px;
    width: 900px;
  }

  .btn-box {
    margin: 20px 0 0 90px;

    .el-button {
      padding: 16px 30px;
    }
  }
}
.time-box {
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
  :deep(.el-input-number) {
    .el-input__inner {
      border: none !important;
      box-shadow: none !important;
    }
    .el-input__wrapper {
      box-shadow: none !important;
    }
  }
}
</style>
