<template>
  <div class="activity-page">
    <ElTablePage
      ref="tableRef"
      :columns="columns"
      :data="tableData"
      :loading="tableLoading"
      :currentPage="currentPage"
      :total="total"
      :tableAction="{
        width: '230',
        fixed: 'right',
      }"
      @page-change="pageChange"
      row-key="id"
    >
      <template #tableHeader>
        <div class="flex-between" style="margin: 20px 0">
          <el-button
            v-btn
            v-hasPermi="['channel:memberActivity:add']"
            icon="Plus"
            type="primary"
            @click="handleAction('add')"
          >
            新增活动
          </el-button>
          <!-- <el-form class="form-box" :inline="true" :model="queryParams" @submit.prevent>
              <el-form-item label="搜索" prop="keyword">
                <el-input
                  v-model="queryParams.keyword"
                  placeholder="请输入活动名称"
                  clearable
                  style="width: 220px"
                ></el-input>
              </el-form-item>
              <el-form-item label="活动状态" prop="status">
                <el-select
                  v-model="queryParams.status"
                  placeholder="请选择"
                  clearable
                  style="width: 170px"
                >
                  <el-option
                    v-for="dict in activityStatusOptions"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item style="margin-right: 0;">
                <el-button v-btn type="primary" icon="Search" native-type="submit" @click="onQuery">
                  搜索
                </el-button>
                <el-button v-btn icon="Refresh" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-form> -->
        </div>
      </template>
      <template #startTime="{ row }">
        <div>每月{{ row.startTime }}~{{ row.endTime }}号</div>
      </template>
      <template #presentedTime="{ row }">
        <div>
          {{ row.presentedTime
          }}{{ row.presentedTimeType == 1 ? '天' : row.presentedTimeType == 2 ? '月' : '年' }}
        </div>
      </template>

      <template #status="{row,index}">
        <el-switch
          :active-value="1"
          :inactive-value="0"
          v-model="row.status"
          @click="changeStatus(row.id, row.status, index.$index)"
        />
      </template>
      <template #tableAction="{ row }">
        <div>
          <el-button
            v-btn
            plain
            type="primary"
            size="small"
            v-hasPermi="['channel:memberActivity:view']"
            @click="handleAction('view', row)"
          >
            查看
          </el-button>
          <el-button
            v-btn
            v-hasPermi="['channel:memberActivity:edit']"
            plain
            size="small"
            type="primary"
            @click="handleAction('edit', row)"
          >
            编辑
          </el-button>
        </div>
      </template>
    </ElTablePage>
  </div>
</template>

<script setup>
import ElTablePage from '@/components/Table/ElTablePage.vue'
import { memberTypeList } from '@/views/channel/memberActivity/index.js'
import { getMemberActivityList, updateMemberActivityStatus } from '@/api/channel/member.js'
import { checkPermi } from '@/utils/permission'
import { ElMessage } from 'element-plus'

const router = useRouter()

const columns = [
  { slot: 'startTime', label: '活动时间' },
  {
    prop: 'memberPackageType',
    label: '会员类型',
    handle: data => {
      let s = memberTypeList.find(item => item.value === data)
      return s ? s.label : '-'
    },
  },
  { slot: 'presentedTime', label: '加赠时间', width: '180' },
  { slot: 'status', prop: 'status', label: '状态', width: '180' },
  { prop: 'createBy', label: '创建人' },
  { prop: 'createTime', label: '创建时间', width: '180' },
]

const tableData = ref([
  {
    id: 1,
    activityTime: '每月1~20号',
    memberType: 1,
    addTime: '2023-01-01 00:00:00',
    status: 0,
    createBy: 'admin',
    createTime: '2023-01-01 00:00:00',
  },
])
const tableLoading = ref(false)
const currentPage = ref(1)
const total = ref(0)
// const queryParams = ref({
//   keyword: '',
//   status: '',
// })

function calculateDaysDiff(date1, date2) {
  const d1 = new Date(date1)
  const d2 = new Date(date2)

  const differenceInTime = Math.abs(d2 - d1)
  const differenceInDays = Math.ceil(differenceInTime / (1000 * 60 * 60 * 24))

  return differenceInDays
}

function onQuery() {
  currentPage.value = 1
  getList()
}

function resetQuery() {
  // queryParams.value = {
  //   keyword: '',
  //   status: '',
  // }
  onQuery()
}

function pageChange(page) {
  currentPage.value = page.currentPage
  getList()
}

function getList() {
  tableLoading.value = true
  getMemberActivityList({
    pageNum: currentPage.value,
    pageSize: 20,
  })
    .then(res => {
      if (res.code === 200) {
        tableData.value = res.data.rows
        total.value = res.data.total
      }
    })
    .finally(() => {
      tableLoading.value = false
    })
}

function handleAction(action, row) {
  if (action === 'add') {
    router.push(`/channel/memberActivity/add`)
    return
  }
  if (action === 'edit' && row.id) {
    router.push(`/channel/memberActivity/edit/${row.id}`)
    return
  }
  if (action === 'view' && row.id) {
    router.push(`/channel/memberActivity/view/${row.id}`)
    return
  }
}

//修改状态
function changeStatus(id,status,index) {
  if (!checkPermi(['channel:memberActivity:status'])) {
    tableData.value[index].status = status == 0 ? 1 : 0
    return ElMessage.warning('暂无权限')
  }
  updateMemberActivityStatus({ id, status: status == 0 ? 0 : 1 }).then(() => {
    ElMessage.success('修改成功')
  })
}

resetQuery()
</script>

<style scoped lang="scss">
.activity-page {
  padding: 20px;

  .form-box {
    .el-form-item {
      margin-bottom: 0;
    }
  }
}
</style>
