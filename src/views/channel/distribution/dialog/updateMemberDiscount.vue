<template>
  <el-dialog
    v-model="discountDialogVisible"
    title="修改会员折扣"
    width="550"
    append-to-body
    align-center
    :close-on-click-modal="false"
    @close="handleDiscountDialogVisible"
  >
    <el-form label-width="100px" :model="form" :rules="rules" ref="formRef">
      <el-form-item label="会员折扣" prop="memberDiscount">
        <el-radio-group v-model="form.memberDiscountType">
          <el-radio-button :value="1">固定金额</el-radio-button>
          <el-radio-button :value="2">固定比例</el-radio-button>
        </el-radio-group>
        <el-input-number
          title=""
          placeholder="请输入会员折扣"
          v-model="form.memberDiscount"
          :min="0"
          :max="form.memberDiscountType == 1 ? 999 : 99"
          :precision="0"
          :step="1"
          :controls="false"
          style="width: 250px"
          @keydown="channelInputLimit"
        >
          <template #suffix>{{ form.memberDiscountType == 1 ? '元' : '%' }}</template>
        </el-input-number>
      </el-form-item>
      <el-form-item>
        <div style="margin-top: 30px">
          <el-button v-btn style="width: 110px" round @click="handleDiscountDialogVisible">取消</el-button>
          <el-button v-btn style="width: 110px" round type="primary" @click="handleUpdateDiscount">
            确定提交
          </el-button>
        </div>
      </el-form-item>
      <div class="tip-box">注意，请谨慎修改会员折扣信息，修改后，产生的新会员订单才生效</div>
    </el-form>

    <div class="record-box">
      <Title>历史修改记录</Title>

      <el-timeline
        style="width: 100%; min-height: 250px; max-height: 300px; overflow-y: auto"
        v-loading="recordsLoading"
      >
        <div v-if="!recordsData.length" style="position: relative; left: -40px">无</div>
        <el-timeline-item placement="top" v-for="(item, index) in recordsData" :key="index" hide-timestamp>
          <div>
            <div class="item-name">{{ item.createBy }}</div>
            <div class="item-content">修改时间：{{ item.createTime }}</div>
            <div class="item-content">
              会员折扣：{{ item.discountType == 1 ? '固定金额' : '固定比例' }}-{{ item.amount
              }}{{ item.discountType == 1 ? '元' : '%' }}
            </div>
          </div>
        </el-timeline-item>
      </el-timeline>
    </div>
  </el-dialog>
</template>

<script setup>
import Title from '@/components/Public/title.vue'
import {
  getMemberDiscount,
  updateMemberDiscount,
} from '@/api/channel/distribution'
import {
  getDistributionChannelDiscountLogList,
} from '@/api/channel/fission'

defineExpose({
  open,
  close,
})

const { proxy } = getCurrentInstance()

const emits = defineEmits(['success'])

const discountDialogVisible = ref(false)
const form = ref({
  memberDiscountType: 1,
  memberDiscount: null,
})
const rules = {
  memberDiscount: [{ required: true, validator: validateMemberDiscount, trigger: 'change' }],
}
const formRef = ref()

const recordsData = ref([])
const recordsLoading = ref(false)

function validateMemberDiscount(rule, value, callback) {
  if (form.value.memberDiscountType == 1) {
    if (value == null || value < 0 || value > 999) {
      return callback(new Error('请输入正确的金额'))
    }
  } else if (form.value.memberDiscountType == 2) {
    if (value == null || value < 0 || value > 99) {
      return callback(new Error('请输入正确的比例'))
    }
  }
  return callback()
}

function open() {
  getMemberDiscount().then(res => {
    if (res.data) {
      form.value.memberDiscountType = res.data.memberDiscountType
      form.value.memberDiscount = res.data.memberDiscount
    }
  })
  getDistributionChannelDiscountLogList({ type: 3 }).then(res => {
    if (res.data) {
      recordsData.value = res.data || []
    }
  })
  discountDialogVisible.value = true
}

function close() {
  discountDialogVisible.value = false
}

const channelInputLimit = e => {
  const key = e.key
  if (key === 'Enter') e.preventDefault()
  const notAllowList = ['e', '+', '-']
  if (notAllowList.includes(key)) {
    e.returnValue = false
    return false
  }
  return true
}

function handleUpdateDiscount() {
  formRef.value.validate(valid => {
    if (valid) {
      proxy.$modal.confirm('确认提交吗？', '提示', {}).then(() => {
        proxy.$modal.loading('正在提交中...')
        updateMemberDiscount({
          memberDiscountType: form.value.memberDiscountType,
          memberDiscount: form.value.memberDiscount,
        })
          .then(res => {
            proxy.$modal.msgSuccess('修改成功')
            close()
            emits('success')
          })
          .finally(() => {
            proxy.$modal.closeLoading()
          })
      })
    }
  })
}

function handleDiscountDialogVisible() {
  discountDialogVisible.value = false
  formRef.value.resetFields()
  form.value = {
    memberDiscountType: 1,
    memberDiscount: null,
  }
  recordsData.value = []
}
</script>

<style scoped lang="scss">
@import '@/assets/styles/customForm.scss';

.tip-box {
  width: 100%;
  text-align: center;
  margin-top: 10px;
  color: #f56c6c;
}

.record-box {
  margin-top: 30px;

  .item-name {
    color: #333;
    font-weight: bold;
    font-size: 15px;
    margin-bottom: 10px;
  }
  .item-content {
    color: #777;
    font-size: 14px;
    margin-bottom: 10px;
  }
}
</style>
