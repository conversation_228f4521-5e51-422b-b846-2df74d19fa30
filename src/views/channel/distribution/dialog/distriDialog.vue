<template>
  <div class="market-dialog">
    <el-dialog
      v-model="dialogVisible"
      :title="title"
      align-center
      width="550px"
      destroy-on-close
      :close-on-click-modal="false"
      @close="close"
    >
      <el-form
        ref="formRef"
        :model="form"
        :validate-on-rule-change="false"
        :rules="rules"
        label-width="110px"
        :disabled="dialogType === 'detail' || disabled"
      >
        <el-form-item label="渠道名称" prop="channelName">
          <el-input v-model="form.channelName" placeholder="请输入渠道名称" @input="channelNameChange" />
        </el-form-item>
        <el-form-item label="海报名称" prop="posterName">
          <el-input v-model="form.posterName" placeholder="请输入海报名称" @input="posterNameChange" />
        </el-form-item>
        <!-- <el-form-item label="负责人姓名" prop="userName">
          <el-input v-model="form.userName" placeholder="请输入" maxlength="10" />
        </el-form-item> -->

        <el-form-item label="账号" prop="seedCode" v-if="dialogType === 'edit'">
          <el-input v-model="form.seedCode" disabled placeholder="请输入" />
        </el-form-item>
        <el-form-item label="密码" prop="password" v-if="dialogType === 'edit'">
          <el-input
            v-model="form.password"
            type="password"
            show-password
            placeholder="请输入密码"
            maxlength="4"
            @input="changePassword"
          ></el-input>
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="form.phone" placeholder="请输入手机号" maxlength="11" />
        </el-form-item>
        <el-form-item label="渠道佣金" prop="brokeRage">
          <el-radio-group v-model="form.settleDiscountType">
            <!-- <el-radio-button :value="1">固定金额</el-radio-button> -->
            <el-radio-button :value="2">固定比例</el-radio-button>
          </el-radio-group>
          <el-input-number
            title=""
            placeholder="请输入"
            v-model="form.brokeRage"
            :min="0"
            :max="form.settleDiscountType == 1 ? 9999 : 100"
            :precision="0"
            :step="1"
            :controls="false"
            style="width: 100%; margin-top: 10px"
            @keydown="channelInputLimit"
          >
            <template #suffix>{{ form.settleDiscountType == 1 ? '元' : '%' }}</template>
          </el-input-number>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" maxlength="60" />
        </el-form-item>
        <!-- <el-form-item label="微信绑定" prop="wechat" required v-if="dialogType === 'add'">
          <div class="wechat-box">
            <QrCode
              ref="qrcodeRef"
              style="justify-content: flex-start"
              :httpCode="generateQrcode"
              :checkCode="checkInsertBizUser"
              :code-type="0"
              @success="checkSuccess"
            />
            <template v-if="qrcodeSuccess">
              <p>绑定成功</p>
            </template>
            <template v-else>
              <span>请将此二维码截图，发送给商家,并使用微信扫码绑定，绑定成功后账号即可创建</span>
              <br />
              <span>温馨提示：重新打开窗口后二维码会更新</span>
            </template>
          </div>
        </el-form-item> -->
        <el-form-item>
          <div style="margin: 20px 0">
            <el-button v-btn style="width: 110px" round @click="close">取消</el-button>
            <el-button v-btn style="width: 110px" round type="primary" @click="onConfirm">确定提交</el-button>
          </div>
        </el-form-item>
      </el-form>

      <div class="record-box" v-if="dialogType === 'edit'">
        <Title>历史修改记录</Title>

        <el-timeline
          style="width: 100%; min-height: 250px; max-height: 300px; overflow-y: auto"
          v-loading="recordsLoading"
        >
          <div v-if="!recordsData.length" style="position: relative; left: -40px">无</div>
          <el-timeline-item placement="top" v-for="(item, index) in recordsData" :key="index" hide-timestamp>
            <div>
              <strong class="item-content">{{ item.createBy }}</strong>
              <div class="item-content">修改时间：{{ item.createTime }}</div>
              <div class="item-content">
                渠道佣金：{{ item.settleDiscountType == 1 ? '固定金额' : '固定比例' }}-{{ item.settleDiscount
                }}{{ item.settleDiscountType == 1 ? '元' : '%' }}
              </div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
// import QrCode from '@/components/QrCode/index.vue'
// import { generateQrcode, checkInsertBizUser } from '@/api/wechat'
import Title from '@/components/Public/title.vue'
import {
  getDistributionDetail,
  saveDistribution,
  editDistribution,
  getPrivacy,
  getDistributionChannelDiscountLogList
} from '@/api/channel/distribution'
import { ElMessage } from 'element-plus'
const { proxy } = getCurrentInstance()
import { stringLength } from '@/utils/index'
const dialogVisible = ref(false)

defineExpose({ open, close })
const emits = defineEmits(['success'])

const props = defineProps({})

const title = ref('新增市场渠道')
// const qrcodeRef = ref(null)
const formRef = ref(null)
// const ticket = ref('')
const qrcodeSuccess = ref(false)
const recordsData = ref([])
const recordsLoading = ref(false)
const disabled = ref(false)
const form = ref({
  channelName: '',
  posterName: '',
  // userName: '',
  settleDiscountType: 2,
  brokeRage: null,
  phone: '',
  remark: '',
  password: null,
  seedCode: null,
  id: null,
})

const rules = {
  channelName: [
    { required: true, message: '请输入渠道名称', trigger: 'change' },
    { validator: checkChannelName, trigger: 'change' },
  ],
  posterName: [
    { required: false, message: '请输入海报名称', trigger: 'change' },
    { validator: checkPosterName, trigger: 'change' },
  ],
  // userName: [{ required: true, message: '请输入负责人姓名', trigger: 'blur' }],
  brokeRage: [
    { required: true, validator: validateDiscount, trigger: 'change' },
  ],
  phone: [
    { required: false, message: '请输入手机号', trigger: 'change' },
    { pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: '请输入正确的手机号码', trigger: 'change' },
  ],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
  seedCode: [{ required: true, message: '请输入账号', trigger: 'blur' }],
  // wechat: [{ required: true, validator: checkWechat, trigger: 'blur' }],
}

function checkChannelName(rule, value, callback) {
  stringLength(value) > 12
    ? callback(new Error('渠道名称不能超过12个字符(中文占2个字符，英文占1个字符)'))
    : callback()
}
function checkPosterName(rule, value, callback) {
  stringLength(value) > 12
    ? callback(new Error('海报名称不能超过12个字符(中文占2个字符，英文占1个字符)'))
    : callback()
}
function validateDiscount(rule, value, callback) {
  if (form.value.settleDiscountType == 1) {
    if (value == null || value < 0 || value > 9999) {
      return callback(new Error('请输入正确的金额'))
    }
  } else if (form.value.settleDiscountType == 2) {
    if (value == null || value < 0 || value > 100) {
      return callback(new Error('请输入正确的比例'))
    }
  }
  return callback()
}

// function checkWechat(rule, value, callback) {
//   if (!ticket.value) {
//     return callback(new Error('请绑定微信'))
//   }
//   return callback()
// }

const dialogType = ref('')
function open(type, id) {
  dialogType.value = type
  title.value = type === 'add' ? '新增渠道' : '修改渠道信息'
  if (type == 'edit') {
    getDetail(id)
    getInfo(id)
    getRecord(id)
  }
  dialogVisible.value = true
}

function getDetail(id) {
  disabled.value = true
  getDistributionDetail(id).then(res => {
    form.value.id = id
    form.value.channelName = res.data.channelName
    form.value.phone = res.data.phone
    form.value.settleDiscountType = 2
    form.value.remark = res.data.remark
    form.value.posterName = res.data.posterName || ''
    form.value.brokeRage = res.data.brokeRage
    disabled.value = false
  })
}
function getInfo(id) {
  getPrivacy(id).then(res => {
    form.value.password = res.data.password
    form.value.seedCode = res.data.seedCode
  })
}
function getRecord(id) {
  recordsLoading.value = true
  getDistributionChannelDiscountLogList({
    channelId: id,
  }).then(res => {
    if (res.data) {
      recordsData.value = res.data.rows || []
    }
  })
  .finally(() => recordsLoading.value = false)
}

function close() {
  // formRef.value
  formRef.value.resetFields()
  form.value = {
    channelName: '',
    posterName: '',
    // userName: '',
    phone: '',
    settleDiscountType: 2,
    brokeRage: null,
    remark: '',
  }
  qrcodeSuccess.value = false
  dialogVisible.value = false
}

// function checkSuccess(data) {
//   proxy.$modal.msgSuccess('绑定成功！')
//   ticket.value = data.ticket
//   qrcodeSuccess.value = true
//   formRef.value.clearValidate('wechat')
// }

function onConfirm() {
  formRef.value.validate(valid => {
    if (valid) {
      if (dialogType.value === 'add') {
        const params = { ...form.value }
        if (!params.phone || params.phone == '') {
          params.phone = null
        }
        // if (!qrcodeSuccess.value) {
        //   proxy.$modal.msgError('请使用微信扫码绑定！')
        //   return
        // }
        proxy.$modal.loading('正在创建中')
        saveDistribution({ ...params })
          .then(res => {
            // if (res.data.loginStatus && res.data.loginStatus === 'BINDING') {
            //   ElMessage.warning('该微信已注册，请换个微信/手机号')
            //   qrcodeRef.value?.clear()
            //   qrcodeRef.value?.load()
            //   qrcodeSuccess.value = false
            //   return
            // }
            // if (res.data.loginStatus && res.data.loginStatus === 'EXPIRE') {
            //   ElMessage.warning('二维码已过期')
            //   qrcodeRef.value?.clear()
            //   qrcodeRef.value?.load()
            //   qrcodeSuccess.value = false
            //   return
            // }
            ElMessage.success('创建成功')
            emits('success')
            // qrcodeSuccess.value = false
            close()
          })
          .finally(() => proxy.$modal.closeLoading())
      } else {
        const { phone, ...params } = form.value
        if (phone == '') {
          form.value.phone = null
        }
        editDistribution(form.value)
          .then(res => {
            ElMessage.success('修改成功')
            emits('success')
            close()
          })
          .finally(() => {})
      }
    }
  })
}
function changePassword(value) {
  const numericValue = value.replace(/\D/g, '')
  nextTick(() => {
    form.value.password = numericValue
  })
}

function channelNameChange(value) {
  if (value[0] === ' ') {
    form.value.channelName = value.substring(1)
  }
}
function posterNameChange(value) {
  if (value[0] === ' ') {
    form.value.posterName = value.substring(1)
  }
}

const channelInputLimit = e => {
  const key = e.key
  if (key === 'Enter') e.preventDefault()
  const notAllowList = ['e', '+', '-']
  if (notAllowList.includes(key)) {
    e.returnValue = false
    return false
  }
  return true
}
</script>

<style scoped lang="scss">
:deep(.el-input-number) {
  .el-input__inner {
    text-align: left;
  }
  .el-input__wrapper {
    padding: 0 10px;
  }
}
.wechat-box {
  .code-img {
    min-width: 150px;
    min-height: 150px;
  }
  span {
    color: red;
    font-size: 13px;
  }
}
</style>
