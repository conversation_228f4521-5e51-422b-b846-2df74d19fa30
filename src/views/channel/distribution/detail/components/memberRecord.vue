<template>
  <div class="invitation-record">
    <div class="record-title flex-between">
      <div style="font-weight: 600">成交数：{{ total }}个</div>
      <div class="flex-center">
        <el-select
          v-model="queryParams.memberPackageType"
          clearable
          style="width: 150px; margin-right: 15px"
          @change="handleQuery"
        >
          <el-option v-for="item in memberTypeList" :label="item.label" :value="item.value" />
        </el-select>
        <div class="felx-center input-box">
          <el-input
            v-model="queryParams.keyword"
            @keyup.enter="handleQuery"
            style="width: 350px"
            placeholder="搜索"
            clearable
          />
          <el-icon color="#aaa" size="16" @click="handleQuery"><Search /></el-icon>
        </div>
        <el-button type="primary" @click="resetQuery" v-btn>重置</el-button>
      </div>
    </div>
    <div class="record-list">
      <el-table v-loading="loading" :data="tableList" max-height="400">
        <el-table-column label="序号" type="index" width="80" />
        <el-table-column label="商家编码" prop="memberCode"></el-table-column>
        <el-table-column label="手机号" prop="bizUserPhone"></el-table-column>
        <el-table-column label="微信名" prop="bizUserNickName"></el-table-column>
        <el-table-column label="会员类型" prop="memberPackageType">
          <template v-slot="{ row }">
            {{
              row.memberPackageType === 0
                ? '季度会员'
                : row.memberPackageType === 1
                ? '年度会员'
                : row.memberPackageType === 2
                ? '三年会员'
                : ''
            }}
          </template>
        </el-table-column>
        <el-table-column label="会员购买时间" prop="createTime"></el-table-column>
        <el-table-column label="结算金额" prop="settleAmount"></el-table-column>
        <el-table-column label="实际结算金额" prop="realSettleAmount">
          <template v-slot="{ row }">
            <div v-if="row.settleStatus == 1">{{ row.realSettleAmount }}</div>
            <div v-else>-</div>
          </template>
        </el-table-column>
        <el-table-column label="状态" prop="settleStatus">
          <template v-slot="{ row }">
            <div v-if="row.settleStatus == null">-</div>
            <template v-if="row.settleStatus == 999">
              <el-tag type="warning">可提现</el-tag>
              <div style="font-size: 12px">暂不可提现</div>
            </template>
            <template v-for="item in settleAccountsStatus">
              <el-tag v-if="row.settleStatus == item.value && item.value != 999" :type="item.type">
                {{ item.label }}
              </el-tag>
            </template>
          </template>
        </el-table-column>
        <el-table-column label="结算时间" prop="settleTime">
          <template v-slot="{ row }">
            <span v-if="row.settleStatus != 6">{{ row.settleTime || '-' }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="flex-end" style="margin: 10px">
      <el-pagination
        background
        @size-change="pageChange({ pageNum: 1, pageSize: $event })"
        @current-change="pageChange({ pageNum: $event, pageSize })"
        :current-page="pageNum"
        :page-size="pageSize"
        :page-sizes="pageSizes"
        layout="total, prev, pager, next, jumper"
        :total="total"
      />
    </div>
  </div>
</template>

<script setup>
import { channelList } from '@/api/finance/distribution.js'
import { memberTypeList } from '@/views/channel/distribution/data.js'
import { settleAccountsStatus } from '@/views/finance/data.js'

const pageNum = ref(1)
const pageSize = ref(50)
const pageSizes = [10, 20, 30, 50]
const total = ref(0)

const props = defineProps({
  distriId: {
    type: String,
    default: '',
  },
})

const queryParams = ref({
  memberPackageType: '',
  keyword: '',
})
defineExpose({ handleQuery })
const tableList = ref([])
const loading = ref(false)

function resetQuery() {
  queryParams.value = {
    memberPackageType: '',
    keyword: '',
  }
  pageNum.value = 1
  pageSize.value = 50
  handleQuery()
}

function handleQuery() {
  loading.value = true
  channelList({
    channelId: props.distriId,
    keyword: queryParams.value.keyword,
    memberPackageType: queryParams.value.memberPackageType,
    pageNum: pageNum.value,
    pageSize: pageSize.value,
  })
    .then(res => {
      tableList.value = res.data.rows || []
      total.value = res.data.total || 0
    })
    .finally(() => {
      loading.value = false
    })
}

// 分页跳转
function pageChange(page) {
  pageNum.value = page.pageNum
  pageSize.value = page.pageSize
  handleQuery()
}
</script>

<style scoped lang="scss">
.invitation-record {
  background-color: #fff;
  border-radius: 4px;
  gap: 10px;
  position: relative;
  box-shadow: var(--el-box-shadow-light);
  padding-bottom: 10px;
}
.record-title {
  padding: 13px;
  border-bottom: 1px solid #e8e8e8;
  .input-box {
    position: relative;
    margin-right: 15px;

    :deep(.el-input) {
      .el-input__wrapper {
        padding-right: 32px;
      }
    }

    .el-icon {
      position: absolute;
      top: 0;
      right: 0;
      cursor: pointer;
      width: 32px;
      height: 32px;
    }
  }
}
.record-list {
  padding: 10px;
}
</style>
