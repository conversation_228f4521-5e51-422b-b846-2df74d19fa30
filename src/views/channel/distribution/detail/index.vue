<template>
  <div class="distribution-detail">
    <Header :data="channelInfo" @checkUrlView="showViewDialog" />

    <div class="distribution-btn">
      <el-radio-group v-model="selectVal" size="large" @change="handleSelect">
        <el-radio-button :value="1">邀请记录</el-radio-button>
        <el-radio-button :value="2">会员成交记录</el-radio-button>
      </el-radio-group>
    </div>

    <InvitationRecord
      ref="invitationRecordRef"
      v-if="selectVal === 1"
      :distriId="distriId"
      @handleDialogDetail="handleDialogDetail"
    />
    <MemberRecord ref="memberRecordRef" v-if="selectVal === 2" :distriId="distriId" />
    <el-dialog
      align-center
      style="background: transparent; box-shadow: none; overflow: hidden; text-align: center"
      v-model="showUrlViewDialog"
      width="650px"
      :show-close="false"
    >
      <template #header>
        <div class="flex-end">
          <el-icon @click="closeUrlViewDialog" style="cursor: pointer" size="32" color="#fff">
            <Close />
          </el-icon>
        </div>
      </template>
      <img style="width: 100%" :src="qrCodeDataUrl" alt="" />
      <!-- <div v-if="imgUrl">
        <img :src="imgUrl" alt="" />
        <div style="color: #fff">右键可保存图片</div>
      </div>
      <div v-else v-loading="loadingImg"></div>
      <div class="qrcode-dialog" id="wn-invite-qrcode-box" style="text-align: center">
        <img class="bg-img" :src="pathHead + '/static/img/invite-C9C_dORL.png'" alt="" />
        <div class="dialog-bottom flex-center">
          <div class="dialog-bottom-right flex-column">
            <div class="left-top">种草码：{{ channelInfo?.seedCode }}</div>
            <div class="img-box">
              <img
                v-if="qrCodeDataUrl"
                v-loading="loadingImg"
                style="width: 120px"
                :src="qrCodeDataUrl"
                alt=""
              />
            </div>
            <div class="left-bottom">扫码添加，以备不时之需</div>
          </div>
        </div>
      </div> -->
    </el-dialog>

    <el-dialog title="查看账号信息" v-model="showDetailDialog" width="600px" align-center :showClose="false">
      <Title style="margin: 0">基础信息</Title>
      <div class="account-info">
        <el-row>
          <el-col :span="12">姓名：{{ detailData.name }}</el-col>
          <el-col :span="12">账号：{{ detailData.id }}</el-col>
        </el-row>
        <el-row>
          <el-col :span="12">绑定微信：{{ detailData.nickName }}</el-col>
          <el-col :span="12">手机号：{{ detailData.phone }}</el-col>
        </el-row>
        <el-row>
          <el-col :span="24">公司名称：{{ detailData.businessName }}</el-col>
          <!-- <el-col :span="12">公司名称：{{ detailData.businessName }}</el-col> -->
        </el-row>
        <el-row>
          <el-col :span="12">
            账号类型：
            <template v-for="dict in identityList" :key="dict.value">
              <span v-if="dict.value == detailData.accountType">{{ dict.label }}</span>
            </template>
          </el-col>
          <el-col :span="12">
            重要程度：
            <template v-for="dict in customerTypeList" :key="dict.value">
              <span v-if="dict.value == detailData.customerType">{{ dict.label }}</span>
            </template>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">对接客服：{{ detailData.waiterName }}</el-col>
          <el-col :span="12">注册时间：{{ detailData.createTime }}</el-col>
        </el-row>
      </div>
      <template #footer>
        <div class="flex-end btn">
          <slot name="button">
            <el-button v-btn plain @click="showDetailDialog = false" round>关闭</el-button>
          </slot>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import Header from '@/views/channel/distribution/detail/components/header.vue'
import InvitationRecord from '@/views/channel/distribution/detail/components/invitationRecord.vue'
import MemberRecord from '@/views/channel/distribution/detail/components/memberRecord.vue'
import { bizUserList } from '@/api/merchant/merchant'
import { identityList, customerTypeList } from '@/views/merchant/data.js'
import { getBizUserList } from '@/api/channel/distribution'
import Title from '@/components/Public/title'
import { getDistributionDetail, previewPoster } from '@/api/channel/distribution'
import html2canvas from 'html2canvas'
import { nextTick, onMounted } from 'vue'
const route = useRoute()

const showDetailDialog = ref(false)
//账号信息弹窗
const { proxy } = getCurrentInstance()
const pathHead = proxy.$picUrl
const detailData = ref({})
const loadingImg = ref(false)
const selectVal = ref(1)
const channelInfo = ref({
  channelName: '',
  userName: '',
  brokeRage: null,
  phone: '',
  remark: '',
  seedCode: '',
})
const distriId = ref('')

const showUrlViewDialog = ref(false)
const qrCodeDataUrl = ref('')

function getDetail() {
  const { id } = route.params
  if (id) {
    distriId.value = id
    getHeadDetail()
  }
}

function getHeadDetail() {
  getDistributionDetail(distriId.value).then(res => {
    channelInfo.value = res.data
  })
  previewPoster(distriId.value).then(res => {
    const imageUrl = URL.createObjectURL(res.data)
    qrCodeDataUrl.value = imageUrl
  })
}

const invitationRecordRef = ref(null)
const memberRecordRef = ref(null)

function handleSelect(val) {
  selectVal.value = val
  val === 1 ? invitationRecordRef.value.handleQuery() : memberRecordRef.value.handleQuery()
}

function showViewDialog(url) {
  loadingImg.value = true
  showUrlViewDialog.value = true
  // qrCodeDataUrl.value = url
  // nextTick(() => {
  //   doChangeImg()
  // })
}
function closeUrlViewDialog() {
  showUrlViewDialog.value = false
  // qrCodeDataUrl.value = ''
}

function handleDialogDetail(userId) {
  showDetailDialog.value = true
  getBizUserList({ bizUserId: userId }).then(res => {
    detailData.value = res.data.rows[0]
  })
}

const imgUrl = ref('')
function doChangeImg() {
  loadingImg.value = true
  const dom = document.querySelector('#wn-invite-qrcode-box')
  html2canvas(dom, { scale: 1.1, useCORS: true, backgroundColor: 'transparent' })
    .then(canvas => {
      // 创建一个图片元素
      var img = document.createElement('img')
      img.src = canvas.toDataURL('image/png')
      imgUrl.value = img.src
      // dom.style.display = 'none'
    })
    .finally(() => {
      loadingImg.value = false
    })
}

onMounted(() => {
  invitationRecordRef.value.handleQuery()
})

getDetail()
</script>

<style scoped lang="scss">
.account-info {
  :deep(.el-row) {
    margin: 10px 0 10px 12px;
  }
}

:deep(.el-radio-group) {
  .el-radio-button__inner {
    box-shadow: 0 0 0 0;
    font-size: 16px;
    padding: 0;
    margin-right: 20px;
    border: none;
  }
  .el-radio-button.is-active {
    .el-radio-button__inner {
      border: none;
      color: var(--el-color-primary);
      background-color: #fff;
      box-shadow: none;
    }
  }
}

.distribution-detail {
  padding: 20px;
}
.distribution-btn {
  margin: 10px 0;
  box-shadow: none;
}
.qrcode-dialog {
  position: absolute;
  top: 100000px;
  // top: -600px;
  // left: 10000px;
  .bg-img {
    position: relative;
    width: 357px;
    height: 450px;
  }
  .qrcode-img {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }
  .dialog-bottom {
    padding: 5px 0 18px 0;
    // margin: -5px 51px 0 52px;
    margin-top: -5px;
    // width: 100%;
    background: #ebf5fe;
    border-bottom-left-radius: 12px;
    border-bottom-right-radius: 12px;
    display: flex;
    align-items: center;
    &-left {
      margin-left: 22px;
      .left-top {
        font-size: 1rem;
        color: #09172f;
      }
      .left-bottom {
        margin-top: 5px;
        color: #6a7484;
        font-size: 0.75rem;
      }
    }
    &-right {
      // flex: 1;
      // width: 77px;
      // height: 77px;
      // margin-right: 18px;
      .left-top {
        font-size: 1rem;
      }
      .img-box {
        margin-top: 0.5rem;
        width: 120px;
      }
      .left-bottom {
        font-size: 0.7rem;
      }
    }
  }
}
</style>
