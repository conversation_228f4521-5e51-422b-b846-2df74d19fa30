<template>
  <div class="distribution-page">
    <div class="page-header">
      <div class="page-header__item">
        <div class="item-number">{{ statisticsData.channelNum }}</div>
        <div class="item-text">分销渠道 (个)</div>
      </div>
      <div class="page-header__item">
        <div class="item-number">{{ statisticsData.uniqueVisitor }}</div>
        <div class="item-text">独立访客 (人)</div>
      </div>
      <div class="page-header__item">
        <div class="item-number">{{ statisticsData.registerNum }}</div>
        <div class="item-text">邀请注册 (个)</div>
      </div>
      <div class="page-header__item">
        <div class="item-number">{{ statisticsData.memberNum }}</div>
        <div class="item-text">会员成交 (个)</div>
      </div>
      <div class="page-header__item">
        <div class="item-number">{{ statisticsData.unSettleAmount }}</div>
        <div class="item-text">待结算金额 (元)</div>
      </div>
    </div>
    <!-- <div class="page-search" style="width: 100%;"> -->
    <el-row class="page-search">
      <div class="page-search margin-bottom-20" style="min-width: 350px">
        日期范围
        <el-radio-group v-model="statusDays" style="margin-left: 10px" @change="changeStatusDays">
          <el-radio-button value="1">今天</el-radio-button>
          <el-radio-button value="2">近7天</el-radio-button>
          <el-radio-button value="3">近30天</el-radio-button>
          <el-radio-button value="4">全部</el-radio-button>
        </el-radio-group>
      </div>
      <div class="margin-bottom-20">
        <el-date-picker
          v-model="placeOrderTime"
          format="YYYY/M/D HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="datetimerange"
          range-separator="-"
          unlink-panels
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
          @change="changeTime"
          :shortcuts="shortcuts"
        ></el-date-picker>
      </div>
      <div class="margin-bottom-20">
        <SelectLoad
          v-model="queryParams.createId"
          :request="getCreateUserList"
          :requestCallback="res => res.data.rows"
          :totalCallback="res => res.data.rows.length"
          keyValue="userId"
          keyLabel="userName"
          keyWord="userName"
          placeholder="请选择创建人"
          style="margin: 0 15px; min-width: 250px"
          @change="init"
        />
      </div>
      <!-- <div class="margin-bottom-20">
        <el-select
          v-model="queryParams.isSettle"
          clearable
          placeholder="请选择结算状态"
          style="min-width: 250px; margin-right: 15px"
          @change="init"
        >
          <el-option label="有" value="1" />
          <el-option label="无" value="0" />
        </el-select>
      </div> -->
      <div class="page-btn margin-bottom-20" style="display: flex; flex: 1">
        <div class="input-box" style="width: 280px">
          <el-input v-model="queryParams.keyword" @keyup.enter="init" placeholder="搜索" clearable />
          <el-icon color="#aaa" size="16" @click="init"><Search /></el-icon>
        </div>
        <el-button type="primary" @click="resetQuery" v-btn>重置</el-button>
      </div>
    </el-row>
    <!-- </div> -->
    <div class="page-btn flex-left-center">
      <div>
        <el-button type="primary" v-btn @click="handleAdd('add')" v-hasPermi="['channel:distribution:add']">
          新增渠道
        </el-button>
        <el-button
          type="primary"
          plain
          v-btn
          @click="handleDiscountDialog"
          v-hasPermi="['system:config:editMemberDiscount']"
        >
          修改会员折扣
        </el-button>
        <!-- <DownloadBtn
          v-hasPermi="['channel:distribution:download:all']"
          type="success"
          plain
          :url="`/biz/marketing/center/distribution/download/poster/all`"
          text="下载全部海报"
          loadingText="下载中"
          message="确认下载"
          :isAsnyc="true"
          :config="{
            timeout: 30000, // 请求超时时间 30s
          }"
        /> -->
      </div>
      <div class="number-box">
        <span style="margin-right: 20px">邀请企微数：{{ listStatistics.addWeChatNum }}</span>
        <span style="margin-right: 20px">邀请注册数：{{ listStatistics.registerNum }}</span>
        <span style="margin-right: 20px">会员成交数：{{ listStatistics.memberNum }}</span>
        <span>会员总金额：{{ listStatistics.memberAmount }}元</span>
      </div>
      <!-- <div class="flex-center">
        <el-select
          v-model="queryParams.isSettle"
          clearable
          placeholder="请选择结算状态"
          style="width: 150px; margin-right: 15px"
          @change="init"
        >
          <el-option label="有" value="1" />
          <el-option label="无" value="0" />
        </el-select>
        <div class="input-box">
          <el-input
            v-model="queryParams.keyword"
            @keyup.enter="init"
            style="width: 400px"
            placeholder="搜索"
            clearable
          />
          <el-icon color="#aaa" size="16" @click="init"><Search /></el-icon>
        </div>
        <el-button type="primary" @click="resetQuery" v-btn>重置</el-button>
      </div> -->
    </div>
    <div class="page-table">
      <el-table v-loading="loading" ref="tableRef" :data="tableList" @sort-change="handleSortChange">
        <el-table-column
          label="渠道名称"
          prop="channelName"
          width="130px"
          show-overflow-tooltip
          align="center"
        ></el-table-column>
        <el-table-column
          label="海报名称"
          prop="posterName"
          width="130px"
          show-overflow-tooltip
          align="center"
        >
          <template v-slot="{ row }">
            <div>
              {{ row.posterName || '-' }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="种草码"
          show-overflow-tooltip
          prop="seedCode"
          align="center"
          width="70px"
        ></el-table-column>
        <el-table-column
          label="独立访客(UV)"
          show-overflow-tooltip
          prop="uniqueVisitor"
          align="center"
          width="90px"
        ></el-table-column>
        <el-table-column
          label="邀请企微数"
          prop="addWeChatNum"
          align="center"
          sortable="custom"
          width="90px"
        ></el-table-column>
        <el-table-column
          label="邀请注册数"
          prop="registerNum"
          align="center"
          sortable="custom"
          width="90px"
        ></el-table-column>
        <el-table-column
          label="会员成交数"
          prop="memberNum"
          align="center"
          sortable="custom"
          width="90px"
        ></el-table-column>
        <el-table-column
          label="会员总金额(元)"
          prop="memberOrderAmount"
          align="center"
          sortable="custom"
          width="115px"
        ></el-table-column>
        <el-table-column label="结算方案" prop="brokeRage" align="center" width="80px" sortable="custom">
          <template v-slot="{ row }">
            <span>
              {{
                row.settleDiscountType == 1
                  ? `固定金额${row.brokeRage >= 0 ? row.brokeRage : '-'}元`
                  : `固定比例${row.brokeRage >= 0 ? row.brokeRage : '-'}%`
              }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          label="订单成交额(元)"
          prop="realPayAmount"
          align="center"
          sortable="custom"
          width="115px"
        ></el-table-column>
        <el-table-column
          label="待结算金额(元)"
          prop="unSettleAmount"
          align="center"
          sortable="custom"
          width="115px"
        ></el-table-column>
        <el-table-column
          label="已结算金额(元)"
          prop="realSettleAmount"
          align="center"
          sortable="custom"
          width="115px"
        ></el-table-column>
        <el-table-column label="分销状态" prop="status" width="80px" align="center">
          <template v-slot="{ row, $index }">
            <el-switch
              :active-value="0"
              :inactive-value="1"
              v-model="row.status"
              @click="changeStatus(row.id, row.status, $index)"
            />
          </template>
        </el-table-column>
        <el-table-column label="创建人" prop="createBy" min-width="180px" align="center">
          <template v-slot="{ row, $index }">
            <div>
              <div>{{ row.createBy }}</div>
              <div>{{ row.createTime }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="账号ID" prop="id" width="70px" align="center"></el-table-column>
        <el-table-column label="操作" fixed="right" width="110px" align="center" class="opt-cell">
          <template v-slot="{ row }">
            <el-button
              v-btn
              link
              type="primary"
              @click="handleDetail('/channel/distribution/detail/' + row.id)"
              v-hasPermi="['channel:distribution:detail']"
            >
              查看
            </el-button>
            <el-button
              v-btn
              link
              type="primary"
              @click="handleAdd('edit', row.id)"
              v-hasPermi="['channel:distribution:edit']"
            >
              编辑
            </el-button>
            <DownloadBtn
              v-if="row.status == 0"
              v-hasPermi="['channel:distribution:download']"
              type="primary"
              link
              :url="`/biz/marketing/center/distribution/download/poster/${row.id}`"
              text="下载海报"
              loadingText="下载中"
              message="确认下载"
            />
            <!-- <CopyButton class="btn" plain  v-hasPermi="['channel:distribution:copy']" :copy-content="handleCopy(row)">
              复制账号密码
            </CopyButton> -->
            <el-button
              v-btn
              link
              type="primary"
              @click="handleCopy(row)"
              v-hasPermi="['channel:distribution:copy']"
            >
              复制账/密
            </el-button>
            <!-- <span id="dom1">6546465</span> -->
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="flex-end" style="margin: 10px 0 60px">
      <PaginationFloatBar :current-page="pageNum" :page-size="pageSize" @update:current-page="handlePageChange" :total="total">
        <DownloadBtn
          v-hasPermi="['channel:distribution:download:all']"
          type="success"
          plain
          :url="`/biz/marketing/center/distribution/download/poster/all`"
          text="下载全部海报"
          loadingText="下载中"
          message="确认下载"
          :isAsnyc="true"
          :config="{
            timeout: 30000, // 请求超时时间 30s
          }"
        />
      </PaginationFloatBar>
      <!-- <el-pagination
        background
        @size-change="pageChange({ pageNum: 1, pageSize: $event })"
        @current-change="pageChange({ pageNum: $event, pageSize })"
        :current-page="pageNum"
        :page-size="pageSize"
        :page-sizes="pageSizes"
        layout="total, prev, pager, next, jumper"
        :total="total"
      /> -->
    </div>
    <DistriDialog ref="distriDialogRef" @success="init" />

    <UpdateMemberDiscount ref="updateMemberDiscountRef" />
  </div>
</template>

<script setup>
import DistriDialog from '@/views/channel/distribution/dialog/distriDialog.vue'
import UpdateMemberDiscount from '@/views/channel/distribution/dialog/updateMemberDiscount.vue'
import DownloadBtn from '@/components/Button/DownloadBtn'
import {
  getDistributionList,
  getDistributionStatistics,
  updateStatus,
  getPrivacy,
  getMemberDiscount,
  getDistributionListStatistics,
} from '@/api/channel/distribution'
import { checkPermi } from '@/utils/permission'
import SelectLoad from '@/components/Select/SelectLoad.vue'
// import { listUser } from '@/api/system/user'
import { getCreateUserList } from '@/api/channel/distribution'
// import CopyButton from '@/components/Button/CopyButton.vue'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import { isSafari, isIOS } from '@/utils/index'
const { proxy } = getCurrentInstance()

const router = useRouter()

const distriDialogRef = ref(null)
const loading = ref(false)
const pageNum = ref(1)
const pageSize = ref(20)
const pageSizes = [10, 20, 30, 50]
const total = ref(0)
const searchText = ref('')
const statisticsData = ref({
  channelNum: 0,
  registerNum: 0,
  memberNum: 0,
  unSettleAmount: 0,
  uniqueVisitor: 0,
})
const queryParams = ref({
  isSettle: '',
  keyword: '',
  createId: '',
  orderByType: null,
  isAsc: null,
  endTime: '',
  startTime: '',
})
const listStatistics = ref({
  channelNum: 0,
  registerNum: 0,
  memberNum: 0,
  unSettleAmount: 0,
  addWeChatNum: 0,
})

const shortcuts = [
  {
    text: '今天',
    value: () => {
      const start = new Date()
      start.setHours(0, 0, 0, 0)
      const end = new Date()
      end.setHours(23, 59, 59, 59)
      return [start, end]
    },
  },
]

const statusDays = ref('4')
const placeOrderTime = ref([])
const tableList = ref([])
const tableRef = ref(null)
const updateMemberDiscountRef = ref()

function handleAdd(type, id = '') {
  distriDialogRef.value.open(type, id)
}
function handleDiscountDialog() {
  updateMemberDiscountRef.value.open()
}
// 详情
// function handleDetail(id) {
//   router.push(`/channel/distribution/detail/${id}`)
// }
function handleDetail(path) {
  const { href } = router.resolve({ path })
  window.open(href, '_blank')
}

// 分页跳转
function pageChange(page) {
  pageNum.value = page.pageNum
  pageSize.value = page.pageSize
  init()
}
function handlePageChange(num) {
  pageNum.value = num
  init()
}
function handleQuery() {
  loading.value = true
  getDistributionList({ ...queryParams.value, pageNum: pageNum.value, pageSize: pageSize.value })
    .then(res => {
      tableList.value = res.data.rows || []
      total.value = res.data.total || 0
    })
    .finally(() => {
      loading.value = false
    })
}

function handleCopy(row) {
  getAccountInfo(row.id)
}
function getAccountInfo(id) {
  getPrivacy(id).then(res => {
    let str = ''
    str += 'hello，蜗牛种草官 ' + res.data.channelName + '\n'
    str += '请牢记你的种草码/账号：' + res.data.seedCode + '  密码：' + res.data.password + '\n'
    str += res.data.channelUrl + '\n'
    str += '登录后可随时查看你的种草收益~'
    proxy.$modal
      .alert(`<div class="template-pre">${str}</div>`, '', {
        confirmButtonText: '确定复制',
        dangerouslyUseHTMLString: true,
      })
      .then(res => {
        copy(str)
      })
  })
}
function copy(str) {
  let isios = isIOS()
  if (!navigator.clipboard || isios || isSafari()) {
    const dom = document.createElement('textarea')
    dom.value = str
    dom.style.position = 'fixed'
    dom.style.top = '-500px'
    dom.style.right = '-500px'
    document.body.appendChild(dom)
    dom.select()
    if (isios) {
      iosCopyToClipboard(dom)
    } else {
      let successful = document.execCommand('copy')
      successful ? ElMessage.success('已复制到剪贴板!') : ElMessage.warning('您的浏览器不支持复制功能!')
      document.body.removeChild(dom)
    }
  } else {
    navigator.clipboard
      .writeText(str)
      .then(function () {
        ElMessage.success('已复制到剪贴板')
      })
      .catch(function (err) {
        ElMessage.error('Failed to copy text:')
        console.error('Failed to copy text: ', err)
      })
  }
}

function iosCopyToClipboard(el) {
  var oldContentEditable = el.contentEditable,
    oldReadOnly = el.readOnly,
    range = document.createRange()

  el.contentEditable = true
  el.readOnly = false
  range.selectNodeContents(el)

  var s = window.getSelection()
  s.removeAllRanges()
  s.addRange(range)

  el.setSelectionRange(0, 999999)

  el.contentEditable = oldContentEditable
  el.readOnly = oldReadOnly

  let successful = document.execCommand('copy')
  successful ? ElMessage.success('已复制到剪贴板!') : ElMessage.warning('您的浏览器暂不支持复制')
  document.body.removeChild(el)
}

//统计
function handleDistributionStatistics() {
  getDistributionStatistics().then(res => {
    statisticsData.value = res.data
  })
}

//修改状态
function changeStatus(id, status, index) {
  if (!checkPermi(['channel:distribution:status'])) {
    tableList.value[index].status = status == 0 ? 1 : 0
    return ElMessage.warning('暂无权限')
  }
  updateStatus({ id, status: status == 0 ? 0 : 1 }).then(() => {
    ElMessage.success('修改成功')
  })
}

function resetQuery() {
  queryParams.value = { isSettle: '', keyword: '', createId: '' }
  searchText.value = ''
  pageNum.value = 1
  pageSize.value = 20
  statusDays.value = '4'
  placeOrderTime.value = []
  tableRef.value?.clearSort()
  init()
}

function handleSortChange(data) {
  if (data.prop == 'addWeChatNum') {
    queryParams.value.orderByType = '1'
  } else if (data.prop == 'registerNum') {
    queryParams.value.orderByType = '2'
  } else if (data.prop == 'memberNum') {
    queryParams.value.orderByType = '3'
  } else if (data.prop == 'brokeRage') {
    queryParams.value.orderByType = '4'
  } else if (data.prop == 'realPayAmount') {
    queryParams.value.orderByType = '5'
  } else if (data.prop == 'unSettleAmount') {
    queryParams.value.orderByType = '6'
  } else if (data.prop == 'settleAmount') {
    queryParams.value.orderByType = '7'
  } else if (data.prop == 'realSettleAmount') {
    queryParams.value.orderByType = '8'
  } else if (data.prop == 'memberOrderAmount') {
    queryParams.value.orderByType = '9'
  }
  console.log(data.order)
  data.order == 'ascending' ? (queryParams.value.isAsc = '1') : (queryParams.value.isAsc = '')
  if (!data.order) queryParams.value.orderByType = null
  handleQuery()
}

//获取范围日期
function getBeforeDate(n, type) {
  var n = n
  var d = new Date()
  var year = d.getFullYear()
  var mon = d.getMonth() + 1
  var day = d.getDate()
  if (day <= n) {
    if (mon > 1) {
      mon = mon - 1
    } else {
      year = year - 1
      mon = 12
    }
  }
  d.setDate(d.getDate() - n)
  year = d.getFullYear()
  mon = d.getMonth() + 1
  day = d.getDate()
  var s = year + '-' + (mon < 10 ? '0' + mon : mon) + '-' + (day < 10 ? '0' + day : day)
  return type == 'one' ? s + ' 00:00:00' : s + ' 23:59:59'
}
function changeStatusDays(val) {
  let timeList = []
  if (val == 1) {
    timeList = [getBeforeDate(0, 'one'), getBeforeDate(0, 'two')]
  } else if (val == 2) {
    timeList = [getBeforeDate(7, 'one'), getBeforeDate(0, 'two')]
  } else if (val == 3) {
    timeList = [getBeforeDate(30, 'one'), getBeforeDate(0, 'two')]
  } else {
    timeList = []
  }
  placeOrderTime.value = []
  queryParams.value.startTime = timeList[0] ? timeList[0] : ''
  queryParams.value.endTime = timeList[0] ? timeList[1] : ''
  init()
}
function changeTime() {
  if (placeOrderTime.value) {
    statusDays.value = null
    queryParams.value.startTime = placeOrderTime.value[0]
    queryParams.value.endTime = placeOrderTime.value[1]
  } else {
    statusDays.value = '4'
    queryParams.value.startTime = ''
    queryParams.value.endTime = ''
  }
  init()
}

function handleListStatistics() {
  getDistributionListStatistics({
    ...queryParams.value,
    pageNum: pageNum.value,
    pageSize: pageSize.value,
  }).then(res => {
    listStatistics.value = res.data
  })
}
function init() {
  handleQuery()
  handleDistributionStatistics()
  handleListStatistics()
}

init()
</script>

<style scoped lang="scss">
.cell {
  .el-button + .el-button {
    margin-left: 0;
  }
}
:deep(.el-input-number) {
  .el-input__inner {
    text-align: left;
  }
}
.distribution-page {
  padding: 20px 20px 0 20px;
}
.margin-bottom-20 {
  margin-bottom: 20px;
}
.page-search {
  display: flex;
  align-items: center;
}
.page-header {
  display: flex;
  // justify-content: space-between;
  margin-bottom: 20px;
  gap: 0 50px;

  &__item {
    border-radius: 9px;
    padding: 20px 0;
    text-align: center;
    width: calc(100% / 4);
    box-shadow: var(--el-box-shadow-light);
    .item-number {
      font-weight: 600;
      color: #666;
      font-size: 28px;
    }
    .item-text {
      margin-top: 10px;
      font-size: 14px;
      color: #999;
    }
  }
}
.flex-left-center {
  display: flex;
  align-items: center;
}
.page-btn {
  .input-box {
    margin-right: 15px;
    position: relative;

    :deep(.el-input) {
      .el-input__wrapper {
        padding-right: 32px;
      }
    }

    .el-icon {
      position: absolute;
      top: 0;
      right: 0;
      cursor: pointer;
      width: 32px;
      height: 32px;
    }
  }
  .number-box {
    margin-left: 50px;
  }
}
.page-table {
  margin-top: 15px;
}
</style>
