<template>
  <div>
    <div class="btn-box">
      <el-upload
        :file-list="fileList"
        action=""
        list-type="picture"
        :auto-upload="false"
        :show-file-list="false"
        :on-change="handleChange"
      >
        <el-button v-btn type="primary">上传裁剪（可编辑）</el-button>
      </el-upload>
      <el-upload
        :file-list="fileList"
        action=""
        list-type="picture"
        :auto-upload="false"
        :show-file-list="false"
        :on-change="handleChange2"
      >
        <el-button v-btn type="primary">上传裁剪（不可编辑）</el-button>
      </el-upload>
    </div>
    <div class="img-box">
      <div>图片预览（原/新）：</div>
      <img :src="imgFile.url" alt="">
      <span> => </span>
      <img :src="nImgFile.url" alt="">
    </div>
    <CropperDialog ref="CropperDialogRef" :img="imgFile" :is-show-action="isShowAction" @confirm="onConfirm" />
  </div>
</template>

<script setup>
import CropperDialog from '@/components/Cropper/cropperDialog'

const CropperDialogRef = ref()

const fileList = ref([])
const imgFile = ref({})
const nImgFile = ref({})
const isShowAction = ref(true)

function handleChange(file) {
  // console.log(file);
  imgFile.value = file
  isShowAction.value = true
  CropperDialogRef.value.open()
}

function handleChange2(file) {
  // console.log(file);
  imgFile.value = file
  isShowAction.value = false
  CropperDialogRef.value.open()
}

function onConfirm(img) {
  nImgFile.value = img
}
</script>

<style scoped lang="scss">
.btn-box {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: center;
  justify-content: flex-start;
  align-content: center;
  gap: 10px;
  margin: 10px;
}
.img-box {
  margin: 10px;
  
  img {
    width: 200px;
  }
}
</style>