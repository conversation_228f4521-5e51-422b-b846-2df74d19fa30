<template>
  <div>
    <ElTablePage
      style="padding: 20px"
      ref="tableRef"
      :columns="columns"
      :data="tableData"
      :loading="tableLoading"
      :currentPage="pageNum"
      :pageSize="pageSize"
      :total="total"
      :tableAction="{
        width: '170',
        fixed: 'right',
      }"
      @page-change="pageChange"
      row-key="id"
    >
      <template #tableHeader>
        <div style="margin-bottom: 10px">
          <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="120px" @submit.prevent>
            <el-form-item label="关键字搜索：">
              <el-input
                v-model="queryParams.name"
                clearable
                style="width: 300px"
                placeholder="请搜索关键字"
              ></el-input>
            </el-form-item>
            <el-form-item label="问题类型：">
              <el-select v-model="queryParams.type" clearable placeholder="请选择类型" style="width: 200px">
                <el-option
                  v-for="item in textType"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button v-btn type="primary" native-type="submit" @click="handleQuery">搜索</el-button>
              <el-button v-btn @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
          <el-button type="primary" v-btn v-hasPermi="['system:help:add']" @click="handleTextInfo('add')">
            新增内容
          </el-button>
        </div>
      </template>
      <template #status="{ row }">
        <div class="one-ell" style="text-align: center">
          <el-switch v-model="row.isShow" @click="handleStatusChange(row)" />
        </div>
      </template>

      <template #tableAction="{ row }">
        <el-button
          v-btn
          link
          type="primary"
          @click="handleTextInfo('detail', row.id)"
          v-hasPermi="['system:help:detail']"
        >
          查看
        </el-button>
        <el-button
          v-btn
          link
          type="primary"
          @click="handleTextInfo('edit', row.id)"
          v-hasPermi="['system:help:edit']"
        >
          修改
        </el-button>
        <el-button v-btn link type="primary" @click="deleteTextInfo(row.id)" v-hasPermi="['system:help:delete']">
          删除
        </el-button>
      </template>
    </ElTablePage>
    <TextDialog ref="textDialogRef" @success="init" />
  </div>
</template>

<script setup>
import ElTablePage from '@/components/Table/ElTablePage'
import { onMounted, ref } from 'vue'
import TextDialog from '@/views/help/components/textDialog.vue'
import { textType } from '@/views/help/data.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import { checkPermi } from '@/utils/permission'

import { getHelpList, deleteHelpData, updateStatus, getHelpDetail } from '@/api/help/help.js'

const textDialogRef = ref(null)

const columns = [
  {
    prop: 'name',
    label: '标题',
    ellipsis: true,
  },
  {
    prop: 'type',
    label: '类型',
    handle: data => {
      let item = textType.find(item => item.value == data)
      if (item) {
        return item.label
      }
      return '-'
    },
  },
  {
    prop: 'updateTime',
    label: '更新时间',
  },
  {
    slot: 'status',
    prop: 'status',
    label: '状态',
  },
  {
    prop: 'sort',
    label: '排序值',
  },
]

const tableData = ref([])
const pageNum = ref(1)
const pageSize = ref(20)
const total = ref(0)
const tableLoading = ref(false)

const queryParams = ref({
  name: '',
  status: '',
  type: '',
})

function resetQuery() {
  queryParams.value = {
    name: '',
    status: '',
    type: '',
  }
  init()
}

function init() {
  handleQuery()
}

function handleQuery() {
  tableLoading.value = true
  getHelpList({
    pageNum: pageNum.value,
    pageSize: pageSize.value,
    ...queryParams.value,
  })
    .then(res => {
      tableData.value = res.data.rows || []
      total.value = res.data.total || 0
      if (tableData.value.length > 0) {
        tableData.value.forEach(item => {
          item.isShow = item.status == 0 ? true : false
        })
      }
    })
    .finally(() => {
      tableLoading.value = false
    })
}
// 分页跳转
function pageChange(page) {
  pageNum.value = page.currentPage
  pageSize.value = page.pageSize
  init()
}

function handleTextInfo(type, id = null) {
  // TODO: 获取详情数据
  // if (type != 'add') {
  //   getHelpDetail(id).then(res => {
  //     if (!res.data) {
  //       ElMessage.warning('暂无此数据~')
  //       init()
  //       return
  //     }
  //     textDialogRef.value.open(type, id)
  //   })
  // } else {
  textDialogRef.value.open(type, id)
  // }
}

function handleStatusChange(row) {
  if(!checkPermi(['system:help:status'])) {
    row.isShow = !row.isShow
    return ElMessage.warning('暂无操作权限')
  } 
  const status = row.status == '0' ? 1 : 0
  updateStatus({ id: row.id, status: status }).then(() => {
    ElMessage({
      message: '更改内容状态成功',
      type: 'success',
    })
    init()
  })
}

function deleteTextInfo(id) {
  ElMessageBox.confirm('确认删除该文本吗？', '提示').then(() => {
    deleteHelpData({ id }).then(() => {
      ElMessage.success('删除成功')
      init()
    })
  })
}
init()
</script>

<style scoped lang="scss"></style>
