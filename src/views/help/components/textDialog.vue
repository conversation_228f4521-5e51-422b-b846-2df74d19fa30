<template>
  <div>
    <el-dialog
      v-model="dialogVisible"
      :title="textTitle"
      :close-on-click-modal="false"
      width="800px"
      align-center
      @close="close"
    >
      <el-form
        ref="textFormRef"
        :model="textForm"
        label-width="80px"
        :rules="rules"
        :disabled="textTitle === '查看问题'"
      >
        <el-form-item label="类型" prop="type">
          <el-radio-group v-model="textForm.type">
            <el-radio-button
              v-for="item in textType"
              :key="item.value"
              :label="item.value"
              :value="item.value"
            >
              {{ item.label }}
            </el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="内容标题" prop="name">
          <el-input v-model="textForm.name" style="width: 50%" placeholder="输入内容" maxlength="50" />
        </el-form-item>
        <el-form-item label="排序值" prop="sort">
          <el-input-number
            title=""
            style="width: 50%"
            :min="0"
            :max="99999"
            :precision="0"
            :step="1"
            v-model="textForm.sort"
            placeholder="输入内容"
            :controls="false"
            @keydown="channelInputLimit"
          />
          <div style="margin-left: 5px">数值越大排序越前</div>
        </el-form-item>
        <el-form-item label="内容详情" prop="content">
          <WangEditor v-if="dialogVisible" v-model="textForm.content" :readOnly="textTitle === '查看问题'" />
          <!-- <div class="content-detail" v-html="textForm.content" v-if="textTitle === '查看问题'"></div>
          <WangEditor v-model="textForm.content" /> -->
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="flex-start" style="margin-left: 80px" v-if="textTitle != '查看问题'">
          <el-button v-btn @click="close">取消</el-button>
          <el-button v-btn type="primary" @click="submitForm">保存</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
// Wangeditor富文本编辑器
import WangEditor from '@/components/WangEditor'
import { ref } from 'vue'
import { textType } from '@/views/help/data.js'
import { addHelpData, editHelpData, getHelpDetail } from '@/api/help/help.js'
import { ElMessage } from 'element-plus'

const textFormRef = ref(null)
const editorRef = ref(null)
const dialogVisible = ref(false)
const textTitle = ref('')
const textDialogType = ref('')
const textForm = ref({
  type: 1,
  name: '',
  content: '',
  sort: 0,
})
const emits = defineEmits(['success'])
defineExpose({ open, close })

const rules = {
  name: [{ required: true, trigger: 'blur', message: '请输入内容标题' }],
  sort: [{ required: true, trigger: 'blur', message: '请输入排序值' }],
  content: [{ required: true, validator: checkContent, trigger: 'blur' }],
}

function checkContent(rule, value, callback) {
  const textContent = value?.replace(/<[^<>]+>/g, '').replace(/&nbsp;/gi, '') || '' // 去掉HTML标签
  const isEmpty = textContent.trim() === '' // 检查是否为空
  if (isEmpty) {
    callback(new Error('请输入文字内容'))
  } else {
    callback()
  }
}

function open(type, id = null) {
  textDialogType.value = type
  if (type == 'add') {
    textTitle.value = '新增问题'
  } else {
    textTitle.value = type == 'edit' ? '编辑问题' : '查看问题'
    handleDetail(id)
  }
  dialogVisible.value = true
}

function close() {
  textFormRef.value.resetFields()
  textForm.value = {
    type: 1,
    name: '',
    content: '',
    sort: 0,
  }
  dialogVisible.value = false
}

const channelInputLimit = e => {
  const key = e.key
  const notAllowList = ['e', '+', '-']
  if (notAllowList.includes(key)) {
    e.returnValue = false
    return false
  }
  return true
}

function handleDetail(id) {
  // TODO: 获取详情数据
  getHelpDetail(id).then(res => {
    // if(!res.data) {
    //   ElMessage.warning('暂无数据~')
    //   close()
    //   return
    // }
    if(res.data) textForm.value = res.data
    else ElMessage.warning('数据不存在，请刷新页面~')
    // textForm.value = res.data
  })
}

function submitForm() {
  textFormRef.value.validate(valid => {
    if (valid) {
      if (textDialogType.value === 'add') {
        // TODO: 新增数据
        addHelpData(textForm.value).then(res => {
          ElMessage.success('新增成功')
          emits('success')
          close()
        })
      } else {
        editHelpData(textForm.value).then(res => {
          ElMessage.success('修改成功')
          emits('success')
          close()
        })
      }

      // TODO: 保存数据
    }
  })
}
</script>

<style scoped lang="scss">
@import '@/assets/styles/form-disabled.scss';
:deep(.el-dialog) {
  border-radius: 15px;
}
:deep(.el-input-number) {
  .el-input__inner {
    text-align: left;
    margin-left: -4px;
  }
}
</style>
