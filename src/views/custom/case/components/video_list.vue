<template>
  <div class="video-list">
    <el-icon class="close-icon" @click="$emit('close')"><CircleCloseFilled /></el-icon>
    <div class="title">
      <div
        class="mark"
      ></div>
      {{title}}
    </div>
    <div class="list-box">

      <div v-if="layout == 1" class="img-box max-width"></div>
      <template v-else>
        <div class="img-box"></div>
        <div class="img-box"></div>
        <div class="img-box"></div>
        <div class="img-box"></div>
      </template>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  title: {
    type: String,
  },
  layout: {
    type: [Number, String],
    default: 2,
  },
})
defineEmits(['close'])
</script>

<style scoped lang="scss">
.video-list {
  position: relative;
  cursor: pointer;
  margin-top: 10px;

  .close-icon {
    position: absolute;
    top: -5px;
    right: 3px;
    color: #aaa;
    cursor: pointer;
    font-size: 13px;
    z-index: 99;

    &:hover {
      color: #ff0000;
    }
  }

  .title {
    position: relative;
    color: var(--van-text-color);
    font-size: 14px;
    font-weight: bold;
    display: flex;
    align-items: center;
    margin-left: 10px;

    .mark {
      display: inline-block;
      border-radius: 5px;
      width: 6px;
      height: 14px;
      margin-right: 4px;
      background-color: var(--van-danger-color);
    }
  }
  .list-box {
    padding: var(--van-padding-md) var(--van-padding-xs) var(--van-padding-sm);
    height: auto;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: flex-start;
    gap: 8px;

    .img-box {
      position: relative;
      width: calc(50% - 4px);
      padding-top: calc(50% - 4px);
      flex-shrink: 0;
      border-radius: var(--van-radius-lg);
      overflow: hidden;
      background-color: #d7d7d7;

      &.max-width {
        width: 100%;
        padding-top: 100%;
      }
    }
  }
}
</style>