<template>
  <div class="action-box">
    <div class="flex-between" style="padding-right: 10px">
      <h4>瓷片区</h4>
    </div>
    <div class="edit-box">
      <el-form ref="formRef" style="width: 100%" :model="data" label-width="100px" @submit.prevent>
        <el-card>
          <el-form-item label="板块布局" prop="layout">
            <el-radio-group v-model="data.layout">
              <el-radio value="12">
                <div class="three-box">
                  <div class="text-box">左1右2</div>
                </div>
              </el-radio>
              <el-radio value="11">
                <div class="two-box">
                  <div class="text-box">左1右1</div>
                </div>
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <div class="item-box">
            <div>左1</div>
            <TileForm :form="data.left1" propName="left1" />
            <div v-if="data.layout == '22'">左2</div>
            <TileForm v-if="data.layout == '22'" :form="data.left2" propName="left2" />
            <div>右1</div>
            <TileForm :form="data.right1" propName="right1" />
            <div v-if="data.layout == '12'">右2</div>
            <TileForm v-if="data.layout == '12'" :form="data.right2" propName="right2" />
          </div>
        </el-card>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import TileForm from '@/views/custom/case/components/tile_option_form.vue'

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
})

const formRef = ref(null)

const verify = () => {
  formRef.value.validate()
}

defineExpose({
  verify,
})
</script>

<style scoped lang="scss">
.action-box {
  min-width: 460px;
  padding-right: 0px !important;

  h4 {
    margin-top: 10px;
  }

  .edit-box {
    width: 100%;
    height: 632px;
    overflow-y: overlay;
    padding-right: 10px;

    .el-card {
      margin-bottom: 10px;
    }

    .two-box {
      width: 40px;
      height: 30px;
      background-color: #d7d7d7;
      position: relative;

      &::before {
        content: '';
        display: block;
        width: 1px;
        height: 100%;
        background-color: #fff;
        position: absolute;
        top: 0;
        left: 50%;
      }
    }
    .three-box {
      width: 40px;
      height: 30px;
      background-color: #d7d7d7;
      position: relative;

      &::before {
        content: '';
        display: block;
        width: 1px;
        height: 100%;
        background-color: #fff;
        position: absolute;
        top: 0;
        left: 50%;
      }
      &::after {
        content: '';
        display: block;
        width: 50%;
        height: 1px;
        background-color: #fff;
        position: absolute;
        top: 50%;
        left: 50%;
      }
    }
    .text-box {
      position: absolute;
      left: -4px;
      bottom: -20px;
      color: #d7d7d7;
      line-height: 16px;
      font-size: 14px;
    }
  }
}
</style>
