<template>
  <el-form-item label="图片" :prop="`${propName}.pic`" :rules="rules.pic" :key="form.pic?.length">
    <ViewerImageList
      v-if="form.pic && form.pic.length > 0"
      :data="form.pic"
      :curIndex="index"
      is-preview-all
      :urlName="'picUrl'"
      :show-delete-btn="true"
      @delete="handleFileDelete"
    />
    <PasteUpload v-else :limit="1" :size="1" @success="handleFileChange"></PasteUpload>
    <div style="width: 100%" v-if="!item.pic || item.pic.length == 0">
      请上传1张大小不超过
      <span style="color: #d9001b">1M</span>
      ，格式为
      <span style="color: #d9001b">png/jpg/jpeg</span>
      的图片
    </div>
  </el-form-item>
  <el-form-item label="跳转类型" :prop="`${propName}.type`" :rules="rules.type">
    <el-select v-model="form.type" clearable placeholder="请选择跳转类型" style="width: 100%">
      <el-option v-for="item in skipTypeList" :key="item.value" :label="item.label" :value="item.value" />
    </el-select>
  </el-form-item>
  <el-form-item v-if="form.type == 1" label="分组选择" :prop="`${propName}.group`" :rules="rules.group">
    <el-select v-model="form.group" clearable placeholder="请选择视频分组" style="width: 100%">
      <el-option
        v-for="item in caseGroupList"
        :key="item.id"
        :label="item.name + `(ID:${item.id})`"
        :value="item.id"
      />
    </el-select>
  </el-form-item>
  <el-form-item v-if="form.type == 2" label="视频链接" :prop="`${propName}.link`" :rules="rules.link">
    <el-input v-model="form.link" clearable placeholder="请输入视频链接" />
  </el-form-item>
</template>

<script setup>
import PasteUpload from '@/components/FileUpload/pasteUpload.vue'
import ViewerImageList from '@/components/ImagePreview/viewerImageList.vue'
import { useCaseGroupList } from '@/views/custom/hooks/case.js'
import { skipTypeList } from '@/views/custom/data.js'

const props = defineProps({
  form: {
    type: Object,
    default: () => ({
      pic: [],
      type: '',
      link: '',
      group: '',
    }),
  },
  propName: {
    type: String,
    required: true,
  },
})

const { caseGroupList } = useCaseGroupList()

const rules = {
  pic: [{ required: true, message: '请选择图片', trigger: 'blur' }],
  type: [{ required: true, message: '请选择跳转类型', trigger: 'blur' }],
  group: [{ required: true, message: '请选择视频分组', trigger: 'blur' }],
  link: [{ required: true, message: '请输入视频链接', trigger: 'blur' }],
}

const handleFileDelete = (data, i, curIndex) => {
  props.form.pic.splice(i, 1)
}

const handleFileChange = (data, curIndex) => {
  if (data && data.length > 0) {
    props.form.pic.push(data[0].data)
  }
}
</script>

<style scoped lang="scss"></style>
