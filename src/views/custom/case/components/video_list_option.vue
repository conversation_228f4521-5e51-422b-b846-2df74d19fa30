<template>
  <div class="action-box">
    <div class="flex-between" style="padding-right: 10px">
      <h4>视频橱窗</h4>
    </div>
    <div class="edit-box">
      <el-form ref="formRef" style="width: 100%" :model="data" :rules="rules" label-width="100px" @submit.prevent>
        <el-card>
          <!-- <template #header>
            <span>视频橱窗</span>
          </template> -->
          <el-form-item label="橱窗名称" prop="title">
            <el-input v-model="data.title" maxlength="32" clearable placeholder="请输入橱窗名称" />
          </el-form-item>
          <el-form-item label="视频来源" prop="group">
            <el-select v-model="data.group" clearable placeholder="请选择视频来源" style="width: 100%">
              <el-option v-for="item in caseGroupList" :key="item.id" :label="item.name + `(ID:${item.id})`" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="视频布局" prop="layout">
            <el-radio-group v-model="data.layout">
              <el-radio :value="2">
                <div class="two-box">
                  <div class="text-box">一行2个</div>
                </div>
              </el-radio>
              <el-radio :value="1">
                <div class="one-box">
                  <div class="text-box">一行1个</div>
                </div>
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-card>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { useCaseGroupList } from '@/views/custom/hooks/case.js'

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
})

const { caseGroupList } = useCaseGroupList()

const formRef = ref(null)

const rules = {
  group: [{ required: true, message: '请选择视频来源', trigger: 'blur' }],
}

const verify = () => {
  formRef.value.validate()
}

defineExpose({
  verify
})
</script>

<style scoped lang="scss">
.action-box {
  min-width: 460px;
  padding-right: 0px !important;

  h4 {
    margin-top: 10px;
  }

  .edit-box {
    width: 100%;
    height: 632px;
    overflow-y: overlay;
    padding-right: 10px;

    .el-card {
      margin-bottom: 10px;
    }

    .one-box {
      width: 40px;
      height: 40px;
      background-color: #d7d7d7;
      position: relative;
    }
    .two-box {
      width: 40px;
      height: 40px;
      background-color: #d7d7d7;
      position: relative;

      &::before {
        content: "";
        display: block;
        width: 1px;
        height: 100%;
        background-color: #fff;
        position: absolute;
        top: 0;
        left: 50%;
      }
      &::after {
        content: "";
        display: block;
        width: 100%;
        height: 1px;
        background-color: #fff;
        position: absolute;
        top: 50%;
        left: 0;
      }
    }
    .text-box {
      position: absolute;
      left: -4px;
      bottom: -20px;
      color: #d7d7d7;
      line-height: 16px;
      font-size: 14px;
    }
  }
}
</style>
