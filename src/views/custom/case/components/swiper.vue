<template>
  <div class="swipe-height" :style="{ 'padding-top': '43%' }">
    <el-icon class="close-icon" @click="$emit('close')"><CircleCloseFilled /></el-icon>
    <div class="swipe-box">
      <van-swipe class="swipe" :autoplay="3000" indicator-color="white" style="height: 100%">
        <van-swipe-item v-for="(img, i) in list" :key="i">
          <van-image
            class="content-img"
            :style="`border-radius: var(--van-radius-lg);`"
            :src="$picUrl + img.pic[0]?.picUrl"
            fit="fill"
          />
        </van-swipe-item>
      </van-swipe>
    </div>
  </div>
</template>

<script setup>
import { Swipe as VanSwipe, SwipeItem as VanSwipeItem, Image as VanImage } from 'vant'

const props = defineProps({
  list: {
    type: Array,
    default: () => []
  },
})

defineEmits(['close'])
</script>

<style scoped lang="scss">
.swipe-height {
  position: relative;
  box-sizing: border-box;
  margin: var(--van-padding-xs) 0;
  padding: 0;
  cursor: pointer;

  .close-icon {
    position: absolute;
    top: -5px;
    right: 3px;
    color: #aaa;
    cursor: pointer;
    font-size: 13px;
    z-index: 99;

    &:hover {
      color: #ff0000;
    }
  }

  .swipe-box {
    box-sizing: border-box;
    padding: 0 var(--van-padding-xs);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;

    .swipe {
      .van-swipe-item {
        background-color: #d7d7d7;
        border-radius: var(--van-radius-lg);
      }
    }

    .content-img {
      overflow: hidden;
      width: 100%;
      height: 100%;
    }
  }
}
</style>