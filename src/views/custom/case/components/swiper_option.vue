<template>
  <div class="action-box">
    <div class="flex-between" style="padding-right: 10px">
      <h4>海报</h4>
      <el-button style="margin-top: -10px" type="primary" size="small" @click="addCard">添加卡片</el-button>
    </div>
    <div class="edit-box">
      <el-form ref="formRef" style="width: 100%" :model="data" label-width="100px" @submit.prevent>
        <el-card v-for="(item, index) in data.options" :key="item.id">
          <template #header>
            <div class="flex-between">
              <span>卡片{{ index + 1 }}</span>
              <el-icon class="cur" v-if="data.options.length > 1" @click="deleteCard(index)">
                <Delete />
              </el-icon>
            </div>
          </template>
          <el-form-item label="标题" :prop="'options.' + index + '.title'" required :rules="rules.title">
            <el-input v-model="item.title" maxlength="32" clearable placeholder="请输入标题" />
          </el-form-item>
          <el-form-item
            label="选择图片"
            :prop="'options.' + index + '.pic'"
            required
            :rules="rules.pic"
            :key="item.pic.length"
          >
            <ViewerImageList
              v-if="item.pic && item.pic.length > 0"
              :data="item.pic"
              :curIndex="index"
              is-preview-all
              :urlName="'picUrl'"
              :show-delete-btn="true"
              @delete="handleFileDelete"
            />
            <PasteUpload
              v-else
              :limit="1"
              :size="1"
              :curIndex="index"
              @success="handleFileChange"
            ></PasteUpload>
            <div style="width: 100%" v-if="!item.pic || item.pic.length == 0">
              请上传1张大小不超过
              <span style="color: #d9001b">1M</span>
              ，格式为
              <span style="color: #d9001b">png/jpg/jpeg</span>
              的图片
            </div>
          </el-form-item>
          <el-form-item label="跳转类型" :prop="'options.' + index + '.type'" required :rules="rules.type">
            <el-select v-model="item.type" clearable placeholder="请选择跳转类型" style="width: 100%">
              <el-option
                v-for="item in skipTypeList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            v-if="item.type == 1"
            label="分组选择"
            :prop="'options.' + index + '.group'"
            required
            :rules="rules.group"
          >
            <el-select v-model="item.group" clearable placeholder="请选择视频分组" style="width: 100%">
              <el-option
                v-for="item in caseGroupList"
                :key="item.id"
                :label="item.name + `(ID:${item.id})`"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            v-if="item.type == 2"
            label="视频链接"
            :prop="'options.' + index + '.link'"
            required
            :rules="rules.link"
          >
            <el-input v-model="item.link" clearable placeholder="请输入视频链接" />
          </el-form-item>
        </el-card>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import PasteUpload from '@/components/FileUpload/pasteUpload.vue'
import ViewerImageList from '@/components/ImagePreview/viewerImageList.vue'
import { useCaseGroupList } from '@/views/custom/hooks/case.js'
import { skipTypeList } from '@/views/custom/data.js'

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
})

const { caseGroupList } = useCaseGroupList()

const formRef = ref(null)

const rules = {
  title: [{ required: true, message: '请输入标题', trigger: 'blur' }],
  pic: [{ required: true, message: '请选择图片', trigger: 'change' }],
  type: [{ required: true, message: '请选择跳转类型', trigger: 'blur' }],
  group: [{ required: true, message: '请选择视频分组', trigger: 'blur' }],
  link: [{ required: true, message: '请输入视频链接', trigger: 'blur' }],
}

const addCard = () => {
  const newOption = {
    id: new Date().getTime(),
    title: '',
    pic: [],
    type: '',
    group: '',
    link: '',
  }
  props.data.options.push(newOption)
}

const deleteCard = i => {
  props.data.options.splice(i, 1)
}

const handleFileDelete = (data, i, curIndex) => {
  props.data.options[curIndex].pic.splice(i, 1)
}

const handleFileChange = (data, curIndex) => {
  if (data && data.length > 0) {
    props.data.options[curIndex].pic.push(data[0].data)
  }
}

const verify = () => {
  formRef.value.validate()
}

defineExpose({
  verify,
})
</script>

<style scoped lang="scss">
.action-box {
  min-width: 460px;
  padding-right: 0px !important;

  h4 {
    margin-top: 10px;
  }

  .cur {
    cursor: pointer;
  }

  .edit-box {
    width: 100%;
    height: 632px;
    overflow-y: overlay;
    padding-right: 10px;

    .el-card {
      margin-bottom: 10px;
    }
  }
}
</style>
