<template>
  <div class="tab-icon-box">
    <el-icon class="close-icon" @click="$emit('close')"><CircleCloseFilled /></el-icon>
    <van-grid :border="false" square>
      <van-grid-item v-for="(item, i) in data.options" :key="i" :text="item.title">
        <template #icon>
          <img v-if="item.pic[0]?.picUrl" class="tab-icon" :src="$picUrl + item.pic[0].picUrl" alt="">
          <van-icon v-else name="photo-o" size="28px" />
        </template>
      </van-grid-item>
    </van-grid>
  </div>
</template>

<script setup>
import { Grid as VanGrid, GridItem as VanGridItem, Icon as VanIcon } from 'vant'

const props = defineProps({
  data: {
    type: Object,
    default: () => ({})
  },
})
defineEmits(['close'])
</script>

<style scoped lang="scss">
.tab-icon-box {
  position: relative;
  cursor: pointer;

  :deep(.van-grid) {
    .van-grid-item__text {
      max-width: 56px;
      white-space: nowrap;
    }
  }

  .close-icon {
    position: absolute;
    top: -5px;
    right: 3px;
    color: #aaa;
    cursor: pointer;
    font-size: 13px;
    z-index: 99;

    &:hover {
      color: #ff0000;
    }
  }

  .tab-icon {
    width: 28px;
    height: 28px;
    object-fit: fill;
  }
}
</style>