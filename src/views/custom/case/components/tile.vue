<template>
  <div class="tile-box">
    <el-icon class="close-icon" @click="$emit('close')"><CircleCloseFilled /></el-icon>
    <div class="grid-box">
      <div class="left-box">
        <div class="grid-bg left1">
          <img v-if="data.left1?.pic[0]?.picUrl" :src="$picUrl + data.left1.pic[0].picUrl" alt="">
        </div>
        <div v-if="data.layout == '22' || data.layout == '21'" class="grid-bg left2">
          <img v-if="data.left2?.pic[0]?.picUrl" :src="$picUrl + data.left2.pic[0].picUrl" alt="">
        </div>
      </div>
      <div class="right-box">
        <div class="grid-bg right1">
          <img v-if="data.right1?.pic[0]?.picUrl" :src="$picUrl + data.right1.pic[0].picUrl" alt="">
        </div>
        <div v-if="data.layout == '12' || data.layout == '22'" class="grid-bg right2">
          <img v-if="data.right2?.pic[0]?.picUrl" :src="$picUrl + data.right2.pic[0].picUrl" alt="">
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  data: {
    type: Object,
    default: () => ({})
  },
})

defineEmits(['close'])
</script>

<style scoped lang="scss">
.tile-box {
  cursor: pointer;
  margin: 10px 0;
  padding: 44% 0 0;
  position: relative;

  .close-icon {
    position: absolute;
    top: -5px;
    right: 3px;
    color: #aaa;
    cursor: pointer;
    font-size: 13px;
    z-index: 99;

    &:hover {
      color: #ff0000;
    }
  }

  .grid-box {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    padding: 0 var(--van-padding-xs);
    display: flex;
    justify-content: space-between;
    gap: 8px;

    .left-box {
      width: 50%;
      height: 100%;
      display: flex;
      flex-direction: column;
      gap: 8px;

      .left1 {
        height: 100%;
      }
      .left2 {
        height: 100%;
      }
    }
    .right-box {
      width: 50%;
      height: 100%;
      display: flex;
      flex-direction: column;
      gap: 8px;

      .right1 {
        height: 100%;
      }
      .right2 {
        height: 100%;
      }
    }
    .grid-bg {
      background-color: #d7d7d7;
      border-radius: 8px;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: fill;
      }
    }

  }
}
</style>