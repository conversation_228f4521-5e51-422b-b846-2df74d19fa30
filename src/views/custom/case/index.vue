<template>
  <div class="custom-case-page">
    <h3 v-if="platform == '0'" style="margin-top: 0px">Amazon 页面配置</h3>
    <h3 v-if="platform == '1'" style="margin-top: 0px">TikTok 页面配置</h3>
    <el-row :gutter="10">
      <div class="box-card module-box">
        <h4>装修板块</h4>
        <draggable
          class="case-module-list"
          :group="{ name: 'case-module', pull: 'clone', put: false }"
          item-key="type"
          animation="200"
          :list="caseModuleList"
          :sort="false"
          :force-fallback="true"
          :clone="handleClone"
          @end="handleEnd"
          filter=".undraggable"
        >
          <template #item="{ element }">
            <div class="case-module" :class="{ undraggable: element.type === 4 && hasVideoList }">
              {{ element.name }}
            </div>
          </template>
        </draggable>
      </div>
      <el-col :span="8">
        <div class="box-card comp-box">
          <div class="flex-between">
            <h4>效果展示</h4>
            <el-button type="primary" size="small" style="margin-top: -10px" @click="onSave">保 存</el-button>
          </div>
          <div class="view-box">
            <div class="scrollbar-box">
              <draggable
                item-key="id"
                animation="200"
                :list="caseModuleListResult"
                class="case-module-view"
                :component-data="{ name: 'el-fade-in-linear' }"
                :group="{ name: 'case-module', pull: true, put: true }"
                :scroll="true"
                :force-fallback="false"
                @end="handleEnd"
                filter=".no-drop"
              >
                <template #item="{ element, index }">
                  <div>
                    <!-- 海报 -->
                    <Swiper
                      v-if="element.type === 1"
                      :class="{ 'active-card': element.id === currentData.id }"
                      :list="element.options"
                      @click="handleEdit(element)"
                      @close="handleRemove(element.id, index)"
                    />
                    <!-- 金刚区 -->
                    <TabIcon
                      v-else-if="element.type === 2"
                      :class="{ 'active-card': element.id === currentData.id }"
                      :data="element"
                      @click="handleEdit(element)"
                      @close="handleRemove(element.id, index)"
                    />
                    <!-- 瓷片区 -->
                    <Tile
                      v-else-if="element.type === 3"
                      :class="{ 'active-card': element.id === currentData.id }"
                      :data="element.option"
                      @click="handleEdit(element)"
                      @close="handleRemove(element.id, index)"
                    />
                    <!-- 视频橱窗 -->
                    <VideoList
                      v-else-if="element.type === 4"
                      class="no-drop"
                      :class="{ 'active-card': element.id === currentData.id }"
                      :title="element.option.title"
                      :layout="element.option.layout"
                      @click="handleEdit(element)"
                      @close="handleRemove(element.id, index)"
                    />
                  </div>
                </template>
              </draggable>
            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="8">
        <!-- 海报配置 -->
        <SwiperOption
          ref="SwiperOptionRef"
          v-show="currentData?.type === 1"
          class="box-card"
          :data="currentData"
        />
        <!-- 金刚区配置 -->
        <TabIconOption
          ref="TabIconOptionRef"
          v-show="currentData?.type === 2"
          class="box-card"
          :data="currentData"
        />
        <!-- 瓷片区配置 -->
        <TileOption
          ref="TileOptionRef"
          v-show="currentData?.type === 3"
          class="box-card"
          :data="currentData.option"
        />
        <!-- 视频橱窗配置 -->
        <VideoListOption
          ref="VideoListOptionRef"
          v-show="currentData?.type === 4"
          class="box-card"
          :data="currentData.option"
        />
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import draggable from 'vuedraggable'
import 'vant/lib/index.css'
import Swiper from '@/views/custom/case/components/swiper.vue'
import SwiperOption from '@/views/custom/case/components/swiper_option.vue'
import TabIcon from '@/views/custom/case/components/tab_icon.vue'
import TabIconOption from '@/views/custom/case/components/tab_icon_option.vue'
import VideoList from '@/views/custom/case/components/video_list.vue'
import VideoListOption from '@/views/custom/case/components/video_list_option.vue'
import Tile from '@/views/custom/case/components/tile.vue'
import TileOption from '@/views/custom/case/components/tile_option.vue'
import { ElLoading, ElMessage, ElNotification } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import { getChooseCaseInfo, saveChooseCase } from '@/api/custom/custom.js'
import { useCaseGroupList } from '@/views/custom/hooks/case.js'

const route = useRoute()
const router = useRouter()

const caseModuleList = ref([
  { name: '海报', type: 1, options: [] },
  { name: '金刚区', type: 2, options: [] },
  { name: '瓷片区', type: 3, option: { layout: '12', left1: {}, left2: {}, right1: {}, right2: {} } },
  { name: '视频橱窗', type: 4, option: { title: '橱窗名称', group: '', layout: 2 } },
])

const caseModuleListResult = ref([])

const currentData = ref({})
const SwiperOptionRef = ref(null)
const TabIconOptionRef = ref(null)
const TileOptionRef = ref(null)
const VideoListOptionRef = ref(null)

const { getGroupList } = useCaseGroupList()
const platform = ref(route.params.platform)
const pageId = ref('')

const option = {
  pic: [],
  title: '',
  type: '',
  link: '',
  group: '',
}
// 拖拽克隆处理
const handleClone = val => {
  let newObj = JSON.parse(JSON.stringify(val))
  newObj.id = new Date().getTime()
  if (newObj.type === 1) {
    newObj.options = [JSON.parse(JSON.stringify(option))]
  } else if (newObj.type === 2) {
    for (let i = 0; i < 4; i++) {
      newObj.options.push({
        pic: [],
        title: '名称',
        type: '',
        link: '',
        group: '',
      })
    }
  } else if (newObj.type === 3) {
    newObj.option = {
      layout: '12',
      left1: JSON.parse(JSON.stringify(option)),
      left2: JSON.parse(JSON.stringify(option)),
      right1: JSON.parse(JSON.stringify(option)),
      right2: JSON.parse(JSON.stringify(option)),
    }
  }
  return newObj
}

// 是否已添加视频橱窗
const hasVideoList = computed(() => {
  let obj = caseModuleListResult.value.find(item => item.type === 4)
  return obj ? true : false
})

const handleEnd = e => {
  if (e.to.className === 'case-module-view') {
    const newIndex = e.newIndex
    // 处理视频橱窗放置位置
    if (caseModuleListResult.value[newIndex].type === 4) {
      if (newIndex < caseModuleListResult.value.length - 1) {
        ElMessage.error('请将视频橱窗放置在最后')
        caseModuleListResult.value.splice(newIndex, 1)
        return
      }
    } else {
      if (caseModuleListResult.value.length > 1 && newIndex === caseModuleListResult.value.length - 1) {
        const item = caseModuleListResult.value[caseModuleListResult.value.length - 2]
        if (item.type === 4) {
          ElMessage.error('请将板块放置在视频橱窗之前')
          const data = caseModuleListResult.value.splice(newIndex, 1)
          caseModuleListResult.value.splice(e.oldIndex, 0, data[0])
          return
        }
      }
    }
    console.log('1==', newIndex)

    nextTick(() => {
      console.log('2==', newIndex)
      currentData.value = caseModuleListResult.value[newIndex]
    })
  }
}

const handleEdit = element => {
  // console.log(element);
  if (element.id === currentData.value.id) {
    currentData.value = {}
  } else {
    currentData.value = element
  }
}

const handleRemove = (id, i) => {
  if (id === currentData.value.id) {
    currentData.value = {}
  }
  caseModuleListResult.value.splice(i, 1)
}

const onSave = () => {
  if (caseModuleListResult.value.length === 0) {
    ElMessage.error('请至少添加一个板块')
    return
  }
  if (currentData.value.id) {
    if (currentData.value.type === 1) {
      SwiperOptionRef.value?.verify()
    } else if (currentData.value.type === 2) {
      TabIconOptionRef.value?.verify()
    } else if (currentData.value.type === 3) {
      TileOptionRef.value?.verify()
    } else if (currentData.value.type === 4) {
      VideoListOptionRef.value?.verify()
    }
  }
  let verify = false
  for (let i = 0; i < caseModuleListResult.value.length; i++) {
    const item = caseModuleListResult.value[i]
    let check = false
    if (item.type === 1 || item.type === 2) {
      // 海报、金刚区
      check = item.options.some(op => {
        if (!op.pic.length || !op.title) return true
        if (op.type == 1 && !op.group) return true
        if (op.type == 2 && !op.link) return true
        return false
      })
    } else if (item.type === 3) {
      // 瓷片区
      const checkArr = []
      if (item.option.layout[0] == '1') {
        checkArr.push(item.option.left1)
      } else if (item.option.layout[0] == '2') {
        checkArr.push(item.option.left1)
        checkArr.push(item.option.left2)
      }
      if (item.option.layout[1] == '1') {
        checkArr.push(item.option.right1)
      } else if (item.option.layout[1] == '2') {
        checkArr.push(item.option.right1)
        checkArr.push(item.option.right2)
      }
      check = checkArr.some(op => {
        if (!op.pic.length) return true
        if (op.type == 1 && !op.group) return true
        if (op.type == 2 && !op.link) return true
        return false
      })
    } else if (item.type === 4) {
      // 视频橱窗
      if (!item.option.title || !item.option.group || !item.option.layout) {
        check = true
      }
    }
    if (check) {
      ElNotification({
        title: `必填项提示：<${item.name}>`,
        message: '有必填项未填写！请补充完整',
        type: 'warning',
        duration: 1000 * 15,
      })
      verify = true
      break
    }
  }
  // console.log('submit', caseModuleListResult.value);
  if (verify) {
    return
  }
  const el_loading = ElLoading.service({
    lock: true,
    text: '正在保存中',
    background: 'rgba(0, 0, 0, 0.7)',
  })
  try {
    let params = handleParams()
    // console.log('submit-params', params);
    saveChooseCase(params)
      .then(res => {
        if (res.code === 200) {
          ElMessage.success('保存成功')
        }
      })
      .finally(() => el_loading.close())
  } catch (e) {
    el_loading.close()
  }
}

function handleParams() {
  let params = {
    id: pageId.value,
    platform: platform.value,
    pageChooseCaseList: [],
    videoShopWindow: null,
  }

  caseModuleListResult.value.forEach(item => {
    // 海报
    if (item.type === 1) {
      params.pageChooseCaseList.push({
        moduleType: item.type,
        pageChooseCaseDetailList: item.options.map(op => {
          let obj = {
            name: op.title,
            imageUrl: op.pic?.length ? op.pic[0].picUrl : [],
            skipType: op.type,
            skipUri: '',
          }
          if (op.type == 1) {
            obj.skipUri = op.group
          }
          if (op.type == 2) {
            obj.skipUri = op.link
          }
          return obj
        }),
      })
      // 金刚区
    } else if (item.type === 2) {
      params.pageChooseCaseList.push({
        moduleType: item.type,
        pageChooseCaseDetailList: item.options.map(op => {
          let obj = {
            name: op.title,
            imageUrl: op.pic?.length ? op.pic[0].picUrl : [],
            skipType: op.type,
            skipUri: '',
          }
          if (op.type == 1) {
            obj.skipUri = op.group
          }
          if (op.type == 2) {
            obj.skipUri = op.link
          }
          return obj
        }),
      })
      // 瓷片区
    } else if (item.type === 3) {
      let layoutType = 1
      let ops = []
      if (item.option.layout == '12') {
        layoutType = 1
        ops = [item.option.left1, item.option.right1, item.option.right2]
        ops[0].title = '左1'
        ops[1].title = '右1'
        ops[2].title = '右2'
      } else if (item.option.layout == '11') {
        layoutType = 2
        ops = [item.option.left1, item.option.right1]
        ops[0].title = '左1'
        ops[1].title = '右1'
      }
      params.pageChooseCaseList.push({
        layoutType,
        moduleType: item.type,
        pageChooseCaseDetailList: ops.map(op => {
          let obj = {
            name: op.title,
            imageUrl: op.pic?.length ? op.pic[0].picUrl : [],
            skipType: op.type,
            skipUri: '',
          }
          if (op.type == 1) {
            obj.skipUri = op.group
          }
          if (op.type == 2) {
            obj.skipUri = op.link
          }
          return obj
        }),
      })
      // 视频橱窗
    } else if (item.type === 4) {
      let layoutType = 1
      if (item.option.layout == 2) {
        layoutType = 1
      } else if (item.option.layout == 1) {
        layoutType = 2
      }
      params.videoShopWindow = {
        name: item.option.title,
        skipType: 1,
        skipUri: item.option.group,
        layoutType,
      }
    }
  })

  return params
}

function getPageData() {
  const el_loading = ElLoading.service({
    lock: true,
    text: '加载中',
    background: 'rgba(0, 0, 0, 0.7)',
  })
  getChooseCaseInfo(pageId.value)
    .then(res => {
      if (res.data) {
        res.data.pageChooseCaseList.forEach((item, index) => {
          // 海报
          if (item.moduleType == 1) {
            let obj = JSON.parse(JSON.stringify(caseModuleList.value[0]))
            obj.id = +('1' + res.data.id + index)
            obj.options = item.pageChooseCaseDetailList.map(op => {
              let pic = op.imageUrl
                ? [
                    {
                      id: op.imageUrl,
                      url: op.imageUrl + '!fullSizecompress',
                      picUrl: op.imageUrl,
                    },
                  ]
                : []
              return {
                pic,
                title: op.name || '',
                link: op.skipType == 2 ? op.skipUri : '',
                type: op.skipType || '',
                group: op.skipType == 1 ? +op.skipUri : '',
              }
            })
            caseModuleListResult.value.push(obj)
            // 金刚区
          } else if (item.moduleType == 2) {
            let obj = JSON.parse(JSON.stringify(caseModuleList.value[1]))
            obj.id = +('2' + res.data.id + index)
            obj.options = item.pageChooseCaseDetailList.map(op => {
              let pic = op.imageUrl
                ? [
                    {
                      id: op.imageUrl,
                      url: op.imageUrl + '!fullSizecompress',
                      picUrl: op.imageUrl,
                    },
                  ]
                : []
              return {
                pic,
                title: op.name || '',
                link: op.skipType == 2 ? op.skipUri : '',
                type: op.skipType || '',
                group: op.skipType == 1 ? +op.skipUri : '',
              }
            })
            caseModuleListResult.value.push(obj)
            // 瓷片区
          } else if (item.moduleType == 3) {
            let obj = JSON.parse(JSON.stringify(caseModuleList.value[2]))
            obj.id = +('3' + res.data.id + index)
            let layout = '12'
            let left1 = JSON.parse(JSON.stringify(option))
            let left2 = JSON.parse(JSON.stringify(option))
            let right1 = JSON.parse(JSON.stringify(option))
            let right2 = JSON.parse(JSON.stringify(option))
            let params = []
            params = item.pageChooseCaseDetailList.map(op => {
              let pic = op.imageUrl
                ? [
                    {
                      id: op.imageUrl,
                      url: op.imageUrl + '!fullSizecompress',
                      picUrl: op.imageUrl,
                    },
                  ]
                : []
              return {
                pic,
                title: op.name || '',
                link: op.skipType == 2 ? op.skipUri : '',
                type: op.skipType || '',
                group: op.skipType == 1 ? +op.skipUri : '',
              }
            })
            if (params[0]) {
              left1 = params[0]
            }
            if (params[1]) {
              right1 = params[1]
            }
            // layoutType
            // 1- 左1右2
            // 2- 左1右1
            if (item.layoutType == 1) {
              layout = '12'
              if (params[2]) {
                right2 = params[2]
              }
            } else if (item.layoutType == 2) {
              layout = '11'
            }
            obj.option = {
              layout,
              left1,
              left2,
              right1,
              right2,
            }
            caseModuleListResult.value.push(obj)
          }
        })
        // 视频橱窗
        if (res.data.videoShopWindow) {
          let obj = JSON.parse(JSON.stringify(caseModuleList.value[3]))
          obj.id = +('4' + res.data.id)
          let layout = 2
          // layoutType
          // 1- 一行2个
          // 2- 一行1个
          if (res.data.videoShopWindow.layoutType == 1) {
            layout = 2
          } else if (res.data.videoShopWindow.layoutType == 2) {
            layout = 1
          }
          obj.option = {
            title: res.data.videoShopWindow.name || '',
            group: res.data.videoShopWindow.skipUri ? +res.data.videoShopWindow.skipUri : '',
            groupName: res.data.videoShopWindow.groupName || '',
            layout,
          }
          caseModuleListResult.value.push(obj)
        }
        // console.log(caseModuleListResult.value);
        currentData.value = {}
      }
      nextTick(() => {
        el_loading.close()
      })
    })
    .catch(() => el_loading.close())
}

function init() {
  if (!route.params.pageId) {
    router.replace('/custom/list')
    return
  }
  pageId.value = route.params.pageId
  getPageData()
  getGroupList()
}

init()
</script>

<style scoped lang="scss">
$box-bg: rgb(242, 242, 242);
$page-bg: #f7f8fa;

.active-card {
  box-shadow: 0 0 4px #0087ff;

  :deep(.close-icon) {
    right: -4px;
  }
}

.custom-case-page {
  padding: 20px;
  min-width: 1000px;
}

.box-card {
  padding: 10px;
  background-color: $box-bg;
  border-radius: 10px;
}

h4 {
  margin-top: 10px;
}

.module-box {
  width: 235px;
  height: fit-content;
  margin-right: 5px;

  .case-module-list {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-content: flex-start;
    gap: 10px;
    height: 632px;

    .case-module {
      cursor: grab;
      background-color: rgb(170, 170, 170);
      color: #fff;
      width: 100px;
      height: 50px;
      text-align: center;
      line-height: 50px;
    }
    .undraggable {
      background-color: rgb(205 205 205);
      cursor: no-drop;
    }
  }
}
.comp-box {
  height: 700px;

  .view-box {
    position: relative;
    width: 225px;
    height: 420px;
    box-sizing: content-box;
    padding: 42px 10px 60px 5px;
    margin: 0 auto;
    background-image: url('@/assets/images/phone-bg.png');
    background-size: 100% 100%;
    scrollbar-width: none;

    .scrollbar-box {
      width: 100%;
      height: 100%;
      box-sizing: border-box;
      padding: 0 5px 0 7px;
      overflow-y: scroll;

      &::-webkit-scrollbar {
        display: none;
      }
    }

    .case-module-view {
      width: 100%;
      min-height: 420px;
      border-top: 1px solid transparent;
      padding-bottom: 20px;
      background-color: $page-bg;
    }
  }
}
</style>
