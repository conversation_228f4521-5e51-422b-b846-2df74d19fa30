<template>
  <van-search
    :placeholder="placeholder"
    input-align="center"
    shape="round"
    background="#f7f8fa"
    left-icon=""
    right-icon="search"
    readonly
    style="--van-search-content-background: #fff;--van-search-padding: 8px 10px;--van-search-input-height: 28px"
  />
</template>

<script setup>
import { Search as VanSearch } from 'vant'

const props = defineProps({
  placeholder: {
    type: String,
    default: ''
  }
})
</script>

<style scoped lang="scss">

</style>