<template>
  <div class="action-box">
    <div class="flex-between" style="padding-right: 10px;">
      <h4>分类</h4>
      <el-button style="margin-top: -10px;" type="primary" size="small" @click="addCard">添加卡片</el-button>
    </div>
    <div class="edit-box">
      <el-form
        ref="formRef"
        style="width: 100%;"
        :model="data"
        label-width="100px"
        @submit.prevent
      >
        <el-card v-for="(item, index) in data.options" :key="item.id">
          <template #header>
            <div class="flex-between">
              <span>分类{{ index + 1 }}</span>
              <el-icon class="cur" v-if="index" @click="deleteCard(index)"><Delete /></el-icon>
            </div>
          </template>
          <el-form-item label="分类名称" :prop="'options.'+index+'.categoryName'" required :rules="rules.categoryName">
            <el-input v-model="item.categoryName" maxlength="32" clearable placeholder="请输入分类名称" />
          </el-form-item>
          <el-form-item label="类目选择" :prop="'options.'+index+'.tagId'" required :rules="rules.tagId">
            <el-select v-model="item.tagId" clearable placeholder="请选择类目" style="width: 100%;">
              <el-option v-if="index === 0" label="所有类目" value="all" />
              <el-option v-for="item in modelCategoryList" :key="item.id" :label="item.name + `(ID:${item.id})`" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-card>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { modelCategorySelectRank } from '@/api/model/model.js'

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
})


const modelCategoryList = ref([])

const formRef = ref(null)

const rules = {
  categoryName: [
    { required: true, message: '请输入分类名称', trigger: 'blur' },
  ],
  tagId: [
    { required: true, message: '请选择类目', trigger: 'change' },
  ],
}

function getModelCategorySelect() {
  modelCategorySelectRank({ rank: 1, categoryId: 1008 }).then(res => {
    if (res.code == 200) {
      modelCategoryList.value = res.data
    }
  })
}
getModelCategorySelect()

const addCard = () => {
  let categoryName = '分类' + props.data.options.length
  props.data.options.push({
    id: new Date().getTime(),
    categoryName,
    tagId: '',
  })
}

const deleteCard = (i) => {
  props.data.options.splice(i, 1)
}

const verify = () => {
  return new Promise((resolve, reject) => {
    formRef.value.validate((valid) => {
      if (valid) {
        resolve(props.data.options)
      } else {
        reject(false)
      }
    })
  })
}

defineExpose({
  verify
})
</script>

<style scoped lang="scss">
.action-box {
  min-width: 460px;
  padding-right: 0px !important;

  h4 {
    margin-top: 10px;
  }

  .cur {
    cursor: pointer;
  }

  .edit-box {
    width: 100%;
    padding-right: 10px;

    .el-card {
      margin-bottom: 10px;
    }
  }
}
</style>