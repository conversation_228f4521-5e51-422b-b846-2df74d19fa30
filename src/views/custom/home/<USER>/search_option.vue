<template>
  <div class="action-box">
    <div class="flex-between" style="padding-right: 10px">
      <h4>搜索</h4>
    </div>
    <div class="edit-box">
      <el-form ref="formRef" style="width: 100%" :model="data" label-width="100px" @submit.prevent>
        <el-card>
          <el-form-item label="展示" prop="showSearch">
            <el-radio-group v-model="data.showSearch">
              <el-radio :value="1">启用</el-radio>
              <el-radio :value="0">关闭</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="提示词" prop="placeholder">
            <el-input v-model="data.placeholder" maxlength="32" clearable placeholder="请输入提示词" />
          </el-form-item>
        </el-card>
      </el-form>
    </div>
  </div>
</template>

<script setup>

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
})

const formRef = ref(null)

const verify = () => {
  return new Promise((resolve, reject) => {
    formRef.value.validate((valid) => {
      if (valid) {
        resolve(props.data)
      } else {
        reject(false)
      }
    })
  })
}

defineExpose({
  verify,
})
</script>

<style scoped lang="scss">
.action-box {
  min-width: 460px;
  padding-right: 0px !important;

  h4 {
    margin-top: 10px;
  }

  .edit-box {
    width: 100%;
    padding-right: 10px;

    .el-card {
      margin-bottom: 10px;
    }

  }
}
</style>
