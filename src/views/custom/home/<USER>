<template>
  <div class="custom-home-page">
    <h3 style="margin-top: 0px">模特端首页-页面配置</h3>
    <el-row :gutter="20">
      <el-col :span="8">
        <div class="box-card comp-box">
          <div class="flex-between">
            <h4>效果展示</h4>
            <el-button type="primary" size="small" style="margin-top: -10px" @click="onSave">保 存</el-button>
          </div>
          <div class="view-box">
            <div class="scrollbar-box">
              <div class="case-module-view">
                <Search v-show="form.showSearch" :placeholder="form.placeholder" />
                <ClassTabs :tabs="form.options" />
              </div>
            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="box-card options-box">
          <SearchOption ref="SearchOptionRef" :data="form" />
          <ClassOption ref="ClassOptionRef" :data="form" />
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import Search from '@/views/custom/home/<USER>/search.vue'
import SearchOption from '@/views/custom/home/<USER>/search_option.vue'
import ClassTabs from '@/views/custom/home/<USER>/class_tabs.vue'
import ClassOption from '@/views/custom/home/<USER>/class_option.vue'
import 'vant/lib/index.css'
import { getHomePageInfo, saveHomePage } from '@/api/custom/custom.js'
import { ElLoading, ElMessage } from 'element-plus'

const route = useRoute()
const router = useRouter()

const SearchOptionRef = ref()
const ClassOptionRef = ref()
const pageId = ref('')
const form = ref({
  showSearch: 1,
  placeholder: '',
  options: [{
    id: new Date().getTime(),
    categoryName: 'ALL',
    tagId: 'all',
  }]
})

function getHomePageInfoData() {
  const el_loading = ElLoading.service({
    lock: true,
    text: '加载中',
    background: 'rgba(0, 0, 0, 0.7)',
  })
  getHomePageInfo(pageId.value).then(res => {
    if (res.code === 200) {
      form.value.showSearch = res.data.showSearch
      form.value.placeholder = res.data.searchHint
      form.value.options = res.data.homePageTagList.map((item, i) => {
        return {
          id: i,
          categoryName: item.categoryName,
          tagId: item.isAll ? 'all' : item.tagId,
        }
      })
    }
  }).finally(() => el_loading.close())
}

async function onSave() {
  let res1 = await SearchOptionRef.value.verify()
  let res2 = await ClassOptionRef.value.verify()
  if(res1 && res2) {
    let params = {
      id: pageId.value,
      searchHint: form.value.placeholder,
      showSearch: form.value.showSearch,
      homePageTagList: []
    }
    params.homePageTagList = form.value.options.map((item) => {
      return {
        tagId: item.tagId == 'all' ? '' : item.tagId,
        isAll: item.tagId == 'all' ? 1 : 0,
        categoryName: item.categoryName,
      }
    })
    const el_loading = ElLoading.service({
      lock: true,
      text: '正在保存中',
      background: 'rgba(0, 0, 0, 0.7)',
    })
    saveHomePage(params)
      .then(res => {
        if (res.code === 200) {
          ElMessage.success('保存成功')
        }
      })
      .finally(() => el_loading.close())
  }
}



function init() {
  if (!route.params.pageId) {
    router.replace('/custom/list')
    return
  }
  pageId.value = route.params.pageId
  getHomePageInfoData()
}
init()
</script>

<style scoped lang="scss">
$box-bg: rgb(242, 242, 242);
$page-bg: #f7f8fa;

.custom-home-page {
  padding: 20px;

  .box-card {
    padding: 10px;
    background-color: $box-bg;
    border-radius: 10px;
  }
  .comp-box {
    min-width: 330px;
    max-width: 400px;
    height: 700px;

    h4 {
      margin-top: 10px;
    }

    .view-box {
      position: relative;
      width: 225px;
      height: 420px;
      box-sizing: content-box;
      padding: 42px 10px 60px 5px;
      margin: 0 auto;
      background-image: url('@/assets/images/phone-bg.png');
      background-size: 100% 100%;
      scrollbar-width: none;

      .scrollbar-box {
        width: 100%;
        height: 100%;
        box-sizing: border-box;
        padding: 0 5px 0 7px;
        overflow-y: scroll;

        &::-webkit-scrollbar {
          display: none;
        }
      }

      .case-module-view {
        width: 100%;
        min-height: 420px;
        border-top: 1px solid transparent;
        padding-bottom: 20px;
        background-color: $page-bg;
      }
    }
  }
  .options-box {
    min-width: 530px;
    max-width: 600px;
    height: 700px;
    overflow-y: overlay;
  }
}
</style>