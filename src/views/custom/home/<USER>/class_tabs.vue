<template>
  <van-tabs
    shrink
    style="
      --van-tabs-nav-background: transparent;
      --van-tabs-line-height: 32px;
      --van-tab-font-size: 12px;
      --van-tab-line-height: 16px;
    "
    title-active-color="rgb(217, 0, 27)"
    color="rgb(217, 0, 27)"
    line-width="30px"
  >
    <van-tab v-for="item in tabs" :title="item.categoryName" :key="item.id"></van-tab>
  </van-tabs>
</template>

<script setup>
import { Tabs as VanTabs, Tab as VanTab } from 'vant'

const props = defineProps({
  tabs: {
    type: Array,
    default: () => [],
  },
})
</script>

<style scoped lang="scss"></style>
