<template>
  <div style="margin: 20px">
    <ElTablePage
      ref="tableRef"
      :columns="columns"
      :data="tableData"
      :loading="tableLoading"
      :currentPage="currentPage"
      :pageSize="pageSize"
      :total="total"
      :tableAction="{
        width: '190',
        fixed: 'right',
      }"
      @page-change="pageChange"
      row-key="id"
    >
      <template #tableHeader>
        <div>
          <el-form :inline="true" :model="queryParams" @submit.prevent>
            <el-form-item label="页面名称">
              <el-input
                v-model="queryParams.name"
                style="width: 200px"
                clearable
                placeholder="请输入页面名称"
              ></el-input>
            </el-form-item>
            <el-form-item label="页面ID">
              <el-input
                v-model="queryParams.id"
                style="width: 200px"
                clearable
                placeholder="请输入页面ID"
              ></el-input>
            </el-form-item>
            <el-form-item>
              <el-button v-btn type="primary" icon="Search" native-type="submit" @click="handleQuery">
                搜索
              </el-button>
              <el-button v-btn icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
          <!-- <div style="margin-bottom: 10px">
            <el-button v-btn type="primary" icon="Plus" @click="handleAdd('home')">
              新增自定义首页页面
            </el-button>
            <el-button v-btn type="primary" icon="Plus" @click="handleAdd('case')">
              新增自定义精选案例页面
            </el-button>
          </div> -->
        </div>
      </template>
      <template #tableAction="{ row }">
        <el-button
          v-btn
          link
          type="primary"
          v-if="
            (row.userPerms == 'page_config_main' && checkPermi(['page_config_main'])) ||
            (row.userPerms == 'page_config_amazon' && checkPermi(['page_config_amazon'])) ||
            (row.userPerms == 'page_config_tiktok' && checkPermi(['page_config_tiktok']))
          "
          @click="toEdit(row.platform, row.id, row.type)"
        >
          修改
        </el-button>
      </template>
    </ElTablePage>
  </div>
</template>
<script setup>
import ElTablePage from '@/components/Table/ElTablePage.vue'
import { pageConfigList } from '@/api/custom/custom.js'
import { checkPermi } from '@/utils/permission'

const router = useRouter()

const tableData = ref([])
const tableLoading = ref(false)
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const queryParams = ref({
  name: '',
  id: '',
})

const columns = ref([
  {
    label: '页面ID',
    prop: 'id',
  },
  {
    label: '页面类型',
    prop: 'type',
    handle: data => {
      if (data == 1) {
        return '首页'
      }
      if (data == 2) {
        return '精选案例'
      }
      return '其他'
    },
  },
  {
    label: '页面名称',
    prop: 'name',
  },
])

const handleAdd = type => {
  if (type === 'home') {
    router.push({ name: 'CustomHomePageAdd' })
  } else if (type === 'case') {
    router.push({ name: 'CustomCasePageAdd' })
  }
}

const toEdit = (platform, id, type) => {
  let path = ''
  if (type == 1) {
    // router.push({ path: `/custom/home-page/edit/${id}` })
    path = '/custom/home-page/edit/' + id
  } else {
    // router.push({ path: `/custom/case-page/edit/${platform}/${id}` })
    path = '/custom/case-page/edit/' + platform + '/' + id
  }
  // const path = '/case/group-video/' + row.id
  const { href } = router.resolve({ path })
  window.open(href, '_blank')
}

const handleQuery = () => {
  tableLoading.value = true
  pageConfigList(queryParams.value)
    .then(res => {
      tableData.value = res.rows
      total.value = res.total
    })
    .finally(() => {
      tableLoading.value = false
    })
}
const resetQuery = () => {
  queryParams.value = {
    name: '',
    id: '',
  }
  handleQuery()
}

const pageChange = page => {
  currentPage.value = page.currentPage
  pageSize.value = page.pageSize
  handleQuery()
}

handleQuery()
</script>

<style scoped lang="scss"></style>
