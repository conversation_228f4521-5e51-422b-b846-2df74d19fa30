<template>
  <div>
    <el-dialog
      align-center
      append-to-body
      title="添加备注"
      v-model="isShow"
      width="600px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="close"
    >
      <div class="remark-box">
        <div class="remark-box-name">
          <span style="color: #606266; font-weight: 700">模特名：</span>
          {{ modelName }}
        </div>
        <el-form ref="formRef" :model="form" :rules="rules" style="margin-top: 20px">
          <el-form-item prop="remark" label="备注内容：">
            <el-input
              v-model="form.remark"
              type="textarea"
              show-word-limit
              maxlength="200"
              resize="none"
              :rows="6"
              placeholder="请输入内容"
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer flex-center">
          <el-button @click="close" round class="btn">取 消</el-button>
          <el-button type="primary" @click="confirm" round class="btn">保 存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { modelDataTableAddRemark } from '@/api/model/modelData'
import { ElMessage, ElMessageBox } from 'element-plus'
const isShow = ref(false)
const formRef = ref(null)

const emits = defineEmits(['success'])

const form = ref({
  remark: '',
  modelId: '',
})
const rules = {
  remark: [{ required: true, message: '请输入备注内容', trigger: 'blur' }],
}

const modelName = ref('')
const open = (id, name = '') => {
  form.value.modelId = id
  modelName.value = name
  isShow.value = true
}

const close = () => {
  form.value.remark = ''
  form.value.modelId = ''
  modelName.value = ''
  formRef.value.resetFields()
  isShow.value = false
}

const confirm = () => {
  formRef.value.validate(valid => {
    if (valid) {
      modelDataTableAddRemark(form.value).then(res => {
        ElMessage.success('备注添加成功')
        emits('success')
        close()
      })
    }
  })
}

defineExpose({ open, close })
</script>

<style lang="scss" scoped>
.remark-box {
  &-name {
    background: #f2f2f2;
    border-radius: 4px;
    padding: 5px 14px;
  }
}
</style>
