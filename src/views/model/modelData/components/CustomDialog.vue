<template>
  <div>
    <el-dialog
      align-center
      append-to-body
      title="自定义数据"
      v-model="isShow"
      width="600px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <div class="dialog-content" v-loading="dialogLoading">
        <el-card v-for="section in sections" :key="section.key" shadow="never" class="card-section">
          <div class="section-title">
            <el-checkbox
              v-model="checked[section.key]"
              :indeterminate="isIndeterminate(section.key)"
              @change="handleParentChange(section.key)"
            >
              {{ section.label }}
            </el-checkbox>
          </div>
          <el-row :gutter="20" class="checkbox-row">
            <el-checkbox-group v-model="checkedFields" style="display: flex; flex-wrap: wrap">
              <el-col :span="6" v-for="item in section.options" :key="item.value">
                <el-checkbox :label="item.value">
                  {{ item.label }}
                </el-checkbox>
              </el-col>
            </el-checkbox-group>
          </el-row>
        </el-card>
      </div>
      <template #footer>
        <span class="dialog-footer flex-center">
          <el-button @click="close" round class="btn">取 消</el-button>
          <el-button type="primary" @click="confirm" round class="btn">保 存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ElMessage, ElMessageBox } from 'element-plus'
const isShow = ref(false)
const dialogLoading = ref(false)
const emits = defineEmits(['success'])

const checked = ref({
  basic: false,
  order: false,
})
const checkedFields = ref([])

const sections = [
  {
    key: 'basic',
    label: '基础数据',
    options: [
      { label: '客服', value: '1' },
      { label: '开发人', value: '2' },
      { label: '性别', value: '3' },
      { label: '国家', value: '4' },
      { label: '平台', value: '5' },
      { label: '类型', value: '6' },
      { label: '年龄层', value: '7' },
      { label: '评分', value: '8' },
      { label: '佣金', value: '9' },
      { label: '案例数', value: '10' },
      { label: '标签数', value: '11' },
      { label: '品类数', value: '12' },
      { label: '蜗牛照', value: '13' },
      { label: '家庭', value: '14' },
      { label: '收藏', value: '15' },
      { label: '拉黑', value: '16' },
    ],
  },
  {
    key: 'order',
    label: '订单数据',
    options: [
      { label: '意向接单率', value: '17' },
      { label: '预选接单率', value: '18' },
      { label: '分发接单率', value: '19' },
      { label: '自选排单率', value: '20' },
      { label: '商家拒绝率', value: '21' },
      { label: '排单数', value: '22' },
      { label: '待拍数', value: '23' },
      { label: '反馈数', value: '24' },
      { label: '超时率', value: '25' },
      { label: '售后率', value: '26' },
      { label: '完成数', value: '27' },
      { label: '丢件数', value: '28' },
      { label: '被取消', value: '29' },
      { label: '被退回', value: '30' },
    ],
  },
]

const handleParentChange = key => {
  const section = sections.find(s => s.key === key)
  const allValues = section.options.map(option => option.value)

  if (isIndeterminate(key) || checked.value[key]) {
    checked.value[key] = true
    checkedFields.value = [...new Set([...checkedFields.value, ...allValues])]
  } else {
    checkedFields.value = checkedFields.value.filter(
      value => !section.options.some(option => option.value === value)
    )
  }
}

const isIndeterminate = key => {
  const section = sections.find(s => s.key === key)
  const allValues = section.options.map(option => option.value)
  const selectedValues = allValues.filter(value => checkedFields.value.includes(value))
  return selectedValues.length > 0 && selectedValues.length < allValues.length
}
const open = () => {
  dialogLoading.value = true
  loadData()
  isShow.value = true
}

const close = () => {
  isShow.value = false
}

const saveData = () => {
  const orderMap = {}
  sections.forEach(section => {
    section.options.forEach((option, index) => {
      orderMap[option.value] = index
    })
  })

  const sortedCheckedFields = checkedFields.value.sort((a, b) => {
    let sectionA, sectionB
    for (const sec of sections) {
      if (sec.options.some(opt => opt.value === a)) sectionA = sec
      if (sec.options.some(opt => opt.value === b)) sectionB = sec
    }

    if (sectionA !== sectionB) {
      return sections.indexOf(sectionA) - sections.indexOf(sectionB)
    }

    return orderMap[a] - orderMap[b]
  })
  const dataToSave = {
    checkedFields: sortedCheckedFields,
    checked: checked.value,
  }
  localStorage.setItem('customDialogData', JSON.stringify(dataToSave))
  ElMessage.success('数据已保存')
  emits('success')
}

const loadData = () => {
  const savedData = localStorage.getItem('customDialogData')
  if (savedData) {
    const parsedData = JSON.parse(savedData)
    checkedFields.value = parsedData.checkedFields || []
    checked.value = parsedData.checked || {}
  }
  dialogLoading.value = false
}

const confirm = () => {
  saveData()
  close()
}

defineExpose({ open, close })
</script>

<style lang="scss" scoped>
:deep(.el-card__body) {
  padding: 0 !important;
}
.dialog-content {
  border: 1px solid #e4e7ed;

  :deep(.el-card) {
    border-radius: 0;
    border: none;
  }
  .section-title {
    font-size: 16px;
    font-weight: 500;
    padding: 5px 10px;
    background-color: #f2f2f2;
  }
  .checkbox-row {
    padding: 5px 10px;
  }
}

.el-dialog__footer {
  text-align: center;
}
.el-button {
  min-width: 96px;
}
</style>
