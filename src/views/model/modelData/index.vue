<template>
  <div class="model-data">
    <div class="flex-between" style="margin-bottom: 10px">
      <div>数据最后更新时间：{{ lastQueryTime }}</div>
      <el-button v-btn type="primary" @click="showCustomData">自定义数据</el-button>
    </div>
    <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="88px" @submit.prevent>
      <el-form-item label="搜索">
        <el-input
          v-model="queryParams.keyword"
          clearable
          style="width: 250px"
          placeholder="请输入模特名/ID"
        ></el-input>
      </el-form-item>
      <el-form-item label="数据范围">
        <el-date-picker
          v-model="queryParams.dataScopeTimes"
          format="YYYY/M/D HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="datetimerange"
          range-separator="-"
          unlink-panels
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
          :shortcuts="shortcuts"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="queryParams.createtimes"
          format="YYYY/M/D HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="datetimerange"
          range-separator="-"
          unlink-panels
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
          :shortcuts="shortcuts"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="排单数" v-if="curTableColumnsList.includes('22')">
        <el-select
          v-model="queryParams.orderScheduledCounts"
          placeholder="请选择"
          multiple
          collapse-tags
          collapse-tags-tooltip
          clearablestyle="width: 180px"
        >
          <el-option
            v-for="item in orderCountOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="客服" v-if="curTableColumnsList.includes('1')">
        <el-select
          v-model="queryParams.serviceIds"
          filterable
          multiple
          collapse-tags
          collapse-tags-tooltip
          placeholder="请选择"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="item in serviceList"
            :key="item.serviceId"
            :label="item.serviceName"
            :value="item.serviceId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="开发人" v-if="curTableColumnsList.includes('2')">
        <el-select
          v-model="queryParams.developerIds"
          filterable
          multiple
          collapse-tags
          collapse-tags-tooltip
          placeholder="请选择"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="item in contactList"
            :key="item.serviceId"
            :label="item.userName"
            :value="item.serviceId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="模特等级" v-if="curTableColumnsList.includes('8')">
        <el-select
          v-model="queryParams.cooperations"
          placeholder="请选择"
          multiple
          collapse-tags
          collapse-tags-tooltip
          clearable
          style="width: 180px"
        >
          <el-option
            v-for="item in modelLevelOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="模特状态">
        <el-select
          v-model="queryParams.statuses"
          placeholder="请选择"
          multiple
          collapse-tags
          collapse-tags-tooltip
          clearable
          style="width: 180px"
        >
          <el-option
            v-for="item in biz_model_status"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="适合平台" v-if="curTableColumnsList.includes('5')">
        <el-select
          v-model="queryParams.platforms"
          placeholder="请选择"
          multiple
          collapse-tags
          collapse-tags-tooltip
          clearable
          style="width: 180px"
        >
          <el-option
            v-for="item in biz_model_platform"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="类型" v-if="curTableColumnsList.includes('6')">
        <el-select v-model="queryParams.modelType" placeholder="请选择" clearable style="width: 180px">
          <el-option
            v-for="item in biz_model_type"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="国家" v-if="curTableColumnsList.includes('4')">
        <el-select
          v-model="queryParams.nations"
          placeholder="请选择"
          multiple
          collapse-tags
          collapse-tags-tooltip
          clearable
          style="width: 180px"
        >
          <el-option v-for="item in biz_nation" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="性别" v-if="curTableColumnsList.includes('3')">
        <el-select
          v-model="queryParams.sexes"
          placeholder="请选择"
          multiple
          collapse-tags
          collapse-tags-tooltip
          clearable
          style="width: 180px"
        >
          <el-option v-for="item in sexOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="年龄层" v-if="curTableColumnsList.includes('7')">
        <el-select
          v-model="queryParams.ageGroups"
          placeholder="请选择"
          multiple
          collapse-tags
          collapse-tags-tooltip
          clearable
          style="width: 180px"
        >
          <el-option
            v-for="item in biz_model_ageGroup"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="预警模特" v-if="shouldShowColumn">
        <el-select v-model="queryParams.isWarningModel" placeholder="请选择" clearable style="width: 180px">
          <el-option
            v-for="item in earlyWarningOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery" :loading="tableLoading">搜索</el-button>
        <el-button @click="handleReset" :loading="tableLoading">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="tableData"
      ref="tableRef"
      v-loading="tableLoading"
      :header-cell-style="getHeaderBgColor"
      @sort-change="handleSortChange"
      :cell-class-name="getCellClass"
      border
    >
      <el-table-column prop="name" fixed="left" label="模特名" align="center" min-width="180">
        <template v-slot="{ row }">
          <div class="flex-center model-name-info">
            <el-tooltip
              placement="top"
              trigger="hover"
              popper-class="public-white-tooltips"
              v-if="row.remarks && row.remarks.length > 0"
            >
              <template #content>
                <div style="max-width: 450px; max-height: 400px; overflow-y: auto; white-space: pre-wrap">
                  <template v-for="(item, index) in row.remarks">
                    <div>{{ index + 1 }}.【{{ item.createBy }}】{{ item.remark }}</div>
                  </template>
                </div>
              </template>
              <el-avatar
                class="model-avatar fs-0"
                icon="UserFilled"
                :src="$picUrl + row.modelPic + '!1x1compress'"
                @click="showViewer([row.modelPic])"
              />
            </el-tooltip>
            <el-avatar
              v-else
              class="model-avatar fs-0"
              icon="UserFilled"
              :src="$picUrl + row.modelPic + '!1x1compress'"
              @click="showViewer([row.modelPic])"
            />
            <div class="model-box">
              <div class="model-box-name">{{ row.name }}</div>
              <div class="model-box-time">{{ row.createTime }}</div>
            </div>
            <el-tag
              effect="dark"
              size="small"
              :type="handleTagType(row.status)"
              :color="row.status == 1 ? '#ccc520' : ''"
            >
              {{ biz_model_status.find(item => item.value == row.status)?.label.slice(0, 2) }}
            </el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        v-for="column in tableColumns"
        :key="column.prop"
        :prop="column.prop"
        :label="column.label"
        :sortable="column.sort ? 'custom' : false"
        :sort-orders="['descending', 'ascending', null]"
        :class-name="getColumnClass(column)"
        :min-width="column.mw"
        align="center"
      >
        <template #header>
          <el-tooltip
            v-if="column.tip"
            popper-class="public-white-tooltips"
            :content="column.tip"
            placement="top"
          >
            <span>{{ column.label }}</span>
          </el-tooltip>
          <span v-else>{{ column.label }}</span>
        </template>
        <template #default="{ row }">
          <div class="flex-center">
            <el-tooltip
              v-if="row[column.prop + 'EarlyWarn'] === 1"
              popper-class="public-white-tooltips"
              :content="column.prop == 'orderScheduledCount' ? row.warningTip : column.warningTip"
              placement="top"
            >
              <div>
                <template v-if="column.prop == 'caseCount'">
                  <div>{{ row.caseCount }}</div>
                  <div class="times-box-red" v-if="row.caseCountUpdateTime">
                    {{ row.caseCountUpdateTime.slice(0, 10) }}
                  </div>
                </template>
                <template v-else-if="column.prop == 'tagCount'">
                  <div>{{ row.tagCount }}</div>
                  <div class="times-box-red" v-if="row.tagCountUpdateTime">
                    {{ row.tagCountUpdateTime.slice(0, 10) }}
                  </div>
                </template>
                <template v-else-if="column.prop == 'categoryCount'">
                  <div>{{ row.categoryCount }}</div>
                  <div class="times-box-red" v-if="row.categoryCountUpdateTime">
                    {{ row.categoryCountUpdateTime.slice(0, 10) }}
                  </div>
                </template>
                <template v-else-if="column.prop == 'intentionOrderRate'">
                  <div>{{ row.intentionOrderRate }}%</div>
                  <div class="times-box-red" v-if="row.intentionCount">
                    {{ row.submitIntentionCount }}/{{ row.intentionCount }}
                  </div>
                </template>
                <template v-else-if="column.prop == 'preSelectOrderRate'">
                  <div>{{ row.preSelectOrderRate }}%</div>
                  <div class="times-box-red" v-if="row.preSelectCount">
                    {{ row.submitPreSelectCount }}/{{ row.preSelectCount }}
                  </div>
                </template>
                <template v-else-if="column.prop == 'selfSelectOrderRate'">
                  <div>{{ row.selfSelectOrderRate }}%</div>
                  <div class="times-box-red" v-if="row.selfSelectCount">
                    {{ row.submitSelfSelectCount }}/{{ row.selfSelectCount }}
                  </div>
                </template>
                <template v-else-if="column.prop == 'rejectOrderRate'">
                  <div>{{ row.rejectOrderRate }}%</div>
                  <div class="times-box-red" v-if="row.submitModelCount">
                    {{ row.changeModelCount }}/{{ row.submitModelCount }}
                  </div>
                </template>
                <template v-else-if="column.prop == 'overtimeRate'">
                  <div>{{ row.overtimeRate }}%</div>
                  <div class="times-box-red" v-if="row.confirmReceiptCount">
                    {{ row.overtimeCount }}/{{ row.confirmReceiptCount }}
                  </div>
                </template>
                <template v-else-if="column.prop == 'afterSaleRate'">
                  <div>{{ row.afterSaleRate }}%</div>
                  <div class="times-box-red" v-if="row.feedbackMaterialsCount">
                    {{ row.afterSaleCount }}/{{ row.feedbackMaterialsCount }}
                  </div>
                </template>
                <template v-else>{{ row[column.prop] }}</template>
              </div>
            </el-tooltip>
            <div v-else>
              <template v-if="column.prop == 'cooperationScore'">
                <div>{{ row.cooperation == 1 ? '优质' : row.cooperation == 0 ? '一般' : '-' }}</div>
                <model-score
                  v-if="row.cooperationScore || row.cooperationScore === 0"
                  :score="row.cooperationScore"
                />
              </template>
              <template v-else-if="column.prop == 'sex'">
                {{ sexOptions.find(item => item.value == row.sex)?.label }}
              </template>
              <template v-else-if="column.prop == 'nation'">
                {{ biz_nation.find(item => item.value == row.nation)?.label }}
              </template>
              <template v-else-if="column.prop == 'platform'">
                <!-- <biz-model-platform :value="row.platform" tag="text" isComma /> -->
                <div style="display: flex; flex-wrap: wrap">
                  <template v-for="(item, index) in modelPlatformOptions" :key="item.value">
                    <div v-if="row.platform && row.platform.split(',').includes(item.value)">
                      {{ item.label }}{{ index < row.platform.split(',').length - 1 ? '、' : '' }}
                    </div>
                  </template>
                </div>
              </template>
              <template v-else-if="column.prop == 'type'">
                {{ row.type === 0 ? '影响者' : '素人' }}
              </template>
              <template v-else-if="column.prop == 'ageGroup'">
                {{ biz_model_ageGroup.find(item => item.value == row.ageGroup)?.label }}
              </template>
              <template v-else-if="column.prop == 'commission'">
                {{ row.commission }}{{ handleCommissionUnit(row.commissionUnit) }}
              </template>
              <template v-else-if="column.prop == 'caseCount'">
                <div>{{ row.caseCount }}</div>
                <div class="times-box" v-if="row.caseCountUpdateTime">
                  {{ row.caseCountUpdateTime.slice(0, 10) }}
                </div>
              </template>
              <template v-else-if="column.prop == 'tagCount'">
                <div>{{ row.tagCount }}</div>
                <div class="times-box" v-if="row.tagCountUpdateTime">
                  {{ row.tagCountUpdateTime.slice(0, 10) }}
                </div>
              </template>
              <template v-else-if="column.prop == 'categoryCount'">
                <div>{{ row.categoryCount }}</div>
                <div class="times-box" v-if="row.categoryCountUpdateTime">
                  {{ row.categoryCountUpdateTime.slice(0, 10) }}
                </div>
              </template>
              <template v-else-if="column.prop == 'haveSnailPic'">
                {{ row.haveSnailPic ? '有' : '无' }}
              </template>
              <template v-else-if="column.prop == 'intentionOrderRate'">
                <div>{{ row.intentionOrderRate }}%</div>
                <div class="times-box" v-if="row.intentionCount">
                  {{ row.submitIntentionCount }}/{{ row.intentionCount }}
                </div>
              </template>
              <template v-else-if="column.prop == 'preSelectOrderRate'">
                <div>{{ row.preSelectOrderRate }}%</div>
                <div class="times-box" v-if="row.preSelectCount">
                  {{ row.submitPreSelectCount }}/{{ row.preSelectCount }}
                </div>
              </template>
              <template v-else-if="column.prop == 'dispatchOrderRate'">
                <div>{{ row.dispatchOrderRate }}%</div>
                <div class="times-box" v-if="row.dispatchCount">
                  {{ row.submitDispatchCount }}/{{ row.dispatchCount }}
                </div>
              </template>
              <template v-else-if="column.prop == 'selfSelectOrderRate'">
                <div>{{ row.selfSelectOrderRate }}%</div>
                <div class="times-box" v-if="row.selfSelectCount">
                  {{ row.submitSelfSelectCount }}/{{ row.selfSelectCount }}
                </div>
              </template>
              <template v-else-if="column.prop == 'rejectOrderRate'">
                <div>{{ row.rejectOrderRate }}%</div>
                <div class="times-box" v-if="row.submitModelCount">
                  {{ row.changeModelCount }}/{{ row.submitModelCount }}
                </div>
              </template>
              <template v-else-if="column.prop == 'overtimeRate'">
                <div>{{ row.overtimeRate }}%</div>
                <div class="times-box" v-if="row.confirmReceiptCount">
                  {{ row.overtimeCount }}/{{ row.confirmReceiptCount }}
                </div>
              </template>
              <template v-else-if="column.prop == 'afterSaleRate'">
                <div>{{ row.afterSaleRate }}%</div>
                <div class="times-box" v-if="row.feedbackMaterialsCount">
                  {{ row.afterSaleCount }}/{{ row.feedbackMaterialsCount }}
                </div>
              </template>
              <div v-else>{{ displayValue(row[column.prop]) }}</div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" min-width="100px" align="center">
        <template v-slot="{ row }">
          <el-button
            link
            type="primary"
            size="small"
            v-btn
            @click="handleDetail('/model/modelData/details/' + row.id)"
          >
            查看
          </el-button>
          <el-button link type="primary" size="small" v-btn @click="showRemarkDialog(row)">备注</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="flex-end" style="margin: 10px">
      <PaginationFloatBar
        :current-page="pageNum"
        :page-size="pageSize"
        @update:current-page="handlePageChange"
        :total="total"
      />
      <!-- <el-pagination
        background
        @size-change="pageChange({ pageNum: 1, pageSize: $event })"
        @current-change="pageChange({ pageNum: $event, pageSize })"
        :current-page="pageNum"
        :page-size="pageSize"
        :page-sizes="pageSizes"
        layout="total, prev, pager, next, jumper"
        :total="total"
      /> -->
    </div>
    <CustomDialog ref="customDialogRef" @success="loadTableColumns" />
    <RemarkDialog ref="remarkDialogRef" @success="handleQuery" />
  </div>
</template>

<script setup>
import CustomDialog from '@/views/model/modelData/components/CustomDialog.vue'
import RemarkDialog from '@/views/model/modelData/components/remarkDialog.vue'
import { dayjs } from 'element-plus'
import { bizCommissionUnit } from '@/utils/dict'
import { useViewer } from '@/hooks/useViewer'
const { showViewer } = useViewer()

import {
  modelDataTablelist,
  modelDataTablelistServiceSelect,
  modelDataTablelistDeveloperSelect,
  modelDataTableGetLastUpdateTime,
} from '@/api/model/modelData.js'
import { useRouter } from 'vue-router'
import { getBeforeDate } from '@/utils/index'
const router = useRouter()
const { proxy } = getCurrentInstance()
const {
  biz_model_type,
  biz_nation,
  biz_model_platform,
  biz_model_cooperation,
  biz_model_ageGroup,
  model_family_relationship_type,
  biz_model_status,
} = proxy.useDict(
  'biz_model_type',
  'biz_nation',
  'biz_model_platform',
  'biz_model_cooperation',
  'biz_model_ageGroup',
  'model_family_relationship_type',
  'biz_model_status'
)

const defaultQueryParams = {
  keyword: '',
  sortColumn: null,
  sortWay: null,
  dataScopeTimes: [],
  createtimes: [],
  isWarningModel: '',
  platforms: [],
  nations: [],
  sexes: [],
  ageGroups: [],
  cooperations: [],
  modelType: '',
  serviceIds: [],
  developerIds: [],
  statuses: [],
  orderScheduledCounts: [],
}

const queryParams = ref({
  ...defaultQueryParams,
})
const tableLoading = ref(false)
const tableColumns = ref([])
const tableRef = ref()

const orderCountOptions = [
  { label: '0单', value: '1' },
  { label: '1-5单', value: '2' },
  { label: '6-10单', value: '3' },
  { label: '11-15单', value: '4' },
  { label: '16-20单', value: '5' },
  { label: '21单以上', value: '6' },
]
const modelLevelOptions = [
  { label: '优质模特(7.8分及以上)', value: '1' },
  { label: '一般模特(7.8分以下)', value: '0' },
]
const sexOptions = [
  { label: '男', value: '1' },
  { label: '女', value: '0' },
]
const earlyWarningOptions = [
  { label: '有预警', value: '1' },
  { label: '无预警', value: '0' },
]
const modelPlatformOptions = [
  { label: 'Amz', value: '0' },
  { label: 'TK', value: '1' },
  { label: '其他', value: '2' },
  { label: 'APP', value: '3' },
]

const allColumns = [
  {
    prop: 'serviceName',
    label: '客服',
    type1: 'base',
    sort: true,
    tip: '负责该模特的客服名称',
    columns: '1',
  },
  {
    prop: 'developerName',
    label: '开发人',
    mw: 100,
    type1: 'base',
    sort: true,
    tip: '该模特的开发人',
    columns: '2',
  },
  { prop: 'sex', label: '性别', type1: 'base', columns: '3' },
  { prop: 'nation', label: '国家', type1: 'base', columns: '4' },
  { prop: 'platform', label: '平台', type1: 'base', columns: '5', mw: 90 },
  { prop: 'type', label: '类型', type1: 'base', tip: '该模特的类型', sort: true, columns: '6' },
  {
    prop: 'ageGroup',
    label: '年龄层',
    type1: 'base',
    mw: 90,
    tip: '该模特的年龄层',
    sort: true,
    columns: '7',
  },
  {
    prop: 'cooperationScore',
    label: '评分',
    type1: 'base',
    columns: '8',
    tip: '模特库填写的模特评分',
    sort: true,
    customSlot: 'cooperationScore',
  },
  { prop: 'commission', label: '佣金', type1: 'base', tip: '模特库填写的佣金金额', sort: true, columns: '9' },
  {
    prop: 'caseCount',
    label: '案例数',
    type1: 'base',
    tip: '该模特已有的案例数量',
    warningTip: '超过3个月未更新',
    earlyWarning: true,
    sort: true,
    mw: 90,
    columns: '10',
  },
  {
    prop: 'tagCount',
    label: '标签数',
    type1: 'base',
    tip: '该模特已有的标签数量',
    warningTip: '超过3个月未更新',
    earlyWarning: true,
    sort: true,
    mw: 90,
    columns: '11',
  },
  {
    prop: 'categoryCount',
    label: '品类数',
    type1: 'base',
    tip: '该模特已有的品类数量',
    warningTip: '超过3个月未更新',
    earlyWarning: true,
    sort: true,
    mw: 90,
    columns: '12',
  },
  {
    prop: 'haveSnailPic',
    label: '蜗牛照',
    type1: 'base',
    tip: '该模特是否有蜗牛照',
    sort: true,
    mw: 90,
    columns: '13',
  },
  {
    prop: 'familyMemberCount',
    label: '家庭',
    type1: 'base',
    tip: '该模特关联的家庭成员数量',
    sort: true,
    columns: '14',
  },
  {
    prop: 'collectCount',
    label: '收藏',
    type1: 'base',
    tip: '被用户收藏的次数（不含已取消）',
    sort: true,
    columns: '15',
  },
  {
    prop: 'blacklistCount',
    label: '拉黑',
    type1: 'base',
    tip: '被用户拉黑的次数（不含已取消）',
    sort: true,
    columns: '16',
  },
  {
    prop: 'intentionOrderRate',
    label: '意向接单率',
    type1: 'order',
    tip: '商家意向模特，成功接单的比例',
    warningTip: '意向接单率少于50%',
    earlyWarning: true,
    sort: true,
    mw: 120,
    columns: '17',
  },
  {
    prop: 'preSelectOrderRate',
    label: '预选接单率',
    type1: 'order',
    tip: '客服自己选的模特，成功提交给商家的比例',
    warningTip: '预选接单率少于60%',
    earlyWarning: true,
    sort: true,
    mw: 120,
    columns: '18',
  },
  {
    prop: 'dispatchOrderRate',
    label: '分发接单率',
    type1: 'order',
    tip: '客服分发，成功接单的比例',
    sort: true,
    mw: 120,
    columns: '19',
  },
  {
    prop: 'selfSelectOrderRate',
    label: '自选排单率',
    type1: 'order',
    tip: '模特在模特清单中选择订单后，成功提交给商家的比例',
    warningTip: '自选排单率少于50%',
    earlyWarning: true,
    sort: true,
    mw: 120,
    columns: '20',
  },
  {
    prop: 'rejectOrderRate',
    label: '商家拒绝率',
    type1: 'order',
    tip: '我们匹配的模特，商家不满意的概率',
    warningTip: '商家拒绝率大于15%',
    earlyWarning: true,
    sort: true,
    mw: 120,
    columns: '21',
  },
  {
    prop: 'orderScheduledCount',
    label: '排单数',
    type1: 'order',
    tip: '筛选的时间范围内，这个模特的排单数量',
    warningTip: `1.新模特未排单\n2.多排单\n3.新模特未排单\n4.排单少`,
    earlyWarning: true,
    sort: true,
    mw: 90,
    columns: '22',
  },
  {
    prop: 'waitPictureCount',
    label: '待拍数',
    type1: 'order',
    tip: '排单数中包含的等待反馈视频的订单数量',
    warningTip: '待拍数过多',
    earlyWarning: true,
    sort: true,
    mw: 90,
    columns: '23',
  },
  {
    prop: 'feedbackCount',
    label: '反馈数',
    type1: 'order',
    tip: '排单数中包含的已经反馈素材给商家的订单数量',
    sort: true,
    mw: 90,
    columns: '24',
  },
  {
    prop: 'overtimeRate',
    label: '超时率',
    type1: 'order',
    tip: '排单数中包含的已经超时的订单概率',
    warningTip: '超时率过高',
    earlyWarning: true,
    sort: true,
    mw: 90,
    columns: '25',
  },
  {
    prop: 'afterSaleRate',
    label: '售后率',
    type1: 'order',
    tip: '排单数中包含的有产生售后的订单概率',
    warningTip: '售后率过高',
    earlyWarning: true,
    sort: true,
    mw: 90,
    columns: '26',
  },
  {
    prop: 'completeCount',
    label: '完成数',
    type1: 'order',
    tip: '排单数中包含的已完成的订单数量',
    sort: true,
    mw: 90,
    columns: '27',
  },
  {
    prop: 'dropCount',
    label: '丢件数',
    type1: 'order',
    tip: '排单数中包含的发生丢件的订单数量',
    warningTip: '超过2次丢件',
    earlyWarning: true,
    sort: true,
    mw: 90,
    columns: '28',
  },
  {
    prop: 'cancelCount',
    label: '被取消',
    type1: 'order',
    tip: '排单数中包含的被取消的订单数量',
    warningTip: '有取消订单',
    earlyWarning: true,
    sort: true,
    mw: 90,
    columns: '29',
  },
  {
    prop: 'returnCount',
    label: '被退回',
    type1: 'order',
    tip: '排单数中包含的被回退的订单数量',
    warningTip: '有回退订单',
    earlyWarning: true,
    sort: true,
    mw: 90,
    columns: '30',
  },
]

const shortcuts = [
  {
    text: '今天',
    value: () => {
      const start = new Date()
      start.setHours(0, 0, 0, 0)
      const end = new Date()
      end.setHours(23, 59, 59, 59)
      return [start, end]
    },
  },
]
function handleDetail(path) {
  const { href } = router.resolve({ path })
  window.open(href, '_blank')
}

const tableData = ref([])

const customDialogRef = ref(null)
const showCustomData = () => {
  customDialogRef.value.open()
}
const remarkDialogRef = ref(null)
const showRemarkDialog = row => {
  remarkDialogRef.value.open(row.id, row.name)
}

const handleSortChange = data => {
  const propToOrderByType = {
    serviceName: '1',
    developerName: '2',
    sex: '3',
    nation: '4',
    platform: '5',
    type: '6',
    ageGroup: '7',
    cooperationScore: '8',
    commission: '9',
    caseCount: '10',
    tagCount: '11',
    categoryCount: '12',
    haveSnailPic: '13',
    familyMemberCount: '14',
    collectCount: '15',
    blacklistCount: '16',
    intentionOrderRate: '17',
    preSelectOrderRate: '18',
    dispatchOrderRate: '19',
    selfSelectOrderRate: '20',
    rejectOrderRate: '21',
    orderScheduledCount: '22',
    waitPictureCount: '23',
    feedbackCount: '24',
    overtimeRate: '25',
    afterSaleRate: '26',
    completeCount: '27',
    dropCount: '28',
    cancelCount: '29',
    returnCount: '30',
  }

  queryParams.value.sortColumn = propToOrderByType[data.prop] || null
  queryParams.value.sortWay = data.order === 'ascending' ? 'ASC' : data.order === 'descending' ? 'DESC' : null
  handleQuery()
}

function isObject(value) {
  return value !== null && typeof value === 'object' && !Array.isArray(value)
}
function displayValue(value) {
  return value === null || value === undefined ? '-' : value
}

function handleCommissionUnit(val) {
  let b = bizCommissionUnit.find(item => item.value == val)
  return b ? b.label : ''
}
const shouldShowColumn = computed(() => {
  const requiredColumns = ['10', '11', '12', '17', '18', '20', '21', '22', '23', '25', '26', '28', '29', '30']
  return requiredColumns.some(item => curTableColumnsList.value.includes(item))
})

const pageNum = ref(1)
const pageSize = ref(20)
const pageSizes = [10, 20, 30, 50]
const total = ref(0)
function pageChange(page) {
  pageNum.value = page.pageNum
  pageSize.value = page.pageSize
  handleQuery()
}

function handlePageChange(num) {
  pageNum.value = num
  handleQuery()
}

const getHeaderBgColor = ({ row, column }) => {
  return {
    'background-color': column.className?.includes('base-column')
      ? '#eef9fe !important'
      : column.className?.includes('order-column')
      ? '#dfdfff !important'
      : '',
  }
}

const getColumnClass = row => {
  const baseClass = row.type1 == 'base' ? 'base-column' : 'order-column'
  // if (row.earlyWarning) {
  //   return `${baseClass} red-cell`
  // } else {
  return baseClass
  // }
}
const getCellClass = ({ row, $index, column }) => {
  const propName = column.property + 'EarlyWarn'
  if (row && row[propName] && row[propName] === 1) {
    return 'red-cell'
  }
}
const curTableColumnsList = ref([])
const loadTableColumns = () => {
  tableLoading.value = true
  const savedData = localStorage.getItem('customDialogData')
  if (savedData) {
    const parsedData = JSON.parse(savedData)
    const savedColumns = parsedData.checkedFields || []
    curTableColumnsList.value = savedColumns
    tableColumns.value = savedColumns
      .map(field => {
        const columnConfig = allColumns.find(col => col.columns === field)
        return columnConfig ? columnConfig : null
      })
      .filter(column => column !== null)
    // tableColumns.value = savedColumns.map(field => {
    //   const columnConfig = allColumns.find(col => col.prop === field)
    //   return columnConfig || { prop: field, label: field }
    // })
  } else {
    localStorage.setItem(
      'customDialogData',
      JSON.stringify({
        checkedFields: [
          '1',
          '2',
          '3',
          '4',
          '5',
          '6',
          '7',
          '8',
          '9',
          '10',
          '11',
          '12',
          '13',
          '14',
          '15',
          '16',
          '17',
          '18',
          '19',
          '20',
          '21',
          '22',
          '23',
          '24',
          '25',
          '26',
          '27',
          '28',
          '29',
          '30',
        ],
        checked: { basic: true, order: true },
      })
    )
    loadTableColumns()
    return
  }
  handleQuery()
}

const contactList = ref([])
const serviceList = ref([])
const getDeveloperList = () => {
  modelDataTablelistDeveloperSelect().then(res => {
    if (res.data) {
      contactList.value = res.data.map(item => ({
        ...item,
        userName: item.serviceName + `(${item.modelCount})`,
      }))
    }
  })
  modelDataTablelistServiceSelect().then(res => {
    if (res.data) {
      serviceList.value = res.data.map(item => ({
        ...item,
        serviceName: item.serviceName + `(${item.modelCount})`,
      }))
    }
  })
}

const handleQueryParams = () => {
  let { modelType, dataScopeTimes, createtimes, ...params } = queryParams.value
  if (modelType) {
    params.types = [modelType]
  }
  if (dataScopeTimes && dataScopeTimes.length > 0) {
    params.dataScopeTimeBegin = dataScopeTimes[0]
    params.dataScopeTimeEnd = dataScopeTimes[1]
  }
  if (createtimes && createtimes.length > 0) {
    params.createTimeBegin = createtimes[0]
    params.createTimeEnd = createtimes[1]
  }
  params.customColumns = curTableColumnsList.value
  return params
}
// const handleEarlyWarn = () => {
//   tableColumns.value.forEach(column => {
//     const propName = column.prop + 'EarlyWarn'
//     if (column.earlyWarning !== undefined) {
//       const tableRow = tableData.value.find(row => row[propName] === 1)
//       if (tableRow && tableRow[propName] && tableRow[propName] === 1) {
//         column.earlyWarning = true
//       } else {
//         column.earlyWarning = false
//       }
//     }
//   })
// }
const lastQueryTime = ref()
function getNowDate(data) {
  modelDataTableGetLastUpdateTime(data).then(res => {
    lastQueryTime.value = res.data
  })
  // return dayjs().format('YYYY-MM-DD HH:mm')
}

const handleQuery = () => {
  tableLoading.value = true
  getDeveloperList()
  const data = handleQueryParams()
  getNowDate(data)
  modelDataTablelist({ pageNum: pageNum.value, pageSize: pageSize.value, ...data })
    .then(res => {
      const tempData = res.data.rows || []
      if (
        tempData &&
        tempData.length > 0 &&
        curTableColumnsList.value.length > 0 &&
        curTableColumnsList.value.includes('22')
      ) {
        tempData.forEach(item => {
          item.warningTip =
            item.orderScheduledCount == 0
              ? '新模特未排单'
              : item.orderScheduledCount > 5
              ? '多排单'
              : '少排单'
        })
      }
      tableData.value = tempData
      total.value = res.data.total || 0
    })
    .finally(() => {
      tableLoading.value = false
    })
}

const handleReset = () => {
  queryParams.value = { ...defaultQueryParams }
  tableRef.value?.clearSort()
  handleQuery()
}

const handleTagType = status => {
  if (status === 0) {
    return 'success'
  } else if (status === 1) {
    return 'warning'
  } else if (status === 2) {
    return 'warning'
  } else if (status === 3) {
    return 'danger'
  }
}

onMounted(() => {
  loadTableColumns()
})
</script>

<style lang="scss">
.public-white-tooltips {
  background: rgb(48 49 51 / 70%) !important;
  border: 1px solid transparent !important;
  .el-popper__arrow {
    &::before {
      display: block;
      border: 4px solid rgb(48 49 51 / 70%) !important;
      width: 0;
      height: 0;
      border-left-color: transparent !important;
      border-top-color: transparent !important;
      background: transparent !important;
      transform: translate(-2px, 2px) rotateZ(45deg) !important;
    }
  }
}
</style>
<style lang="scss" scoped>
:deep(.el-table) {
  overflow: visible;
}
:deep(.el-table__header-wrapper) {
  position: sticky;
  top: 0;
  z-index: 5;
}
.model-data {
  padding: 20px 20px 40px 20px;
  //   :deep(.el-table) {
  //     --el-table-row-hover-bg-color: none;
  //     // &:hover > td {
  //     //   background-color: #0b6170 !important; // 鼠标悬停时的背景色
  //     // }
  //   }
}
:deep(.red-cell) {
  background-color: #fceaec !important;
  color: #d9001b;
}

.model-box {
  margin: 0 8px;
  &-name {
    color: #02a7f0;
    font-size: 13px;
    text-align: left;
  }
  &-time {
    color: #aaa;
    font-size: 11px;
  }
}
.model-name-info {
  :deep(.el-tag--dark) {
    border: none;
  }
}
.times-box {
  color: #aaa;
  font-size: 11px;
}
.times-box-red {
  color: #d9001b;
  font-size: 11px;
}
</style>
