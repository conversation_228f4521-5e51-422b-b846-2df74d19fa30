<template>
  <div class="flex-start model-info">
    <div class="model-info-section">
      <ModelInfo @send-name="handleSendName" />
    </div>
    <div class="table-box">
      <div class="table-btn">
        <el-radio-group v-model="selectVal" size="large" @change="handleSelect">
          <el-radio-button :value="1">排单记录</el-radio-button>
          <el-radio-button :value="2">备注记录</el-radio-button>
        </el-radio-group>
      </div>
      <template v-if="selectVal === 1">
        <div class="table-content" style="max-height: 750px; overflow: auto">
          <div class="table-content-tab">
            <el-radio-group v-model="curTab" @change="handleTabChange">
              <el-radio-button v-for="(tab, i) in tabList" :key="tab.value" :value="tab.value">
                {{ tab.label }}({{ tab.number }})
              </el-radio-button>
            </el-radio-group>
          </div>
          <el-table ref="tableRef" :data="tableData" v-loading="tableLoading" @sort-change="handleSortChange">
            <el-table-column label="视频编码" prop="videoCode" width="100px">
              <template v-slot="{ row }">
                <div>
                  <el-button
                    v-if="checkPermi(['order:manage:details'])"
                    v-btn
                    link
                    type="primary"
                    @click="routerNewWindow('/order/details/' + row.videoId)"
                  >
                    {{ row.videoCode }}
                  </el-button>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="产品名称" prop="productChinese" />
            <el-table-column
              label="来源"
              sortable="custom"
              :sort-orders="['descending', 'ascending', null]"
              prop="shootModelAddType"
              width="100px"
            >
              <template v-slot="{ row }">
                {{ sourceOptions.find(item => item.value === row.shootModelAddType)?.label }}
              </template>
            </el-table-column>
            <el-table-column
              label="排单时间"
              sortable="custom"
              :sort-orders="['descending', 'ascending', null]"
              prop="submitTime"
              width="180"
            />
            <el-table-column
              label="排单方式"
              sortable="custom"
              :sort-orders="['descending', 'ascending', null]"
              prop="scheduleType"
              width="100px"
            >
              <template v-slot="{ row }">
                <div v-if="row.scheduleType === 1">
                  {{ row.commission }}{{ handleCommissionUnit(row.commissionUnit) }}
                </div>
                <div v-else-if="row.scheduleType === 2">
                  {{ row.carryType == 1 ? '主携带' : row.carryType == 2 ? '被携带' : '-' }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              label="素材反馈时长"
              sortable="custom"
              :sort-orders="['descending', 'ascending', null]"
              prop="feedbackDuration"
              width="130px"
            >
              <template v-slot="{ row }">
                {{ row.feedbackDuration || row.feedbackDuration === 0 ? row.feedbackDuration + '天' : '-' }}
              </template>
            </el-table-column>
            <el-table-column
              label="订单状态"
              sortable="custom"
              :sort-orders="['descending', 'ascending', null]"
              prop="status"
              width="100px"
            >
              <template v-slot="{ row }">
                {{ orderStatusMap[row.status] }}
              </template>
            </el-table-column>
            <el-table-column
              label="订单完成时间"
              sortable="custom"
              :sort-orders="['descending', 'ascending', null]"
              prop="statusTime"
              width="180px"
            >
              <template v-slot="{ row }">
                {{ row.statusTime || '-' }}
              </template>
            </el-table-column>
            <el-table-column label="标签" prop="tags">
              <template v-slot="{ row }">
                <div
                  style="display: flex; flex-wrap: wrap"
                  class="gap-5"
                  v-if="row.tags && row.tags.length > 0"
                >
                  <el-tag
                    v-for="(item, index) in row.tags"
                    :key="index"
                    size="small"
                    round
                    effect="plain"
                    :type="tagsOptions.find(data => data.value == item)?.type"
                    style="width: 64px !important"
                    :style="{ color: item == '5' ? 'purple' : '', borderColor: item == '5' ? 'purple' : '' }"
                  >
                    {{ tagsOptions.find(data => data.value == item)?.label }}
                  </el-tag>
                  <!-- <el-tag v-for="item in row.tags.join(',')" size="small" round effect="plain" type="danger">取消订单</el-tag>
                  <el-tag size="small" round effect="plain" type="success">补偿订单</el-tag>
                  <el-tag size="small" round effect="plain" type="warning" style="width: 64px">工单</el-tag> -->
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="flex-end" style="margin: 10px">
          <el-pagination
            background
            @size-change="pageChange({ pageNum: 1, pageSize: $event })"
            @current-change="pageChange({ pageNum: $event, pageSize })"
            :current-page="pageNum"
            :page-size="pageSize"
            :page-sizes="pageSizes"
            layout="total, prev, pager, next, jumper"
            :total="total"
          />
        </div>
      </template>
      <RemarkTable :model-name="modelName" v-if="selectVal === 2" />
    </div>
  </div>
</template>

<script setup>
import ModelInfo from '@/views/model/modelData/details/components/modelInfo.vue'
import RemarkTable from '@/views/model/modelData/details/components/remarkTable.vue'
import {
  modelDataTableOrderScheduledRecord,
  modelDataTableOrderScheduledRecordTagCount,
} from '@/api/model/modelData'
import { bizCommissionUnit } from '@/utils/dict'
import { checkPermi } from '@/utils/permission'
import { sourceOptions } from '@/views/task/preselection/index.js'
import { orderStatusMap } from '@/views/order/list/data.js'
const route = useRoute()
const router = useRouter()

const tableLoading = ref(false)
const tableData = ref([
])
const tableRef = ref()

const selectVal = ref(1)
const handleSelect = val => {
  selectVal.value = val
  if (val === 1) {
    initTableData()
  }
}

const modelName = ref('')
function handleSendName(val) {
  modelName.value = val || ''
}

const tabList = ref([
  { label: '全部', value: '', number: 0 },
  { label: '无异常', value: 0, number: 0 },
  { label: '工单', value: 1, number: 0 },
  { label: '售后单', value: 2, number: 0 },
  { label: '补偿订单', value: 3, number: 0 },
  { label: '取消订单', value: 4, number: 0 },
  { label: '回退订单', value: 5, number: 0 },
])
const tagsOptions = [
  {
    label: '工单',
    value: 1,
    type: 'warning',
  },
  {
    label: '售后单',
    value: 2,
    type: 'primary',
  },
  {
    label: '补偿订单',
    value: 3,
    type: 'success',
  },
  {
    label: '取消订单',
    value: 4,
    type: 'danger',
  },
  {
    label: '回退订单',
    value: 5,
    type: 'primary',
  },
]
// tab切换
const curTab = ref('')
function handleTabChange(name) {
  curTab.value = name
  tableRef.value?.clearSort()
  queryParams.value.sortColumn = null
  queryParams.value.sortWay = null
  initTableData()
}

const queryParams = ref({
  sortColumn: null,
  sortWay: null,
})
const handleSortChange = data => {
  const propToOrderByType = {
    shootModelAddType: '1',
    submitTime: '2',
    scheduleType: '3',
    feedbackDuration: '4',
    status: '5',
    statusTime: '6',
  }
  queryParams.value.sortColumn = propToOrderByType[data.prop] || null
  queryParams.value.sortWay = data.order === 'ascending' ? 'ASC' : data.order === 'descending' ? 'DESC' : null
  initTableData()
}

function routerNewWindow(path) {
  const { href } = router.resolve({ path })
  window.open(href, '_blank')
}

const pageNum = ref(1)
const pageSize = ref(20)
const pageSizes = [10, 20, 30, 50]
const total = ref(0)
function pageChange(page) {
  pageNum.value = page.pageNum
  pageSize.value = page.pageSize
  initTableData()
}
function handleCommissionUnit(val) {
  let b = bizCommissionUnit.find(item => item.value == val)
  return b ? b.label : ''
}

const initTableData = () => {
  modelDataTableOrderScheduledRecord({
    modelId: route.params.modelId,
    pageNum: pageNum.value,
    pageSize: pageSize.value,
    tag: curTab.value,
    ...queryParams.value,
  }).then(res => {
    tableData.value = res.data.rows || []
    if (tableData.value && tableData.value.length > 0) {
      tableData.value.forEach(item => {
        if (item.tags && item.tags.length > 0) {
          item.tags = item.tags.split(',')
        }
      })
    }
    total.value = res.data.total
  })
  modelDataTableOrderScheduledRecordTagCount({ modelId: route.params.modelId }).then(res => {
    const tabCountMap = {
      0: 'noExceptionCount',
      1: 'workOrderCount',
      2: 'afterSaleOrderCount',
      3: 'compensationOrderCount',
      4: 'cancelOrderCount',
      5: 'rollbackOrderCount',
    }
    const countData = res.data || {}
    tabList.value = tabList.value.map(tab => ({
      ...tab,
      number: countData[tabCountMap[tab.value]] || 0,
    }))
    tabList.value[0].number = res.data.allCount || 0
  })
}

initTableData()
</script>

<style lang="scss" scoped>
.model-info {
  padding: 20px;
  align-items: normal;
  gap: 0 15px;
  // max-height: 100vh;
  &-section {
    // width: 20%;
    min-width: 300px;
    // max-height: 90vh;
    // overflow: auto;
    border-radius: 4px;
    box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.349019607843137);
  }
}
.table-btn {
  :deep(.el-radio-group) {
    .el-radio-button {
      margin-right: 20px;
    }
    .el-radio-button__inner {
      box-shadow: 0 0 0 0;
      font-size: 18px;
      padding: 0;
      // margin-right: 40px;
      border: none;
    }
    .el-radio-button.is-active {
      .el-radio-button__inner {
        box-shadow: 0 0 0 0;
        border: none;
        color: var(--el-color-primary);
        background-color: #fff;
      }
    }
  }
}
.table-box {
  width: calc(100% - 315px);
  .table-content {
    margin-top: 10px;
    padding: 20px 0;
    border-radius: 4px;
    box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.349019607843137);
    &-tab {
      padding-left: 20px;
      margin-bottom: 20px;
    }
  }
}
</style>
