<template>
  <div class="info-box">
    <div class="info-box-head">
      <el-avatar
        class="model-avatar fs-0"
        :size="112"
        icon="UserFilled"
        :src="$picUrl + data.modelPic + '!1x1compress'"
        @click="showViewer([data.modelPic])"
      />
      <div class="model-name">{{ data.name }}</div>
      <div class="model-id">(ID:{{ data.account }})</div>
      <div class="flex-center" style="gap: 20px">
        <el-text v-if="data.sex === 1">
          <el-icon color="#777777"><Male /></el-icon>
          男
        </el-text>
        <el-text v-else>
          <el-icon color="#777777"><Female /></el-icon>
          女
        </el-text>
        <biz-model-ageGroup :value="data.ageGroup" tag="text">
          <template v-slot="{ dict }">
            <el-text>
              <el-icon color="#777777"><User /></el-icon>
              {{ dict.label }}
            </el-text>
          </template>
        </biz-model-ageGroup>
        <biz-nation :value="data.nation" tag="text">
          <template v-slot="{ dict }">
            <el-text>
              <el-icon color="#777777"><LocationInformation /></el-icon>
              {{ dict.label }}
            </el-text>
          </template>
        </biz-nation>
      </div>
    </div>
    <el-divider style="margin: 15px 0" />
    <div class="info-box-content">
      <div class="flex-start">
        <div class="label fs-0">模特类型</div>
        <BizModelTypeNew :value="data.type" />
        <!-- v-if="modelInfo.cooperationScore || modelInfo.cooperationScore === 0" -->
      </div>
      <div class="flex-start">
        <div class="label fs-0">模特评分</div>
        <ModelScore
          v-if="data.cooperationScore || data.cooperationScore === 0"
          :score="data.cooperationScore"
        />
      </div>
      <div class="flex-start" style="align-items: baseline">
        <div class="label fs-0">适合平台</div>
        <div class="platform-box"><biz-model-platform :value="data.platform" tag="text" isComma /></div>
      </div>
      <div class="flex-start">
        <div class="label fs-0">合作状态</div>
        <div class="platform-box">
          <biz-model-status :value="data.status" tag="text" />
          <span v-if="data.status == 2 && data.tripRecoveryTime">
            ({{ data.tripRecoveryTime.slice(0, 16) }}恢复)
          </span>
        </div>
      </div>
      <div class="flex-start">
        <div class="label fs-0">佣金</div>
        <div class="platform-box">
          <span>{{ data.commission }}{{ handleCommissionUnit(data.commissionUnit) }}</span>
        </div>
      </div>
    </div>
    <el-divider style="margin: 15px 0" />
    <div class="flex-around">
      <div class="merchant-box">
        <div class="merchant-box-number">{{ data.collectCount || 0 }}</div>
        <div class="merchant-box-text">商家收藏</div>
      </div>
      <div class="merchant-box">
        <div class="merchant-box-number">{{ data.blacklistCount || 0 }}</div>
        <div class="merchant-box-text">商家拉黑</div>
      </div>
    </div>
    <el-divider style="margin: 15px 0" />
    <div class="family-box">
      <div class="family-box-title">家庭成员</div>
      <div class="family-grid" v-if="data.familyMember && data.familyMember.length > 0">
        <div v-for="(model, index) in data.familyMember" :key="index" class="family-member">
          <el-avatar
            class="model-avatar fs-0"
            :size="62"
            icon="UserFilled"
            :src="$picUrl + model.modelPic + '!1x1compress'"
            @click="showViewer([model.modelPic])"
          />
          <div class="member-name one-ell">{{ model.name }}</div>
        </div>
      </div>
    </div>
    <el-divider style="margin: 15px 0" />
    <div class="info-box-content">
      <div class="flex-start">
        <div class="label fs-0">客服</div>
        <div class="platform-box">
          <span>{{ data.serviceName }}</span>
        </div>
      </div>
      <div class="flex-start">
        <div class="label fs-0">创建时间</div>
        <div class="platform-box">
          <span>{{ data.createTime }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { modelDataTableDetail } from '@/api/model/modelData'
import { bizCommissionUnit } from '@/utils/dict'
import { useViewer } from '@/hooks/useViewer'
const { showViewer } = useViewer()
const route = useRoute()

const { proxy } = getCurrentInstance()

const emits = defineEmits(['sendName'])
const {
  biz_model_type,
  biz_nation,
  biz_model_platform,
  biz_model_cooperation,
  biz_model_ageGroup,
  model_family_relationship_type,
  biz_model_status,
} = proxy.useDict(
  'biz_model_type',
  'biz_nation',
  'biz_model_platform',
  'biz_model_cooperation',
  'biz_model_ageGroup',
  'model_family_relationship_type',
  'biz_model_status'
)

const data = ref({
  ageGroup: 1,
  nation: 1,
  status: 0,
})

function handleCommissionUnit(val) {
  let b = bizCommissionUnit.find(item => item.value == val)
  return b ? b.label : ''
}
const init = () => {
  if (route.params.modelId) {
    modelDataTableDetail({ modelId: route.params.modelId }).then(res => {
      if (res.data) {
        data.value = res.data
        emits('sendName', res.data.name)
      }
    })
  }
}
init()
</script>

<style lang="scss" scoped>
.info-box {
  padding: 20px;
  &-head {
    text-align: center;
    .model-name {
      font-size: 18px;
      color: #333;
      margin-top: 5px;
    }
    .model-id {
      font-size: 12px;
      color: #aaa;
      margin-bottom: 8px;
    }
  }
  &-content {
    display: grid;
    gap: 10px 0;
    .label {
      font-size: 13px;
      color: #7f7f7f;
      width: 75px;
    }
    .platform-box {
      :deep(span) {
        font-size: 13px;
        color: #555;
      }
    }
  }
  .merchant-box {
    &-number {
      font-size: 18px;
      color: #7f7f7f;
      font-weight: 700;
      text-align: center;
    }
    &-text {
      font-size: 12px;
      color: #aaa;
    }
  }
  .family-box {
    &-title {
      font-size: 14px;
      color: #333;
    }
    .family-grid {
      display: grid;
      //   grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
      grid-template-columns: repeat(3, 1fr);
      gap: 10px;
      margin-top: 10px;
    }
    .family-member {
      text-align: center;
      .member-name {
        font-size: 12px;
        color: #555;
        margin-top: 5px;
        max-width: 80px;
        text-align: center;
      }
    }
  }
}
</style>
