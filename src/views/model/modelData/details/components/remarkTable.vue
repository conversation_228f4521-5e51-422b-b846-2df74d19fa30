<template>
  <div>
    <el-button type="primary" style="margin: 10px 0" @click="handleOpenRemarkDialog">+ 添加备注</el-button>
    <div class="table-box" style="max-height: 708px; overflow: auto">
      <el-table :data="tableData" v-loading="tableLoading">
        <el-table-column label="备注" prop="remark"></el-table-column>
        <el-table-column label="添加人" prop="createBy" width="180px"></el-table-column>
        <el-table-column label="添加时间" prop="createTime" width="180px"></el-table-column>
      </el-table>
    </div>
    <div class="flex-end" style="margin: 10px">
      <el-pagination
        background
        @size-change="pageChange({ pageNum: 1, pageSize: $event })"
        @current-change="pageChange({ pageNum: $event, pageSize })"
        :current-page="pageNum"
        :page-size="pageSize"
        :page-sizes="pageSizes"
        layout="total, prev, pager, next, jumper"
        :total="total"
      />
    </div>
    <RemarkDialog ref="remarkDialogRef" @success="init" />
  </div>
</template>

<script setup>
import RemarkDialog from '@/views/model/modelData/components/remarkDialog.vue'
import { modelDataTableRemarkList } from '@/api/model/modelData'
const route = useRoute()

const props = defineProps({
  modelName: {
    type: String,
    default: '',
  },
})

const tableData = ref([])
const tableLoading = ref(false)
const remarkDialogRef = ref(null)

const pageNum = ref(1)
const pageSize = ref(50)
const pageSizes = [10, 20, 30, 50]
const total = ref(0)
function pageChange(page) {
  pageNum.value = page.pageNum
  pageSize.value = page.pageSize
  init()
}

const handleOpenRemarkDialog = () => {
  remarkDialogRef.value.open(route.params.modelId, props.modelName)
}

const init = () => {
  modelDataTableRemarkList({
    modelId: route.params.modelId,
    pageNum: pageNum.value,
    pageSize: pageSize.value,
  }).then(res => {
    tableData.value = res.data.rows || []
    total.value = res.data.total
  })
}
init()
</script>

<style lang="scss" scoped>
.table-box {
  border-radius: 8px;
  box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.349019607843137);
  :deep(.el-table) {
    border-radius: 8px;
  }
}
</style>
