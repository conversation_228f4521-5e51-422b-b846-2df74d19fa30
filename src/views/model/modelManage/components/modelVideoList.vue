<template>
  <el-dialog
    v-model="dialogVisible"
    title="案例视频"
    width="550px"
    align-center
    @close="closeChange"
    modal-class="model-video-list-dialog"
  >
    <div class="model-details-box" v-loading="loading">
      <div class="flex-start videoList">
        <VideoCover
          v-for="item in amazonVideo"
          platform="0"
          :key="item.id"
          :src="item.picUri"
          :title="item.name"
          suffix="!squarecompress"
          @click="openVideo(item.videoUrl)"
        />
        <VideoCover
          v-for="item in tiktokVideo"
          platform="1"
          :key="item.id"
          :src="item.picUri"
          :title="item.name"
          suffix="!squarecompress"
          @click="openVideo(item.videoUrl)"
        />
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import VideoCover from '@/components/ImagePreview/videoCover.vue'
import { ElMessage } from 'element-plus'
import { http_reg } from '@/utils/RegExp'
import { getModelDetail } from '@/api/model/model'

const dialogVisible = ref(false)
const loading = ref(false)
const data = ref(null)
const amazonVideo = ref([])
const tiktokVideo = ref([])
const modelId = ref('')

const emits = defineEmits(['close', 'openVideo'])

const open = (id) => {
  modelId.value = id
  dialogVisible.value = true
  getModelDetails()
}

const close = () => {
  dialogVisible.value = false
}

const closeChange = () => {
  emits('close')
}

function getModelDetails() {
  loading.value = true
  getModelDetail(modelId.value)
    .then(res => {
      data.value = res.data
      amazonVideo.value = res.data.amazonVideo
      tiktokVideo.value = res.data.tiktokVideo
    })
    .finally(() => (loading.value = false))
}

function openVideo(videoUrl) {
  if (!videoUrl) {
    ElMessage.warning('无视频链接！')
    return
  }
  if (!http_reg.test(videoUrl)) {
    ElMessage.warning('视频链接格式有误！')
    return
  }
  emits('openVideo', videoUrl)
}

defineExpose({
  open,
  close,
})
</script>

<style scoped lang="scss">
.model-details-box {
  min-height: 200px;
  
  .gap {
    gap: 10px;
  }
  .info-box {
    margin: 10px 0;
    gap: 5px;

    .head-img {
      width: 80px;
      height: 80px;
      flex-shrink: 0;
      border-radius: 50%;
      border: 1px solid var(--border-gray-color);
    }
    .name {
      font-size: 16px;
      font-weight: 600;
      color: var(--text-color);
    }
    .text {
      font-size: 14px;
      color: var(--text-color);
    }

    .collect-box {
      flex-direction: column;
      flex-shrink: 0;
      cursor: pointer;
      background: #cee6a9;
      border-radius: 10px;
      padding: 10px;
      color: #ec808d;
    }
  }
  .tag-box {
    margin: 10px 0;
  }

  .videoList {
    gap: 20px 40px;
    flex-wrap: wrap;
  }
}
</style>
