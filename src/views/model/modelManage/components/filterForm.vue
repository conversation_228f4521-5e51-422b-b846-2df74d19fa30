<template>
  <el-form
    ref="queryRef"
    class="filter-form"
    :inline="false"
    style="max-width: 100%"
    label-width="80"
    @submit.prevent
  >
    <el-row>
      <el-col :span="12">
        <el-form-item label="搜索">
          <div class="flex-center input-box">
            <el-input
              v-model="keyword"
              @keyup.enter="change"
              style="width: 450px"
              placeholder="可搜索模特标签"
              clearable
            />
            <el-icon color="#aaa" size="16" @click="change"><Search /></el-icon>
          </div>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="模特姓名">
          <el-select
            v-model="modelId"
            placeholder="请选择"
            filterable
            clearable
            style="width: 180px"
            :loading="modelListLoading"
            @change="change"
          >
            <el-option
              v-for="item in modelList"
              :key="item.id"
              :label="item.nameAccount"
              :value="item.id"
            />
          </el-select>
          <el-checkbox v-model="isFamilyChecked" label="包含家庭成员" style="margin-left: 10px" @change="change" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="12">
        <el-form-item label="适合平台" prop="platform">
          <el-checkbox-button v-model="platformCheckAll" @change="handleCheckAllChange($event, 'platform')">
            全部
          </el-checkbox-button>
          <el-checkbox-group v-model="platform" @change="handleCheckedChange($event, 'platform')">
            <el-checkbox-button v-for="item in bizModelPlatform" :key="item.value" :value="item.value">
              {{ item.label }}
            </el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="模特类型">
          <el-checkbox-button v-model="modelTypeCheckAll" @change="handleCheckAllChange($event, 'modelType')">
            全部
          </el-checkbox-button>
          <el-checkbox-group v-model="type" @change="handleCheckedChange($event, 'modelType')">
            <el-checkbox-button v-for="item in bizModelType" :key="item.value" :value="item.value">
              {{ item.label }}
            </el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="12">
        <el-form-item label="模特状态">
          <el-checkbox-button v-model="statusCheckAll" @change="handleCheckAllChange($event, 'status')">
            全部
          </el-checkbox-button>
          <el-checkbox-group v-model="status" @change="handleCheckedChange($event, 'status')">
            <el-checkbox-button v-for="item in bizModelStatus" :key="item.value" :value="item.value">
              {{ item.label }}
            </el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="模特等级">
          <el-checkbox-button
            v-model="cooperationCheckAll"
            @change="handleCheckAllChange($event, 'cooperation')"
          >
            全部
          </el-checkbox-button>
          <el-checkbox-group v-model="cooperation" @change="handleCheckedChange($event, 'cooperation')">
            <el-checkbox-button v-for="item in bizModelCooperation" :key="item.value" :value="item.value">
              {{ item.label }}{{ item.value == 0 ? '(7.8分以下)' : item.value == 1 ? '(7.8分及以上)' : '' }}
            </el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="12">
        <el-form-item label="性别">
          <el-checkbox-button v-model="sexCheckAll" @change="handleCheckAllChange($event, 'sex')">
            全部
          </el-checkbox-button>
          <el-checkbox-group v-model="sex" @change="handleCheckedChange($event, 'sex')">
            <el-checkbox-button :value="1">男性</el-checkbox-button>
            <el-checkbox-button :value="0">女性</el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="年龄层">
          <el-checkbox-button v-model="ageGroupCheckAll" @change="handleCheckAllChange($event, 'ageGroup')">
            全部
          </el-checkbox-button>
          <el-checkbox-group v-model="ageGroup" @change="handleCheckedChange($event, 'ageGroup')">
            <el-checkbox-button v-for="item in bizModelAgeGroup" :key="item.value" :value="item.value">
              {{ item.label }}
            </el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="12">
        <el-form-item label="国家">
          <el-checkbox-button v-model="nationCheckAll" @change="handleCheckAllChange($event, 'nation')">
            全部
          </el-checkbox-button>
          <el-checkbox-group v-model="nation" @change="handleCheckedChange($event, 'nation')">
            <el-checkbox-button v-for="item in bizNation" :key="item.value" :value="item.value">
              {{ item.label }}
            </el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="关联客服">
          <el-select
            v-model="persons"
            placeholder="请选择负责对接的客服/开发人"
            multiple
            filterable
            collapse-tags
            collapse-tags-tooltip
            clearable
            style="width: 280px"
            @change="change"
          >
            <el-option v-for="item in assigneeList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
          <!-- <SelectLoad
            v-model="persons"
            :request="listUser"
            :requestCallback="res => res.data"
            :totalCallback="res => res.data.length"
            keyValue="userId"
            keyLabel="userName"
            keyWord="userName"
            :multiple="true"
            :collapseTags="true"
            :reserveKeyword="false"
            placeholder="请选择负责对接的客服"
            style="width: 280px"
            @change="change"
          /> -->
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="12">
        <el-form-item label="亲属关系">
          <el-checkbox-button v-model="familyCheckAll" @change="handleCheckAllChange($event, 'family')">
            全部
          </el-checkbox-button>
          <el-checkbox-group v-model="family" @change="handleCheckedChange($event, 'family')">
            <el-checkbox-button v-for="item in bizModelFamilyType" :key="item.value" :value="item.value">
              {{ item.label }}
            </el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="是否展示">
          <el-checkbox-button v-model="switchCheckAll" @change="handleCheckAllChange($event, 'isShow')">
            全部
          </el-checkbox-button>
          <el-checkbox-group v-model="isShow" @change="handleCheckedChange($event, 'isShow')">
            <el-checkbox-button :value="1">展示</el-checkbox-button>
            <el-checkbox-button :value="0">隐藏</el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="12">
        <el-form-item label="创建时间" style="width: 500px">
          <el-date-picker
            v-model="submitTime"
            format="YYYY/M/D HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="datetimerange"
            range-separator="-"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
            :shortcuts="shortcuts"
            @change="change"
          ></el-date-picker>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="蜗牛照">
          <el-checkbox-button
            v-model="snailPicCheckAll"
            @change="handleCheckAllChange($event, 'haveSnailPics')"
          >
            全部
          </el-checkbox-button>
          <el-checkbox-group v-model="haveSnailPics" @change="handleCheckedChange($event, 'haveSnailPics')">
            <el-checkbox-button :value="1">有</el-checkbox-button>
            <el-checkbox-button :value="0">无</el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-form-item label="擅长品类">
        <div class="flex-start" style="align-items: flex-start">
          <el-checkbox-button v-model="categoryCheckAll" @change="handleCheckAllChange($event, 'category')">
            全部
          </el-checkbox-button>
          <el-checkbox-group v-model="category" @change="handleCheckedChange($event, 'category')">
            <el-checkbox-button v-for="item in modelCategoryList" :key="item.id" :value="item.id">
              {{ item.name }}
            </el-checkbox-button>
          </el-checkbox-group>
        </div>
      </el-form-item>
    </el-row>
    <!-- <el-form-item label="性别">
      <el-checkbox-button v-model="sexCheckAll" @change="handleCheckAllChange($event, 'sex')">
        全部
      </el-checkbox-button>
      <el-checkbox-group v-model="sex" @change="handleCheckedChange($event, 'sex')">
        <el-checkbox-button :value="1">男性</el-checkbox-button>
        <el-checkbox-button :value="0">女性</el-checkbox-button>
      </el-checkbox-group>
    </el-form-item> -->
    <!-- <div class="filter-box" :class="{ maxHeight: isFilterSearchBox }"> -->
    <!-- <el-form-item label="年龄层">
        <el-checkbox-button v-model="ageGroupCheckAll" @change="handleCheckAllChange($event, 'ageGroup')">
          全部
        </el-checkbox-button>
        <el-checkbox-group v-model="ageGroup" @change="handleCheckedChange($event, 'ageGroup')">
          <el-checkbox-button
            v-for="item in bizModelAgeGroup"
            :key="item.value"
            :value="item.value"
          >
            {{ item.label }}
          </el-checkbox-button>
        </el-checkbox-group>
      </el-form-item> -->

    <!-- <el-form-item label="模特状态">
        <el-checkbox-button v-model="statusCheckAll" @change="handleCheckAllChange($event, 'status')">
          全部
        </el-checkbox-button>
        <el-checkbox-group v-model="status" @change="handleCheckedChange($event, 'status')">
          <el-checkbox-button
            v-for="item in bizModelStatus"
            :key="item.value"
            :value="item.value"
          >
            {{ item.label }}
          </el-checkbox-button>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="模特等级">
        <el-checkbox-button
          v-model="cooperationCheckAll"
          @change="handleCheckAllChange($event, 'cooperation')"
        >
          全部
        </el-checkbox-button>
        <el-checkbox-group v-model="cooperation" @change="handleCheckedChange($event, 'cooperation')">
          <el-checkbox-button
            v-for="item in bizModelCooperation"
            :key="item.value"
            :value="item.value"
          >
            {{ item.label }}
          </el-checkbox-button>
        </el-checkbox-group>
      </el-form-item> -->
    <!-- </div> -->
  </el-form>
  <div class="filtter-btn">
    <div class="filtter-selected">
      <div class="filter-selected__title">已选条件</div>
      <div class="filter-selected__content">
        <el-tag
          v-for="item in platformVals"
          :key="item.value"
          closable
          effect="plain"
          @close="closeTags('platform', item.value)"
        >
          {{ item.label }}
        </el-tag>
        <el-tag
          v-for="item in nationVals"
          :key="item.value"
          closable
          effect="plain"
          @close="closeTags('nation', item.value)"
        >
          {{ item.label }}
        </el-tag>
        <el-tag
          v-for="item in sexVals"
          :key="item.value"
          closable
          effect="plain"
          @close="closeTags('sex', item.value)"
        >
          {{ item.label }}
        </el-tag>
        <el-tag
          v-for="item in ageGroupVals"
          :key="item.value"
          closable
          effect="plain"
          @close="closeTags('ageGroup', item.value)"
        >
          {{ item.label }}
        </el-tag>
        <el-tag
          v-for="item in modelTypeVals"
          :key="item.value"
          closable
          effect="plain"
          @close="closeTags('modelType', item.value)"
        >
          {{ item.label }}
        </el-tag>
        <el-tag
          v-for="item in statusVals"
          :key="item.value"
          closable
          effect="plain"
          @close="closeTags('status', item.value)"
        >
          {{ item.label }}
        </el-tag>
        <el-tag
          v-for="item in cooperationVals"
          :key="item.value"
          closable
          effect="plain"
          @close="closeTags('cooperation', item.value)"
        >
          {{ item.label }}
        </el-tag>
        <el-tag
          v-for="item in familyVals"
          :key="item.value"
          closable
          effect="plain"
          @close="closeTags('family', item.value)"
        >
          {{ item.label }}
        </el-tag>
        <el-tag
          v-for="item in categoryVals"
          :key="item.id"
          closable
          effect="plain"
          @close="closeTags('category', item.id)"
        >
          {{ item.name }}
        </el-tag>
        <el-tag
          v-for="item in isShowVals"
          :key="item.id"
          closable
          effect="plain"
          @close="closeTags('isShow', item.id)"
        >
          {{ item.name }}
        </el-tag>
        <el-tag
          v-for="item in haveSnailPicsVals"
          :key="item.id"
          closable
          effect="plain"
          @close="closeTags('haveSnailPics', item.id)"
        >
          {{ item.name }}
        </el-tag>
      </div>
      <el-button
        v-if="
          platformVals.length ||
          nationVals.length ||
          sexVals.length ||
          ageGroupVals.length ||
          modelTypeVals.length ||
          statusVals.length ||
          cooperationVals.length ||
          familyVals.length ||
          categoryVals.length ||
          isShowVals.length ||
          haveSnailPicsVals.length
        "
        link
        type="primary"
        @click="reset"
      >
        清除筛选
      </el-button>
    </div>
    <!-- <el-button link type="primary" @click="isFilterSearchBox = !isFilterSearchBox">
      {{ isFilterSearchBox ? '收起' : '展开' }}
    </el-button> -->
  </div>
</template>

<script setup>
import { bizModelStatus } from '@/utils/dict'
import { modelCategorySelectRank, modelNameAccountSelect } from '@/api/model/model'
import SelectLoad from '@/components/Select/SelectLoad.vue'
import { listUser } from '@/api/system/user'
import { modelPersonsSelect } from '@/api/model/model'

const emits = defineEmits(['change'])
const props = defineProps({
  bizModelPlatform: {
    type: Array,
    default: () => [],
  },
  bizNation: {
    type: Array,
    default: () => [],
  },
  bizModelAgeGroup: {
    type: Array,
    default: () => [],
  },
  bizModelType: {
    type: Array,
    default: () => [],
  },
  bizModelCooperation: {
    type: Array,
    default: () => [],
  },
  bizModelFamilyType: {
    type: Array,
    default: () => [],
  },
})
const sexOptions = [
  { label: '男性', value: 1 },
  { label: '女性', value: 0 },
]
const shortcuts = [
  {
    text: '今天',
    value: () => {
      const start = new Date()
      start.setHours(0, 0, 0, 0)
      const end = new Date()
      end.setHours(23, 59, 59, 59)
      return [start, end]
    },
  },
]

const modelListLoading = ref(false)
const modelId = ref('')
const modelList = ref([])
const modelCategoryList = ref([])

function getModelListSelect() {
  modelListLoading.value = true
  modelNameAccountSelect().then(res => {
    if (res.code == 200) {
      modelList.value = res.data
    }
  }).finally(() => {
    modelListLoading.value = false
  })
}
// 模特擅长品类
function getModelCategorySelect() {
  modelCategorySelectRank({ rank: 1, categoryId: 1008 }).then(res => {
    if (res.code == 200) {
      modelCategoryList.value = res.data
    }
  })
}
const assigneeList = ref([])
function getUserList() {
  modelPersonsSelect().then(res => {
    assigneeList.value = res.data || []
  })
}

getModelListSelect()
getModelCategorySelect()
getUserList()

const isFilterSearchBox = ref(false)

const isFamilyChecked = ref(false)

const platformCheckAll = ref(true)
const nationCheckAll = ref(true)
const sexCheckAll = ref(true)
const ageGroupCheckAll = ref(true)
const modelTypeCheckAll = ref(true)
const statusCheckAll = ref(true)
const cooperationCheckAll = ref(true)
const familyCheckAll = ref(true)
const switchCheckAll = ref(true)
const categoryCheckAll = ref(true)
const snailPicCheckAll = ref(true)

const keyword = ref('')
const platform = ref([])
const nation = ref([])
const sex = ref([])
const ageGroup = ref([])
const persons = ref([])
const type = ref([])
const status = ref([])
const cooperation = ref([])
const family = ref([])
const isShow = ref([])
const category = ref([])
const haveSnailPics = ref([])
const submitTime = ref([])

const platformVals = computed(() => {
  return props.bizModelPlatform.filter(item => platform.value.includes(item.value))
})
const nationVals = computed(() => {
  return props.bizNation.filter(item => nation.value.includes(item.value))
})
const ageGroupVals = computed(() => {
  return props.bizModelAgeGroup.filter(item => ageGroup.value.includes(item.value))
})
const sexVals = computed(() => {
  return sexOptions.filter(item => sex.value.includes(item.value))
})
const modelTypeVals = computed(() => {
  return props.bizModelType.filter(item => type.value.includes(item.value))
})
const statusVals = computed(() => {
  return bizModelStatus.filter(item => status.value.includes(item.value))
})
const cooperationVals = computed(() => {
  return props.bizModelCooperation.filter(item => cooperation.value.includes(item.value))
})
const familyVals = computed(() => {
  return props.bizModelFamilyType.filter(item => family.value.includes(item.value))
})
const isShowVals = computed(() => {
  return [
    { id: 1, name: '展示' },
    { id: 0, name: '隐藏' },
  ].filter(item => isShow.value.includes(item.id))
})
const categoryVals = computed(() => {
  return modelCategoryList.value.filter(item => category.value.includes(item.id))
})
const haveSnailPicsVals = computed(() => {
  return [
    { id: 1, name: '有蜗牛照' },
    { id: 0, name: '无蜗牛照' },
  ].filter(item => haveSnailPics.value.includes(item.id))
})
/**
 * 已选条件关闭标签
 */
function closeTags(typeAction, value) {
  switch (typeAction) {
    case 'nation':
      nation.value = nation.value.filter(item => item !== value)
      if (nation.value.length == 0) nationCheckAll.value = true
      break
    case 'ageGroup':
      ageGroup.value = ageGroup.value.filter(item => item !== value)
      if (ageGroup.value.length == 0) ageGroupCheckAll.value = true
      break
    case 'platform':
      platform.value = platform.value.filter(item => item !== value)
      if (platform.value.length == 0) platformCheckAll.value = true
      break
    case 'sex':
      sex.value = sex.value.filter(item => item !== value)
      if (sex.value.length == 0) sexCheckAll.value = true
      break
    case 'modelType':
      type.value = type.value.filter(item => item !== value)
      if (type.value.length == 0) modelTypeCheckAll.value = true
      break
    case 'status':
      status.value = status.value.filter(item => item !== value)
      if (status.value.length == 0) statusCheckAll.value = true
      break
    case 'cooperation':
      cooperation.value = cooperation.value.filter(item => item !== value)
      if (cooperation.value.length == 0) cooperationCheckAll.value = true
      break
    case 'family':
      family.value = family.value.filter(item => item !== value)
      if (family.value.length == 0) familyCheckAll.value = true
      break
    case 'isShow':
      isShow.value = isShow.value.filter(item => item !== value)
      if (isShow.value.length == 0) switchCheckAll.value = true
      break
    case 'category':
      category.value = category.value.filter(item => item !== value)
      if (category.value.length == 0) categoryCheckAll.value = true
      break
    case 'haveSnailPics':
      haveSnailPics.value = haveSnailPics.value.filter(item => item !== value)
      if (haveSnailPics.value.length == 0) snailPicCheckAll.value = true
      break
  }
  change()
}
/**
 * 全部按钮事件
 */
function handleCheckAllChange(val, typeAction) {
  switch (typeAction) {
    case 'platform':
      platform.value.length = 0
      platformCheckAll.value = true
      break
    case 'nation':
      nation.value.length = 0
      nationCheckAll.value = true
      break
    case 'ageGroup':
      ageGroup.value.length = 0
      ageGroupCheckAll.value = true
      break
    case 'sex':
      sex.value.length = 0
      sexCheckAll.value = true
      break
    case 'modelType':
      type.value.length = 0
      modelTypeCheckAll.value = true
      break
    case 'status':
      status.value.length = 0
      statusCheckAll.value = true
      break
    case 'cooperation':
      cooperation.value.length = 0
      cooperationCheckAll.value = true
      break
    case 'family':
      family.value.length = 0
      familyCheckAll.value = true
      break
    case 'isShow':
      isShow.value.length = 0
      switchCheckAll.value = true
      break
    case 'category':
      category.value.length = 0
      categoryCheckAll.value = true
      break
    case 'haveSnailPics':
      haveSnailPics.value.length = 0
      snailPicCheckAll.value = true
      break
  }
  change()
}
/**
 * checkbox事件
 */
function handleCheckedChange(val, typeBtn) {
  switch (typeBtn) {
    case 'platform':
      platform.value.length == 0 ? (platformCheckAll.value = true) : (platformCheckAll.value = false)
      break
    case 'nation':
      nationCheckAll.value = false
      nation.value.length == 0 ? (nationCheckAll.value = true) : (nationCheckAll.value = false)
      break
    case 'ageGroup':
      ageGroupCheckAll.value = false
      ageGroup.value.length == 0 ? (ageGroupCheckAll.value = true) : (ageGroupCheckAll.value = false)
      break
    case 'sex':
      sexCheckAll.value = false
      sex.value.length == 0 ? (sexCheckAll.value = true) : (sexCheckAll.value = false)
      break
    case 'modelType':
      modelTypeCheckAll.value = false
      type.value.length == 0 ? (modelTypeCheckAll.value = true) : (modelTypeCheckAll.value = false)
      break
    case 'status':
      statusCheckAll.value = false
      status.value.length == 0 ? (statusCheckAll.value = true) : (statusCheckAll.value = false)
      break
    case 'cooperation':
      cooperationCheckAll.value = false
      cooperation.value.length == 0 ? (cooperationCheckAll.value = true) : (cooperationCheckAll.value = false)
      break
    case 'family':
      familyCheckAll.value = false
      // 单选
      if (val.length > 1) {
        family.value = [val[1]]
      } else {
        family.value = val
      }
      family.value.length == 0 ? (familyCheckAll.value = true) : (familyCheckAll.value = false)
      break
    case 'isShow':
      switchCheckAll.value = false
      isShow.value.length == 0 ? (switchCheckAll.value = true) : (switchCheckAll.value = false)
      break
    case 'category':
      categoryCheckAll.value = false
      category.value.length == 0 ? (categoryCheckAll.value = true) : (categoryCheckAll.value = false)
      break
    case 'haveSnailPics':
      snailPicCheckAll.value = false
      haveSnailPics.value.length == 0 ? (snailPicCheckAll.value = true) : (snailPicCheckAll.value = false)
      break
  }
  change()
}
/**
 * 清除筛选
 */
function reset() {
  nation.value.length = 0
  nationCheckAll.value = true
  platform.value.length = 0
  platformCheckAll.value = true
  sex.value.length = 0
  sexCheckAll.value = true
  ageGroup.value.length = 0
  ageGroupCheckAll.value = true
  type.value.length = 0
  modelTypeCheckAll.value = true
  status.value.length = 0
  statusCheckAll.value = true
  cooperation.value.length = 0
  cooperationCheckAll.value = true
  family.value.length = 0
  familyCheckAll.value = true
  isShow.value.length = 0
  switchCheckAll.value = true
  category.value.length = 0
  categoryCheckAll.value = true
  snailPicCheckAll.value = true
  haveSnailPics.value.length = 0
  change()
}

function change() {
  emits('change', setParams())
}

function setParams() {
  return {
    keyword: keyword.value,
    modelId: modelId.value,
    includeFamily: isFamilyChecked.value ? 1 : undefined,
    platform: platform.value.join(','),
    nation: nation.value.join(','),
    sex: sex.value.join(','),
    ageGroup: ageGroup.value.join(','),
    type: type.value.join(','),
    status: status.value.join(','),
    cooperation: cooperation.value.join(','),
    modelFamilyRelationships: family.value.join(','),
    specialtyCategory: category.value.join(','),
    persons: persons.value,
    isShow: isShow.value.join(','),
    createTimeBegin: submitTime.value && submitTime.value.length > 0 ? submitTime.value[0] : '',
    createTimeEnd: submitTime.value && submitTime.value.length > 0 ? submitTime.value[1] : '',
    haveSnailPics: haveSnailPics.value.join(','),
  }
}
</script>

<style scoped lang="scss">
@import '@/assets/styles/customForm.scss';
// @import '@/assets/styles/form-disabled.scss';

:deep(.el-radio-button) {
  margin-bottom: 0;
}
:deep(.el-checkbox-button) {
  margin-bottom: 5px;
}

.filter-form {
  .el-form-item {
    margin-bottom: 8px;
  }
}

.input-box {
  position: relative;
  margin-bottom: 5px;

  :deep(.el-input) {
    .el-input__wrapper {
      padding-right: 32px;
    }
  }

  .el-icon {
    position: absolute;
    top: 0;
    right: 0;
    cursor: pointer;
    width: 32px;
    height: 32px;
  }
}
.filter-box {
  max-height: 0;
  overflow: hidden;
  transition: 0.5s;

  &.maxHeight {
    max-height: 400px;
  }
}
.filtter-btn {
  display: flex;
  align-items: baseline;
  justify-content: space-between;
}
.filtter-selected {
  display: flex;
  align-items: baseline;
  gap: 10px;

  .filter-selected__title {
    width: 70px;
    font-size: 14px;
    color: #606266;
    font-weight: 700;
    text-align: right;
  }

  .filter-selected__content {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }
}
</style>
