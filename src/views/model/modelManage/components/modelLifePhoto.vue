<template>
  <el-dialog
    v-model="dialogVisible"
    title="模特生活照"
    width="660px"
    align-center
    append-to-body
    destroy-on-close
    modal-class="model-life-photo-dialog"
  >
    <div class="flex-between model-life-photo-list">
      <div class="list-item">
        <template v-for="item in lifePhotoListLeft" :key="item.id">
          <img :src="$picUrl + item.picUrl" alt="">
        </template>
      </div>
      <div class="list-item">
        <template v-for="item in lifePhotoListRight" :key="item.id">
          <img :src="$picUrl +item.picUrl" alt="">
        </template>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
defineExpose({
  open,
})

const dialogVisible = ref(false)
const lifePhotoListLeft = ref([])
const lifePhotoListRight = ref([])

function open(photos) {
  if(!photos.length) return
  lifePhotoListLeft.value = []
  lifePhotoListRight.value = []

  photos.forEach((item, i) => {
    if(i % 2 === 0) {
      let obj = {
        picUrl: item + (i === 0 ? '!1x1compress' : '!3x4compress')
      }
      lifePhotoListLeft.value.push(obj)
    } else {
      let obj = {
        picUrl: item + '!3x4compress'
      }
      lifePhotoListRight.value.push(obj)
    }
  })
  dialogVisible.value = true
}

</script>

<style scoped lang="scss">
.model-life-photo-list {
  align-items: flex-start;
  min-height: 400px;
  max-height: 700px;
  padding-right: 10px;
  overflow-y: overlay;

  .list-item {
    width: 300px;

    img {
      width: 100%;
    }
  }
}
</style>
<style lang="scss">
.model-life-photo-dialog {
  .el-dialog__body {
    padding: 0 5px 20px;
  }
}
</style>
