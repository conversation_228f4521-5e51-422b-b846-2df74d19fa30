<template>
  <div class="flex-start">
    <input ref="inputFileRef" type="file" style="display: none" @change="handleChange" />
    <el-image v-if="file.url" :src="file.url" style="width: 50px; height: 50px" fit="fill"></el-image>
    <el-button v-btn v-if="!disabled" type="warning" plain style="margin-left: 10px" link @click="handleClick">
      {{ buttonText }}
    </el-button>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'
import { uploadCloudFile } from '@/api/index'
import { ElMessage } from 'element-plus'

const props = defineProps({
  file: {
    type: Object,
    default: () => ({}),
  },
  disabled: {
    type: Boolean,
    default: false,
  },
})

const emits = defineEmits(['change'])

const file = toRef(props, 'file')
const inputFileRef = ref()
// const disabled = ref(false)
const buttonText = computed(() => {
  return file.value?.url ? '修改' : '+ 封面'
})

function handleClick() {
  // console.log(inputFileRef.value);
  inputFileRef.value.click()
}

function handleChange(f) {
  // console.log(f.target.files[0]);
  if (beforeUpload(f.target.files[0])) {
    const formData = new FormData()
    formData.append('file', f.target.files[0])
    uploadCloudFile(formData)
      .then(res => {
        if (res.code == 200) {
          file.value.name = res.data.name
          file.value.url = res.data.picUrl
          file.value.id = res.data.id
          emits('change')
        }
      })
      .catch(() => {
        file.value.name = ''
        file.value.url = ''
        file.value.id = ''
      })
  }
}

const whiteList = 'PNG,png,JPG,jpg,JPEG,jpeg'

function beforeUpload(raw) {
  const fileSuffix = raw.name.substring(raw.name.lastIndexOf('.') + 1)
  if (whiteList.indexOf(fileSuffix) === -1) {
    ElMessage({
      message: `请上传格式为png、jpg、jpeg的图片！`,
      type: 'warning',
    })
    return false
  }
  if (raw.size > 1024 * 1024 * 5) {
    ElMessage({
      message: `上传大小不超过5M！`,
      type: 'warning',
    })
    return false
  }
  return true
}
</script>

<style scoped lang="scss"></style>
