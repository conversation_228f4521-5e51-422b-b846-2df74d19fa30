<template>
  <div class="fileBox">
    <el-upload
      class="upload-video"
      action=""
      :show-file-list="false"
      :limit="limit"
      :http-request="uploadVideoFile"
      :before-upload="beforeUpload"
    >
      {{ btnBeforText }}
      <el-button v-btn
        v-if="!disabled"
        type="warning"
        plain
        icon="Plus"
        style="margin-left: 10px"
        :loading="loading"
      />
      {{ btnAfterText }}
    </el-upload>
    <div class="list flex-between" v-for="(item, i) in fileList" :key="item.url">
      <span class="name one-ell" @click="open(item.url)">{{ item.name }}</span>
      <el-popconfirm style="flex-shrink: 0" v-if="!disabled" title="确定删除吗？" @confirm="del(item, i)">
        <template #reference>
          <!-- <span class="del">删除</span> -->
          <el-icon class="del"><Delete /></el-icon>
        </template>
      </el-popconfirm>
    </div>
  </div>
</template>

<script setup name="upVideo">
import { uploadCloudFile } from '@/api/index'
import { ElMessage } from 'element-plus'

const props = defineProps({
  btnBeforText: {
    type: String,
  },
  btnAfterText: {
    type: String,
  },
  limit: {
    type: Number,
    default: 10,
  },
  fileList: {
    type: Array,
    default: () => [],
  },
  disabled: {
    type: Boolean,
    default: false,
  },
})

const loading = ref(false)
const fileList = toRef(props, 'fileList')

// 上传接口
function uploadVideoFile(options) {
  const formData = new FormData()
  formData.append('file', options.file)
  uploadCloudFile(formData).then(res => {
    if (res.code == 200) {
      fileList.value.push(res.data)
    }
  })
}

const whiteList = 'MP4,mp4,WMV,wmv,AVI,avi'

function beforeUpload(raw) {
  const fileSuffix = raw.name.substring(raw.name.lastIndexOf('.') + 1)
  if (whiteList.indexOf(fileSuffix) === -1) {
    ElMessage({
      message: `请上传格式为MP4、wmv、avi的文件！`,
      type: 'warning',
    })
    return false
  }
  if (raw.size > 1024 * 1024 * 5) {
    ElMessage({
      message: `上传大小不超过5M！`,
      type: 'warning',
    })
    return false
  }
  if (fileList.value.length >= props.limit) {
    ElMessage({
      message: `最多可上传${props.limit}个视频！`,
      type: 'warning',
    })
    return false
  }
  return true
}

// 删除
function del(item, i) {
  fileList.value.splice(i, 1)
}

function open(url) {
  window.open(url)
}
</script>

<style lang="scss" scoped>
.fileBox {
  .list {
    max-width: 200px;
    line-height: 26px;
    font-size: 14px;
    margin-left: 30px;

    .name {
      cursor: pointer;
      color: #3bb4f2;
    }
    .del {
      margin-left: 10px;
      cursor: pointer;
      color: #d34d4d;
    }
  }
}
</style>
