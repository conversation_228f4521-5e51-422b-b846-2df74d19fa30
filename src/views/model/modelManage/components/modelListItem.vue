<template>
  <label :for="`model-list-checkbox-${data.id}`" class="model-list-item">
    <el-checkbox v-if="isSelection" class="checkbox" :id="`model-list-checkbox-${data.id}`" :value="data.id">
      {{ '' }}
    </el-checkbox>
    <div class="sort-number">排序值：{{ data.sort }}</div>
    <div class="left-box">
      <div class="tag-box" :style="{ backgroundColor: data.isShow == 1 ? '#BDDE8B' : '#C35A67' }">
        {{ data.isShow == 1 ? '展示' : '隐藏' }}
      </div>
      <div class="status-box">
        <el-text v-if="data.status == 0" class="primary" @click.prevent="handleAction('正常合作')">
          正常合作
          <el-icon><EditPen /></el-icon>
        </el-text>
        <el-text v-else-if="data.status == 1" class="warning" @click.prevent="handleAction('暂停合作')">
          暂停合作
          <el-icon><EditPen /></el-icon>
        </el-text>
        <el-text v-else-if="data.status == 3" class="danger" @click.prevent="handleAction('取消合作')">
          取消合作
          <el-icon><EditPen /></el-icon>
        </el-text>
        <el-tooltip v-else :content="`${data.travelEndTime}恢复`" placement="bottom-end" effect="light">
          <el-text class="success" @click.prevent="handleAction('行程中')">
            行程中
            <el-icon><EditPen /></el-icon>
          </el-text>
        </el-tooltip>
      </div>
    </div>
    <div class="flex-start gap-10 content-box" ref="modelListBoxRef">
      <div class="percentage-img" @click.prevent>
        <PercentageImg
          width="90px"
          :src="$picUrl + data?.modelPic"
          :moreLength="data?.lifePhoto?.length || 0"
          radius="4px"
          @more="viewLifePhoto"
        >
          <template #footer>
            <div class="text-below-image">
              {{ data?.haveSnailPic === 0 ? '无蜗牛照' : data?.haveSnailPic === null ? '暂未填写' : '' }}
            </div>
          </template>
        </PercentageImg>
        <div class="hint" v-if="data.topTime != null">置顶</div>
        <div class="hint-box" v-if="data.status">
          <div class="hint-box-text" v-if="data.status == 1">
            <img style="width: 30px; height: 30px" src="@/assets/icons/svg/pause.svg" />
            <div style="color: #e6a23c">暂停合作</div>
          </div>
          <div class="hint-box-text" v-if="data.status == 2">
            <img style="width: 30px; height: 30px" src="@/assets/icons/svg/during.svg" />
            <div style="color: #67c23a">行程中</div>
          </div>
          <div class="hint-box-text" v-if="data.status == 3">
            <img style="width: 30px; height: 30px" src="@/assets/icons/svg/stop.svg" />
            <div style="color: #f56c6c">取消合作</div>
          </div>
        </div>
      </div>
      <div class="info-box">
        <div class="name one-ell" style="margin-bottom: 10px">
          <div>{{ data.name }}</div>
          <div class="acc">{{ data.account ? `(ID:${data.account})` : '' }}</div>
        </div>

        <!-- <div class="flex-start tag-info">
          <div style="color: #606266" v-if="data.cooperationScore || data.cooperationScore === 0">
            模特评分：{{ data.cooperationScore }}分
          </div>
          <biz-model-cooperation :value="data.cooperation" type="primary" />
        </div> -->
        <div class="flex-start tag-info">
          <ModelScore
            v-if="data.cooperationScore || data.cooperationScore === 0"
            :score="data.cooperationScore"
          />
          <BizModelTypeNew :value="data.type" />
          <!-- <biz-model-cooperation :value="data.cooperation" /> -->
          <!-- <biz-model-platform :value="data.platform" /> -->
        </div>
        <div class="flex-start tag-info" style="gap: 10px">
          <el-text v-if="data.sex == 1">
            <el-icon color="#777777"><Male /></el-icon>
            男
          </el-text>
          <el-text v-else>
            <el-icon color="#777777"><Female /></el-icon>
            女
          </el-text>
          <biz-model-ageGroup :value="data.ageGroup" tag="text">
            <template v-slot="{ dict }">
              <el-text>
                <el-icon color="#777777"><User /></el-icon>
                {{ dict.label }}
              </el-text>
            </template>
          </biz-model-ageGroup>
          <biz-nation :value="data.nation" tag="text">
            <template v-slot="{ dict }">
              <el-text>
                <el-icon color="#777777"><LocationInformation /></el-icon>
                {{ dict.label }}
              </el-text>
            </template>
          </biz-nation>
          <el-text v-if="data.familyNum">
            <el-icon color="#777777">
              <svg-icon-users />
            </el-icon>
            {{ data.familyNum }}
          </el-text>
        </div>
        <div class="flex-start tag-info">
          <span style="color: #777">平台：</span>
          <biz-model-platform :value="data.platform" type="info" />
        </div>
        <!-- <div class="tips">
          <span>客服：{{ data.persons[0]?.name }}</span>
          &emsp;
          <span>开发人：{{ data.developerUser?.name || '暂未填写' }}</span>
          <br />
          <span>创建时间：{{ data.createTime }}</span>
        </div> -->
      </div>
      <div class="tags-box" @click.prevent>
        <div class="flex-start gap algin_b" style="margin-bottom: 10px">
          <div class="label-text" style="color: #333">模特标签</div>
          <div class="flex-start gap tag-list">
            <template v-for="(item, index) in data.tags" :key="item.dictId">
              <template v-if="index < 9">
                <el-tag v-if="stringLength(item.name) <= 9" class="one-ell tag" round type="info">
                  {{ item.name }}
                </el-tag>
                <el-tooltip v-else placement="top" effect="light" trigger="hover">
                  <el-tag class="one-ell tag cur" type="info" round>{{ item.name }}</el-tag>
                  <template #content>
                    <el-tag class="tag" round type="info">{{ item.name }}</el-tag>
                  </template>
                </el-tooltip>
              </template>
            </template>
            <template v-if="data.tags.length > 9">
              <el-tooltip placement="top" effect="light" trigger="click">
                <el-tag class="tag cur" round type="info">. . .</el-tag>
                <template #content>
                  <div class="tooltip-tag" style="max-width: 900px">
                    <template v-for="item in data.tags" :key="item.dictId">
                      <el-tag class="tag" round type="info">{{ item.name }}</el-tag>
                    </template>
                  </div>
                </template>
              </el-tooltip>
            </template>
          </div>
        </div>
        <div class="flex-start gap algin_b">
          <div class="label-text" style="color: #333">擅长领域</div>
          <div class="flex-start gap tag-list">
            <template v-for="(item, index) in data.specialtyCategory" :key="item.dictId">
              <template v-if="index < 9">
                <el-tag v-if="stringLength(item.name) <= 9" class="one-ell tag" type="info" round>
                  {{ item.name }}
                </el-tag>
                <el-tooltip v-else placement="top" effect="light" trigger="hover">
                  <el-tag class="one-ell tag cur" type="info" round>{{ item.name }}</el-tag>
                  <template #content>
                    <el-tag class="tag" type="info" round>{{ item.name }}</el-tag>
                  </template>
                </el-tooltip>
              </template>
            </template>
            <template v-if="data.specialtyCategory.length > 9">
              <el-tooltip placement="top" effect="light" trigger="click">
                <el-tag class="tag cur" type="info" round>. . .</el-tag>
                <template #content>
                  <div class="tooltip-tag" style="max-width: 900px">
                    <template v-for="item in data.specialtyCategory" :key="item.dictId">
                      <el-tag class="tag" type="info" round>{{ item.name }}</el-tag>
                    </template>
                  </div>
                </template>
              </el-tooltip>
            </template>
          </div>
        </div>
      </div>
      <div class="video-box flex-start gap-10">
        <VideoCover
          v-for="(item, i) in data.amazonVideo"
          :key="i"
          platform="0"
          class="img"
          width="100px"
          height="100px"
          suffix="!squarecompress"
          :src="item.picUri"
          fit="fill"
          @click.prevent="openVideo(item)"
        />
        <VideoCover
          v-for="(item, i) in data.tiktokVideo"
          :key="i"
          platform="1"
          class="img"
          width="100px"
          height="100px"
          suffix="!squarecompress"
          :src="item.picUri"
          fit="fill"
          @click.prevent="openVideo(item)"
        />
      </div>
      <div class="flex-column more-box" v-hasPermi="['model:manage:detail']">
        <div v-if="isOverflow" class="gengduo" @click.prevent="openDetails()">···</div>
      </div>
      <div class="tips-box">
        <div>客服：{{ data.persons[0]?.name }}</div>
        &emsp;
        <div>开发人：{{ data.developerUser?.name || '暂未填写' }}</div>
        &emsp;
        <div>创建时间：{{ data.createTime }}</div>
      </div>
    </div>
    <el-divider />
    <div class="flex-between">
      <el-row class="statistics-box">
        <el-col :span="3">
          <el-statistic title="可拍数" :value="data.can" />
        </el-col>
        <el-col :span="3">
          <el-statistic title="待拍数" :value="data.waits" />
        </el-col>
        <el-col :span="3">
          <el-statistic title="待确认" :value="data.toBeConfirm" />
        </el-col>
        <el-col :span="3">
          <el-statistic title="超时率" :value="data.overtimeRate * 100">
            <template #suffix>%</template>
          </el-statistic>
        </el-col>
        <el-col :span="3">
          <el-statistic title="售后率" :value="data.afterSaleRate * 100">
            <template #suffix>%</template>
          </el-statistic>
        </el-col>
        <el-col :span="3">
          <el-statistic title="已完成" :value="data.finished" />
        </el-col>
        <el-col :span="3">
          <el-statistic title="被收藏" :value="data.collected" />
        </el-col>
        <el-col :span="3">
          <el-statistic title="拉黑数" :value="data.blacklistCount" />
        </el-col>
      </el-row>
      <div class="flex-center">
        <el-button
          v-btn
          link
          type="primary"
          icon="Document"
          v-hasPermi="['model:manage:detail']"
          @click.stop="routerNewWindow('/model/handleModel/read/1/' + data.id)"
        >
          详情
        </el-button>
        <!-- v-hasPermi="['model:manage:edit']" -->
        <el-button
          v-if="checkPermi(['model:manage:edit'])"
          v-btn
          link
          type="primary"
          icon="Edit"
          @click.stop="routerNewWindow('/model/handleModel/edit/' + data.id)"
        >
          编辑
        </el-button>
        <el-button
          v-btn
          link
          type="primary"
          v-hasPermi="['model:manage:family']"
          @click.stop="handleAction('家庭成员')"
        >
          <template #icon>
            <el-icon>
              <svg-icon-users />
            </el-icon>
          </template>
          家庭成员
        </el-button>
        <el-button
          v-btn
          v-if="data.topTime == null"
          link
          type="primary"
          icon="Upload"
          @click.stop="handleAction('置顶')"
          v-hasPermi="['model:manage:top']"
        >
          置顶
        </el-button>
        <el-button
          v-btn
          v-else
          link
          type="primary"
          icon="Remove"
          @click.stop="handleAction('取消置顶')"
          v-hasPermi="['model:manage:top']"
        >
          取消置顶
        </el-button>
        <CopyButton
          v-if="checkPermi(['model:manage:link'])"
          v-btn
          link
          type="primary"
          icon="Link"
          :loading="buttonLoading"
          :async-copy-content="() => handleCopyLink(row)"
        >
          复制链接
        </CopyButton>
        <!-- <el-button
          v-btn
          link
          type="primary"
          icon="Link"
          @click.stop="handleAction('后台链接')"
          v-hasPermi="['model:manage:link']"
        >
          后台链接
        </el-button> -->
        <el-button
          v-btn
          link
          type="primary"
          icon="Sort"
          @click.stop="handleAction('排序')"
          v-hasPermi="['model:manage:sort']"
        >
          修改排序
        </el-button>
      </div>
    </div>
  </label>
</template>

<script setup>
import PercentageImg from '@/components/ImagePreview/percentageImg.vue'
import VideoCover from '@/components/ImagePreview/videoCover.vue'
import SvgIconUsers from '@/components/SvgIcon/users.vue'
import CopyButton from '@/components/Button/CopyButton'
// import BizModelTypeNew from '@/components/DictTag/bizModelTypeNew.vue'
import { http_reg } from '@/utils/RegExp'
import { stringLength } from '@/utils/index'
import { checkPermi } from '@/utils/permission'
import { createBackstageLink } from '@/api/model/model'

const { proxy } = getCurrentInstance()
const router = useRouter()

const props = defineProps({
  data: {
    type: Object,
    required: true,
  },
  isSelection: {
    type: Boolean,
    default: false,
  },
})

const emits = defineEmits(['openDetails', 'openVideo', 'openPhoto', 'action'])

const data = toRef(() => props.data)

const modelListBoxRef = ref()
const isOverflow = ref(false)

function openDetails() {
  emits('openDetails', data.value)
}

function openVideo(item) {
  if (!checkPermi(['model:manage:detail'])) return proxy.$modal.msgWarning('暂无权限')
  if (!item?.videoUrl) {
    proxy.$modal.msgWarning(`无视频链接！`)
    return
  }
  if (!http_reg.test(item.videoUrl)) {
    proxy.$modal.msgWarning(`视频链接格式有误！`)
    return
  }
  emits('openVideo', item.videoUrl)
}

function viewLifePhoto() {
  emits('openPhoto', data.value.lifePhoto)
}

function handleAction(btn) {
  if (btn === '详情') {
    router.push(`/model/handleModel/read/1/${data.value.id}`)
    return
  }
  if (btn === '编辑') {
    router.push(`/model/handleModel/edit/${data.value.id}`)
    return
  }
  emits('action', btn, data.value)
}
function routerNewWindow(path) {
  const { href } = router.resolve({ path })
  window.open(href, '_blank')
}

const buttonLoading = ref(false)
function handleCopyLink() {
  return new Promise((resolve, reject) => {
    buttonLoading.value = true
    createBackstageLink(data.value.id)
      .then(res => {
        if (res.data) {
          resolve(res.data)
        } else {
          proxy.$modal.msgWarning('获取链接错误')
          reject()
        }
      })
      .catch(() => {
        proxy.$modal.msgWarning('获取链接错误')
        reject()
      })
      .finally(() => buttonLoading.value = false)
  })
}

const resize = () => {
  if (
    modelListBoxRef.value.offsetWidth <
    modelListBoxRef.value.children[1].offsetWidth +
      modelListBoxRef.value.children[2].offsetWidth +
      modelListBoxRef.value.children[3].offsetWidth +
      100
  ) {
    isOverflow.value = true
  } else {
    isOverflow.value = false
  }
}

defineExpose({
  resize,
})
</script>

<style scoped lang="scss">
.algin_b {
  align-items: baseline;
}
.model-list-item {
  position: relative;
  margin: 10px 0;
  padding: 30px 10px 15px 35px;
  border-radius: 5px;
  border: 1px solid var(--el-color-primary-light-7);
  display: block;
  font-weight: 400;
  cursor: auto;

  .checkbox {
    position: absolute;
    top: calc(50% - 16px);
    left: 10px;
  }

  .sort-number {
    position: absolute;
    top: 10px;
    left: 35px;
    z-index: 3;
    font-size: 13px;
    // cursor: pointer;
  }
  .left-box {
    display: flex;
    align-items: baseline;
    position: absolute;
    top: 0;
    right: 0;
    z-index: 2;
    font-size: 13px;
  }
  .tag-box {
    // position: absolute;
    // top: 1px;
    // right: 100px;
    // z-index: 4;
    margin: 1px 10px 0 0;
    padding: 3px 10px;
    border-radius: 3px;
    color: #fff;
  }

  .status-box {
    // position: absolute;
    // top: 0;
    // right: 0;
    // z-index: 2;
    // font-size: 13px;
    border-bottom: 1px solid var(--el-color-primary-light-7);
    border-left: 1px solid var(--el-color-primary-light-7);
    padding: 3px 10px;
    border-radius: 0 5px;
    background: #fff;

    .el-text {
      cursor: pointer;
    }

    .primary {
      color: var(--el-color-primary);
    }
    .success {
      color: var(--el-color-success);
    }
    .warning {
      color: var(--el-color-warning);
    }
    .danger {
      color: var(--el-color-danger);
    }
  }

  .content-box {
    overflow: hidden;
    // position: relative;
  }

  .tips-box {
    display: flex;
    position: absolute;
    right: 20px;
    bottom: 90px;
    z-index: 999;
    font-size: 12px;
    color: #7f7f7f;
  }

  .percentage-img {
    position: relative;
    flex-shrink: 0;
    margin: auto 0;
    overflow: hidden;
    .text-below-image {
      margin-top: 3px;
      font-size: 12px;
      color: #7f7f7f;
      text-align: center;
      width: 100%;
    }
    .hint-box {
      position: absolute;
      z-index: 998;
      background: #333333b0;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      &-text {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        font-size: 14px;
      }
    }
    .hint {
      background: var(--el-color-warning);
      color: #fff;
      font-size: 13px;
      width: 60px;
      position: absolute;
      left: -20px;
      top: -2px;
      transform: scale(0.8) rotateZ(-45deg);
      text-align: center;
      line-height: 23px;
    }
  }

  .info-box {
    font-size: 14px;
    width: 300px;
    flex-shrink: 0;

    .name {
      display: flex;
      align-items: center;
      font-size: 16px;
      font-weight: 600;
      color: #333;
      max-width: 300px;
      margin-bottom: 5px;

      .acc {
        margin-left: 5px;
        font-size: 12px;
        font-weight: 300;
        color: #7f7f7f;
      }
    }

    .tag-info {
      margin-bottom: 10px;
      flex-wrap: wrap;
      gap: 5px;
    }

    .tips {
      font-size: 12px;
      color: #7f7f7f;
    }
  }

  .tags-box {
    font-size: 14px;
    flex-shrink: 0;

    .tag-list {
      flex-wrap: wrap;
      width: 450px;
    }
    .tag {
      width: 80px;
      display: block;
      line-height: 24px;
      text-align: center;

      &.cur {
        cursor: pointer;
      }
    }

    .gap {
      gap: 8px;
    }
  }

  .video-box {
    position: relative;
    // margin: 0 8px -30px;
  }

  .more-box {
    position: absolute;
    top: 45px;
    right: 0;
    z-index: 9;
    width: 10px;
    height: 110px;
    padding-left: 18px;
    justify-content: center;
    gap: 10px;
    background: linear-gradient(90deg, transparent, #fff 30%);

    .gengduo {
      cursor: pointer;
      width: 24px;
      height: 25px;
      line-height: 24px;
      text-align: center;
      margin-left: -32px;
      color: #d7d7d7;
      font-size: 25px;
      background: #fff;
      border-radius: 50%;
      box-shadow: 0px 0px 4px rgb(135 135 135);

      &:hover {
        color: #80b2ec;
        box-shadow: 0px 0px 4px #80b2ec;
      }
    }
  }

  .el-divider {
    margin: 18px 0;
  }

  .statistics-box {
    width: 800px;

    .el-col {
      text-align: center;
    }
  }
}
.tooltip-tag {
  max-width: 620px;
  max-height: 600px;
  overflow: auto;
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}
</style>
