<template>
  <el-dialog
    v-model="dialogVisible"
    title="家庭成员"
    width="700"
    append-to-body
    align-center
    center
    :before-close="handleBeforeClose"
    @close="handleClose"
  >
    <div class="model-family-box">
      <div class="flex-between head">
        <span>成员数量：{{ modelFamilyData.length }}</span>
        <div v-if="isInitiator || !familyId">
          <el-button type="primary" icon="Plus" size="small" @click="openAddDialog">添加现有模特</el-button>
          <el-button
            v-if="checkPermi(['model:manage:add'])"
            type="primary"
            icon="Plus"
            size="small"
            @click="openAddModel"
          >
            添加新模特
          </el-button>
        </div>
      </div>
      <div class="list-box" v-loading="loading">
        <div class="empty" v-if="!modelFamilyData.length">
          模特暂未加入家庭，可添加成员后创建家庭或者被添加为家庭成员
          <br />
          一个模特只能加入一个家庭成员哦~
        </div>
        <div class="model-list-item" v-for="(item, index) in modelFamilyData">
          <div class="flex-start gap-10" :style="{ backgroundColor: item.id == modelId ? '#cacfd27d' : '' }">
            <div class="img-box">
              <!-- <el-tag v-if="item.id == modelId" class="img-tag" type="danger" size="small" round hit effect="dark">
                本人
              </el-tag> -->
              <PercentageImg
                width="100px"
                :src="$picUrl + item?.modelPic"
                :moreLength="item?.lifePhoto?.length || 0"
                radius="4px"
                @more="viewLifePhoto(item?.lifePhoto)"
              />
            </div>
            <div class="info-box">
              <div class="name one-ell">
                <div>{{ item.name }}</div>
                <div class="acc">{{ item.account ? `(ID:${item.account})` : '' }}</div>
                <el-tag
                  v-if="item.id == modelId"
                  class="img-tag"
                  type="primary"
                  size="small"
                  round
                  effect="dark"
                >
                  本人
                </el-tag>
              </div>
              <div class="flex-start gap-10 tag-info">
                <biz-model-ageGroup :value="item.ageGroup" tag="text">
                  <template v-slot="{ dict }">
                    <el-text>
                      <el-icon color="#3b99fc"><User /></el-icon>
                      {{ dict.label }}
                    </el-text>
                  </template>
                </biz-model-ageGroup>
                <el-text v-if="item.sex == 1">
                  <el-icon color="#3b99fc"><Male /></el-icon>
                  男
                </el-text>
                <el-text v-else>
                  <el-icon color="#f69661"><Female /></el-icon>
                  女
                </el-text>
                <biz-nation :value="item.nation" tag="text">
                  <template v-slot="{ dict }">
                    <el-text>
                      <el-icon color="#3b99fc"><LocationInformation /></el-icon>
                      {{ dict.label }}
                    </el-text>
                  </template>
                </biz-nation>
              </div>
              <div class="flex-start tag-info">
                <biz-model-type :value="item.type" />
                <biz-model-cooperation :value="item.cooperation" />
                <!-- <biz-model-platform :value="item.platform" /> -->
                <el-tag type="warning" size="small" round>
                  {{ handleStatus(item.status) }}
                </el-tag>
              </div>
              <div class="flex-start algin_b">
                <div class="label-text">模特标签：</div>
                <el-tooltip
                  effect="dark"
                  :content="`<div style='max-width: 500px'>${handleTags(item.tags)}</div>`"
                  raw-content
                  placement="top"
                  :hide-after="0"
                  :offset="5"
                  :disabled="!item.tagsMore"
                >
                  <div class="flex-start more-ell" v-has-ellipsis:tagsMore="item">
                    {{ handleTags(item.tags) }}
                  </div>
                </el-tooltip>
              </div>
              <div class="flex-start algin_b">
                <div class="label-text">擅长品类：</div>
                <el-tooltip
                  effect="dark"
                  :content="`<div style='max-width: 500px'>${handleTags(item.specialtyCategory)}</div>`"
                  raw-content
                  placement="top"
                  :hide-after="0"
                  :offset="5"
                  :disabled="!item.categoryMore"
                  style="max-width: 500px"
                >
                  <div class="flex-start more-ell" v-has-ellipsis:categoryMore="item">
                    {{ handleTags(item.specialtyCategory) }}
                  </div>
                </el-tooltip>
              </div>
              <div>
                模特佣金：{{ item.commission }}{{ handleCommissionUnit(item.commissionUnit) }}&emsp;
                剩余待拍数：{{ item.waits }}
              </div>
            </div>
            <div class="flex-start">
              <div class="call">
                <biz-model-family-type tag="text" :value="item.modelFamilyRelationship" />
              </div>
              <el-icon
                v-if="isInitiator && item.id != modelId"
                class="del-btn"
                :size="18"
                @click="handleDelete(item)"
              >
                <Delete />
              </el-icon>
            </div>
          </div>
        </div>
      </div>
    </div>
    <el-dialog
      v-model="addDialogVisible"
      title=""
      width="350"
      align-center
      center
      :close-on-click-modal="false"
      destroy-on-close
    >
      <template #header="{ titleClass }">
        <div :class="titleClass" style="padding-left: 32px">添加成员</div>
      </template>
      <el-form ref="formRef" :model="form" label-width="80px" :rules="rules">
        <el-form-item label="选择模特" prop="modelId">
          <SelectLoad
            v-model="form.modelId"
            keyValue="id"
            keyLabel="name"
            keyWord="name"
            :request="modelSelectList"
            :requestParams="{
              filterModelId: modelId,
              isFamilyModel: 0,
              pageNum: 1,
              pageSize: 10000,
            }"
            :requestCallback="
              res =>
                res.data.rows?.map(item => ({
                  ...item,
                  name: item.name + (item.account ? ` (${item.account})` : ''),
                })) || []
            "
          />
        </el-form-item>
        <el-form-item label="亲属关系" prop="relation">
          <el-select v-model="form.relation" placeholder="请选择关系" clearable style="width: 100%">
            <el-option
              v-for="item in model_family_type_options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button v-btn @click="addDialogVisible = false">取消</el-button>
          <el-button v-btn type="primary" @click="confirmAdd">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<script setup>
import PercentageImg from '@/components/ImagePreview/percentageImg.vue'
import SelectLoad from '@/components/Select/SelectLoad.vue'
import { bizCommissionUnit } from '@/utils/dict'
import { checkPermi } from '@/utils/permission'
import { modelFamilyList, addFamilyModel, deleteFamilyModel, modelSelectList } from '@/api/model/model'
const router = useRouter()
const { proxy } = getCurrentInstance()
const { model_family_relationship_type } = proxy.useDict('model_family_relationship_type')
const model_family_type_options = computed(() => {
  return model_family_relationship_type.value.filter(item => item.value != 0)
})

const dialogVisible = ref(false)
const modelFamilyData = ref([])
const addDialogVisible = ref(false)
const formRef = ref(null)
const form = ref({
  modelId: '',
  relation: '',
})
const loading = ref(false)
const modelId = ref('')
const familyId = ref('')
const isInitiator = ref(false)
const modelFamilySelectList = ref([])
const rules = {
  modelId: [{ required: true, message: '请选择模特', trigger: 'blur' }],
  relation: [{ required: true, message: '请选择亲属关系', trigger: 'blur' }],
}
const isEdit = ref(false)

const emits = defineEmits(['openPhoto', 'update'])

defineExpose({
  open,
  close,
})

function open(id, fId, initiator) {
  modelId.value = id
  familyId.value = fId
  isInitiator.value = initiator ? true : false
  if (fId) {
    getList()
  }
  isEdit.value = false
  dialogVisible.value = true
}

function close() {
  addDialogVisible.value = false
  dialogVisible.value = false
}

function handleBeforeClose(done) {
  if (isEdit.value) {
    emits('update')
  }
  done()
}

function handleClose() {
  modelFamilyData.value.length = 0
  modelFamilySelectList.value.length = 0
  form.value = {
    modelId: '',
    relation: '',
  }
  addDialogVisible.value = false
}

function getList() {
  loading.value = true
  modelFamilyList({ familyId: familyId.value })
    .then(res => {
      modelFamilyData.value = res.data.rows.map(item => ({
        ...item,
        tagsMore: false,
        categoryMore: false,
      }))
    })
    .finally(() => {
      loading.value = false
    })
}

function viewLifePhoto(lifePhoto) {
  emits('openPhoto', lifePhoto)
}

function handleTags(tags) {
  if (tags.length) {
    return tags.map(item => item.name).join('、')
  }
  return ''
}

function handleCommissionUnit(val) {
  let b = bizCommissionUnit.find(item => item.value == val)
  return b ? b.label : ''
}

function handleStatus(val) {
  switch (val) {
    case 0:
      return '正常合作'
    case 1:
      return '暂停合作'
    case 2:
      return '行程中'
    case 3:
      return '取消合作'
    default:
      return ''
  }
}

function handleDelete(item) {
  proxy.$modal.confirm('确定要删除该成员吗？', '提示').then(() => {
    proxy.$modal.loading('正在删除')
    deleteFamilyModel({ modelId: item.id, familyId: item.familyId })
      .then(() => {
        isEdit.value = true
        getList()
      })
      .finally(() => proxy.$modal.closeLoading())
  })
}

function openAddDialog() {
  form.value = {
    modelId: '',
    relation: '',
  }
  addDialogVisible.value = true
}

function openAddModel() {
  let path = '/model/handleModel/add/' + modelId.value
  const { href } = router.resolve({ path })
  window.open(href, '_blank')
}

function confirmAdd() {
  formRef.value.validate(valid => {
    if (valid) {
      proxy.$modal.loading('正在添加')
      addFamilyModel({
        modelId: form.value.modelId,
        familyId: modelId.value,
        modelFamilyRelationship: form.value.relation,
      })
        .then(res => {
          if (res.code === 200) {
            isEdit.value = true
            if (!familyId.value) {
              familyId.value = modelId.value
              isInitiator.value = true
            }
            getList()
            addDialogVisible.value = false
          }
        })
        .finally(() => proxy.$modal.closeLoading())
    }
  })
}
</script>

<style scoped lang="scss">
.model-family-box {
  .head {
    padding: 0 0 8px;
    border-bottom: 1px solid #e2e2e2;
  }

  .algin_b {
    align-items: baseline;
  }

  .list-box {
    height: 500px;
    overflow-y: auto;
    padding: 10px 0 0;

    .empty {
      width: 100%;
      margin-top: 200px;
      text-align: center;
      font-size: 14px;
    }

    .model-list-item {
      margin-bottom: 10px;

      .img-box {
        position: relative;

        .img-tag {
          position: absolute;
          top: 2px;
          left: 2px;
          z-index: 2;
          width: 50px;
        }
      }

      .info-box {
        font-size: 14px;
        width: 380px;
        flex-shrink: 0;

        .name {
          display: flex;
          align-items: center;
          font-size: 16px;
          font-weight: 600;
          color: #333;
          max-width: 300px;
          margin-bottom: 5px;

          .acc {
            margin-left: 5px;
            font-size: 12px;
            font-weight: 300;
            color: #7f7f7f;
          }
        }

        .img-tag {
          margin-left: 10px;
          width: 50px;
        }

        .label-text {
          flex-shrink: 0;
        }

        .tag-info {
          margin-bottom: 5px;
        }
      }

      .call {
        width: 140px;
        text-align: center;
        font-size: 16px;
      }

      .del-btn {
        cursor: pointer;

        &:hover {
          color: var(--el-color-danger);
        }
      }
    }
  }
}
</style>
