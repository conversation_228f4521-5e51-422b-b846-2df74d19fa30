<template>
  <div>
    <el-dialog
      v-model="visible"
      :title="dialogTitle"
      align-center
      width="80%"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      @close="close"
      :style="{ 'margin-left': appStore.sidebar.opened ? '15vw' : 'auto' }"
    >
      <div style="min-height: 200px; max-height: 80vh; overflow-y: auto" v-loading="loading">
        <template v-if="dialogType == 1">
          <el-checkbox-group v-model="modelCategoryIds">
            <el-checkbox-button v-for="item in modelCategoryList" :key="item.id" :value="item.id">
              {{ item.name }}
            </el-checkbox-button>
          </el-checkbox-group>
        </template>
        <template v-if="dialogType == 2">
          <div style="gap: 10px; display: grid" v-if="modelTagsList && modelTagsList.length > 0">
            <div class="text-warp" v-for="(item, index) in modelTagsList" :key="item.id">
              <div class="fs-0 text-label">{{ item.name }}</div>
              <div>
                <el-checkbox-group v-model="modelTagIds">
                  <el-checkbox-button v-for="item2 in item.children" :key="item2.id" :value="item2.id">
                    {{ item2.name }}
                  </el-checkbox-button>
                </el-checkbox-group>
              </div>
            </div>
          </div>
          <el-divider />
          <div class="box-add">
            <span class="add-title">添加自定义标签</span>
            <span class="add-tip">此为一次性标签，不计入标签库</span>
          </div>
          <div class="flex-start" style="gap: 5px; margin: 10px 60px; max-width: 90%; flex-wrap: wrap">
            <template v-if="customTagList && customTagList.length > 0">
              <el-tag
                v-for="item in customTagList"
                :key="item.name"
                effect="dark"
                size="large"
                closable
                @close="handleCloseCustomTag(item)"
              >
                {{ item.name }}
              </el-tag>
            </template>
            <el-popover :visible="isShowCustom" placement="right" :width="310">
              <div class="flex-center">
                自定义标签:&nbsp;
                <el-input
                  v-model="customTagName"
                  size="small"
                  maxlength="50"
                  show-word-limit
                  style="width: 200px"
                  clearable
                  placeholder="请输入自定义标签"
                />
              </div>
              <div class="custom-btn">
                <el-button size="small" style="padding: 8px 20px" v-btn @click="cancelCustom">取消</el-button>
                <el-button
                  size="small"
                  :disabled="!customTagName"
                  style="padding: 8px 20px"
                  v-btn
                  type="primary"
                  @click="confirmCustom"
                >
                  保存
                </el-button>
              </div>
              <template #reference>
                <el-button plain color="#409eff" class="myself-btn" @click="isShowCustom = true">
                  +自定义
                </el-button>
              </template>
            </el-popover>
          </div>
        </template>
      </div>
      <template #footer>
        <div class="flex-end btn">
          <slot name="button">
            <el-button v-btn plain @click="close">取消</el-button>
            <el-button v-btn type="primary" @click="onConfirm">保存</el-button>
          </slot>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { modelCategorySelect, modelCategorySelectRank } from '@/api/model/model'
import useAppStore from '@/store/modules/app'
import { ElMessage } from 'element-plus'
const appStore = useAppStore()
const visible = ref(false)
const dialogTitle = ref('')
const dialogType = ref('')
const modelCategoryList = ref([])
const modelCategoryIds = ref([])
const modelTagsList = ref([])
const modelTagIds = ref([])
const loading = ref(false)

const isShowCustom = ref(false)
const customTagName = ref('')
const customTagList = ref([])

const emit = defineEmits(['success'])

defineExpose({
  open,
  close,
})

function open(type, ids = []) {
  type == 1 ? (dialogTitle.value = '请选择符合模特的擅长品类') : (dialogTitle.value = '请选择符合模特的标签')
  dialogType.value = type
  visible.value = true

  if (type == 1) {
    loading.value = true
    getModelCategorySelect()
    modelCategoryIds.value = ids.map(item => item.id)
  } else if (type == 2) {
    loading.value = true
    getModelTagsSelect()
    modelTagIds.value = ids.map(item => item.id)
    customTagList.value = ids.filter(item => !item.id)
  }
}

function close() {
  isShowCustom.value = false
  modelCategoryList.value = []
  modelCategoryIds.value = []
  modelTagsList.value = []
  modelTagIds.value = []
  visible.value = false
}

// 模特擅长品类下拉
function getModelCategorySelect() {
  modelCategorySelectRank({ rank: 1, categoryId: 1008 })
    .then(res => {
      if (res.code == 200) {
        modelCategoryList.value = res.data
      }
    })
    .finally(() => {
      loading.value = false
    })
}

// 模特标签下拉
function getModelTagsSelect() {
  modelCategorySelect({ rank: 2, status: 0, categoryId: 1009 })
    .then(res => {
      if (res.code == 200) {
        let countList = res.data
        if (countList && countList.length > 0) {
          modelTagsList.value = countList.filter(item => item.children.length > 0)
        } else {
          modelTagsList.value = []
        }
      }
    })
    .finally(() => {
      loading.value = false
    })
}
//确认选择
function onConfirm() {
  if (dialogType.value == 1) {
    let tempList = []
    if (modelCategoryIds.value && modelCategoryIds.value.length > 0) {
      tempList = modelCategoryList.value.filter(item => modelCategoryIds.value.includes(item.id))
    }
    emit('success', dialogType.value, tempList)
    close()
  } else if (dialogType.value == 2) {
    let tempList = []
    if (modelTagIds.value && modelTagIds.value.length > 0) {
      modelTagsList.value.forEach(item => {
        // 过滤出 children 中 id 包含在 modelTagIds 中的项
        const filteredChildren = item.children.filter(item2 => modelTagIds.value.includes(item2.id))
        // 如果 filteredChildren 不为空，则将其添加到 tempList
        if (filteredChildren.length > 0) {
          tempList.push(...filteredChildren)
        }
      })
    }
    if (customTagList.value.length > 0) {
      customTagList.value.forEach(item => {
        tempList.push({ name: item.name })
      })
    }
    emit('success', dialogType.value, tempList)
    close()
  }
}

function cancelCustom() {
  customTagName.value = ''
  isShowCustom.value = false
}
function confirmCustom() {
  if (customTagName.value.trim() == '') return ElMessage.warning('标签名称不能为空')
  customTagList.value.push({ name: customTagName.value })
  cancelCustom()
}

function handleCloseCustomTag(data) {
  const index = customTagList.value.findIndex(item => item.name == data.name)
  if (index !== -1) {
    customTagList.value.splice(index, 1)
  }
}
</script>

<style scoped lang="scss">
@import '@/assets/styles/customForm.scss';
@import '@/assets/styles/form-disabled.scss';

.text-warp {
  word-break: break-all;
  line-break: anywhere;
  white-space: break-spaces;
  display: flex;
  //   align-items: baseline;
  .text-label {
    text-align: right;
    width: 120px;
    margin: 5px 10px 0 0;
    text-align: right;
    width: 120px;
    font-weight: bold;
  }
}
.box-add {
  .add-title {
    font-size: 14px;
    margin-left: 60px;
    font-weight: bold;
  }
  .add-tip {
    color: #939393;
    font-size: 12px;
    margin-left: 10px;
  }
}
.custom-btn {
  text-align: center;
  margin-top: 10px;
}
.myself-btn {
  background: #fff;
  color: #409eff;
  :hover {
    color: #409eff;
  }
}
</style>
