<template>
  <!-- <div class="image-items"> -->
  <div class="image-list" v-for="(item, i) in props.urlList" :key="item.id">
    <div class="image-modal" v-if="!isShowIcon && disabledEdit">
      <el-icon size="20" color="#333">
        <Edit style="cursor: pointer" @click="editImage(item, i)" />
      </el-icon>
      <el-icon size="20" color="#ff3b30">
        <Delete style="cursor: pointer" @click="deleteImage(i)" />
      </el-icon>
    </div>
    <template v-if="props.showVideo">
      <VideoCover
        class="img"
        width="100px"
        height="100px"
        suffix="!squarecompress"
        :src="item.picUrl"
        fit="fill"
        @click="openVideo(item)"
      />
    </template>
    <template v-else>
      <el-image
        ref="imgViewRef"
        preview-teleported
        :src="$picUrl + item.picUrl + '!squarecompress'"
        fit="fill"
        class="image-item"
      >
        <template #error>
          <div class="image-error">暂无图片</div>
        </template>
      </el-image>
    </template>

    <div class="one-ell" style="text-align: center; width: 100px">{{ item.name }}</div>
  </div>
  <!-- </div> -->
</template>

<script setup>
import VideoCover from '@/components/ImagePreview/videoCover.vue'
import { http_reg } from '@/utils/RegExp'
import { ElMessage } from 'element-plus'
import { getCurrentInstance } from 'vue'
const { proxy } = getCurrentInstance()
const pathHead = proxy.$picUrl
const props = defineProps({
  urlList: {
    type: Array,
    default: () => [],
  },
  type: {
    type: String,
    default: 'amazon',
  },
  isShowIcon: {
    type: Boolean,
    default: false,
  },
  showVideo: {
    type: Boolean,
    default: false,
  },
  disabledEdit: {
    type: Boolean,
    default: true,
  }
})

const emits = defineEmits(['change', 'openVideo'])

function openVideo(item) {
  if (!item?.videoUrl) {
    ElMessage.warning('无视频链接！')
    return
  }
  if (!http_reg.test(item.videoUrl)) {
    ElMessage.warning('视频链接格式有误！')
    return
  }
  emits('openVideo', item.videoUrl)
}

function disposeUrl(url) {
  // return url.startsWith(pathHead)? url : pathHead + url
}
//删除对应的数据
function deleteImage(i) {
  props.urlList.splice(i, 1)
}
//编辑
function editImage(data, i) {
  emits('change', data, i, props.type)
}
</script>

<style scoped lang="scss">
// .image-items {
//     display: flex;
//     max-width: 600px;
//     flex-wrap: wrap;
.image-list {
  height: 100px;
  position: relative;
  margin-right: 10px;
  margin-bottom: 25px;
  .image-modal {
    position: absolute;
    bottom: 0;
    right: 1px;
    z-index: 9;
    //   position: absolute;
    //   bottom: 5px;
    //   right: 5px;
    //   width: 100px;
    //   height: 100%;
    //   z-index: 9;
    //   border-radius: 6px;
    //   background-color: #2c2b2b66;
    //   opacity: 0;
    //   &:hover {
    //     opacity: 1;
    //   }
  }
}
.image-item {
  // border-radius: 6px;
  box-sizing: border-box;
  width: 100px;
  height: 100px;
  // margin-right: 10px;
}
// .image-modal {

// }

.image-error {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid #333;
}
// }
</style>
