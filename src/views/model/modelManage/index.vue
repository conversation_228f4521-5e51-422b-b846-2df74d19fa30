<template>
  <div class="pages">
    <div id="header-box">
      <FilterForm
        :bizModelPlatform="biz_model_platform"
        :bizNation="biz_nation"
        :bizModelAgeGroup="biz_model_ageGroup"
        :bizModelType="biz_model_type"
        :bizModelCooperation="biz_model_cooperation.filter(item => item.value != 2)"
        :bizModelFamilyType="model_family_type_options"
        @change="handleFilterChange"
      />
      <div style="margin: 20px 0">
        <el-button
          v-btn
          type="primary"
          plain
          icon="Plus"
          @click="routerNewWindow('/model/handleModel/add')"
          v-hasPermi="['model:manage:add']"
        >
          新增
        </el-button>
        <!-- <DownloadBtn
          type="success"
          plain
          icon="Download"
          url="/biz/model/export"
          :params="handleParams()"
          fileName="模特信息列表.xlsx"
          v-hasPermi="['model:manage:export']"
        /> -->
        <el-button
          v-btn
          type="primary"
          plain
          icon="Edit"
          @click="handleEdit"
          v-hasPermi="['model:manage:batchChangeConectUser']"
          :disabled="!multipleSelection.length"
        >
          批量更换对接人
        </el-button>
      </div>
      <div class="flex-between" style="margin: 20px 0">
        <div class="flex-start gap-10">
          <el-checkbox
            v-model="checkAll"
            :indeterminate="isIndeterminate"
            @change="handleCheckAllChange"
            style="color: var(--text-color)"
          >
            全选
          </el-checkbox>
          <SortButton v-model="sort" @change="handleSortChange" style="font-size: 14px">待拍数</SortButton>
          <SortButton v-model="toBeConfirmSort" @change="handleSortChange" style="font-size: 14px">
            待确认
          </SortButton>
        </div>
        <div style="font-size: 13px">
          当前置顶数：
          <span style="color: var(--el-color-success)">{{ modelTopInfo.topCount }}</span>
          &emsp;&emsp;剩余可置顶数：
          <span style="color: var(--el-color-danger)">{{ modelTopInfo.surplusCount }}</span>
        </div>
      </div>
    </div>
    <div style="min-height: 500px; padding-bottom: 25px" v-loading="tableLoading">
      <div v-show="!tableData.length">
        <el-empty description="暂无数据" :image-size="80" v-once></el-empty>
      </div>
      <el-checkbox-group
        v-model="multipleSelection"
        @change="handleCheckedCitiesChange"
        style="font-size: normal; line-height: normal"
      >
        <ModelListItem
          v-for="item in tableData"
          :key="item.id"
          ref="ModelListRef"
          :data="item"
          is-selection
          @action="handleAction"
          @openVideo="handleOpenVideo"
          @openPhoto="handleOpenPhoto"
          @openDetails="handleOpenDetails"
        />
      </el-checkbox-group>
    </div>
    <div class="pagination-box">
      <PaginationFloatBar :current-page="currentPage" :page-size="pageSize" @update:current-page="handlePageChange" :total="total">
        <DownloadBtn
          type="success"
          plain
          icon="Download"
          url="/biz/model/export"
          :params="handleParams()"
          fileName="模特信息列表.xlsx"
          v-hasPermi="['model:manage:export']"
        />
      </PaginationFloatBar>
      <!-- <el-pagination
        background
        @current-change="pageChange({ pageNum: $event, pageSize })"
        :current-page="currentPage"
        :page-size="pageSize"
        layout="total, prev, pager, next, jumper"
        :total="total"
      /> -->
    </div>

    <!-- 修改关联人员 -->
    <el-dialog
      v-model="dialogVisible"
      title="批量更换英文客服"
      width="350"
      append-to-body
      align-center
      center
      :close-on-click-modal="false"
    >
      <!-- <SelectLoad
        style="width: 100%"
        v-model="dialogPersons"
        :request="modelPersonsSelect"
        :requestCallback="res => res.data.rows"
        keyValue="id"
        keyLabel="name"
        keyWord="keyword"
      /> -->
      <el-select v-model="dialogPersons" placeholder="请选择" clearable style="width: 100%">
        <el-option
          v-for="item in modelPersonsSelectList"
          :key="item.userId"
          :label="item.userName"
          :value="item.userId"
        />
      </el-select>
      <div style="margin-top: 20px; width: 100%; text-align: center">
        共
        <span style="color: red">{{ multipleSelection.length }}</span>
        位模特需修改关联人员
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button v-btn @click="dialogVisible = false">取消</el-button>
          <el-button v-btn type="primary" @click="confirmAddPersons">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 修改模特排序 -->
    <el-dialog
      v-model="sortDialogVisible"
      title="修改模特排序"
      width="400"
      append-to-body
      align-center
      center
      :close-on-click-modal="false"
    >
      <div class="flex-start">
        列表排序：
        <el-input-number
          title=""
          v-model="modelSort"
          :min="0"
          :max="99999"
          :precision="0"
          :step="1"
          controls-position="right"
          style="width: 250px"
          @keydown="channelInputLimit"
        />
      </div>
      <template #footer>
        <div class="flex-center gap-10">
          <el-button v-btn @click="sortDialogVisible = false">取消</el-button>
          <el-button v-btn type="primary" @click="handleUpdateSort">保存</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 模特链接 -->
    <el-dialog
      v-model="modelLinkDialog"
      title="后台链接"
      width="450"
      append-to-body
      align-center
      center
      :close-on-click-modal="true"
    >
      <div>
        {{ modelLink }}
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button v-btn @click="modelLinkDialog = false">关闭</el-button>
          <CopyButton :copy-content="modelLink" type="primary" />
        </div>
      </template>
    </el-dialog>

    <EditModelStatus ref="EditModelStatusRef" @success="handleQuery" />
    <ModelVideoList ref="ModelVideoListRef" @openVideo="handleOpenVideo" />
    <ModelLifePhoto ref="ModelLifePhotoRef" />
    <ModelFamily ref="ModelFamilyRef" @openPhoto="handleOpenPhoto" @update="handleQuery" />

    <DialogVideo ref="dialogVideoRef" :videoSrc="videoSrc" @close="videoSrc = ''" />
  </div>
</template>

<script setup name="modelManage">
import ModelListItem from '@/views/model/modelManage/components/modelListItem'
import DownloadBtn from '@/components/Button/DownloadBtn'
import SortButton from '@/components/Button/SortButton.vue'
import CopyButton from '@/components/Button/CopyButton'
// import ElTablePage from '@/components/Table/ElTablePage'
// import SelectLoad from '@/components/Select/SelectLoad'
// import PercentageImg from '@/components/ImagePreview/percentageImg.vue'
import EditModelStatus from '@/views/model/modelManage/components/editModelStatus'
import FilterForm from '@/views/model/modelManage/components/filterForm'
import DialogVideo from '@/components/Dialog/video'
import ModelVideoList from '@/views/model/modelManage/components/modelVideoList'
import ModelLifePhoto from '@/views/model/modelManage/components/modelLifePhoto'
import ModelFamily from '@/views/model/modelManage/components/modelFamily.vue'
import { ElMessage } from 'element-plus'
import {
  getModelList,
  modelTop,
  modelTopCount,
  modelUpdateSort,
  updateRelevance,
  modelEnable,
  modelCategorySelectRank,
  createBackstageLink,
  modelPersonsSelect,
} from '@/api/model/model'
import { listUser } from '@/api/system/user'
import { bizModelStatus } from '@/utils/dict'
const { scrollToTop } = inject('appMainScroll')
const router = useRouter()
const { proxy } = getCurrentInstance()
const {
  biz_model_type,
  biz_nation,
  biz_model_platform,
  biz_model_cooperation,
  biz_model_ageGroup,
  model_family_relationship_type,
} = proxy.useDict(
  'biz_model_type',
  'biz_nation',
  'biz_model_platform',
  'biz_model_cooperation',
  'biz_model_ageGroup',
  'model_family_relationship_type'
)
const model_family_type_options = computed(() => {
  return model_family_relationship_type.value.filter(item => item.value != 0)
})

const EditModelStatusRef = ref(null)
const ModelListRef = ref(null)
const ModelLifePhotoRef = ref(null)
const ModelVideoListRef = ref(null)
const ModelFamilyRef = ref(null)

const nationMap = ref([])
const platformMap = ref([])
const ageGroupMap = ref([])
watchEffect(() => {
  if (biz_nation.value.length && nationMap.value.length != biz_nation.value.length) {
    biz_nation.value.forEach(item => {
      nationMap.value[item.value] = item.label
    })
  }
  if (biz_model_platform.value.length && platformMap.value.length != biz_model_platform.value.length) {
    biz_model_platform.value.forEach(item => {
      platformMap.value[item.value] = item.label
    })
  }
  if (biz_model_ageGroup.value.length && ageGroupMap.value.length != biz_model_ageGroup.value.length) {
    biz_model_ageGroup.value.forEach(item => {
      ageGroupMap.value[item.value] = item.label
    })
  }
})

const queryParams = ref({
  keyword: '',
  name: undefined,
  id: undefined,
  persons: '',
  platform: '',
  nation: '',
  sex: '',
  ageGroup: '',
  specialtyCategory: '',
  type: '',
  status: '',
  cooperation: '',
})

const columns = reactive([
  { type: 'selection', width: '55', reserveSelection: true },
  { slot: 'pic', prop: 'pic', label: '模特图', width: '100' },
  { slot: 'msg', prop: 'msg', label: '模特基本信息', width: '200' },
  {
    prop: 'persons',
    label: '关联人员',
    width: '120',
    html: data => {
      let dom = []
      if (data) {
        data.forEach(item => {
          dom.push(item.name + '<br/>' + item.phonenumber)
        })
      }
      dom = dom.join('<br/>')
      return dom
    },
  },
  { slot: 'specialtyCategory', prop: 'specialtyCategory', label: '擅长品类', width: '180' },
  { slot: 'tags', prop: 'tags', label: '模特标签', width: '180' },
  {
    prop: 'platform',
    label: '合作平台',
    width: '100',
    html: data => {
      let dom = []
      data.split(',').forEach(item => {
        dom.push(platformMap.value[item])
      })
      dom = dom.sort().join('<br/>')
      return dom
    },
  },
  {
    prop: 'status',
    label: '模特状态',
    minWidth: '80',
    handle: data => {
      let item = bizModelStatus.find(item => item.value == data)
      return item ? item.label : ''
      // return data == 0 ? '禁用' : '启用'
    },
  },
  {
    prop: 'createUser',
    label: '创建信息',
    width: '160',
    html: (data, row) => {
      let msg = ''
      if (data?.name) {
        msg += data.name + '<br/>'
      }
      if (data?.phonenumber) {
        msg += data.phonenumber + '<br/>'
      }
      if (row.createTime) {
        msg += row.createTime
      }
      return msg
    },
  },
])

const modelCategoryList = ref([])

// 模特擅长品类下拉
function getModelCategorySelect() {
  modelCategorySelectRank({ rank: 1, categoryId: 1008 }).then(res => {
    if (res.code == 200) {
      modelCategoryList.value = res.data
    }
  })
}

const modelPersonsSelectList = ref([])

function getModelPersonsSelectList() {
  // modelPersonsSelect().then(res => {
  //   if (res.code == 200) {
  //     modelPersonsSelectList.value = res.data
  //   }
  // })
  listUser().then(res => {
    if (res.code == 200 && res.data?.length) {
      modelPersonsSelectList.value = res.data.map(item => ({
        ...item,
        userName: item.userName + `(${item.userId})`,
      }))
    }
  })
}

const tableData = ref([])
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const tableLoading = ref(false)
const checkAll = ref(false)
const isIndeterminate = ref(false)
const multipleSelection = ref([])
const sort = ref('')
const toBeConfirmSort = ref('')
const modelTopInfo = ref({
  surplusCount: 0,
  topCount: 0,
})

const dialogVisible = ref(false)
const dialogPersons = ref('')

const modelLinkDialog = ref(false)
const modelLink = ref('')

const dialogVideoRef = ref()
const videoSrc = ref('')

const sortDialogVisible = ref(false)
const modelSort = ref(0)
const modelSortId = ref(0)

function isPureNumber(value) {
  // 使用正则表达式判断是否为纯数字
  const numberPattern = /^[0-9]+$/
  return numberPattern.test(value)
}
// 修改排序
function handleUpdateSort() {
  if (!isPureNumber(modelSort.value)) return ElMessage.warning('请输入数字')
  proxy.$modal.confirm('确认提交保存！', '提示', {}).then(() => {
    proxy.$modal.loading('保存中')
    modelUpdateSort({ id: modelSortId.value, sort: modelSort.value })
      .then(res => {
        sortDialogVisible.value = false
        proxy.$modal.msgSuccess('保存成功')
        handleQuery()
      })
      .finally(() => proxy.$modal.closeLoading())
  })
}

// 确认修改关联人员
function confirmAddPersons() {
  if (!dialogPersons.value) {
    proxy.$modal.msgError('请选择关联人员！')
    return
  }
  proxy.$modal.confirm('确认保存修改！', '提示', {}).then(() => {
    proxy.$modal.loading('保存中')
    updateRelevance({ modelIds: multipleSelection.value, userIds: [dialogPersons.value] })
      .then(res => {
        dialogVisible.value = false
        proxy.$modal.msgSuccess('保存成功')
        currentPage.value = 1
        handleQuery()
      })
      .finally(() => proxy.$modal.closeLoading())
  })
}
const handleCheckAllChange = val => {
  multipleSelection.value = val ? tableData.value.map(item => item.id) : []
  isIndeterminate.value = false
}
const handleCheckedCitiesChange = value => {
  const checkedCount = value.length
  checkAll.value = checkedCount === tableData.value.length
  isIndeterminate.value = checkedCount > 0 && checkedCount < tableData.value.length
}

const channelInputLimit = e => {
  const key = e.key
  const notAllowList = ['e', '+', '-']
  if (notAllowList.includes(key)) {
    e.returnValue = false
    return false
  }
  return true
}
// 多选
function selectionChange(arr) {
  multipleSelection.value = arr
}

// 分页跳转
function pageChange(page) {
  currentPage.value = page.pageNum
  pageSize.value = page.pageSize
  handleQuery(true)
}
function handlePageChange(pageNum) {
  currentPage.value = pageNum
  // pageSize.value = page.pageSize
  handleQuery(true)
}
function handleFilterChange(params) {
  queryParams.value = params
  currentPage.value = 1
  handleQuery()
}
function handleSortChange() {
  currentPage.value = 1
  handleQuery()
}
function handleParams() {
  let params = {
    ...queryParams.value,
    waitsSort: sort.value,
    toBeConfirmSort: toBeConfirmSort.value,
  }
  return params
}
let queryTimer = ''
// 搜索
function handleQuery(isScroll) {
  tableLoading.value = true
  queryTimer = Math.random().toString(36)
  let curTimer = queryTimer
  getModelList({
    ...handleParams(),
    pageNum: currentPage.value,
    pageSize: pageSize.value,
  })
    .then(res => {
      // console.log(res);
      if (curTimer != queryTimer) return
      if (res.code == 200) {
        tableData.value = res.data.rows
        total.value = res.data.total
        if (isScroll) {
          nextTick(() => {
            scrollToTop()
            // let dom = document.getElementById('header-box')
            // if (dom) {
            //   dom.scrollIntoView({ behavior: 'smooth' })
            // }
          })
        }
        multipleSelection.value.length = 0
        isIndeterminate.value = false
        checkAll.value = false
        setTimeout(() => resizeChange())
      }
    })
    .finally(() => {
      if (curTimer != queryTimer) return
      tableLoading.value = false
    })
}

// 重置
function resetQuery() {
  queryParams.value = {
    keyword: '',
    name: undefined,
    id: undefined,
    persons: '',
    platform: '',
    nation: '',
    sex: '',
    ageGroup: '',
    specialtyCategory: '',
    type: '',
    status: '',
    cooperation: '',
  }
  handleQuery()
}

function handleAction(btn, row) {
  switch (btn) {
    case '置顶':
      setModelTop(row.id)
      break
    case '排序':
      sortDialogVisible.value = true
      modelSort.value = row.sort
      modelSortId.value = row.id
      break
    case '取消置顶':
      setModelDown(row.id)
      break
    case '后台链接':
      openBackstageLink(row)
      break
    case '家庭成员':
      ModelFamilyRef.value?.open(row.id, row.familyId, row.isInitiator)
      break
    case '正常合作':
      EditModelStatusRef.value?.open(row, 0)
      break
    case '暂停合作':
      EditModelStatusRef.value?.open(row, 1, '', row.statusExplain)
      break
    case '取消合作':
      EditModelStatusRef.value?.open(
        row,
        3,
        '',
        row.statusExplain,
        row.cancelCooperationSubType,
        row.cancelCooperationType
      )
      break
    case '行程中':
      EditModelStatusRef.value?.open(row, 2, row.travel, row.statusExplain)
      break
  }
}

function showModelType(key) {
  switch (key) {
    case 0:
      return '影响者'
    case 1:
      return '素人'
    case 2:
      return '都属于'
    default:
      return ''
  }
}
function showModelCooperation(key) {
  switch (key) {
    case 0:
      return '一般模特'
    case 1:
      return '优质模特'
    default:
      return ''
  }
}

function openAllTags(data, title) {
  let dom = '<div style="display: flex;gap: 10px;flex-direction: row;flex-wrap: wrap;">'
  data.forEach(item => {
    dom += `<el-tag class="el-tag el-tag--info el-tag--default el-tag--plain">${item.name}</el-tag>`
  })
  dom += '</div>'
  proxy.$modal.alert(dom, title, {
    dangerouslyUseHTMLString: true,
  })
}

// 新增按钮
function handleAdd() {
  router.push('/model/handleModel/add')
}
function routerNewWindow(path) {
  const { href } = router.resolve({ path })
  window.open(href, '_blank')
}
// 修改关联人员按钮
function handleEdit() {
  dialogVisible.value = true
  dialogPersons.value = ''
}
// 启用/禁用
function handleEnable(id, status, msg) {
  proxy.$modal.confirm(`确认${msg}！`, '提示', {}).then(() => {
    tableLoading.value = true
    modelEnable(id, status)
      .then(res => {
        if (res.code == 200) {
          proxy.$modal.msgSuccess(`${msg}成功！`)
          handleQuery()
        }
      })
      .catch(() => (tableLoading.value = false))
  })
}
// 置顶
function setModelTop(id) {
  modelTopOperate('置顶模特', id)
}
function setModelDown(id) {
  modelTopOperate('取消置顶模特', id)
}

function modelTopOperate(tip, id) {
  proxy.$modal.confirm('确认' + tip + '?', '提示', {}).then(() => {
    tableLoading.value = true
    modelTop(id)
      .then(res => {
        if (res.code == 200) {
          proxy.$modal.msgSuccess(`${tip}成功！`)
          handleQuery()
          getModelTopCount()
        }
      })
      .catch(() => (tableLoading.value = false))
  })
}
// 后台链接
function openBackstageLink(row) {
  proxy.$modal.loading('获取中')
  createBackstageLink(row.id)
    .then(res => {
      if (res.data) {
        modelLinkDialog.value = true
        modelLink.value = res.data
      } else {
        proxy.$modal.msgWarning('获取链接错误')
      }
    })
    .finally(() => proxy.$modal.closeLoading())
}

function handleOpenVideo(src) {
  videoSrc.value = src
  dialogVideoRef.value?.open()
}
function handleOpenPhoto(photos) {
  ModelLifePhotoRef.value?.open(photos)
}
function handleOpenDetails(row) {
  ModelVideoListRef.value?.open(row.id)
}

function resizeChange() {
  // console.dir(ModelListRef.value);
  ModelListRef.value?.forEach(item => {
    item.resize()
  })
}

onMounted(() => {
  window.addEventListener('resize', resizeChange)
})

function getModelTopCount() {
  modelTopCount().then(res => {
    if (res.code == 200) {
      modelTopInfo.value = res.data
    }
  })
}

handleQuery(true)
getModelCategorySelect()
getModelPersonsSelectList()
getModelTopCount()
</script>

<style lang="scss" scoped>
.pages {
  padding: 20px;
}
.tag-box {
  display: flex;
  gap: 10px;
  flex-direction: row;
  flex-wrap: wrap;
}
.pic-box {
  position: relative;
  overflow: hidden;
  width: fit-content;

  .hint {
    background: var(--el-color-warning);
    color: #fff;
    font-size: 13px;
    width: 60px;
    position: absolute;
    left: -20px;
    top: -2px;
    transform: scale(0.8) rotateZ(-45deg);
  }
}
.pagination-box {
  display: flex;
  justify-content: flex-end;
  margin-top: 12px;
}
</style>
