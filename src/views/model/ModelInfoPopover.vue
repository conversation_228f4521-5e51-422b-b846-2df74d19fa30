<template>
  <!-- 虚拟触发 -->
  <el-popover
    :placement="placementType"
    :width="680"
    :show-after="100"
    :hide-after="0"
    :offset="0"
    @before-leave="resetModelInfo()"
    ref="popoverRef"
    :virtual-ref="virtualRef"
    :visible="popoverVisible"
    virtual-triggering
    style="border-radius: 10px; transition: transform 0.15s ease-out"
    @after-enter="checkViewportSpace()"
  >
    <div
      class="model-info-box"
      v-loading="loading"
      @mouseenter="handleMouseEnter()"
      @mouseleave="handleMouseLeave()"
    >
      <div class="head-info flex-start gap">
        <el-image
          v-if="modelInfo.modelPic"
          class="head-img"
          :src="$picUrl + modelInfo.modelPic + '!1x1compress'"
          fit="fill"
        />
        <!-- <div class="head-img img-name" v-else-if="modelInfo.name">
          {{ modelInfo.name.substring(0,1) }}
        </div> -->
        <div class="flex-center head-img gray" v-else>
          <el-icon :size="20"><Picture /></el-icon>
        </div>
        <div style="width: 100%">
          <div class="flex-between" style="align-items: baseline">
            <div class="flex-start gap-10 name">
              {{ modelInfo.name }}
              <el-tag v-if="modelInfo.status == 0" type="primary" round>正常合作</el-tag>
              <el-tag v-else-if="modelInfo.status == 1" type="warning" round>暂停合作</el-tag>
              <el-tag v-else-if="modelInfo.status == 3" type="danger" round>取消合作</el-tag>
              <el-tag v-else-if="modelInfo.status == 2" type="success" round>行程中</el-tag>
            </div>
            <div>英文客服：{{ modelInfo.issueUserName || '-' }}</div>
          </div>
          <div class="flex-start gap-5" style="margin: 5px 0">
            <model-score
              v-if="modelInfo.cooperationScore || modelInfo.cooperationScore === 0"
              :score="modelInfo.cooperationScore"
              style="width: 32px"
            />
            <biz-model-type-new :value="modelInfo.type" />
            <!-- <div style="color: #606266" v-if="modelInfo.cooperationScore || modelInfo.cooperationScore === 0">
              模特评分：{{ modelInfo.cooperationScore }}分
            </div> -->
            <!-- <biz-model-cooperation :value="modelInfo.cooperation" type="primary" /> -->
          </div>
          <div class="flex-start" style="flex-wrap: wrap">
            <!-- 国家 -->
            <biz-nation :value="modelInfo.nation" />
            <!-- 平台 -->
            <biz-model-platform :value="modelInfo.platform" />
            <el-tag v-if="modelInfo.platform == '2'" type="warning" size="small" round>其他</el-tag>
            <!-- 模特类型 -->
            <!-- <biz-model-type :value="modelInfo.type" /> -->
            <el-tag style="margin-right: 5px" type="warning" size="small" round>
              {{ modelInfo.sex ? '男性' : '女性' }}
            </el-tag>
          </div>
        </div>
      </div>

      <el-divider style="margin: 10px 0" />

      <div class="flex-start tag-box gap" v-show="modelInfo.specialtyCategory.length">
        <div class="label-text">擅长品类</div>
        <template v-for="item in modelInfo.specialtyCategory" :key="item.dictId">
          <el-tag type="danger" round>{{ item.name }}</el-tag>
        </template>
      </div>

      <div class="flex-start videoList" v-if="videoList && videoList.length > 0">
        <template v-for="(item, index) in videoList" :key="item.videoUrl">
          <VideoCover
            v-if="index < 4"
            :src="item.picUri"
            width="136px"
            height="90px"
            fit="cover"
            @click="playVideo(item.videoUrl)"
          />
        </template>
      </div>

      <div v-if="familyList && familyList.length > 0">
        <el-divider style="margin: 10px 0" v-if="videoList && videoList.length > 0" />

        <div>家庭成员：{{ familyList.length }}</div>

        <div class="flex-between family-box gap">
          <template v-for="(item, index) in familyList" :key="item.id">
            <div class="flex-start gap" style="width: calc(100% / 2 - 20px)">
              <el-image class="head-img" :src="$picUrl + item.modelPic + '!1x1'" fit="fill"></el-image>
              <div class="family-info">
                <div style="margin-bottom: 3px; display: flex">
                  <div style="margin-right: 8px; display: flex">
                    <div class="one-ell" style="max-width: 50px">{{ item.name }}</div>
                    <div>(ID:{{ item.account }})</div>
                  </div>
                  <biz-nation tag="text" :value="item.nation" />
                  <span style="margin: 0 8px">
                    {{ item.sex ? '男性' : '女性' }}
                  </span>
                  <biz-model-ageGroup tag="text" :value="item.ageGroup" />
                </div>
                <div class="flex-start">
                  <model-score
                    v-if="item.cooperationScore || item.cooperationScore === 0"
                    :score="item.cooperationScore"
                    style="margin-right: 5px"
                  />
                  <biz-model-type-new :value="item.type" />
                  <!-- <biz-model-type :value="item.type" /> -->
                  <!-- <biz-model-cooperation :value="item.cooperation" /> -->
                  <biz-model-status :value="item.status" />
                </div>
              </div>
            </div>
          </template>
        </div>
      </div>

      <div class="flex-center">
        <!-- <el-button type="warning" link @click="toModelPage">查看模特更多详情＞</el-button> -->
      </div>
    </div>
  </el-popover>
  <!-- <DialogVideo ref="dialogCompRef" :videoSrc="videoSrc" /> -->
</template>

<script setup>
import VideoCover from '@/views/model/components/VideoCover.vue'
// import DialogVideo from '@/components/public/video/DialogVideo.vue'
import { useModelMap } from '@/hooks/modelDetailsInfo'
import { http_reg } from '@/utils/RegExp'
import { ElMessage } from 'element-plus'

// const router = useRouter()

const { modelInfo, loading, getModelInfo, resetModelInfo } = useModelMap()

defineExpose({
  open,
  close,
})

let key = 0
const popoverRef = ref()
const virtualRef = ref()
const placementType = ref('top')
const popoverVisible = ref(false)
const popoverFocus = ref(false)
const modelId = ref('')
const videoList = computed(() => {
  if (modelInfo.value.amazonVideo.length || modelInfo.value.tiktokVideo.length) {
    return [...modelInfo.value.amazonVideo, ...modelInfo.value.tiktokVideo]
  }
  return []
})

const familyList = computed(() => {
  if (modelInfo.value.modelFamilys && modelInfo.value.modelFamilys.length) {
    return modelInfo.value.modelFamilys
  }
  return []
})

const family = [
  { name: '父亲', age: 50, src: '', id: 4644477 },
  { name: '母亲', age: 45, src: '', id: 1234567 },
  { name: '儿子', age: 12, src: '', id: 7654321 },
]
// const dialogCompRef = ref()
const videoSrc = ref('')

function playVideo(url) {
  if (!url || !http_reg.test(url)) {
    ElMessage.warning('视频链接有误！')
    return
  }
  // dialogCompRef.value.open()
  // videoSrc.value = url
  // videoSrc.value = 'https://uqcf789uvlq.feishu.cn/file/QlnVbKaUrooTn3xUvNjcdQGynrg'
  window.open(url)
}

function open(el, id, type = 'top') {
  key++
  getModelInfo(id)
  placementType.value = type
  virtualRef.value = el
  modelId.value = id
  popoverFocus.value = false
  popoverVisible.value = true
}

const checkViewportSpace = () => {
  nextTick(() => {
    const popoverContent = document.querySelector('.model-info-box')
    if (!popoverContent) return
    const contentHeight = popoverContent.offsetHeight
    const triggerRect = virtualRef.value?.getBoundingClientRect()
    placementType.value = contentHeight > triggerRect.top ? 'bottom' : 'top'
  })
}
function close() {
  let oldKey = key
  // 延迟关闭，不然鼠标移入不了
  setTimeout(() => {
    // console.log('close', popoverFocus.value, key, oldKey);
    if (!popoverFocus.value && key == oldKey) {
      popoverVisible.value = false
    }
  }, 50)
}
function handleMouseEnter() {
  popoverFocus.value = true
}
function handleMouseLeave() {
  popoverFocus.value = false
  close()
}

// function toModelPage() {
//   router.push({
//     path: '/modelWarehouse/modelList',
//     query: {
//       id: modelId.value,
//       name: JSON.stringify(modelInfo.value.name),
//       d: 1
//     }
//   })
// }
</script>

<style scoped lang="scss">
.model-info-box {
  padding: 10px;

  .gap {
    gap: 10px;
  }

  .head-info {
    gap: 10px;
    margin: 10px 0;

    .head-img {
      width: 60px;
      height: 60px;
      flex-shrink: 0;
      border-radius: 50%;
      border: 1px solid var(--border-gray-color);

      &.img-name {
        font-size: 20px;
        color: #fff;
        background-color: #3b99fc;
        text-align: center;
        line-height: 57px;
        font-weight: 600;
      }

      &.gray {
        background-color: var(--bg);
      }
    }
    .name {
      // margin-bottom: 10px;
      font-size: 16px;
      font-weight: 600;
      color: var(--text-color);
      flex-wrap: wrap;
    }
    .text {
      font-size: 14px;
      color: var(--text-color);
    }
  }

  .tag-box {
    margin: 20px 0;
    flex-wrap: wrap;
  }

  .videoList {
    flex-wrap: wrap;
    margin: 20px 0;
    gap: 20px;
  }
  .family-box {
    margin-top: 10px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    max-height: 200px;
    overflow-y: auto;
    .head-img {
      width: 50px;
      height: 50px;
      flex-shrink: 0;
      border-radius: 50%;
      border: 1px solid var(--border-gray-color);

      &.img-name {
        font-size: 20px;
        color: #fff;
        background-color: #3b99fc;
        text-align: center;
        line-height: 57px;
        font-weight: 600;
      }

      &.gray {
        background-color: var(--bg);
      }
    }
    .family-info {
      font-size: 12px;
    }
  }
}
</style>
