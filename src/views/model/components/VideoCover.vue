<template>
  <div class="vc-box" :style="boxStyle">
    <div class="image-box" :style="{
      width: width,
      height: height
    }">
      <div class="play-modal flex-center">
        <el-icon :size="size" :color="color"><VideoPlay /></el-icon>
      </div>
      <!-- <PercentageImg :src="src" :fit="fit" direction="horizontal" /> -->
      <el-image
        :src="$picUrl + src"
        :fit="fit"
        class="cover-img"
        preview-teleported
      >
        <template #error>
          <div class="image-error">
            <el-icon :size="25"><Picture /></el-icon>
          </div>
        </template>
      </el-image>
    </div>
    <div class="vc-title">
      {{ title }}
    </div>
  </div>
</template>

<script setup lang="ts">
// import PercentageImg from '@/components/public/image/PercentageImg.vue'

interface Props {
  title?: string
  width?: string
  height?: string
  color?: string
  size?: string | number
  fit?: string
  src: string
  boxStyle?: {[x: string]: any}
}

const props = withDefaults(defineProps<Props>(), {
  title: '',// 视频标题
  width: '140px',
  height: '140px',
  color: '#ffffffc4',
  size: 38,
  fit: 'contain',
  src: '',
  boxStyle: () => ({})
})
</script>

<style scoped lang="scss">
.vc-box {
  position: relative;

  .image-box {
    width: 100px;
    height: 100px;
    flex-shrink: 0;
    position: relative;
    cursor: pointer;
    background: var(--bottom-bg-color);
    border-radius: 8px;
    border: 1px solid var(--border-gray-color);
    overflow: hidden;
  
    &:hover {
      .play-modal {
        opacity: 1;
      }
    }
  
    .el-image {
      width: 100%;
      height: 100%;
    }
    
    .play-modal {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: #66666666;
      z-index: 9;
      opacity: 0;
    }

    .cover-img {
      width: 100%;
      height: 100%;
    }
    .image-error {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
  .vc-title {
    font-size: 16px;
    font-weight: 500;
    text-align: center;
  }
}
</style>