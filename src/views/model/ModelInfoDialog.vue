<template>
  <div>
    <el-dialog
      align-center
      append-to-body
      title=""
      v-model="isShow"
      width="600px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="close"
    >
      <div class="model-info-box" v-loading="loading">
        <div class="head-info flex-start gap">
          <el-image
            v-if="modelInfo.modelPic"
            class="head-img"
            :src="$picUrl + modelInfo.modelPic + '!1x1compress'"
            fit="fill"
            @click="showViewer([modelInfo.modelPic], { raw: true })"
          />
          <!-- <div class="head-img img-name" v-else-if="modelInfo.name">
          {{ modelInfo.name.substring(0,1) }}
        </div> -->
          <div class="flex-center head-img gray" v-else>
            <el-icon :size="20"><Picture /></el-icon>
          </div>
          <div>
            <div class="flex-start gap-10 name">
              {{ modelInfo.name }}
              <el-tag v-if="modelInfo.status == 0" type="primary" round>正常合作</el-tag>
              <el-tag v-else-if="modelInfo.status == 1" type="warning" round>暂停合作</el-tag>
              <el-tag v-else-if="modelInfo.status == 3" type="danger" round>取消合作</el-tag>
              <el-tag v-else-if="modelInfo.status == 2" type="success" round>行程中</el-tag>
            </div>
            <div class="flex-start gap-5" style="margin: 5px 0">
              <model-score
                v-if="modelInfo.cooperationScore || modelInfo.cooperationScore === 0"
                :score="modelInfo.cooperationScore"
              />
              <biz-model-type-new :value="modelInfo.type" />
              <!-- <div
                style="color: #606266"
                v-if="modelInfo.cooperationScore || modelInfo.cooperationScore === 0"
              >
                模特评分：{{ modelInfo.cooperationScore }}分
              </div> -->
              <!-- <biz-model-cooperation :value="modelInfo.cooperation" type="primary" /> -->
            </div>
            <div class="flex-start" style="flex-wrap: wrap; gap: 5px 0">
              <!-- 国家 -->
              <biz-nation :value="modelInfo.nation" />
              <!-- 平台 -->
              <biz-model-platform :value="modelInfo.platform" />
              <el-tag v-if="modelInfo.platform == '2'" type="warning" size="small" round>其他</el-tag>
              <!-- 模特类型 -->
              <!-- <biz-model-type :value="modelInfo.type" /> -->
              <el-tag style="margin-right: 5px" type="warning" size="small" round>
                {{ modelInfo.sex ? '男性' : '女性' }}
              </el-tag>
              <!-- <biz-model-cooperation :value="modelInfo.cooperation" /> -->
            </div>
          </div>
        </div>

        <el-divider style="margin: 10px 0" />

        <div class="flex-start tag-box gap" v-show="modelInfo.specialtyCategory.length">
          <div class="label-text">擅长品类</div>
          <template v-for="item in modelInfo.specialtyCategory" :key="item.dictId">
            <el-tag type="danger" round>{{ item.name }}</el-tag>
          </template>
        </div>

        <div class="flex-start videoList" v-if="videoList && videoList.length > 0">
          <template v-for="(item, index) in videoList" :key="item.videoUrl">
            <VideoCover
              v-if="index < 4"
              :src="item.picUri"
              width="136px"
              height="90px"
              fit="cover"
              @click="playVideo(item.videoUrl)"
            />
          </template>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import VideoCover from '@/views/model/components/VideoCover.vue'
import { http_reg } from '@/utils/RegExp'
import { ElMessage } from 'element-plus'
import { useModelMap } from '@/hooks/modelDetailsInfo'
import { useViewer } from '@/hooks/useViewer'
const { showViewer } = useViewer()

const { modelInfo, loading, getModelInfo, resetModelInfo } = useModelMap()
const isShow = ref(false)

const videoList = computed(() => {
  if (modelInfo.value.amazonVideo.length || modelInfo.value.tiktokVideo.length) {
    return [...modelInfo.value.amazonVideo, ...modelInfo.value.tiktokVideo]
  }
  return []
})

const emits = defineEmits(['succes'])
const open = id => {
  getModelInfo(id)
  isShow.value = true
}

const close = () => {
  isShow.value = false
}

function playVideo(url) {
  if (!url || !http_reg.test(url)) {
    ElMessage.warning('视频链接有误！')
    return
  }
  // dialogCompRef.value.open()
  // videoSrc.value = url
  // videoSrc.value = 'https://uqcf789uvlq.feishu.cn/file/QlnVbKaUrooTn3xUvNjcdQGynrg'
  window.open(url)
}

defineExpose({ open, close })
</script>

<style lang="scss" scoped>
.model-info-box {
  padding: 10px;

  .gap {
    gap: 10px;
  }

  .head-info {
    gap: 10px;
    margin: 10px 0;

    .head-img {
      width: 60px;
      height: 60px;
      flex-shrink: 0;
      border-radius: 50%;
      border: 1px solid var(--border-gray-color);
      cursor: pointer;

      &.img-name {
        font-size: 20px;
        color: #fff;
        background-color: #3b99fc;
        text-align: center;
        line-height: 57px;
        font-weight: 600;
      }

      &.gray {
        background-color: var(--bg);
      }
    }
    .name {
      // margin-bottom: 10px;
      font-size: 16px;
      font-weight: 600;
      color: var(--text-color);
      flex-wrap: wrap;
    }
    .text {
      font-size: 14px;
      color: var(--text-color);
    }
  }

  .tag-box {
    margin: 20px 0;
    flex-wrap: wrap;
  }

  .videoList {
    flex-wrap: wrap;
    margin: 20px 0;
    gap: 20px;
  }
  .family-box {
    margin-top: 10px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    max-height: 200px;
    overflow-y: auto;
    .head-img {
      width: 50px;
      height: 50px;
      flex-shrink: 0;
      border-radius: 50%;
      border: 1px solid var(--border-gray-color);

      &.img-name {
        font-size: 20px;
        color: #fff;
        background-color: #3b99fc;
        text-align: center;
        line-height: 57px;
        font-weight: 600;
      }

      &.gray {
        background-color: var(--bg);
      }
    }
    .family-info {
      font-size: 12px;
    }
  }
}
</style>
