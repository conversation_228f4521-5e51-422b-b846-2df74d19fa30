<template>
  <div class="verification-page">
    <Title>财务对账表格</Title>
    <el-form :inline="true" :model="form" @submit.prevent>
      <el-form-item label="统计时间" style="width: 400px">
        <el-date-picker
          v-model="form.time"
          format="YYYY/M/D HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="datetimerange"
          range-separator="~"
          unlink-panels
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
          :shortcuts="shortcuts"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="表格类型" prop="isAll">
        <el-radio-group v-model="form.isAll">
          <el-radio-button label="全部字段" :value="1" />
          <el-radio-button label="出纳字段" :value="0" />
        </el-radio-group>
      </el-form-item>
      <el-form-item>
        <DownloadBtn
          type="primary"
          plain
          :url="`/order/finance/exportFinancialVerification/export`"
          is-asnyc
          text="导出"
          loadingText="导出中"
          message="确认导出"
          :params="handleParams"
          :config="{
            timeout: 30000, // 请求超时时间 30s
          }"
        />
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import Title from '@/components/Public/title'
import DownloadBtn from '@/components/Button/DownloadBtn'

const form = reactive({
  time: [],
  isAll: 1,
})

const shortcuts = [
  {
    text: '今天',
    value: () => {
      const start = new Date()
      start.setHours(0, 0, 0, 0)
      const end = new Date()
      end.setHours(23, 59, 59, 59)
      return [start, end]
    },
  },
]


function handleParams() {
  const params = {
    isAll: form.isAll,
  }
  if (form.time.length == 2) {
    params.startTime = form.time[0]
    params.endTime = form.time[1]
  }
  return params
}
</script>

<style scoped lang="scss">
.verification-page {
  padding: 20px 30px;

  .title {
    font-size: 15px;
    margin-bottom: 30px;
  }
}
</style>
