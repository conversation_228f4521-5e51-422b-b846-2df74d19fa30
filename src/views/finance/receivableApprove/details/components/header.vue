<template>
  <div class="header-box flex-between" style="align-items: unset">
    <div class="flex-center">
      <div class="box-order">
        <div v-if="!data.isMergeOrder || data.isMergeOrder == 0">
          <span class="head-title">订单号：</span>
          {{ data.orderNum || '-' }}
        </div>
        <div>
          <span class="head-title">支付号：</span>
          {{ data.payNum || '-' }}
        </div>
        <div>
          <el-tag type="warning" effect="dark" round size="large">
            {{ auditStatusList.find(item => item.value == data.auditStatus)?.label }}
          </el-tag>
        </div>
      </div>
      <el-divider style="height: 80%; margin: 0 30px" direction="vertical" />
      <div class="box-order">
        <div style="font-weight: 600">
          {{ data.orderUser?.businessName || '-' }}
        </div>
        <div v-if="type === 'order'">
          <span class="head-title">下单运营：</span>
          姓名: {{ data.orderUserName || '-' }}/微信名: {{ data.orderUserNickName || '-' }}
        </div>
      </div>
      <el-divider style="height: 80%; margin: 0 30px" direction="vertical" />
      <div class="box-order">
        <template v-if="data.isMergeOrder">
          <div v-if="data?.mergeOrderPromotionAmount">限时满减活动：￥{{ data.mergeOrderPromotionAmount }}</div>
          <div>合计美元：${{ data?.mergeOrderAmountDollar }}</div>
          <div>合计人民币：￥{{ data?.mergeOrderAmount }}</div>
          <div>合并时间：{{ data?.mergeTime }}</div>
          <div>提交审批：{{ data?.submitCredentialTime || '-' }}</div>
        </template>
        <template v-else>
          <div v-if="data?.orderPromotionAmount">
            <span class="head-title">限时满减活动：</span>
            ￥{{ data.orderPromotionAmount }}
          </div>
          <div>
            <span class="head-title">美元：</span>
            ${{ type === 'order' ? data?.orderAmountDollar : data?.packageAmount }}
          </div>
          <div>
            <span class="head-title">百度汇率：</span>
            {{ data.currentExchangeRate || '-' }}
          </div>
          <div>
            <span class="head-title">人民币：</span>
            ￥{{ type === 'order' ? data?.orderAmount : data?.orderAmount }}
          </div>
        </template>
      </div>
    </div>
    <div>
      <div style="font-weight: 600">支付结果</div>
      <div>
        <span class="head-title">支付方式：</span>
        {{ payTypeMapNew[data.payType] }}
        <el-tag v-if="data.payTypeDetail && (data.payType == 7 || data.payType == 17)" type="warning" round>
          {{ payMoneyTypeMap[data.payTypeDetail] || '-' }}
        </el-tag>
      </div>
      <div v-if="data.useBalance || data.mergeUseBalance">
        <span class="head-title">使用余额：</span>
        ￥{{ data.isMergeOrder ? data.mergeUseBalance : data.useBalance }}
      </div>
      <div>
        <span class="head-title">剩余支付：</span>
        ￥{{ data.isMergeOrder ? data.mergeSurplusAmount : data.surplusAmount }}
      </div>
      <!-- v-if="data.payType == 7 || data.payType == 17" -->
      <div>
        <span class="head-title">实付金额：</span>
        <span v-if="data.auditStatus == 0 || data.auditStatus == 2 || data.auditStatus == 3">-</span>
        <span v-else>
          <template
            v-if="
              data.realPayAmountCurrency == 0 ||
              data.realPayAmountCurrency ||
              data.mergeRealPayAmountCurrency == 0 ||
              data.mergeRealPayAmountCurrency
            "
          >
            {{ data.isMergeOrder ? data.mergeRealPayAmountCurrency : data.realPayAmountCurrency }}
          </template>
          <template v-else>-</template>
        </span>
        <!-- {{ data.auditStatus == 0 ? '-' : data.realPayAmountCurrency || '0' }} -->
      </div>
      <!-- <div v-else>
          实付金额：{{ data.auditStatus == 0 ? '-' : data.realPayAmount || '0' }}
        </div> -->
      <div>
        <span class="head-title">支付币种：</span>
        {{ handleCurrency(data.currency) }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { payTypeMapNew } from '@/utils/dict'
import { setMealTypeList, payMoneyTypeMap } from '@/views/finance/data.js'
import { Warning } from '@element-plus/icons-vue'

const { proxy } = getCurrentInstance()
const { sys_money_type } = proxy.useDict('sys_money_type')

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
  type: {
    type: String,
    default: '',
  },
})

const auditStatusList = [
  { value: 0, label: '待审核', type: 'warning' },
  { value: 1, label: '审核通过', type: 'succcess' },
  { value: 2, label: '审核异常', type: 'danger' },
  { value: 3, label: '已关闭', type: 'info' },
]

function handleCurrency(val) {
  if (val == '1') {
    return '人民币'
  }
  return sys_money_type.value.find(item => item.value == val)?.label || '-'
}

function getType(val, key) {
  let type = setMealTypeList.find(item => item.value === val)
  if (type) {
    return type[key]
  }
  return ''
}
</script>

<style scoped lang="scss">
.header-box {
  background-color: #fff;
  padding: 13px 100px 13px 13px;
  border-radius: 4px;
  gap: 10px;
  position: relative;
  box-shadow: var(--el-box-shadow-light);
  color: #666;
  .head-title {
    font-size: 14px;
    color: #999;
  }
}
.el-col-6,
.el-col-3,
.el-col-4,
.el-col-5 {
  display: flex;
  flex-direction: column;
  line-break: anywhere;
  word-break: break-all;
  gap: 8px 0;
  //   width: calc(100% - 20px);
}
.box-order {
  gap: 8px 0px;
  display: grid;
}
</style>
