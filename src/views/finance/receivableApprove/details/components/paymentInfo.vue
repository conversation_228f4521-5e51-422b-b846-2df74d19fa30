<template>
  <div>
    <Title>付款信息</Title>
    <el-table :data="list" style="width: 60%; min-width: 800px" border>
      <el-table-column label="付款账户" prop="payAccount" width="300">
        <template v-slot="{ row }">
          {{ row.payAccount || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="商家凭证" prop="picUrls" width="200">
        <template v-slot="{ row }">
          <el-button v-if="row.picUrls?.length" link type="primary" @click="showViewer(row.picUrls,{raw: true})">
            查看
          </el-button>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="收款账号" prop="orderPayeeAccountVO">
        <template v-slot="{ row }">
          <PayeeAccountVO :data="row.orderPayeeAccountVO" :payType="row.payType" />
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import Title from '@/components/Public/title.vue'
import PayeeAccountVO from '@/views/finance/receivableApprove/components/payeeAccountVO.vue'
import { useViewer } from '@/hooks/useViewer'

const { showViewer } = useViewer()
const props = defineProps({
  list: {
    type: Array,
    default: () => [],
  },
})
</script>
