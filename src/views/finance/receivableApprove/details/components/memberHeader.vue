<template>
  <div class="header-box flex-start" style="align-items: unset">
    <el-row style="flex: 1">
      <el-col :span="6">
        <div class="box-order" style="width: 90%">
          <div>
            <span class="head-title">订单编号：</span>
            {{ data.orderNum }}
          </div>
          <div>
            <span class="head-title">公司名称：</span>
            {{ data.orderUser?.businessName || '-' }}
          </div>
          <div v-if="type === 'order'">
            <span class="head-title">下单运营：</span>
            姓名: {{ data.orderUser?.name || '-' }}/微信名: {{ data.orderUser?.nickName || '-' }}
            <!-- {{ data.orderUser?.nickName || data.orderUser?.name || '-' }} -->
          </div>
          <div>
            <span class="head-title">审核状态：</span>
            {{ auditStatusList.find(item => item.value == data.auditStatus)?.label }}
          </div>
        </div>
      </el-col>
      <el-col :span="1">
        <el-divider style="height: 100%; margin: 0 0 0 -15px" :direction="'vertical'" />
      </el-col>
      <el-col :span="3">
        <div class="head-title">下单时间</div>
        <div>{{ data.orderTime }}</div>
      </el-col>
      <el-col :span="3">
        <div class="head-title">提交审批</div>
        <div>{{ data.submitCredentialTime }}</div>
      </el-col>
      <el-col :span="2">
        <div class="head-title">支付方式</div>
        <div>
          {{ payTypeMapNew[data.payType] }}
        </div>
        <div style="margin-top: 10px">
          <el-tag v-if="data.payTypeDetail && (data.payType == 7 || data.payType == 17)" type="warning" round>
            {{ payMoneyTypeMap[data.payTypeDetail] || '-' }}
          </el-tag>
        </div>
      </el-col>
      <el-col :span="3">
        <div class="head-title">{{ type == 'order' ? '订单金额' : '套餐金额' }}</div>
        <template v-if="type === 'order'">
          <div>美元：${{ data?.payAmountDollar }}</div>
          <div>百度汇率：{{ data.currentExchangeRate || '-' }}</div>
          <div>人民币：￥{{ data?.payAmount }}</div>
        </template>
        <template v-else>
          <div>美元：${{ data?.packageAmount }}</div>
          <div>百度汇率：{{ data.currentExchangeRate || '-' }}</div>
          <div>人民币：￥{{ data?.orderAmount }}</div>
        </template>
      </el-col>
      <el-col :span="3">
        <div class="head-title">优惠信息</div>
        <template v-if="data.orderDiscountDetailVOS && data.orderDiscountDetailVOS.length > 0">
          <div>
            优惠类型：{{
              disountTypeList.find(item => item.value == data.orderDiscountDetailVOS[0].type)?.label
            }}
          </div>
          <div v-if="data.orderDiscountDetailVOS[0].type == 3">
            {{ data.orderDiscountDetailVOS[0].channelType == 7 ? '裂变' : '渠道' }}名称：{{
              data.orderDiscountDetailVOS[0].channelName
            }}{{ data.seedId ? ` (ID${data.seedId})` : '' }}
          </div>
          <div>
            会员折扣：{{ data.orderDiscountDetailVOS[0].discountRatio
            }}{{ data.orderDiscountDetailVOS[0].discountType == 1 ? '元 (固定金额)' : '% (固定比例)' }}
          </div>
          <div>优惠金额：￥{{ data.orderDiscountDetailVOS[0].discountAmount }}</div>
        </template>
        <span v-else>-</span>
      </el-col>
      <el-col :span="3">
        <div class="head-title">支付情况</div>
        <div v-if="data.useBalance">钱包余额抵扣：￥{{ data.useBalance }}</div>
        <div>剩余支付：￥{{ data.surplusAmount }}</div>
        <!-- v-if="data.payType == 7 || data.payType == 17" -->
        <div>
          实付金额：
          <span v-if="data.auditStatus == 0 || data.auditStatus == 2 || data.auditStatus == 3">-</span>
          <span v-else>
            <template v-if="data.realPayAmountCurrency == 0 || data.realPayAmountCurrency">
              {{ data.realPayAmountCurrency }}
            </template>
            <template v-else>-</template>
          </span>
          <!-- {{ data.auditStatus == 0 ? '-' : data.realPayAmountCurrency || '0' }} -->
        </div>
        <!-- <div v-else>
            实付金额：{{ data.auditStatus == 0 ? '-' : data.realPayAmount || '0' }}
          </div> -->
        <div>支付币种：{{ handleCurrency(data.currency) }}</div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { payTypeMapNew } from '@/utils/dict'
import { setMealTypeList, payMoneyTypeMap } from '@/views/finance/data.js'
import { disountTypeList } from '@/views/order/vip/data.js'

const { proxy } = getCurrentInstance()
const { sys_money_type } = proxy.useDict('sys_money_type')

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
  type: {
    type: String,
    default: '',
  },
})

const auditStatusList = [
  { value: 0, label: '待审核' },
  { value: 1, label: '审核通过' },
  { value: 2, label: '审核异常' },
  { value: 3, label: '已关闭' },
]

function handleCurrency(val) {
  if (val == '1') {
    return '人民币'
  }
  return sys_money_type.value.find(item => item.value == val)?.label || '-'
}

function getType(val, key) {
  let type = setMealTypeList.find(item => item.value === val)
  if (type) {
    return type[key]
  }
  return ''
}
</script>

<style scoped lang="scss">
.header-box {
  background-color: #fff;
  padding: 13px;
  border-radius: 4px;
  gap: 10px;
  position: relative;
  box-shadow: var(--el-box-shadow-light);
  color: #666;
  .head-title {
    font-size: 14px;
    color: #999;
  }
}
.el-col-6,
.el-col-3,
.el-col-4,
.el-col-5 {
  display: flex;
  flex-direction: column;
  line-break: anywhere;
  word-break: break-all;
  gap: 8px 0;
  //   width: calc(100% - 20px);
}
.box-order {
  gap: 8px 0px;
  display: grid;
}
</style>
