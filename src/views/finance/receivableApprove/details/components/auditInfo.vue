<template>
  <div>
    <Title>审核信息</Title>
    <el-table :data="auditList" style="width: 100%">
      <el-table-column label="审核时间" prop="auditTime"></el-table-column>
      <el-table-column label="审核人" prop="auditUserName"></el-table-column>
      <el-table-column label="实付金额" prop="realPayAmountCurrency">
        <template v-slot="{ row }">
          <div>
            <span v-if="row.auditStatus == 2 || row.auditStatus == 0 || row.auditStatus == 3">-</span>
            <span v-else>
              {{ row.realPayAmountCurrency || '0' }}
            </span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="币种" prop="currency">
        <template v-slot="{ row }">
          <div>
            <div v-if="row.currency == '1'">人民币</div>
            <div v-else>{{ sys_money_type.find(item => item.value == row.currency)?.label || '-' }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="实付人民币" prop="realPayAmount">
        <template v-slot="{ row }">
          {{ row.payType == '7' || row.payType == '17' ? row.realPayAmount : '-' }}
        </template>
      </el-table-column>
      <el-table-column label="差额" prop="differenceAmount">
        <template v-slot="{ row }">
          <span v-if="row.differenceAmount || row.differenceAmount == 0">
            {{
              row.differenceAmount > 0
                ? '+' + row.differenceAmount
                : row.differenceAmount
                ? row.differenceAmount
                : '0'
            }}元
          </span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="支付时间" prop="payTime">
        <template v-slot="{ row }">{{ row.payTime || '-' }}</template>
      </el-table-column>
      <el-table-column label="支付方式" prop="payType" width="180">
        <template v-slot="{ row }">
          {{ payTypeMapNew[row.payType] || '-' }}
          <el-tag v-if="row.payTypeDetail && (row.payType == 7 || row.payType == 17)" type="warning" round>
            {{ payMoneyTypeMap[row.payTypeDetail] || '-' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="审核凭证" prop="auditVoucher">
        <template v-slot="{ row }">
          <el-button
            link
            type="primary"
            v-if="row.auditVoucher && row.auditVoucher.length > 0"
            @click="showViewer(row.auditVoucher)"
          >
            查看
          </el-button>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="备注" prop="remark"></el-table-column>
      <el-table-column label="审核结果" prop="auditStatus">
        <template v-slot="{ row }">
          {{ auditStatusList.find(item => item.value == row.auditStatus)?.label }}
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import Title from '@/components/Public/title.vue'
import { useViewer } from '@/hooks/useViewer'
import { payTypeSelectList, payTypeMapNew } from '@/utils/dict'
import { payMoneyTypeMap } from '@/views/finance/data.js'

const { proxy } = getCurrentInstance()
const { sys_money_type } = proxy.useDict('sys_money_type')

const { showViewer } = useViewer()
const props = defineProps({
  list: {
    type: Array,
    default: () => [],
  },
})

const auditList = computed(() => {
  // if(props.list?.length && props.list.length > 0) {
  //   return
  // }
  return props.list?.length
    ? props.list.map(item => {
        return {
          auditTime: item.auditTime,
          auditUserName: item.auditUserName,
          realPayAmount: item.realPayAmount,
          payTime: item.payTime,
          payType: item.payType,
          auditVoucher: item.resourceVos?.map(item => item.objectKey) || [],
          remark: item.remark,
          realPayAmountCurrency: item.realPayAmountCurrency,
          auditStatus: item.auditStatus,
          currency: item.currency,
          differenceAmount: item.differenceAmount,
          payTypeDetail: item.payTypeDetail,
        }
      })
    : []
})

const auditStatusList = [
  { value: 0, label: '待审核' },
  { value: 1, label: '审核通过' },
  { value: 2, label: '审核异常' },
  { value: 3, label: '已关闭' },
]
</script>
