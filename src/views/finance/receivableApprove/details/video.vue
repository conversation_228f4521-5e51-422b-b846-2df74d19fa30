<template>
  <div class="video-detail-box">
    <Header :data="orderInfo" type="order" />
    <div class="video-info-box">
      <div>
        <Title>订单信息</Title>
        <template v-if="orderInfo.isMergeOrder">
          <el-table
            style="width: 100%"
            :data="orderInfo.orderListVOList"
            row-key="id"
            border
            :expand-row-keys="expandedRows"
            @expand-change="treeTableExpandChange"
          >
            <template #empty>
              <el-empty description="暂无数据" :image-size="80"></el-empty>
            </template>
            <el-table-column type="expand" class-name="expand-box">
              <template v-slot="{ row }">
                <el-table ref="tableRef" :data="row.orderVideoVOS" style="width: 100%; margin: -8px 0" border>
                  <template #empty>
                    <el-empty description="暂无数据" :image-size="80"></el-empty>
                  </template>
                  <el-table-column
                    prop="productPic"
                    label="产品图"
                    align="center"
                    width="150"
                    class-name="product-img-box"
                  >
                    <template v-slot="{ row }">
                      <div style="padding: 20px 0 10px">
                        <el-image
                          style="width: 90px; height: 90px; cursor: pointer"
                          :src="
                            row.productPic
                              ? $picUrl +
                                row.productPic +
                                '?imageMogr2/format/webp/interlace/1/quality/100/interlace/0/thumbnail/200x'
                              : ''
                          "
                          fit="scale-down"
                          preview-teleported
                          @click="() => row.productPic?.length && showViewer([$picUrl + row.productPic])"
                        >
                          <template #error>
                            <img
                              :src="$picUrl + 'static/assets/no-img.png'"
                              alt=""
                              style="width: 100%; height: 100%"
                            />
                          </template>
                        </el-image>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="msg" label="产品信息" minWidth="300">
                    <template v-slot="{ row }">
                      <div style="text-align: left">
                        <div>视频编码:{{ row.videoCode }}</div>
                        <div>中文名称:{{ row.productChinese }}</div>
                        <div>英文名称:{{ row.productEnglish }}</div>
                        <div class="one-ell productLink">
                          产品链接:
                          <el-link :underline="false" target="_blank" type="primary" :href="row.productLink">
                            {{ row.productLink }}
                          </el-link>
                        </div>
                        <div class="flex-start">
                          <biz-model-platform :value="row.platform" />
                          <biz-nation :value="row.shootingCountry" />
                          <biz-model-type :value="row.modelType" />
                          <template v-for="op in videoFormatOptions" :key="op.value">
                            <el-tag
                              class="tag"
                              v-if="op.value == row.videoFormat"
                              type="warning"
                              size="small"
                              round
                            >
                              {{ op.label }}
                            </el-tag>
                          </template>
                        </div>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="picCount" label="照片数量" align="center" minWidth="110">
                    <template v-slot="scope">
                      {{ handleSelectiveAssembly(scope.row.picCount) }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="payAmount" label="订单金额" align="left" minWidth="180">
                    <template v-slot="{ row }">
                      <div style="text-align: left">
                        <div>美元：${{ row.amountDollar }}</div>
                        <div>百度汇率：{{ row.currentExchangeRate }}</div>
                        <div>人民币：￥{{ row.amount }}</div>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="payAmount" label="优惠明细" align="center" minWidth="120">
                    <template v-slot="{ row }">
                      <div style="text-align: left">
                        <div v-if="handleShowDiscount(row.orderDiscountDetailVOS, '1')">
                          限时满减活动：￥{{ handleDiscount(row.orderDiscountDetailVOS, '1') }}
                        </div>
                        <div v-if="handleShowDiscount(row.orderDiscountDetailVOS, '4')">
                          每月首单立减：{{ handleDiscount(row.orderDiscountDetailVOS, '4') }}
                        </div>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="amount" label="费用明细" align="left" minWidth="180">
                    <template v-slot="{ row }">
                      <div style="text-align: left">
                        <div>视频佣金：${{ row.videoPrice }}</div>
                        <div>照片佣金：${{ row.picPrice }}</div>
                        <div>佣金代缴税费：${{ row.commissionPaysTaxes }}</div>
                        <div>paypal代付手续费：${{ row.exchangePrice }}</div>
                        <div>蜗牛服务费：${{ row.servicePrice }}</div>
                        <div>百度汇率：{{ row.currentExchangeRate }}</div>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="orderNum" label="订单号"></el-table-column>
            <el-table-column align="center" prop="videoCount" label="视频数"></el-table-column>
            <el-table-column align="center" prop="payAmountDollar" label="美金">
              <template v-slot="{ row }">
                <div>${{ row.payAmountDollar }}</div>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="currentExchangeRate" label="百度汇率"></el-table-column>
            <el-table-column align="center" prop="payAmount" label="人民币">
              <template v-slot="{ row }">
                <div>￥{{ row.payAmount }}</div>
              </template>
            </el-table-column>
            <!-- <el-table-column align="center" label="操作">
              <template v-slot="{ row }">
                <el-button type="primary" size="small" link v-btn @click="treeTableExpandChange(row)">
                  {{ expandedRows.includes(row.id) ? '收起' : '展开' }}
                </el-button>
              </template>
            </el-table-column> -->
          </el-table>
        </template>
        <template v-else>
          <el-table ref="tableRef" :data="tableData" style="width: 100%" border>
            <template #empty>
              <el-empty description="暂无数据" :image-size="80"></el-empty>
            </template>
            <el-table-column
              prop="productPic"
              label="产品图"
              align="center"
              width="150"
              class-name="product-img-box"
            >
              <template v-slot="{ row }">
                <div style="padding: 20px 0 10px">
                  <el-image
                    style="width: 90px; height: 90px; cursor: pointer"
                    :src="
                      row.productPic
                        ? $picUrl +
                          row.productPic +
                          '?imageMogr2/format/webp/interlace/1/quality/100/interlace/0/thumbnail/200x'
                        : ''
                    "
                    fit="scale-down"
                    preview-teleported
                    @click="() => row.productPic?.length && showViewer([$picUrl + row.productPic])"
                  >
                    <template #error>
                      <img
                        :src="$picUrl + 'static/assets/no-img.png'"
                        alt=""
                        style="width: 100%; height: 100%"
                      />
                    </template>
                  </el-image>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="msg" label="产品信息" minWidth="300">
              <template v-slot="{ row }">
                <div style="text-align: left">
                  <div>视频编码:{{ row.videoCode }}</div>
                  <div>中文名称:{{ row.productChinese }}</div>
                  <div>英文名称:{{ row.productEnglish }}</div>
                  <div class="one-ell productLink">
                    产品链接:
                    <el-link :underline="false" target="_blank" type="primary" :href="row.productLink">
                      {{ row.productLink }}
                    </el-link>
                  </div>
                  <div class="flex-start">
                    <biz-model-platform :value="row.platform" />
                    <biz-nation :value="row.shootingCountry" />
                    <biz-model-type :value="row.modelType" />
                    <template v-for="op in videoFormatOptions" :key="op.value">
                      <el-tag
                        class="tag"
                        v-if="op.value == row.videoFormat"
                        type="warning"
                        size="small"
                        round
                      >
                        {{ op.label }}
                      </el-tag>
                    </template>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="picCount" label="照片数量" align="center" minWidth="110">
              <template v-slot="scope">
                {{ handleSelectiveAssembly(scope.row.picCount) }}
              </template>
            </el-table-column>
            <el-table-column prop="payAmount" label="订单金额" align="left" minWidth="180">
              <template v-slot="{ row }">
                <div style="text-align: left">
                  <div>美元：${{ row.amountDollar }}</div>
                  <div>百度汇率：{{ row.currentExchangeRate }}</div>
                  <div>人民币：￥{{ row.amount }}</div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="payAmount" label="优惠明细" align="center" minWidth="120">
              <template v-slot="{ row }">
                <div style="text-align: left">
                  <div v-if="handleShowDiscount(row.orderDiscountDetailVOS, '1')">
                    限时满减活动：￥{{ handleDiscount(row.orderDiscountDetailVOS, '1') }}
                  </div>
                  <div v-if="handleShowDiscount(row.orderDiscountDetailVOS, '4')">
                    每月首单立减：{{ handleDiscount(row.orderDiscountDetailVOS, '4') }}
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="amount" label="费用明细" align="left" minWidth="180">
              <template v-slot="{ row }">
                <div style="text-align: left">
                  <div>视频佣金：${{ row.videoPrice }}</div>
                  <div>照片佣金：${{ row.picPrice }}</div>
                  <div>佣金代缴税费：${{ row.commissionPaysTaxes }}</div>
                  <div>paypal代付手续费：${{ row.exchangePrice }}</div>
                  <div>蜗牛服务费：${{ row.servicePrice }}</div>
                  <div>百度汇率：{{ row.currentExchangeRate }}</div>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </template>
      </div>
      <div>
        <PaymentInfo :list="payMentData" />
      </div>
      <div v-if="orderInfo.status != '0'">
        <AuditInfo :list="orderInfo.orderAuditFlows" />
      </div>
    </div>
  </div>
</template>

<script setup>
import Header from './components/header.vue'
import PaymentInfo from './components/paymentInfo.vue'
import AuditInfo from './components/auditInfo.vue'
import Title from '@/components/Public/title.vue'
import { picCountNumOptions, videoFormatOptions } from '@/views/order/list/data.js'
import { useViewer } from '@/hooks/useViewer'
import { receivableAuditInfo } from '@/api/finance/refundApproval'

const { showViewer } = useViewer()
const route = useRoute()

const loading = ref(false)
const orderInfo = ref({})
const tableData = ref([])
const payMentData = ref([
  {
    payAccount: '',
    picUrls: [],
    payType: '',
    orderPayeeAccountVO: {
      accountName: '',
      bankAccount: '',
      bankName: '',
    },
  },
])
const orderId = ref('')

function handleSelectiveAssembly(val) {
  let str = picCountNumOptions.find(item => item.value == val)
  return str ? str.label : '-'
}

function init() {
  if (route.params.id) {
    orderId.value = route.params.id
    loading.value = true
    // 获取订单详情
    receivableAuditInfo(route.params.id)
      .then(res => {
        if (res.data) {
          orderInfo.value = res.data
          if (res.data.orderVideoReceivableAuditDetails?.length) {
            tableData.value = res.data.orderVideoReceivableAuditDetails
          }
          if (res.data.payType) {
            payMentData.value[0].payType = res.data.payType
          }
          if (res.data.orderPayeeAccountVO) {
            payMentData.value[0].orderPayeeAccountVO = res.data.orderPayeeAccountVO
          }
          if (res.data.payAccount) {
            payMentData.value[0].payAccount = res.data.payAccount
          }
          if (res.data.businessResourceVos && res.data.businessResourceVos.length > 0) {
            payMentData.value[0].picUrls = res.data?.businessResourceVos?.map(item => item.objectKey)
          }
        }
      })
      .finally(() => {
        loading.value = false
      })
  }
}

const expandedRows = ref([])
function treeTableExpandChange(row, expanded) {
  const index = expandedRows.value.indexOf(row.id)
  if (index > -1) {
    expandedRows.value.splice(index, 1)
  } else {
    expandedRows.value.push(row.id)
  }
}

const handleDiscount = (list, type) => {
  if (type == 1) {
    return list.find(item => item.type == 1).amount
  } else if (type == 4) {
    let data = list.find(item => item.type == 4)
    return '￥' + data.discountAmount + '（$' + data.amount + '）'
  }
}
const handleShowDiscount = (list, type) => {
  return list && list.length > 0 ? list.some(item => item.type == type) : false
}

init()
</script>

<style scoped lang="scss">
.video-detail-box {
  padding: 20px;

  .video-info-box {
    margin-top: 20px;
    background-color: #fff;
    padding: 13px;
    border-radius: 4px;
    gap: 10px;
    position: relative;
    box-shadow: var(--el-box-shadow-light);
    color: #666;

    .table-box {
      :deep(.el-table) {
        th {
          text-align: center;
        }
        .product-img-box {
          position: relative;

          .top-tag {
            position: absolute;
            top: 2px;
            left: 1px;

            .el-tag + .el-tag {
              margin-left: 5px;
            }
          }
        }
      }
      .productLink {
        position: relative;
        padding-right: 5px;

        :deep(.el-link) {
          display: contents;

          .el-link__inner {
            display: inline;
          }
        }
      }
    }
    :deep(.el-table) {
      .el-table__expand-icon {
        // display: none;
      }
      .expand-box {
        // display: none;
      }
    }
  }
}
</style>
