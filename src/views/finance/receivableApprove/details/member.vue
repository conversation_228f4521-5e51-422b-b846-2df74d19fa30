<template>
  <div class="member-details">
    <MemberHeader :data="detailInfo" />
    <div class="member-info">
      <div>
        <Title>会员信息</Title>
        <el-table :data="memberInfo" border style="width: 45%">
          <el-table-column label="套餐类型" prop="memberPackageType">
            <template v-slot="{ row }">
              {{ vipTypeList.find(item => item.value == row.memberPackageType)?.label }}
            </template>
          </el-table-column>
          <el-table-column label="赠送活动" prop="zshd">
            <template v-slot="{ row }">
              <div>{{ handleActivity(row.presentedTime, row.presentedTimeType) }}</div>
            </template>
          </el-table-column>
          <!-- <el-table-column label="种草优惠来源" prop="channelName">
            <template v-slot="{ row }">
              <div v-if="row.seedCodeDiscount" style="text-align: left">
                <div>{{ row.channelType == 7 ? '裂变' : '渠道' }}名称：{{ row.channelName }}</div>
                <div>会员折扣：{{ row.settleRage }}%</div>
                <div>优惠金额：￥{{ row.seedCodeDiscount }}</div>
              </div>
              <div v-else>-</div>
            </template>
          </el-table-column> -->
        </el-table>
      </div>
      <div>
        <PaymentInfo :list="payMentData" />
      </div>
      <div v-if="detailInfo.auditStatus != '0'">
        <AuditInfo :list="detailInfo.orderAuditFlows" />
      </div>
    </div>
  </div>
</template>

<script setup>
import Header from './components/header.vue'
import MemberHeader from './components/memberHeader.vue'
import PaymentInfo from './components/paymentInfo.vue'
import AuditInfo from './components/auditInfo.vue'
import Title from '@/components/Public/title.vue'
import { receivableAuditInfo } from '@/api/finance/refundApproval'
import { vipTypeList } from '@/views/finance/data.js'

const route = useRoute()

const detailId = ref('')
const detailInfo = ref({})
const memberInfo = ref([
  {
    memberPackageType: '',
    seedCodeDiscount: '',
    channelName: '',
    settleRage: '',
    channelType: '',
  },
])
const payMentData = ref([
  {
    payAccount: '',
    picUrls: [],
    payType: '',
    orderPayeeAccountVO: {
      accountName: '',
      bankAccount: '',
      bankName: '',
    },
  },
])

function getDetail() {
  receivableAuditInfo(detailId.value).then(res => {
    detailInfo.value = res.data
    if (res.data?.orderPayeeAccountVO) {
      payMentData.value[0].orderPayeeAccountVO = res.data.orderPayeeAccountVO
    }
    if (res.data?.payAccount) {
      payMentData.value[0].payAccount = res.data.payAccount
    }
    if (res.data.payType) {
      payMentData.value[0].payType = res.data.payType
    }
    if (res.data?.businessResourceVos && res.data.businessResourceVos.length > 0) {
      payMentData.value[0].picUrls = res.data?.businessResourceVos?.map(item => item.objectKey)
    }
    memberInfo.value[0] = {
      memberPackageType: res.data.memberPackageType,
      seedCodeDiscount: res.data.seedCodeDiscount,
      channelName: res.data.channelName,
      settleRage: res.data.settleRage,
      channelType: res.data.channelType,
      presentedTime: res.data.presentedTime,
      presentedTimeType: res.data.presentedTimeType,
    }
  })
}

function handleActivity(presentedTime, presentedTimeType) {
  if (presentedTime) {
    let s =
      presentedTimeType == 1 ? '天' : presentedTimeType == 2 ? '月' : presentedTimeType == 3 ? '年' : '-'
    return `赠送${presentedTime}${s}`
  } else {
    return '-'
  }
}

function getDetailInfo() {
  const { id } = route.params
  if (id) {
    detailId.value = id
    getDetail()
  }
}

getDetailInfo()
</script>

<style scoped lang="scss">
.member-details {
  padding: 20px;
}
.member-info {
  margin-top: 20px;
  background-color: #fff;
  padding: 13px;
  border-radius: 4px;
  gap: 10px;
  position: relative;
  box-shadow: var(--el-box-shadow-light);
  color: #666;
}
</style>
