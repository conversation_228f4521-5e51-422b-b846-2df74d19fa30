<template>
  <div class="order-info-box">
    <div class="flex-between head-box">
      <div class="flex-around" style="gap: 20px">
        <div class="head-label">
          <span>订单号：</span>
          {{ data.orderNum }}
        </div>
        <div class="head-label">
          <span>
            {{
              (data.merchantInfo?.businessName || '') +
              (data.merchantInfo?.memberCode ? '(' + data.merchantInfo?.memberCode + ')' : '')
            }}
          </span>
        </div>
        <div class="head-label">
          <span>{{ data.orderUser?.businessName }}</span>
          ({{ data.orderUser?.memberCode }})
        </div>
        <div class="head-label">
          <span>下单运营：</span>
          {{ data.orderUser?.name ? data.orderUser?.name : data.orderUser?.nickName }}
          <!-- {{ tableData[0].createOrderUserNickName }} -->
        </div>
        <div class="head-label">
          <span>下单时间：</span>
          {{ data.orderTime }}
        </div>
        <div class="head-label" v-if="data.submitCredentialTime">
          <span>提交审批：</span>
          {{ data.submitCredentialTime }}
        </div>
        <!-- <div class="head-label">
          <span>支付方式：</span>
          {{ payTypeMap[data.payType] }}
        </div> -->
      </div>
      <div>
        <div class="head-label">
          <span>支付方式：</span>
          {{ payTypeMapNew[data.payType]?.substring(0, 2) }}
        </div>
        <!-- <el-button
          v-btn
          v-if="data.auditStatus == 0 && tableData[0].status != orderStatusMap['交易关闭']"
          v-hasPermi="['receivable:approve:audit']"
          round
          type="primary"
          size="small"
          @click="handleAction('审核')"
        >
          去审核
        </el-button>
        <el-button
          v-btn
          v-else-if="data.auditStatus == 2"
          v-hasPermi="['receivable:approve:audit']"
          round
          type="primary"
          size="small"
          @click="handleAction('重新审核')"
        >
          重新审核
        </el-button> -->
      </div>
    </div>
    <div class="table-box">
      <el-table ref="tableRef" :data="tableData" style="width: 100%" border :span-method="objectSpanMethod">
        <template #empty>
          <el-empty description="暂无数据" :image-size="80"></el-empty>
        </template>
        <el-table-column
          prop="productPic"
          label="产品图"
          align="center"
          width="130"
          class-name="product-img-box"
        >
          <template v-slot="{ row }">
            <div class="flex-start top-tag">
              <el-tag v-if="row.isCare" type="danger" size="small" round>照顾单</el-tag>
              <el-tag v-if="row.carryType == 1" type="danger" size="small" round>主携带</el-tag>
              <el-tag v-if="row.carryType == 2" type="danger" size="small" round>被携带</el-tag>
            </div>
            <div style="padding: 20px 0 10px">
              <el-image
                style="width: 90px; height: 90px; cursor: pointer"
                :src="row.productPic ? $picUrl + row.productPic + '?imageMogr2/format/webp/interlace/1/quality/100/interlace/0/thumbnail/200x' : ''"
                fit="scale-down"
                preview-teleported
                @click="showViewer([$picUrl + row.productPic])"
              >
                <template #error>
                  <img :src="$picUrl + 'static/assets/no-img.png'" alt="" style="width: 100%; height: 100%" />
                </template>
              </el-image>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="msg" label="产品信息" minWidth="250">
          <template v-slot="{ row }">
            <div style="text-align: left">
              <div>视频编码:{{ row.videoCode }}</div>
              <div>中文名称:{{ row.productChinese }}</div>
              <div>英文名称:{{ row.productEnglish }}</div>
              <div class="one-ell productLink">
                产品链接:
                <el-link :underline="false" target="_blank" type="primary" :href="row.productLink">
                  {{ row.productLink }}
                </el-link>
              </div>
              <div class="flex-start">
                <biz-model-platform :value="row.platform" />
                <biz-nation :value="row.shootingCountry" />
                <biz-model-type :value="row.modelType" />
                <template v-for="op in videoFormatOptions" :key="op.value">
                  <el-tag class="tag" v-if="op.value == row.videoFormat" type="warning" size="small" round>
                    {{ op.label }}
                  </el-tag>
                </template>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="picCount" label="照片数量" align="center" width="110">
          <template v-slot="scope">
            {{ handleSelectiveAssembly(scope.row.picCount) }}
          </template>
        </el-table-column>
        <el-table-column prop="user" label="付款账户" align="center" width="150">
          <template #default>
            <div>
              {{ data.payAccount || '' }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="amount" label="订单金额" align="center" width="110">
          <template #default="{ row }">
            <span v-if="row.amount != null">￥{{ row.amount }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="payAmount" label="支付情况" align="center" width="200">
          <template #default>
            <div v-if="data.surplusAmount != null">剩余支付：￥{{ data.surplusAmount }}</div>
            <div v-if="data.useBalance">钱包余额抵扣：￥{{ data.useBalance }}</div>
            <div>实际支付：{{ data.realPayAmountCurrency }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="auditStatus" label="审核状态" align="center" width="110">
          <template v-slot="{ row }">
            <div class="flex-center">
              <el-tag v-if="data.auditStatus == 1" type="success">已审核</el-tag>
              <el-tag v-else-if="data.auditStatus == 0">待审核</el-tag>
              <el-tooltip
                v-if="data.auditStatus == 2"
                :content="data.orderRemark"
                placement="top"
                effect="light"
              >
                <el-tag type="danger">
                  <span>有异常</span>
                  <el-icon color="red" style="margin-left: 3px"><WarnTriangleFilled /></el-icon>
                </el-tag>
              </el-tooltip>
              <el-tooltip
                v-if="data.orderRemark && data.auditStatus != 2"
                :content="data.orderRemark"
                placement="top"
                effect="light"
              >
                <el-icon color="red" style="margin-left: 3px"><WarnTriangleFilled /></el-icon>
              </el-tooltip>
            </div>
            <el-tag
              v-if="row.status == orderStatusMap['交易关闭'] && data.auditStatus != 1"
              type="info"
              style="margin-top: 5px"
            >
              已关闭
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" :align="'center'" width="130">
          <template #default>
            <el-button
              v-btn
              v-if="data.auditStatus == 0 && tableData[0].status != orderStatusMap['交易关闭']"
              v-hasPermi="['receivable:approve:video:audit']"
              plain
              type="primary"
              size="small"
              @click="handleAction('审核')"
            >
              去审核
            </el-button>
            <el-button
              v-btn
              v-else-if="data.auditStatus == 2"
              v-hasPermi="['receivable:approve:video:reaudit']"
              plain
              type="primary"
              size="small"
              @click="handleAction('重新审核')"
            >
              重新审核
            </el-button>
            <el-button
              v-btn
              v-else-if="data.auditStatus == 1"
              v-hasPermi="['receivable:approve:video:view']"
              type="primary"
              plain
              size="small"
              @click="handleAction('查看详情')"
            >
              查看详情
            </el-button>

            <!-- <el-button v-btn
              v-if="data.auditStatus == 0"
              v-hasPermi="['receivable:approve:audit']"
              round
              type="primary"
              size="small"
              @click="handleAction('审核')"
            >
              审核
            </el-button>
            <el-button v-btn
              v-else-if="data.auditStatus == 2"
              v-hasPermi="['receivable:approve:audit']"
              round
              type="primary"
              size="small"
              @click="handleAction('重新审核')"
            >
              重新审核
            </el-button> -->
          </template>
        </el-table-column>
      </el-table>
      <div class="flex-end bottom-box" :style="{ display: data.auditUserName ? 'block' : 'none' }">
        审核人:{{ data.auditUserName || '-' }} 审核时间:{{ data.auditTime || '-' }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { payTypeMapNew, videoFormatOptions } from '@/utils/dict'
import { orderStatusMap, picCountOptions, picCountNumOptions } from '@/views/order/list/data.js'
import { useViewer } from '@/hooks/useViewer'

const { showViewer } = useViewer()

const props = defineProps({
  data: {
    type: Object,
    required: true,
  },
})
const emits = defineEmits(['action'])

const tableData = computed(() => {
  return props.data.orderVideoVOS
})

function objectSpanMethod({ row, column, rowIndex, columnIndex }) {
  if (columnIndex > 4) {
    if (rowIndex === 0) {
      return {
        rowspan: tableData.value.length,
        colspan: 1,
      }
    } else {
      return {
        rowspan: 0,
        colspan: 0,
      }
    }
  }
}

function handleSelectiveAssembly(val) {
  let str = picCountNumOptions.find(item => item.value == val)
  return str ? str.label : '-'
}

function handleAction(btn) {
  emits('action', btn, props.data.id)
}
</script>

<style scoped lang="scss">
.order-info-box {
  .head-box {
    font-size: 14px;
    padding: 18px 25px 18px 15px;
    background: #f3f3f6;

    .head-label {
      span {
        font-weight: bold;
      }
    }
  }
  // .el-button {
  // font-weight: bold;
  // }
  .table-box {
    :deep(.el-table) {
      th {
        text-align: center;
      }

      .product-img-box {
        position: relative;

        .top-tag {
          position: absolute;
          top: 2px;
          left: 1px;

          .el-tag + .el-tag {
            margin-left: 5px;
          }
        }
      }
    }
    .productLink {
      position: relative;
      padding-right: 5px;

      :deep(.el-link) {
        display: contents;

        .el-link__inner {
          display: inline;
        }
      }
    }
    .bottom-box {
      background: #fff;
      border: 1px solid var(--el-border-color-lighter);
      border-top: none;
      padding: 10px;
      text-align: end;
      font-size: 14px;
    }
  }
}
</style>
