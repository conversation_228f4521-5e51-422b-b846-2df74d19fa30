<template>
  <template v-if="data">
    <div
      v-if="payType == payTypeMap['银行卡'] || payType == payTypeMap['银行卡+余额']"
      style="text-align: left"
    >
      <div>银行卡号：{{ data.bankAccount || ' - ' }}</div>
      <div>姓名：{{ data.accountName || ' - ' }}</div>
      <div>开户行：{{ data.bankName || ' - ' }}</div>
    </div>
    <div
      v-else-if="payType == payTypeMap['对公'] || payType == payTypeMap['对公+余额']"
      style="text-align: left"
    >
      <div>收款公司名称：{{ data.accountName || ' - ' }}</div>
      <div>收款银行账号：{{ data.bankAccount || ' - ' }}</div>
      <div>开户行名称：{{ data.bankName || ' - ' }}</div>
    </div>
    <div
      v-else-if="payType == payTypeMap['全币种'] || payType == payTypeMap['全币种+余额']"
      style="text-align: left"
    >
      <div>账户名：{{ data.accountName || ' - ' }}</div>
      <div>收款账户类型：{{ data.companyAccountType || ' - ' }}</div>
    </div>
    <div v-else style="text-align: left">
      <div>收款主体：{{ data.accountName || ' - ' }}</div>
      <div>商户号：{{ data.bankAccount || ' - ' }}</div>
    </div>
  </template>
  <div v-else>-</div>
</template>

<script setup>
import { payTypeMap } from '@/utils/dict'

const props = defineProps({
  payType: {
    type: [Number, String],
  },
  data: {
    type: Object,
    default: null,
  },
})
</script>

<style scoped lang="scss"></style>
