<template>
  <div>
    <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="68px" @submit.prevent>
      <el-form-item label="搜索">
        <el-input
          v-model="queryParams.keyword"
          clearable
          style="width: 260px"
          placeholder="请输入商家信息、会员编码或申请人"
        >
          <!-- <template #prepend>
                <el-select v-model="queryParams.select" placeholder="请选择" clearable style="width: 100px">
                  <el-option
                    v-for="item in videoSearchSelectList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </template> -->
        </el-input>
      </el-form-item>
      <el-form-item label="审核状态" prop="auditStatus" v-if="auditStatus === ''">
        <el-select v-model="queryParams.auditStatus" placeholder="请选择" clearable style="width: 180px">
          <el-option
            v-for="item in payAuditStatusList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="钱包充值类型" prop="orderType" label-width="100px">
        <el-select v-model="queryParams.orderType" placeholder="请选择" clearable style="width: 180px">
          <el-option
            v-for="item in walletTopUpType"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="全币种支付类型" prop="payTypeDetails" label-width="110px">
        <el-select
          v-model="queryParams.payTypeDetails"
          placeholder="请选择"
          multiple
          collapse-tags
          collapse-tags-tooltip
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="item in payMoneyTypeAllList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button v-btn type="primary" icon="Search" native-type="submit" @click="onQuery">搜索</el-button>
        <el-button v-btn icon="Refresh" plain @click="resetQuery">重置</el-button>
        <!-- <DownloadBtn
          v-hasPermi="['finance:prepay:export']"
          type="success"
          plain
          icon="Download"
          url="/biz/business/backend/businessBalancePrepay/list/export"
          :params="getParams()"
          fileName="应收审批-钱包充值.xlsx"
        /> -->
      </el-form-item>
    </el-form>
    <!-- <div v-if="auditStatus === ''" style="margin-bottom: 10px">
      待处理提现：{{ curTabListNumber?.preApproveNum || 0 }}条
    </div> -->

    <div style="padding-bottom: 35px">
      <el-table v-loading="tableLoading" :data="tableData" border style="width: 100%">
        <template #empty>
          <el-empty description="暂无数据" :image-size="80"></el-empty>
        </template>
        <el-table-column label="预付审批号" prop="prepayNum" width="220" align="center"></el-table-column>
        <el-table-column label="商家信息" prop="nickName" min-width="260">
          <template v-slot="{ row }">
            <div>{{ row.businessName }}{{ row.memberCode ? `(${row.memberCode})` : '' }}</div>
            <!-- <div>
              {{ row.nickName }}
              <el-tag effect="light" round type="warning" size="small" v-if="row.isProxy == 1">代理</el-tag>
            </div>
            <div>公司名称：{{ row.businessName }}</div>
            <div>注册时间：{{ row.businessRegisterTime }}</div> -->
          </template>
        </el-table-column>
        <el-table-column label="预付金额" prop="amount" align="center" width="180"></el-table-column>
        <el-table-column label="实付人民币" prop="realPayAmount" align="center" width="180">
          <template v-slot="{ row }">
            <div>{{ row.realPayAmount || '-' }}</div>
          </template>
        </el-table-column>
        <el-table-column label="钱包充值类型" prop="orderType" align="center" width="180">
          <template v-slot="{ row }">
            <div>{{ handleWalletType(row.orderType) }}</div>
          </template>
        </el-table-column>
        <el-table-column label="申请人" prop="createBy" align="center" width="180"></el-table-column>
        <el-table-column label="申请时间" prop="creatTime" align="center" width="170"></el-table-column>
        <el-table-column label="状态" prop="auditStatus" align="center" width="200">
          <template v-slot="{ row }">
            {{ payAuditStatusList.find(item => item.value == row.auditStatus)?.label || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="审核人" prop="auditUserName" align="center" width="180">
          <template v-slot="{ row }">
            {{ row.auditUserName || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="付款账号" prop="payAccount" align="center" width="180">
          <template v-slot="{ row }">
            {{ row.payAccount || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="支付时间" prop="payTime" align="center" width="170">
          <template v-slot="{ row }">
            {{ row.payTime || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="支付方式" prop="payType" align="center" width="180">
          <template v-slot="{ row }">
            <div v-if="row.payType == '99'">其他</div>
            <div v-else>{{ payTypeMap[row.payType] || '-' }}</div>
            <el-tag v-if="row.payTypeDetail && (row.payType == 7 || row.payType == 17)" type="warning" round>
              {{ payMoneyTypeMap[row.payTypeDetail] || '-' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="支付币种" prop="currency" align="center" width="180">
          <template v-slot="{ row }">
            <div v-if="row.payType == '7' || row.payType == '17'">
              {{ sys_money_type.find(item => item.value == row.currency)?.label || '-' }}
            </div>
            <div v-else>-</div>
          </template>
        </el-table-column>
        <el-table-column label="实付金额" prop="realPayAmountCurrency" align="center" width="180">
          <template v-slot="{ row }">
            <div v-if="row.payType == '7' || row.payType == '17'">{{ row.realPayAmountCurrency || '-' }}</div>
            <div v-else>-</div>
          </template>
        </el-table-column>
        <el-table-column label="收款账号" prop="account" align="center" width="250">
          <template v-slot="{ row }">
            <PayeeAccountVO :data="row.orderPayeeAccountVO" :payType="row.payType" />
          </template>
        </el-table-column>
        <el-table-column label="审核时间" prop="auditTime" align="center" width="170">
          <template v-slot="{ row }">
            {{ row.auditTime || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="备注" prop="remark" align="center" width="170" show-overflow-tooltip>
          <template v-slot="{ row }">
            {{ row.remark || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" align="center" width="170">
          <template v-slot="{ row }">
            <el-button
              type="primary"
              v-hasPermi="['finance:prepay:detail']"
              v-btn
              size="small"
              plain
              @click="handleOpenDetail(row.id)"
            >
              查看
            </el-button>
            <el-button
              type="success"
              v-hasPermi="['finance:prepay:audit']"
              v-btn
              size="small"
              plain
              @click="handleOpenDialog(row.id, row.amount, row.businessId)"
              v-if="row.auditStatus == '0'"
            >
              审核
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="flex-end" style="margin-top: 12px">
      <PaginationFloatBar :current-page="pageNum" :page-size="pageSize" @update:current-page="handlePageChange" :total="total">
        <DownloadBtn
          v-hasPermi="['finance:prepay:export']"
          type="success"
          plain
          icon="Download"
          url="/biz/business/backend/businessBalancePrepay/list/export"
          :params="getParams()"
          fileName="应收审批-钱包充值.xlsx"
        />
      </PaginationFloatBar>
      <!-- <el-pagination
        background
        @size-change="pageChange({ pageNum: 1, pageSize: $event })"
        @current-change="pageChange({ pageNum: $event, pageSize })"
        :current-page="pageNum"
        :page-size="pageSize"
        :page-sizes="pageSizes"
        layout="total, prev, pager, next, jumper"
        :total="total"
      /> -->
    </div>
    <AdvanceDialog ref="AdvanceDialogRef" @success="init" />
    <RecordDialog ref="RecordDialogRef" />
  </div>
</template>

<script setup>
import DownloadBtn from '@/components/Button/DownloadBtn.vue'
import AdvanceDialog from '@/views/finance/receivableApprove/dialog/advanceDialog.vue'
import RecordDialog from '@/views/finance/receivableApprove/dialog/recordDialog.vue'
import PayeeAccountVO from '@/views/finance/receivableApprove/components/payeeAccountVO.vue'
import { prepayAuditList, prepayAuditStatistics } from '@/api/finance/receivable'
import { payAuditStatusList, walletTopUpType, payMoneyTypeAllList, payMoneyTypeMap } from '../../data'
import { payTypeMap } from '@/utils/dict'
import { useTabList } from '@/views/finance/receivableApprove/hooks'

const { curTabListNumber, auditStatus } = useTabList()
const { proxy } = getCurrentInstance()
const { sys_money_type } = proxy.useDict('sys_money_type')

const route = useRoute()

const props = defineProps({
  tab: {
    type: [Number, String],
  },
  auditStatus: {
    type: [Number, String],
    default: '',
  },
})

const emits = defineEmits(['action'])

defineExpose({
  handleQuery,
  init,
  clearResetQuery,
})

const loading = ref(false)
const AdvanceDialogRef = ref(null)
const RecordDialogRef = ref(null)

const queryParams = ref({
  keyword: '',
  auditStatus: '',
  orderType: '',
  payTypeDetails: [],
})

function resetQuery() {
  queryParams.value = {
    keyword: '',
    auditStatus: '',
    orderType: '',
    payTypeDetails: [],
  }
  handleQuery()
}
function clearResetQuery() {
  queryParams.value = {
    keyword: '',
    auditStatus: null,
    orderType: '',
    payTypeDetails: [],
  }
}
const bizMerchantOptions = ref([])

const tableData = ref([])
const pageNum = ref(1)
const pageSize = ref(20)
const pageSizes = [10, 20, 30, 50]
const total = ref(0)
const tableLoading = ref(false)

function onQuery() {
  pageNum.value = 1
  handleQuery()
}
function handleQuery() {
  tableLoading.value = true
  prepayAuditList({
    pageNum: pageNum.value,
    pageSize: pageSize.value,
    ...getParams(),
  })
    .then(res => {
      tableData.value = res.data.rows
      total.value = res.data.total
    })
    .finally(() => {
      tableLoading.value = false
    })
}
function getParams() {
  let params = queryParams.value
  if (auditStatus.value === '') {
    // console.log(queryParams.value.auditStatus, 444)
    params.auditStatus = queryParams.value.auditStatus
    // params.auditStatus = null
  } else {
    params.auditStatus = auditStatus.value
  }
  return params
}

function pageChange(page) {
  pageNum.value = page.pageNum
  pageSize.value = page.pageSize
  handleQuery()
}
function handlePageChange(num) {
  pageNum.value = num
  handleQuery()
}
//获取统计
function getStatistics() {
  prepayAuditStatistics().then(res => {
    curTabListNumber.value = res.data
  })
}

// 获取下单用户列表
function getBizMerchantList() {
  businessAccountList(getParams).then(res => {
    bizMerchantOptions.value = res.data.rows
  })
}

if (route.query && route.query.orderNum) {
  queryParams.value.select = 'orderNum'
  queryParams.value.val = route.query.orderNum
}

function handleWalletType(type) {
  let item = walletTopUpType.find(item => item.value == type)
  return item ? item.label : '-'
}

//打开审核弹窗
function handleOpenDialog(id, amount, businessId) {
  AdvanceDialogRef.value.open(id, amount, businessId)
}

//打开详情弹窗
function handleOpenDetail(id) {
  RecordDialogRef.value.open(id)
}

function init() {
  handleQuery()
  getStatistics()
}
// handleQuery()
// getBizMerchantList()
</script>

<style scoped lang="scss"></style>
