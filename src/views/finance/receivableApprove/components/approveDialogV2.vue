<template>
  <div>
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="1100"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      align-center
      @close="handleClose"
    >
      <div style="margin-bottom: 20px">
        <Title>付款信息</Title>
        <el-row>
          <el-col :span="5">
            <div class="label">付款账号：</div>
            <div
              class="content"
              v-if="
                detailsData.payType == payTypeMap['对公'] || detailsData.payType == payTypeMap['对公+余额']
              "
            >
              {{ detailsData.payAccount || '-' }}
            </div>
            <div v-else class="content">{{ detailsData.payAccount || '-' }}</div>
          </el-col>
          <el-col :span="7">
            <div class="label">收款账号：</div>
            <div class="content">
              <PayeeAccountVO :data="detailsData.orderPayeeAccountVO" :payType="detailsData.payType" />
            </div>
          </el-col>
          <el-col :span="5">
            <div class="label">{{ dialogType == 'video' ? '订单金额：' : '套餐金额：' }}</div>
            <div class="content">
              <div v-if="curType == 1">美元：${{ detailsData?.payAmountDollar || '-' }}</div>
              <div v-if="curType == 2">美元：${{ detailsData?.packageAmount || '-' }}</div>
              <div>百度汇率：{{ detailsData.currentExchangeRate || '-' }}</div>
              <div v-if="curType == 1">人民币：￥{{ detailsData?.payAmount || '-' }}</div>
              <div v-if="curType == 2">人民币：￥{{ detailsData?.orderAmount || '-' }}</div>
            </div>
          </el-col>
          <el-col :span="7">
            <div div class="label">优惠信息：</div>
            <div class="content">
              <template
                v-if="detailsData.orderDiscountDetailVOS && detailsData.orderDiscountDetailVOS.length > 0"
              >
                <div>
                  优惠类型：{{
                    disountTypeList.find(item => item.value == detailsData.orderDiscountDetailVOS[0].type)
                      ?.label
                  }}
                </div>
                <div v-if="detailsData.orderDiscountDetailVOS[0].type == 3">
                  {{ detailsData.orderDiscountDetailVOS[0].channelType == 7 ? '裂变' : '渠道' }}名称：{{
                    detailsData.orderDiscountDetailVOS[0].channelName
                  }}{{ detailsData.seedId ? ` (ID${detailsData.seedId})` : '' }}
                </div>
                <div>
                  会员折扣：{{ detailsData.orderDiscountDetailVOS[0].discountRatio
                  }}{{
                    detailsData.orderDiscountDetailVOS[0].discountType == 1 ? '元 (固定金额)' : '% (固定比例)'
                  }}
                </div>
                <div>优惠金额：￥{{ detailsData.orderDiscountDetailVOS[0].discountAmount }}</div>
              </template>
              <span v-else>-</span>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <div div class="label">支付情况：</div>
            <div class="content">
              <div v-if="detailsData.useBalance">钱包余额抵扣：￥{{ detailsData.useBalance }}</div>
              <div>剩余支付：￥{{ detailsData.surplusAmount || '-' }}</div>
            </div>
          </el-col>
          <!-- <el-col :span="6" v-if="curType != 1">
            <div div class="label">种草优惠：</div>
            <div class="content">
              <div v-if="detailsData.seedCodeDiscount" style="text-align: left">
                <div>
                  {{ detailsData.channelType == 7 ? '裂变' : '渠道' }}名称：{{ detailsData.channelName }}
                </div>
                <div>会员折扣：{{ detailsData.settleRage }}%</div>
                <div>优惠金额：￥{{ detailsData.seedCodeDiscount }}</div>
              </div>
              <div v-else>-</div>
            </div>
          </el-col> -->
        </el-row>
      </div>
      <div class="flex-start audit-box" v-loading="loading">
        <div class="image-box">
          <div class="inner">
            <el-empty
              v-if="!detailsData.resourceVos.length"
              class="img-content"
              description="无"
              :image-size="80"
            ></el-empty>
            <el-carousel
              v-else
              class="img-content"
              indicator-position="none"
              :autoplay="false"
              height="90%"
              @change="handleCarouselChange"
            >
              <el-carousel-item v-for="(url, index) in detailsData.resourceVos" :key="url">
                <el-tag class="tag" type="info" effect="light" round>
                  {{ url.uploadType == 1 ? '平台上传' : '商家上传' }}
                </el-tag>
                <el-image
                  style="width: 100%; height: 100%; cursor: pointer"
                  :src="$picUrl + url.picUrl + '!fullSizecompress'"
                  fit="fill"
                  @click="openViewer(index)"
                />
              </el-carousel-item>
            </el-carousel>
          </div>
          <div
            v-if="detailsData.resourceVos.length"
            style="margin-top: -35px; text-align: center; font-size: 18px"
          >
            {{ curPicIndex }} / {{ detailsData.resourceVos.length }}
          </div>

          <el-button
            v-btn
            class="btn"
            type="primary"
            size="small"
            :disabled="loading || disabled"
            @click="add()"
          >
            添加凭证
          </el-button>
        </div>
        <div>
          <el-form ref="formRef" :model="form" :rules="rules" :disabled="disabled" label-width="140px">
            <el-form-item label="支付方式:" prop="payType">
              <el-select
                v-model="form.payType"
                style="width: 260px"
                placeholder="请选择支付方式"
                @change="changePayType"
              >
                <el-option
                  v-for="(item, index) in payTypeAllList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="全币种支付类型:" prop="payTypeDetail" v-if="form.payType == 7">
              <el-select v-model="form.payTypeDetail" style="width: 260px" placeholder="请选择全币种支付类型">
                <el-option
                  v-for="(item, index) in payMoneyTypeAllList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="支付主体:" prop="payeeId">
              <el-select v-model="form.payeeId" style="width: 260px" placeholder="请选择支付主体">
                <el-option
                  v-for="(item, index) in payMainList"
                  :key="item.detailId"
                  :label="item.accountName"
                  :value="item.detailId"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="支付币种:" prop="currency">
              <el-select
                v-model="form.currency"
                style="width: 260px"
                placeholder="请选择币种"
                :disabled="currencyDisable"
              >
                <template v-if="form.payType != 7">
                  <el-option :key="1" :label="'人民币'" :value="1"></el-option>
                </template>
                <template v-else>
                  <el-option
                    v-for="(item, index) in sys_money_type"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </template>
                <!-- <el-option
                    v-for="(item, index) in sys_money_type"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                  <el-option :key="1" :label="'人民币'" :value="1"></el-option> -->
              </el-select>
            </el-form-item>
            <el-form-item label="实付金额:" prop="amount">
              <div class="flex-start">
                <el-input-number
                  title=""
                  v-model="form.amount"
                  placeholder="请输入凭证上的实付金额"
                  controls-position="right"
                  :precision="2"
                  :step="0.1"
                  :max="999999"
                  :min="0"
                  clearable
                  style="width: 260px"
                  @keydown="channelInputLimit"
                />
                <!-- <span style="margin-left: 10px">
                    差价：
                    <span style="color: red">{{ priceDiff }}</span>
                  </span> -->
              </div>
            </el-form-item>
            <el-form-item label="实付差额:">
              <span :style="{ color: priceDiff < 0 ? '#F59A23' : priceDiff == 0 ? 'black' : 'green' }">
                {{ priceDiff > 0 ? `+${priceDiff}` : priceDiff }}元
              </span>
            </el-form-item>
            <el-form-item label="实付人民币:" prop="currencyAmount" v-if="form.payType == 7">
              <div class="flex-start">
                <el-input-number
                  title=""
                  v-model="form.currencyAmount"
                  placeholder="请输入实付人民币"
                  controls-position="right"
                  :precision="2"
                  :step="0.1"
                  :max="999999"
                  :min="0"
                  clearable
                  style="width: 260px"
                  @keydown="channelInputLimit"
                />
              </div>
            </el-form-item>
            <el-form-item label="支付时间:" prop="payTime">
              <el-date-picker
                v-model="form.payTime"
                clearable
                type="datetime"
                format="YYYY/M/D HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                placeholder="请选择支付时间"
                style="width: 260px"
              />
            </el-form-item>
            <el-form-item label="备注:" prop="remark">
              <el-input
                v-model="form.remark"
                style="width: 260px"
                :autosize="{ minRows: 2, maxRows: 4 }"
                type="textarea"
                placeholder=""
              />
            </el-form-item>
            <!-- <el-form-item style="margin-top: 50px">
                <el-button v-btn round plain @click="onSubmit(0)">标记异常</el-button>
                <el-button v-btn type="primary" round @click="onSubmit(1)">审核通过</el-button>
              </el-form-item> -->
          </el-form>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button v-btn plain @click="onSubmit(0)">标记异常</el-button>
          <el-button v-btn type="primary" @click="onSubmit(1)">审核通过</el-button>
        </div>
      </template>
    </el-dialog>
    <DragUploadDialog
      ref="DragUploadDialogRef"
      title="上传凭证"
      :before-success="uploadSuccess"
      :bucketType="'credit'"
    />
    <el-dialog
      v-model="dialogAuditVisible"
      title="审批确认"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      align-center
    >
      <div class="flex-start" style="padding: 0 10px">
        <el-icon size="30" color="#F59A23"><Warning /></el-icon>
        <div style="font-size: 16px; font-weight: bold; margin-left: 10px">产生差额，是否确认审批通过</div>
      </div>
      <div class="table-box">
        <table>
          <tr>
            <th>应付金额</th>
            <th>{{ form.payType == 7 ? '实付人民币' : '实付金额' }}</th>
            <th>差额</th>
          </tr>
          <tr>
            <td>{{ detailsData.surplusAmount }}元</td>
            <td>{{ form.payType == 7 ? form.currencyAmount : form.amount }}元</td>
            <td>
              <span :style="{ color: priceDiff < 0 ? '#F59A23' : priceDiff == 0 ? 'black' : 'green' }">
                {{ priceDiff > 0 ? `+${priceDiff}` : priceDiff }}元
              </span>
            </td>
          </tr>
        </table>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button v-btn plain @click="dialogAuditVisible = false">取消</el-button>
          <el-button v-btn type="primary" @click="dialogAuditConfirm()">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import PayeeAccountVO from '@/views/finance/receivableApprove/components/payeeAccountVO.vue'
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'
import { receivableAuditDetail, receivableAuditOrder } from '@/api/finance/receivable'
import { getSubjectList } from '@/api/system/collection'
import { useViewer } from '@/hooks/useViewer'
import { payTypeAllList, setMealTypeList, payMoneyTypeAllList } from '@/views/finance/data'
import Title from '@/components/Public/title.vue'
import { payTypeMap } from '@/utils/dict'
import { disountTypeList } from '@/views/order/vip/data.js'
// sys_money_type

const { proxy } = getCurrentInstance()
const { showViewer } = useViewer()
const { sys_money_type } = proxy.useDict('sys_money_type')

const payMainList = ref([])

const dialogVisible = ref(false)
const dialogTitle = ref('审核订单')
const isDetail = ref(false)
const form = ref({
  payType: '',
  payeeId: '',
  currency: '',
  orderAmount: 0,
  amount: 0,
  currencyAmount: 0,
  payTime: '',
  remark: '',
  payTypeDetail: '',
})
const formRef = ref()
const rules = {
  payType: [{ required: true, message: '请选择支付方式', trigger: 'change' }],
  payeeId: [{ required: true, message: '请选择支付主体', trigger: 'change' }],
  currency: [{ required: true, message: '请选择支付币种', trigger: 'change' }],
  amount: [
    { required: true, message: '请输入实付金额', trigger: 'blur' },
    { validator: checkNumber, trigger: 'change' },
  ],
  currencyAmount: [{ required: true, message: '请输入实付人民币', trigger: 'blur' }],
  payTime: [{ required: true, message: '请选择支付时间', trigger: 'change' }],
  remark: [{ required: true, message: '请输入备注', trigger: 'blur' }],
  payTypeDetail: [{ required: true, message: '请选择全币种支付类型', trigger: 'change' }],
}

function checkNumber(rule, value, callback) {
  if (isNaN(value)) {
    return callback(new Error('请输入数字'))
  }
  return callback()
}

const detailsData = ref({
  resourceVos: [],
})
const UpResourceVos = ref([])
const loading = ref(false)
const disabled = ref(false)
const disabled_audit = ref(false)
const priceDiff = computed(() => {
  let num = 0
  if (!isNaN(form.value.amount) && form.value.payType != 7) {
    num = (form.value.amount * 100 - detailsData.value.surplusAmount * 100) / 100
  } else if (!isNaN(form.value.currencyAmount) && form.value.payType == 7) {
    num = (form.value.currencyAmount * 100 - detailsData.value.surplusAmount * 100) / 100
  }
  return num.toFixed(2)
})

const orderId = ref(0)

const DragUploadDialogRef = ref()

const emits = defineEmits(['success'])
defineExpose({
  open,
  close,
})

const curType = ref(1)
const dialogType = ref('')
function open(id, btn, type, dialogTypeString) {
  curType.value = type * 1
  dialogType.value = dialogTypeString
  dialogVisible.value = true
  if (btn && btn == '查看详情') {
    dialogTitle.value = '审核详情'
    disabled.value = true
  } else {
    dialogTitle.value = '审核订单'
    disabled.value = false
  }
  if (id) {
    orderId.value = id
    getDetails()
  }
}

function close() {
  dialogVisible.value = false
}
function getType(val, key) {
  let type = setMealTypeList.find(item => item.value === val)
  if (type) {
    return type[key]
  }
  return ''
}

function handleClose() {
  UpResourceVos.value = []
  form.value = {
    payType: '',
    payeeId: '',
    currency: '',
    orderAmount: 0,
    amount: 0,
    currencyAmount: 0,
    payTime: '',
    remark: '',
    payTypeDetail: '',
  }
  payMainList.value = []
  dialogType.value = ''
  currencyDisable.value = false
  formRef.value?.resetFields()
}

const curPicIndex = ref(1)
function handleCarouselChange(index) {
  curPicIndex.value = index + 1
}

function openViewer(index) {
  let urls = detailsData.value.resourceVos.map(item => item.picUrl)
  showViewer(urls, { index, scale: 'fullSize' })
}

function getDetails() {
  loading.value = true
  receivableAuditDetail(orderId.value)
    .then(res => {
      detailsData.value = res.data
      // form.value.payType = res.data.payType || ''
      const temp = payTypeAllList.find(item => item.value == res.data.payType)
      temp ? (form.value.payType = res.data.payType) : ''
      if (form.value.payType != 7 && res.data.payType && temp) {
        currencyDisable.value = true
        form.value.currency = 1
      }
      if (res.data.payType == 16) {
        currencyDisable.value = true
        form.value.currency = 1
        form.value.payType = 6
      }
      if (res.data.payType == 17) {
        form.value.payType = 7
      }
      if (res.data.payAmount) {
        form.value.orderAmount = res.data.payAmount
      }
      if (!res.data.resourceVos) {
        detailsData.value.resourceVos = []
      } else {
        detailsData.value.resourceVos.forEach(item => {
          item.picUrl = item.objectKey
        })
      }
      if (form.value.payType) {
        getPayMainList(form.value.payType)
      }
      if (res.data.payTypeDetail) {
        form.value.payTypeDetail = res.data.payTypeDetail
      }
      if (disabled.value) {
        form.value.amount = res.data.realPayAmount
        form.value.payTime = res.data.payTime
        form.value.remark = res.data.orderRemark
      }
    })
    .finally(() => (loading.value = false))
}

function getPayMainList(type) {
  getSubjectList(type).then(res => {
    payMainList.value = res.data || []
    form.value.payeeId = payMainList.value.find(item => item.status == 1)?.detailId || ''
  })
}

const currencyDisable = ref(false)
function changePayType(val) {
  form.value.currency = ''
  if (val == 7) {
    currencyDisable.value = false
  } else {
    currencyDisable.value = true
    form.value.currency = 1
  }
  getPayMainList(val)
}

function add() {
  DragUploadDialogRef.value.open()
}

function uploadSuccess(data, close) {
  detailsData.value.resourceVos.push(
    ...data.map(item => {
      // UpResourceVos.value.push(item.id)
      UpResourceVos.value.push(item.picUrl)
      return {
        ...item,
        uploadType: 1,
      }
    })
  )
  close()
}

function onSubmit(n) {
  let msg = ''
  let auditStatus = 1
  if (n) {
    msg = '确认通过？'
    auditStatus = 1
    formRef.value.validate(valid => {
      if (valid) {
        // if (form.value.amount != detailsData.value.surplusAmount && form.value.currency == 1) {
        if (priceDiff.value != 0) {
          auditHintFn(auditStatus)
        } else {
          auditFn(msg, auditStatus)
        }
      }
    })
  } else {
    msg = '确认将订单标记为异常？'
    auditStatus = 2
    auditFn(msg, auditStatus)
  }
}

function auditLoadingFn(auditStatus) {
  const el_loading = ElLoading.service({
    lock: true,
    text: '正在执行中，请稍后',
    background: 'rgba(0, 0, 0, 0.7)',
  })
  let params = {
    id: orderId.value,
    auditStatus,
  }
  if (auditStatus === 1) {
    params.orderRemark = form.value.remark
    params.payTime = form.value.payTime
    params.payeeId = form.value.payeeId
    params.realPayAmount = form.value.amount
    params.currency = form.value.currency
    params.payType = form.value.payType
    if (form.value.payType == 7) {
      params.realPayAmount = form.value.currencyAmount
      params.realPayAmountCurrency = form.value.amount
      params.payTypeDetail = form.value.payTypeDetail
    } else {
    }
  }
  if (auditStatus === 2) {
    params.orderRemark = form.value.remark
  }
  // params.resourceIds = UpResourceVos.value
  params.objectKeys = UpResourceVos.value
  // objectKeys
  receivableAuditOrder(params)
    .then(res => {
      ElMessage.success('操作成功')
      emits('success')
      close()
    })
    .finally(() => el_loading.close())
}

function auditFn(msg, auditStatus) {
  ElMessageBox.confirm(msg, '审核提示', {
    autofocus: false,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
  })
    .then(() => {
      auditLoadingFn(auditStatus)
    })
    .catch(() => {})
}

//有差额时的二次弹窗
const dialogAuditVisible = ref(false)

function auditHintFn(auditStatus) {
  if (priceDiff.value != 0) {
    dialogAuditVisible.value = true
  }
  // proxy.$modal
  //   .customConfirm(
  //     `审核订单存在差额<br/><span>当前订单剩余支付：￥${detailsData.value.surplusAmount}，实际支付：￥${form.value.amount}，存在差额，确定审核通过吗？</span>`,
  //     '差额提醒'
  //   )
  //   .then(() => {
  //     auditLoadingFn(auditStatus)
  //   })
  //   .catch(() => {})
}

function dialogAuditConfirm() {
  dialogAuditVisible.value = false
  auditLoadingFn(1)
}

const channelInputLimit = e => {
  const key = e.key
  if (key === 'Enter') e.preventDefault()
  const notAllowList = ['e', '+', '-']
  if (notAllowList.includes(key)) {
    e.returnValue = false
    return false
  }
  return true
}
</script>

<style scoped lang="scss">
.table-box {
  padding: 10px 50px;
  table {
    border-collapse: collapse; /* 边框合并，避免重叠 */
    width: 100%; /* 表格宽度占满容器 */
  }
  th,
  td {
    border: 1px solid black; /* 边框线变粗，颜色为黑色 */
    padding: 10px 0; /* 单元格内边距为0 */
    text-align: center; /* 文字居中 */
  }
}

.audit-box {
  align-items: flex-start;
  gap: 20px;
}

.image-box {
  position: relative;
  width: 300px;
  margin-bottom: 15px;

  .inner {
    width: 100%;
    padding-top: 130%;
    position: relative;

    .img-content {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }
  }

  .tag {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 2;
  }

  .btn {
    position: absolute;
    left: 50%;
    transform: translate(-50%, 0);
    // bottom: -30px;
  }
}
.el-col-6,
.el-col-5,
.el-col-7 {
  display: flex;
  margin-bottom: 10px;
  font-size: 13px;
  align-items: baseline;
  .label {
    flex-shrink: 0;
    width: 80px;
    color: #999;
    text-align: end;
  }
  .content {
    font-weight: 600;
    flex-shrink: 0;
    width: calc(100% - 85px);
    word-break: break-all;
    line-break: anywhere;
  }
}
</style>
