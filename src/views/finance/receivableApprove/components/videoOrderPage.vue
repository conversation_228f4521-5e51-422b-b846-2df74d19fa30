<template>
  <div>
    <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="68px" @submit.prevent>
      <el-form-item label="搜索">
        <el-input
          v-model="queryParams.searchName"
          clearable
          style="width: 260px"
          placeholder="请输入公司名称/商家编码/下单运营"
        >
          <!-- <template #prepend>
              <el-select v-model="queryParams.select" placeholder="请选择" clearable style="width: 100px">
                <el-option
                  v-for="item in videoSearchSelectList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </template> -->
        </el-input>
      </el-form-item>
      <!-- <el-form-item label="平台" prop="platform">
        <el-select
          v-model="queryParams.platform"
          placeholder="请选择"
          multiple
          collapse-tags
          collapse-tags-tooltip
          clearable
          style="width: 180px"
        >
          <el-option
            v-for="item in biz_model_platform"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item> -->
      <!-- <el-form-item label="下单运营" prop="orderUserId">
        <el-select
          v-model="queryParams.orderUserId"
          placeholder="请选择"
          multiple
          filterable
          collapse-tags
          collapse-tags-tooltip
          clearable
          style="width: 180px"
        >
          <el-option
            v-for="item in bizMerchantOptions"
            :key="item.id"
            :label="item.nickName"
            :value="item.id"
          />
        </el-select>
      </el-form-item> -->
      <el-form-item label="审核状态" prop="auditStatusList" v-if="auditStatus === ''">
        <el-select
          v-model="queryParams.auditStatusList"
          placeholder="请选择"
          multiple
          collapse-tags
          collapse-tags-tooltip
          clearable
          style="width: 180px"
        >
          <el-option
            v-for="item in auditStatusList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="支付方式" prop="payType">
        <el-select
          v-model="queryParams.payType"
          placeholder="请选择"
          multiple
          collapse-tags
          collapse-tags-tooltip
          clearable
          style="width: 180px"
        >
          <el-option v-for="item in payTypeList" :key="item.value" :label="item.short" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="全币种支付类型" prop="payTypeDetails" label-width="110px">
        <el-select
          v-model="queryParams.payTypeDetails"
          placeholder="请选择"
          multiple
          collapse-tags
          collapse-tags-tooltip
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="item in payMoneyTypeAllList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="下单时间" style="width: 450px">
        <el-date-picker
          popper-class="picker-box"
          v-model="queryParams.placeOrderTime"
          format="YYYY/M/D HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="datetimerange"
          range-separator="-"
          unlink-panels
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
          :shortcuts="shortcuts"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button v-btn type="primary" icon="Search" native-type="submit" @click="onQuery">搜索</el-button>
        <el-button v-btn icon="Refresh" plain @click="resetQuery">重置</el-button>
        <!-- <DownloadBtn
          v-hasPermi="['receivable:approve:video:export']"
          type="success"
          plain
          icon="Download"
          url="/order/finance/receivableAuditList/export"
          :params="getParams()"
          fileName="应收审批-视频订单.xlsx"
        /> -->
      </el-form-item>
    </el-form>

    <!-- 大订单+小订单列表 -->
    <!-- <div v-loading="tableLoading">
      <el-empty v-if="!tableData.length" description="暂无数据" :image-size="80"></el-empty>
      <template v-for="(item, i) in tableData" :key="item.orderNum">
        <OrderListItem v-if="defer(i)" :data="item" @action="handleButtonAction"/>
      </template>
    </div>

    <div class="flex-end" style="margin-top: 12px">
      <el-pagination
        background
        @size-change="pageChange({ currentPage: 1, pageSize: $event })"
        @current-change="pageChange({ currentPage: $event, pageSize })"
        :current-page="pageNum"
        :page-size="pageSize"
        :page-sizes="pageSizes"
        layout="total, prev, pager, next, jumper"
        :total="total"
      />
    </div> -->

    <!-- 大订单列表 -->
    <ElTablePage
      ref="tableRef"
      :columns="columns"
      :data="tableData"
      :loading="tableLoading"
      :currentPage="pageNum"
      :pageSize="pageSize"
      :total="total"
      :tableAction="{
        width: '170',
        fixed: 'right',
      }"
      :tableOptions="{
        border: true,
      }"
      @page-change="pageChange"
    >
      <template #info="{ row }">
        <div style="text-align: left">
          <template v-if="row.isMergeOrder">
            <div v-for="(item, index) in row.orderNumGroup" :key="item + index">
              {{ index == 0 ? '订单号：' : '' }}{{ item }}
            </div>
            <div>支付号：{{ row.payNum || '-' }}</div>
            <div>合并时间：{{ row.mergeTime }}</div>
          </template>
          <template v-else>
            <div>订单号：{{ row.orderNum }}</div>
            <div>支付号：{{ row.payNum || '-' }}</div>
            <div>下单时间：{{ row.orderTime }}</div>
          </template>

          <div v-if="row.submitCredentialTime">提交审批：{{ row.submitCredentialTime }}</div>
        </div>
      </template>
      <template #payType="{ row }">
        <div>
          <div>{{ payTypeMapNew[row.payType] }}</div>
          <el-tag v-if="row.payTypeDetail && (row.payType == 7 || row.payType == 17)" type="warning" round>
            {{ payMoneyTypeMap[row.payTypeDetail] || '-' }}
          </el-tag>
        </div>
      </template>
      <template #orderUser="{ row }">
        <div style="text-align: left">
          <div>姓名：{{ row.orderUserName || '-' }}</div>
          <div>微信名：{{ row.orderUserNickName || '-' }}</div>
        </div>
      </template>
      <template #amount="{ row }">
        <div style="text-align: left">
          <template v-if="row.isMergeOrder">
            <div>合计美元：${{ row.mergeOrderAmountDollar }}</div>
            <div>合计人民币：￥{{ row.mergeOrderAmount }}</div>
          </template>
          <template v-else>
            <div>美元：${{ row.orderAmountDollar }}</div>
            <div>百度汇率：{{ row.currentExchangeRate || '-' }}</div>
            <div>人民币：￥{{ row.orderAmount }}</div>
          </template>
        </div>
      </template>
      <template #account="{ row }">
        <PayeeAccountVO :data="row.orderPayeeAccountVO" :payType="row.payType" />
      </template>
      <template #pay="{ row }">
        <div style="text-align: left">
          <div v-if="row.mergeOrderPromotionAmount">优惠金额：￥{{ row.mergeOrderPromotionAmount }}</div>
          <div v-else-if="row.orderPromotionAmount">优惠金额：￥{{ row.orderPromotionAmount }}</div>
          <div v-if="row.useBalance || row.mergeUseBalance">
            使用余额：￥{{ row.isMergeOrder ? row.mergeUseBalance : row.useBalance }}
          </div>

          <div>剩余支付：￥{{ row.isMergeOrder ? row.mergeSurplusAmount : row.surplusAmount }}</div>
          <div>
            实付金额：
            <span v-if="row.auditStatus == 0 || row.auditStatus == 2 || row.auditStatus == 3">-</span>
            <span v-else>
              <template
                v-if="
                  row.realPayAmountCurrency == 0 ||
                  row.realPayAmountCurrency ||
                  row.mergeRealPayAmountCurrency == 0 ||
                  row.mergeRealPayAmountCurrency
                "
              >
                {{ row.isMergeOrder ? row.mergeRealPayAmountCurrency : row.realPayAmountCurrency }}
              </template>
              <template v-else>-</template>
            </span>
          </div>
          <div>支付币种：{{ handleCurrency(row.currency) }}</div>
        </div>
      </template>
      <template #auditStatus="{ row }">
        <span v-if="row.auditStatus == 1">已审核</span>
        <span v-else-if="row.auditStatus == 0">待审核</span>
        <span v-else-if="row.auditStatus == 2">有异常</span>
        <span v-else-if="row.auditStatus == 3">已关闭</span>
        <!-- <span v-if="row.status == orderStatusMap['交易关闭'] && row.auditStatus != 1">已关闭</span> -->
      </template>
      <template #tableAction="{ row }">
        <el-button
          v-btn
          v-hasPermi="['receivable:approve:video:view']"
          type="primary"
          plain
          size="small"
          @click="toDetail(row)"
        >
          查看
        </el-button>
        <el-button
          v-btn
          v-if="row.auditStatus == 0"
          v-hasPermi="['receivable:approve:video:audit']"
          plain
          type="success"
          size="small"
          @click="handleButtonAction('审核', row.id, 'video')"
        >
          审核
        </el-button>
        <el-button
          v-btn
          v-if="row.auditStatus == 2"
          v-hasPermi="['receivable:approve:video:reaudit']"
          plain
          type="warning"
          size="small"
          @click="handleButtonAction('重新审核', row.id, 'video')"
        >
          重新审核
        </el-button>
      </template>
      <template #pageLeft>
        <DownloadBtn
          v-hasPermi="['receivable:approve:video:export']"
          type="success"
          plain
          icon="Download"
          url="/order/finance/receivableAuditList/export"
          :params="getParams()"
          fileName="应收审批-视频订单.xlsx"
        />
      </template>
    </ElTablePage>
  </div>
</template>

<script setup>
import ElTablePage from '@/components/Table/ElTablePage'
// import OrderListItem from '@/views/finance/receivableApprove/components/orderListItem.vue'
import DownloadBtn from '@/components/Button/DownloadBtn.vue'
import PayeeAccountVO from '@/views/finance/receivableApprove/components/payeeAccountVO.vue'
import { businessAccountList, receivableAuditList, receivableStatistics } from '@/api/finance/receivable'
import { auditStatusList, payTypeList, payMoneyTypeAllList, payMoneyTypeMap } from '../../data'
import { orderStatusMap } from '@/views/order/list/data.js'
import { payTypeMapNew } from '@/utils/dict'
import { useTabList } from '@/views/finance/receivableApprove/hooks'
const { auditStatus, curTabListNumber } = useTabList()

const route = useRoute()
const router = useRouter()

const { proxy } = getCurrentInstance()
const { sys_money_type } = proxy.useDict('sys_money_type')

const props = defineProps({
  tab: {
    type: [Number, String],
  },
})

const emits = defineEmits(['action'])

defineExpose({
  handleQuery,
  init,
})

const queryParams = ref({
  searchName: '',
  val: '',
  select: 'videoCode',
  platform: [],
  orderUserId: [],
  auditStatusList: [],
  payType: undefined,
  placeOrderTime: [],
  payTypeDetails: [],
})

function resetQuery() {
  router.replace({ query: {} })
  queryParams.value = {
    searchName: '',
    val: '',
    select: 'videoCode',
    platform: [],
    orderUserId: [],
    auditStatusList: [],
    payType: [],
    placeOrderTime: [],
    payTypeDetails: [],
  }
  handleQuery()
}
const bizMerchantOptions = ref([])

const shortcuts = [
  {
    text: '今天',
    value: () => {
      const start = new Date()
      start.setHours(0, 0, 0, 0)
      const end = new Date()
      end.setHours(23, 59, 59, 59)
      return [start, end]
    },
  },
]

const columns = [
  {
    prop: 'merchantInfo',
    label: '商家信息',
    width: '200',
    handle: data => {
      return (data?.businessName || '') + (data?.memberCode ? '(' + data?.memberCode + ')' : '')
    },
  },
  { slot: 'info', prop: 'info', label: '单号信息', width: '300' },
  { slot: 'orderUser', prop: 'orderUser', label: '下单运营', width: '200' },
  { slot: 'amount', prop: 'amount', label: '订单金额', width: '180' },
  {
    prop: 'payAccount',
    label: '付款账号',
    width: '250',
    handle: (data, row) => {
      if (row.payType == payTypeMapNew['对公'] || row.payType == payTypeMapNew['对公+余额']) {
        return data || '-'
      }
      return data || '-'
    },
  },
  {
    slot: 'payType',
    prop: 'payType',
    label: '支付方式',
    width: '180',
    handle: data => payTypeMapNew[data] || '-',
  },
  { slot: 'account', prop: 'account', label: '收款账号', width: '250' },
  { slot: 'pay', prop: 'pay', label: '支付情况', width: '200' },
  { slot: 'auditStatus', prop: 'auditStatus', label: '审核状态', width: '120' },
  { prop: 'auditUserName', label: '审核人', width: '120', handle: data => data || '-' },
  { prop: 'auditTime', label: '审核时间', width: '170', handle: data => data || '-' },
  { prop: 'orderRemark', label: '备注', width: '170', 'show-overflow-tooltip': true },
]

const tableData = ref([])
const pageNum = ref(1)
const pageSize = ref(20)
const pageSizes = [10, 20, 30, 50]
const total = ref(0)
const tableLoading = ref(false)

function onQuery() {
  pageNum.value = 1
  handleQuery()
}
function handleQuery() {
  tableLoading.value = true
  receivableAuditList({
    pageNum: pageNum.value,
    pageSize: pageSize.value,
    ...getParams(),
  })
    .then(res => {
      tableData.value = res.data.rows
      total.value = res.data.total
    })
    .finally(() => (tableLoading.value = false))
}
function getParams() {
  let { val, select, platform, auditStatusList, placeOrderTime, orderUserId, payType, ...params } =
    queryParams.value
  if (placeOrderTime != null && placeOrderTime.length === 2) {
    params.orderTimeBegin = placeOrderTime[0]
    params.orderTimeEnd = placeOrderTime[1]
  }
  if (select) {
    params[select] = val
  }
  if (platform?.length) {
    params.platform = platform.join(',')
  }
  if (auditStatusList?.length && auditStatus.value == '') {
    params.auditStatusList = auditStatusList.join(',')
  } else {
    params.auditStatusList = auditStatus.value + ''
  }
  if (orderUserId?.length) {
    params.orderUserId = orderUserId.join(',')
  }
  if (payType?.length) {
    params.payType = payType.join(',')
  }
  return params
}

function pageChange(page) {
  pageNum.value = page.currentPage
  pageSize.value = page.pageSize
  handleQuery()
}

// 获取下单用户列表
function getBizMerchantList() {
  businessAccountList(getParams).then(res => {
    bizMerchantOptions.value = res.data.rows
  })
}

function handleCurrency(val) {
  if (val == '1') {
    return '人民币'
  }
  return sys_money_type.value.find(item => item.value == val)?.label || '-'
}

function toDetail(row) {
  // router.push(`/finance/receivableApprove/detail/video/${row.id}`)
  const { href } = router.resolve({ path: `/finance/receivableApprove/detail/video/${row.id}` })
  window.open(href, '_blank')
}

function handleButtonAction(btn, id, type) {
  emits('action', btn, id, type)
}

if (route.query && route.query.orderNum) {
  queryParams.value.select = 'orderNum'
  queryParams.value.val = route.query.orderNum
}

function getReceivableStatistics() {
  receivableStatistics().then(res => {
    curTabListNumber.value = res.data
  })
}

function init() {
  if (route.query.v_search) {
    queryParams.value.searchName = route.query.v_search
  }
  handleQuery()
  getReceivableStatistics()
}

// handleQuery()
// getBizMerchantList()
</script>

<style scoped lang="scss">
.el-picker-panel.picker-box {
  display: none;
  // .el-picker-panel,
  // .el-date-range-picker {
  //   display: none;
  // }
}
</style>
