<template>
  <div>
    <ElTablePage
      ref="tableRef"
      :columns="columns"
      :data="tableData"
      :loading="tableLoading"
      :currentPage="pageNum"
      :pageSize="pageSize"
      :total="total"
      :tableAction="{
        width: '170',
        fixed: 'right',
      }"
      :tableOptions="{
        border: true,
      }"
      @page-change="pageChange"
    >
      <template #tableHeader>
        <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="68px" @submit.prevent>
          <el-form-item label="搜索">
            <el-input
              v-model="queryParams.searchName"
              clearable
              style="width: 300px"
              placeholder="请输入商家信息/商家编码/订单运营"
            >
              <!-- <template #prepend>
                <el-select v-model="queryParams.select" placeholder="请选择" clearable style="width: 100px">
                  <el-option
                    v-for="item in memberSearchSelectList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </template> -->
            </el-input>
          </el-form-item>
          <el-form-item label="套餐类型" prop="packageType">
            <el-select v-model="queryParams.packageType" placeholder="请选择" clearable style="width: 180px">
              <el-option
                v-for="item in setMealTypeList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="审核状态" prop="auditStatusList" v-if="auditStatus === ''">
            <el-select
              v-model="queryParams.auditStatusList"
              placeholder="请选择"
              multiple
              collapse-tags
              collapse-tags-tooltip
              clearable
              style="width: 180px"
            >
              <el-option
                v-for="item in auditStatusList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="支付方式" prop="payTypes">
            <el-select
              v-model="queryParams.payTypes"
              placeholder="请选择"
              multiple
              collapse-tags
              collapse-tags-tooltip
              clearable
              style="width: 180px"
            >
              <el-option
                v-for="item in payMoneyTypeList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="全币种支付类型" prop="payTypeDetails" label-width="110px">
            <el-select
              v-model="queryParams.payTypeDetails"
              placeholder="请选择"
              multiple
              collapse-tags
              collapse-tags-tooltip
              clearable
              style="width: 200px"
            >
              <el-option
                v-for="item in payMoneyTypeAllList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="下单时间" style="width: 400px">
            <el-date-picker
              v-model="queryParams.placeOrderTime"
              format="YYYY/M/D HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              type="datetimerange"
              range-separator="-"
              unlink-panels
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
              :shortcuts="shortcuts"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="优惠类型">
            <el-select
              v-model="queryParams.promotionActivityTypes"
              placeholder="请选择"
              style="width: 200px"
              multiple
              collapse-tags
              collapse-tags-tooltip
              clearable
            >
              <el-option
                v-for="dict in promotionActivityTypeList"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button v-btn type="primary" icon="Search" native-type="submit" @click="onQuery">
              搜索
            </el-button>
            <el-button v-btn icon="Refresh" plain @click="resetQuery">重置</el-button>
            <!-- <DownloadBtn
              v-hasPermi="['receivable:approve:vip:export']"
              type="success"
              plain
              icon="Download"
              url="/order/finance/member/receivableAuditList/export"
              :params="getParams()"
              fileName="应收审批-会员订单.xlsx"
            /> -->
          </el-form-item>
        </el-form>
      </template>
      <template #packageAmount="{ row }">
        <div>美元: ${{ row.packageAmount || '-' }}</div>
        <div>百度汇率: {{ row.currentExchangeRate || '-' }}</div>
        <div>人民币: ￥{{ row.orderAmount || '-' }}</div>
      </template>
      <template #seedCodeDiscount="{ row }">
        <div v-if="row.seedCodeDiscount" style="text-align: left">
          <div>{{ row.channelType == 7 ? '裂变' : '渠道' }}名称：{{ row.channelName }}</div>
          <div>会员折扣：{{ row.settleRage }}%</div>
          <div>优惠金额：￥{{ row.seedCodeDiscount }}</div>
        </div>
        <div v-else>-</div>
      </template>
      <template #merchant="{ row }">
        <div style="text-align: left">
          <div>公司名称：{{ row.businessName || '-' }}</div>
          <div>会员编码：{{ row.memberCode || '-' }}</div>
          <div>微信名：{{ row.nickName || '-' }}</div>
        </div>
      </template>
      <template #discountDetail="{ row }">
        <div
          style="text-align: left"
          v-if="row.orderDiscountDetailVOS && row.orderDiscountDetailVOS.length > 0"
        >
          <div>
            优惠类型：{{
              disountTypeList.find(item => item.value == row.orderDiscountDetailVOS[0].type)?.label
            }}
          </div>
          <div v-if="row.orderDiscountDetailVOS[0].type == 3">
            {{ row.orderDiscountDetailVOS[0].channelType == 7 ? '裂变' : '渠道' }}名称：{{
              row.orderDiscountDetailVOS[0].channelName
            }}{{ row.seedId ? ` (ID${row.seedId})` : '' }}
          </div>
          <div>
            会员折扣：{{ row.orderDiscountDetailVOS[0].discountRatio
            }}{{ row.orderDiscountDetailVOS[0].discountType == 1 ? '元 (固定金额)' : '% (固定比例)' }}
          </div>
          <div>优惠金额：￥{{ row.orderDiscountDetailVOS[0].discountAmount }}</div>
        </div>
        <span v-else>-</span>
      </template>
      <template #orderPayeeAccountVO="{ row }">
        <PayeeAccountVO :data="row.orderPayeeAccountVO" :payType="row.payType" />
      </template>
      <template #useBalance="{ row }">
        <div v-if="row.useBalance">钱包余额抵扣: ￥{{ row.useBalance }}</div>
        <div>剩余支付:￥ {{ row.surplusAmount }}</div>
        <div>
          实付金额:
          <span v-if="row.auditStatus == 0 || row.auditStatus == 2 || row.auditStatus == 3">-</span>
          <span v-else>
            <template v-if="row.realPayAmountCurrency == 0 || row.realPayAmountCurrency">
              {{ row.realPayAmountCurrency }}
            </template>
            <template v-else>-</template>
          </span>
          <!-- {{ row.auditStatus == 1 ? row.realPayAmountCurrency : row.realPayAmountCurrency || '-' }} -->
        </div>
        <div>支付币种: {{ handleCurrency(row.currency) }}</div>
      </template>

      <template #info="{ row }">
        <div>{{ row.orderNum }}</div>
        <div>下单时间：{{ row.orderTime }}</div>
        <div v-if="row.submitCredentialTime">提交审批：{{ row.submitCredentialTime }}</div>
      </template>
      <template #payType="{ row }">
        <div>
          <div>{{ payTypeMapNew[row.payType] }}</div>
          <el-tag v-if="row.payTypeDetail && (row.payType == 7 || row.payType == 17)" type="warning" round>
            {{ payMoneyTypeMap[row.payTypeDetail] || '-' }}
          </el-tag>
        </div>
      </template>
      <template #auditStatus="{ row }">
        <div class="flex-center">
          <span v-if="row.auditStatus == 1">已审核</span>
          <span v-else-if="row.auditStatus == 0">待审核</span>
          <span v-else-if="row.auditStatus == 2">有异常</span>
          <span v-else-if="row.status == 4">已关闭</span>
        </div>
      </template>
      <template #tableAction="{ row }">
        <el-button
          v-btn
          v-hasPermi="['receivable:approve:vip:view']"
          type="primary"
          plain
          size="small"
          @click="handleDetail('/finance/receivableApprove/detail/member/' + row.orderId)"
        >
          查看
        </el-button>
        <el-button
          v-btn
          v-if="row.auditStatus == 0 && row.status != 4"
          v-hasPermi="['receivable:approve:vip:audit']"
          plain
          type="success"
          size="small"
          @click="handleButtonAction('审核', row.orderId, 'member')"
        >
          审核
        </el-button>
        <el-button
          v-btn
          v-else-if="row.auditStatus == 2"
          v-hasPermi="['receivable:approve:vip:reaudit']"
          plain
          type="warning"
          size="small"
          @click="handleButtonAction('重新审核', row.orderId, 'member')"
        >
          重新审核
        </el-button>
      </template>
      <template #pageLeft>
        <DownloadBtn
          v-hasPermi="['receivable:approve:vip:export']"
          type="success"
          plain
          icon="Download"
          url="/order/finance/member/receivableAuditList/export"
          :params="getParams()"
          fileName="应收审批-会员订单.xlsx"
        />
      </template>
    </ElTablePage>
  </div>
</template>

<script setup>
import ElTablePage from '@/components/Table/ElTablePage'
import DownloadBtn from '@/components/Button/DownloadBtn.vue'
import PayeeAccountVO from '@/views/finance/receivableApprove/components/payeeAccountVO.vue'
import {
  memberSearchSelectList,
  auditStatusList,
  payTypeList,
  payMoneyTypeList,
  setMealTypeList,
} from '../../data'
import { payTypeMapNew, payTypeMap } from '@/utils/dict'
import { receivableMemberAuditList, memberReceivableStatistics } from '@/api/finance/receivable.js'
import { disountTypeList, promotionActivityTypeList } from '@/views/order/vip/data.js'
import { payMoneyTypeAllList, payMoneyTypeMap } from '../../data'
import { useTabList } from '@/views/finance/receivableApprove/hooks'
const { auditStatus, curTabListNumber } = useTabList()
const router = useRouter()
const route = useRoute()

const { proxy } = getCurrentInstance()
const { sys_money_type } = proxy.useDict('sys_money_type')

function handleCurrency(val) {
  if (val == '1') {
    return '人民币'
  }
  return sys_money_type.value.find(item => item.value == val)?.label || '-'
}
const shortcuts = [
  {
    text: '今天',
    value: () => {
      const start = new Date()
      start.setHours(0, 0, 0, 0)
      const end = new Date()
      end.setHours(23, 59, 59, 59)
      return [start, end]
    },
  },
]

const columns = [
  { slot: 'merchant', prop: 'merchant', label: '商家信息', width: '200' },
  // {
  //   prop: 'phone',
  //   label: '下单账号',
  //   width: '160',
  //   handle: (data, row) => {
  //     let str = ''
  //     if (row.nickName) {
  //       str += row.nickName + '\n'
  //     }
  //     if (data) {
  //       str += data
  //     }
  //     return str
  //   },
  // },
  { slot: 'info', prop: 'info', label: '单号信息', minWidth: '250' },
  {
    prop: 'packageType',
    label: '套餐类型',
    width: '140',
    handle: data => {
      let item = setMealTypeList.find(item => item.value == data)
      if (item) {
        return item.label
      }
      return '-'
    },
  },
  { slot: 'discountDetail', label: '优惠信息', width: '220' },
  {
    prop: 'presentedTime',
    label: '赠送活动',
    width: '150',
    handle: (data, row) => {
      if (data) {
        let s =
          row.presentedTimeType == 1
            ? '天'
            : row.presentedTimeType == 2
            ? '月'
            : row.presentedTimeType == 3
            ? '年'
            : '-'
        return `赠送${data || '-'}${s}`
      } else {
        return '-'
      }
    },
  },
  {
    slot: 'packageAmount',
    label: '套餐金额',
    width: '160',
  },
  // {
  //   slot: 'seedCodeDiscount',
  //   label: '种草优惠来源',
  //   width: '200',
  // },
  {
    prop: 'payAccount',
    label: '付款账号',
    width: '180',
    handle: (data, row) => {
      if (row.payType == payTypeMapNew['对公'] || row.payType == payTypeMapNew['对公+余额']) {
        return data || '-'
      }
      return data || '-'
    },
  },
  {
    slot: 'payType',
    prop: 'payType',
    label: '支付方式',
    minWidth: '150',
    handle: data => {
      let str = payTypeMapNew[data]
      if (str == '全币种') {
        return '全币种'
      } else {
        return str ? str : '-'
      }
    },
  },
  {
    slot: 'orderPayeeAccountVO',
    label: '收款账号',
    width: '250',
  },
  {
    slot: 'useBalance',
    label: '支付情况',
    width: '250',
  },

  { slot: 'auditStatus', prop: 'auditStatus', label: '审核状态', width: '130' },
  {
    prop: 'auditUserName',
    label: '审核人',
    width: '160',
    handle: data => {
      return data ? data : '-'
    },
  },
  {
    prop: 'auditTime',
    label: '审核时间',
    width: '180',
    handle: data => {
      return data ? data : '-'
    },
  },
  {
    prop: 'orderRemark',
    label: '备注',
    width: '200',
    handle: data => {
      return data ? data : '-'
    },
  },
]

const tableData = ref([])
const pageNum = ref(1)
const pageSize = ref(20)
const total = ref(0)
const tableLoading = ref(false)

const props = defineProps({
  tab: {
    type: [Number, String],
  },
})

const emits = defineEmits(['action'])

defineExpose({
  handleQuery,
  init,
})

const queryParams = ref({
  searchName: '',
  val: '',
  select: 'orderNum',
  packageType: '',
  auditStatusList: [],
  payTypes: undefined,
  placeOrderTime: [],
  promotionActivityTypes: [],
  payTypeDetails: [],
})

function resetQuery() {
  router.replace({ query: {} })
  queryParams.value = {
    searchName: '',
    val: '',
    select: 'orderNum',
    packageType: '',
    auditStatusList: [],
    payTypes: undefined,
    placeOrderTime: [],
    promotionActivityTypes: [],
    payTypeDetails: [],
  }
  handleQuery()
}

function getParams() {
  let { val, select, placeOrderTime, auditStatusList, payTypes, ...params } = queryParams.value
  if (placeOrderTime != null && placeOrderTime.length === 2) {
    params.orderTimeBegin = placeOrderTime[0]
    params.orderTimeEnd = placeOrderTime[1]
  }
  if (auditStatusList?.length && auditStatus.value == '') {
    params.auditStatusList = auditStatusList.join(',')
  } else {
    params.auditStatusList = auditStatus.value + ''
  }
  if (payTypes?.length) {
    params.payTypes = payTypes.join(',')
  }
  if (select) {
    params[select] = val
  }
  return params
}
function onQuery() {
  pageNum.value = 1
  handleQuery()
}
function handleQuery() {
  tableLoading.value = true
  receivableMemberAuditList({
    pageNum: pageNum.value,
    pageSize: pageSize.value,
    ...getParams(),
  })
    .then(res => {
      tableData.value = res.data.rows
      total.value = res.data.total
    })
    .finally(() => (tableLoading.value = false))
}

function pageChange(page) {
  pageNum.value = page.currentPage
  pageSize.value = page.pageSize
  handleQuery()
}

function getMemberReceivableStatistics() {
  memberReceivableStatistics().then(res => {
    curTabListNumber.value = res.data
  })
}
function handleDetail(path) {
  const { href } = router.resolve({ path })
  window.open(href, '_blank')
  // router.push(path)
}
function handleButtonAction(btn, id, type) {
  emits('action', btn, id, type)
}

function init() {
  if (route.query.m_search) {
    queryParams.value.searchName = route.query.m_search
  }
  console.log(auditStatus.value)
  getMemberReceivableStatistics()
  handleQuery()
}
</script>

<style scoped lang="scss">
.audit-status::after {
  content: '';
  display: block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #f5222d;
  position: absolute;
  top: 42%;
  left: 23px;
}
</style>
