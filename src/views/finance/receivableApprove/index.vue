<template>
  <div style="padding: 20px">
    <el-radio-group v-model="curTab" size="large" @change="handleTabChange()">
      <el-radio-button :value="1" v-hasPermi="['receivable:approve:video:list']">
        视频订单({{ statisticsData.videoCount || 0 }})
      </el-radio-button>
      <el-radio-button :value="2" v-hasPermi="['receivable:approve:vip:list']">
        会员订单({{ statisticsData.memberCount || 0 }})
      </el-radio-button>
      <el-radio-button :value="3" v-hasPermi="['finance:prepay:list']">
        钱包充值({{ statisticsData.prepayCount || 0 }})
      </el-radio-button>
    </el-radio-group>
    <el-tabs v-model="auditStatus" class="tabs" @tab-change="handleStatusChange">
      <el-tab-pane v-for="(tab, i) in tabList" :key="tab" :name="tab.value">
        <template #label>
          <div v-if="i">
            {{ tab.label }}
            {{ '(' }}
            <span style="color: red">{{ tab.number }}</span>
            {{ ')' }}
          </div>
          <div v-else>{{ tab.label }}</div>
        </template>
      </el-tab-pane>
    </el-tabs>
    <div v-if="curTab == 1">
      <VideoOrderPage ref="VideoOrderPageRef" :tab="1" @action="handleButtonAction" />
    </div>
    <div v-if="curTab == 2">
      <MemberOrderPage ref="MemberOrderPageRef" :tab="2" @action="handleButtonAction" />
    </div>
    <div v-if="curTab == 3">
      <AdvancePayment ref="AdvancePaymentRef" :tab="3" />
    </div>
    <!-- <el-tabs v-model="curTab" class="tabs" @tab-change="handleTabChange()">
      <el-tab-pane :name="1" label="视频订单">
        <VideoOrderPage ref="VideoOrderPageRef" :tab="1" @action="handleButtonAction" />
      </el-tab-pane>
      <el-tab-pane :name="2" label="会员订单">
        <MemberOrderPage ref="MemberOrderPageRef" :tab="2" @action="handleButtonAction" />
      </el-tab-pane>
    </el-tabs> -->

    <ApproveDialog ref="ApproveDialogRef" @success="handleTabChange()" />
    <ApproveDialogV2 ref="ApproveDialogV2Ref" @success="handleTabChange()" />
  </div>
</template>

<script setup>
import ApproveDialog from '@/views/finance/receivableApprove/components/approveDialog.vue'
import ApproveDialogV2 from '@/views/finance/receivableApprove/components/approveDialogV2.vue'
import VideoOrderPage from '@/views/finance/receivableApprove/components/videoOrderPage.vue'
import MemberOrderPage from '@/views/finance/receivableApprove/components/memberOrderPage.vue'
import AdvancePayment from '@/views/finance/receivableApprove/components/advancePayment.vue'
import { onMounted, watch } from 'vue'
import { useTabList } from '@/views/finance/receivableApprove/hooks'
const { curTabListNumber, changeAuditStatus } = useTabList()
import { getUnApproveStatusStatistics } from '@/api/finance/refundApproval'
import { checkPermi } from '@/utils/permission'

const route = useRoute()

const curTab = ref(1)
let tabList = ref([
  { value: '', label: '所有订单', number: 0 },
  { value: 0, label: '待审核', number: 0 },
  { value: 2, label: '有异常', number: 0 },
  { value: 1, label: '已审核', number: 0 },
  { value: 3, label: '已关闭', number: 0 },
])
const tabOrderList = [
  { value: '', label: '所有订单', number: 0 },
  { value: 0, label: '待审核', number: 0 },
  { value: 2, label: '有异常', number: 0 },
  { value: 1, label: '已审核', number: 0 },
  { value: 3, label: '已关闭', number: 0 },
]
const tabPaymentList = [
  { value: '', label: '所有审核', number: 0 },
  { value: 0, label: '待审核', number: 0 },
  { value: 1, label: '已通过', number: 0 },
  { value: 2, label: '已拒绝', number: 0 },
]

const VideoOrderPageRef = ref()
const MemberOrderPageRef = ref()
const AdvancePaymentRef = ref()

const ApproveDialogRef = ref()
const auditStatus = ref('')

watch(
  () => curTabListNumber.value,
  val => {
    if (val) {
      handleTabData(val)
    }
  }
)

function handleTabData(data) {
  if (curTab.value == 3) {
    tabList.value[1].number = data?.preApproveNum || 0
    tabList.value[2].number = data?.approveNum || 0
    tabList.value[3].number = data?.cancelNum || 0
  } else {
    tabList.value[1].number = data?.unCheckNum || 0
    tabList.value[2].number = data?.exceptionNum || 0
    tabList.value[3].number = data?.approveNum || 0
    tabList.value[4].number = data?.closeNum || 0
  }
}

// tab切换
function handleTabChange(status) {
  auditStatus.value = isNaN(status) ? '' : status
  changeAuditStatus(auditStatus.value)
  if (curTab.value == 3) {
    // if(!checkPermi(['model:manage:status'])) return
    tabList.value = [...tabPaymentList]
  } else {
    tabList.value = [...tabOrderList]
  }
  handleStatusChange()
}
function handleStatusChange(val) {
  changeAuditStatus(auditStatus.value)
  getStatistics()
  handleTabsChange()
}

//切换时查询list
function handleTabsChange() {
  if (curTab.value == 1) {
    VideoOrderPageRef.value?.init()
  } else if (curTab.value == 2) {
    MemberOrderPageRef.value?.init()
  } else if (curTab.value == 3) {
    AdvancePaymentRef.value?.clearResetQuery()
    AdvancePaymentRef.value?.init()
  }
}
const ApproveDialogV2Ref = ref()
function handleButtonAction(btn, id, type) {
  type == 'video'
    ? ApproveDialogRef.value.open(id, btn, curTab.value, type)
    : ApproveDialogV2Ref.value.open(id, btn, curTab.value, type)
}

const statisticsData = ref({
  videoCount: 0,
  memberCount: 0,
  prepayCount: 0,
})

function getStatistics() {
  getUnApproveStatusStatistics().then(res => {
    statisticsData.value = res.data
  })
}

function init() {
  let s = ''
  if (route.query.as && !isNaN(route.query.as)) {
    s = Number(route.query.as)
  }
  
  let tab = route.query.tab ? route.query.tab : undefined
  if (tab ? tab == 1 : true && checkPermi(['receivable:approve:video:list'])) {
    auditStatus.value = s
    changeAuditStatus(s)
    curTab.value = 1
    nextTick(() => {
      VideoOrderPageRef.value?.init()
    })
    return
  }
  if (tab ? tab == 2 : true && checkPermi(['receivable:approve:vip:list'])) {
    curTab.value = 2
    handleTabChange(s)
    nextTick(() => {
      MemberOrderPageRef.value?.init()
    })
    return
  }
  if (tab ? tab == 3 : true && checkPermi(['finance:prepay:list'])) {
    curTab.value = 3
    handleTabChange(s)
    nextTick(() => {
      AdvancePaymentRef.value?.init()
    })
    return
  }
}

onMounted(() => {
  init()
})

getStatistics()
</script>

<style scoped lang="scss">
.tabs {
  :deep(.el-tabs__item:focus-visible) {
    box-shadow: none;
  }
}
</style>
