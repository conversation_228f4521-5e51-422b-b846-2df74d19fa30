<template>
  <div class="advance-dialog">
    <el-dialog
      v-model="dialogVisible"
      align-center
      width="700px"
      title="钱包充值审核"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="close"
    >
      <div style="color: #f59a23">请注意：确认后，商家余额将发生变化，请谨慎操作</div>
      <el-form ref="formRef" :model="form" label-width="auto" :rules="rules">
        <div class="form-info">
          <el-form-item label="申请来源：">{{ handleWalletType(detailInfo.orderType) }}</el-form-item>
          <el-form-item label="申请增加金额：">{{ money }}元</el-form-item>
          <el-form-item label="含赠送金额：">{{ containPresentedAmount }}元</el-form-item>
          <el-form-item label="支付凭证：">
            <template v-if="detailInfo.resources && detailInfo.resources.length > 0">
              <div class="img-box">
                <ViewerImageList :data="detailInfo.resources" is-preview-all :show-delete-btn="false" />
              </div>
            </template>
          </el-form-item>
          <el-form-item label="申请备注：">
            <div style="word-break: break-all">{{ detailInfo.applyRemark }}</div>
          </el-form-item>
          <el-form-item label="支付方式：">
            {{ detailPayType }}
            <el-tag
              v-if="detailInfo.payTypeDetail && (detailInfo.payType == 7 || detailInfo.payType == 17)"
              type="warning"
              round
            >
              {{ payMoneyTypeMap[detailInfo.payTypeDetail] || '-' }}
            </el-tag>
          </el-form-item>
          <el-form-item label="收款账号：">
            {{ detailInfo.orderPayeeAccountVO?.accountName || '-' }}
          </el-form-item>
          <el-form-item label="应付金额：">
            <span v-if="detailPayType == '全币种'">
              {{ detailInfo.payAmount }} CNY / {{ detailInfo.payAmountDollar }} USD
            </span>
            <span v-else>{{ detailInfo.payAmount }}元</span>
          </el-form-item>
        </div>
        <el-form-item label="审核结果：" style="align-items: center" prop="auditStatus">
          <el-radio-group v-model="form.auditStatus" @change="handleChangeStatus">
            <el-radio :value="1" size="large">通过</el-radio>
            <el-radio :value="2" size="large">拒绝</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="钱包充值增加金额：" prop="realAmount" v-if="form.auditStatus == 1">
          <el-input-number
            title=""
            v-model="form.realAmount"
            :precision="2"
            :step="1"
            :max="9999999"
            :min="0"
            @keydown="channelInputLimit"
            clearable
            :controls="false"
            style="width: 370px; text-align: left; z-index: 88"
          />
          &nbsp; 元
        </el-form-item>
        <el-form-item label=" " style="margin: -25px 0 0 0">
          <div v-if="isShowError" style="color: var(--el-color-danger); font-size: 12px; margin-top: 0">
            与申请金额不一致
          </div>
        </el-form-item>
        <el-form-item label="实付人民币：" prop="realPayAmount" v-if="form.auditStatus == 1">
          <el-input-number
            title=""
            v-model="form.realPayAmount"
            :precision="2"
            :step="1"
            :max="9999999"
            :min="0"
            clearable
            @keydown="channelInputLimit"
            :controls="false"
            style="width: 370px; text-align: left; z-index: 88"
          />
          &nbsp; 元
        </el-form-item>
        <template v-if="(detailInfo.payType == 7 || detailInfo.payType == 17) && form.auditStatus == 1">
          <el-form-item label="支付币种：" prop="currency">
            <el-select v-model="form.currency" placeholder="请选择币种" style="width: 400px">
              <el-option
                v-for="item in sys_money_type"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="实付金额：" prop="realPayAmountCurrency">
            <el-input-number
              title=""
              v-model="form.realPayAmountCurrency"
              :precision="2"
              :step="1"
              :max="9999999"
              :min="0"
              clearable
              controls-position="right"
              @keydown="channelInputLimit"
              style="width: 400px; text-align: left; z-index: 88"
            />
          </el-form-item>
        </template>
        <el-form-item label="支付时间：" prop="payTime" v-if="form.auditStatus == 1">
          <el-date-picker
            v-model="form.payTime"
            type="datetime"
            placeholder="请选择支付时间"
            style="width: 400px"
            format="YYYY-M-D HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
        <el-form-item v-if="form.auditStatus == 1" label="备注：" prop="remark">
          <el-input
            type="textarea"
            maxlength="100"
            :rows="3"
            style="width: 400px"
            v-model="form.remark"
            placeholder="请输入备注"
          />
        </el-form-item>
        <el-form-item v-else label="拒绝原因：" prop="rejectCause">
          <el-input
            type="textarea"
            maxlength="100"
            :rows="3"
            style="width: 400px"
            v-model="form.rejectCause"
            placeholder="请输入拒绝原因"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer flex-center">
          <el-button v-btn @click="close">取消</el-button>
          <el-button v-btn type="primary" @click="handleSubmit">确认</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import ViewerImageList from '@/components/ImagePreview/viewerImageList.vue'
import { prepayAuditDetail, prepayAudit } from '@/api/finance/receivable'
import { ElMessage } from 'element-plus'
import { payTypeList } from '@/utils/dict.js'
import { walletTopUpType, payMoneyTypeMap } from '@/views/finance/data.js'

const { proxy } = getCurrentInstance()
const { sys_money_type } = proxy.useDict('sys_money_type')

const dialogVisible = ref(false)

defineExpose({ open, close })
const emits = defineEmits(['success'])
const isShowError = ref(false)

const formRef = ref(null)
const form = ref({
  auditStatus: 1,
  realAmount: null,
  realPayAmount: null,
  realPayAmountCurrency: null,
  payTime: '',
  remark: '',
  id: null,
  rejectCause: '',
  currency: '',
})

const rules = ref({
  auditStatus: [{ required: true, message: '请选择审核结果', trigger: 'change' }],
  realAmount: [
    { required: true, message: '请输入钱包充值增加金额', trigger: 'blur' },
    { type: 'number', min: 0.01, max: 9999999, message: '请输入大于0的数字', trigger: 'blur' },
  ],
  realPayAmount: [{ required: true, message: '请输入实付人民币', trigger: 'blur' }],
  payTime: [{ required: true, message: '请选择支付时间', trigger: 'change' }],
  rejectCause: [{ required: true, message: '请输入拒绝原因', trigger: 'blur' }],
  currency: [{ required: true, message: '请选择币种', trigger: 'change' }],
  realPayAmountCurrency: [{ required: true, message: '请输入实付金额', trigger: 'blur' }],
})
const money = ref(0)
const containPresentedAmount = ref(0)
function open(id, amount, businessId) {
  money.value = amount || 0
  form.value.id = id
  form.value.businessId = businessId
  getDetails(id)
  dialogVisible.value = true
}

function close() {
  dialogVisible.value = false
  isShowError.value = false
  form.value = {
    auditStatus: 1,
    realAmount: null,
    realPayAmount: null,
    realPayAmountCurrency: null,
    payTime: '',
    remark: '',
    id: null,
    rejectCause: '',
    payCurrency: '',
  }
  containPresentedAmount.value = 0
  formRef.value.resetFields()
}

const detailInfo = ref({})
const detailPayType = computed(() => {
  return payTypeList.find(item => item.value == detailInfo.value.payType)?.label
})

function getDetails(id) {
  prepayAuditDetail(id).then(res => {
    containPresentedAmount.value = res.data.containPresentedAmount || 0
    detailInfo.value = res.data
  })
}

const handleFileChange = () => {
  proxy.$refs['dialogFormRef'].validateField('file')
}

function handleWalletType(type) {
  let item = walletTopUpType.find(item => item.value == type)
  return item ? item.label : '-'
}

function handleChangeStatus() {
  if (form.value.auditStatus == 2) {
    // rules.value.remark[0].required = true
  } else {
    // rules.value.remark[0].required = false
    form.value.rejectCause = ''
  }
}
function handleSubmit() {
  isShowError.value = false
  formRef.value.validate(valid => {
    if (valid) {
      // if (form.value.auditStatus == 1 && form.value.realAmount != money.value)
      //   return (isShowError.value = true)
      if (form.value.auditStatus == 2) {
        form.value = {
          rejectCause: form.value.rejectCause,
          id: form.value.id,
          businessId: form.value.businessId,
          auditStatus: form.value.auditStatus,
        }
      }
      prepayAudit(form.value)
        .then(res => {
          ElMessage.success('审核成功')
          emits('success')
          dialogVisible.value = false
          close()
        })
        .finally(() => {})
    }
  })
}

const channelInputLimit = e => {
  const key = e.key
  if (key === 'Enter') e.preventDefault()
  const notAllowList = ['e', '+', '-']
  if (notAllowList.includes(key)) {
    e.returnValue = false
    return false
  }
  return true
}
</script>

<style scoped lang="scss">
:deep(.el-input__inner) {
  text-align: left;
}
.form-info {
  margin: 10px 0;
  background: #f2f2f2;
  border-radius: 5px;
  padding: 5px 0;
  .img-box {
    :deep(.viewer-list) {
      overflow: hidden;
    }
  }

  .el-form-item {
    margin-bottom: 8px;
  }
}
</style>
