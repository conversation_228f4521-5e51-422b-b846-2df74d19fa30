<template>
  <div>
    <el-dialog
      v-model="dialogRecordsVisible"
      width="600px"
      title="钱包充值审批记录"
      align-center
      :showClose="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-timeline style="max-width: 600px" v-loading="detailLoading">
        <el-timeline-item
          placement="top"
          v-for="(item, index) in auditFlowData"
          :key="index"
          :timestamp="index == 0 ? item.creatTime : item.auditTime"
        >
          <div v-if="index == 0">
            <div class="records-title">申请钱包充值金额增加{{ item.amount }}元</div>
            <div style="display: flex; margin-top: 5px">
              支付凭证：
              <template v-if="item.resources && item.resources.length > 0">
                <div class="img-box">
                  <ViewerImageList :data="item.resources" is-preview-all :show-delete-btn="false" />
                </div>
              </template>
            </div>
            <div style="margin: 5px 0">
              支付方式：{{ PayType }}
              <el-tag
                v-if="item.payTypeDetail && (item.payType == 7 || item.payType == 17)"
                type="warning"
                round
              >
                {{ payMoneyTypeMap[item.payTypeDetail] || '-' }}
              </el-tag>
            </div>
            <div>含赠送金额：{{ item.containPresentedAmount }}元</div>
            <div style="margin: 5px 0" v-if="PayType == '全币种'">
              应付金额：{{ item.payAmount }} CNY / {{ item.payAmountDollar }} USD
            </div>
            <div style="margin: 5px 0" v-else>应付金额：{{ item.payAmount }}元</div>
            <div class="more-ell" style="--l: 9; word-break: break-all">申请备注：{{ item.applyRemark }}</div>
            <div style="margin: 5px 0">申请人：{{ item.createBy }}</div>
          </div>
          <div class="records-two" v-if="item.auditStatus == 1 || item.auditStatus == 2">
            <div class="records-right">
              <div class="records-title" v-if="item.auditStatus == 1">
                钱包金额成功增加{{ item.realAmount }}元
              </div>
              <div class="records-title" v-if="item.auditStatus == 2">
                钱包金额增加{{ item.realAmount }}元审批拒绝
              </div>
              <div class="records-line" v-if="item.auditStatus == 1">
                实付人民币：{{ item.realPayAmount }}元
              </div>
              <template v-if="item.auditStatus == 1 && (item.payType == 7 || item.payType == 17)">
                <div class="records-line">
                  支付币种：{{ sys_money_type.find(data => data.value == item.currency)?.label || '-' }}
                </div>
                <div class="records-line">实付金额：{{ item.realPayAmountCurrency }}</div>
              </template>
              <div class="records-line">处理人：{{ item.auditUserName }}</div>
              <div class="records-line" v-if="item.auditStatus == 1">支付时间：{{ item.payTime }}</div>

              <div class="records-line" style="display: flex">
                <div :style="{ minWidth: item.auditStatus == 1 ? '42px' : '75px' }">
                  {{ item.auditStatus == 1 ? '备注：' : item.auditStatus == 2 ? '拒绝原因：' : '' }}
                </div>
                <div class="more-ell" style="--l: 9; word-break: break-all">
                  {{ item.auditStatus == 1 ? item.remark : item.rejectCause }}
                </div>
              </div>
            </div>
          </div>
        </el-timeline-item>
      </el-timeline>
      <template #footer>
        <div class="flex-center">
          <el-button round type="primary" @click="closeDialogRecords">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import ViewerImageList from '@/components/ImagePreview/viewerImageList.vue'
import { prepayAuditDetail } from '@/api/finance/receivable'
import { payMoneyTypeMap } from '../../data'
import { payTypeList } from '@/utils/dict.js'
import { parseTime } from '@/utils/ruoyi'
import { useViewer } from '@/hooks/useViewer'
const { showViewer } = useViewer()
const { proxy } = getCurrentInstance()
const { sys_money_type } = proxy.useDict('sys_money_type')

defineExpose({
  open,
  close,
})
const dialogRecordsVisible = ref(false)
const detailLoading = ref(false)
const auditFlowData = ref([])

const PayType = computed(() => {
  return payTypeList.find(item => item.value == auditFlowData.value[0].payType).label
})

function open(id) {
  dialogRecordsVisible.value = true
  getDetails(id)
}

function close() {
  dialogRecordsVisible.value = false
}

function getDetails(id) {
  detailLoading.value = true
  prepayAuditDetail(id)
    .then(res => {
      let data = res.data
      let list = []
      list.push({
        amount: data.amount || '-',
        resources: data.resources || '-',
        createBy: data.createBy || '-',
        creatTime: data.creatTime || '-',
        applyRemark: data.applyRemark || '-',
        payType: data.payType || '-',
        payAmount: data.payAmount || 0,
        payAmountDollar: data.payAmountDollar || 0,
        containPresentedAmount: data.containPresentedAmount || 0,
        payTypeDetail: data.payTypeDetail || '',
      })
      list.push({
        realAmount: data.realAmount || data.amount,
        payTime: data.payTime || '-',
        auditUserName: data.auditUserName || '-',
        auditTime: data.auditTime || '-',
        auditStatus: data.auditStatus || '',
        remark: data.remark || '-',
        realPayAmount: data.realPayAmount || 0,
        rejectCause: data.rejectCause || '-',
        realPayAmountCurrency: data.realPayAmountCurrency || 0,
        currency: data.currency || '-',
        payType: data.payType || '-',
      })
      auditFlowData.value = list
    })
    .finally(() => {
      detailLoading.value = false
    })
}

const closeDialogRecords = () => {
  dialogRecordsVisible.value = false
  auditFlowData.value = []
}
</script>

<style lang="scss" scoped>
.records-two {
  // display: flex;
  // .records-left {
  //   position: relative;
  //   border-left: 2px solid #e6e6e6;
  //   left: -24px;
  //   top: -11px;
  // }
  .records-right::before {
    content: ' ';
    border-left: 2px solid #e6e6e6;
    position: absolute;
    height: 100%;
    left: 4px;
    top: 13px;
  }
  .records-line {
    margin: 5px 0;
  }
}

.records-title {
  font-size: 18px;
}
.img-box {
  :deep(.viewer-list) {
    overflow: hidden;
  }
}
</style>
