/**
 * 视频订单搜索下拉
 */
export const videoSearchSelectList = [
  { label: '订单号', value: 'orderNum' },
  { label: '商家名称', value: 'merchantName' },
  { label: '视频编码', value: 'videoCode' },
  { label: '产品名称', value: 'productName' },
]
/**
 * 审核状态
 */
export const auditStatusList = [
  { label: '待审核', value: '0' },
  { label: '已审核', value: '1' },
  { label: '有异常', value: '2' },
  { label: '已关闭', value: '3' },
]
/**
 * 钱包充值审核状态
 */
export const payAuditStatusList = [
  { label: '待审核', value: '0' },
  { label: '已通过', value: '1' },
  { label: '已拒绝', value: '2' },
]
/**
 * 钱包充值类型
 */
export const walletTopUpType = [
  { label: '线上钱包充值', value: 5 },
  { label: '线下钱包充值', value: 3 },
]
/**
 * 支付方式
 */
export const payTypeList = [
  { label: '微信', short: '微信', value: 1 },
  { label: '支付宝', short: '支付宝', value: 2 },
  { label: '对公转账', short: '对公', value: 6 },
  { label: '银行卡转账', short: '银行', value: 5 },
  { label: '全币种支付', short: '全币种', value: 7 },
]
export const payMoneyTypeList = [
  { label: '微信', value: 1 },
  { label: '支付宝', value: 2 },
  { label: '对公', value: 6 },
  { label: '银行', value: 5 },
  { label: '全币种', value: 7 },
]
export const payTypeAllList = [
  { label: '微信', value: 1 },
  { label: '支付宝支付', value: 2 },
  { label: '银行', value: 5 },
  { label: '对公转账', value: 6 },
  { label: '全币种', value: 7 },
]

/**
 * 会员订单搜索下拉
 */
export const memberSearchSelectList = [
  { label: '会员订单号', value: 'orderNum' },
  { label: '商家微信昵称', value: 'nickName' },
  { label: '商家账号', value: 'businessAccount' },
]
/**
 * 套餐类型
 */
export const setMealTypeList = [
  { label: '季度会员', value: 0, price: 980, $: 119.7 },
  { label: '一年会员', value: 1, price: 1980, $: 238.8 },
  { label: '三年会员', value: 2, price: 4980, $: 644.4 },
]
/**
 * 会员类型
 */
export const vipTypeList = [
  { label: '季度会员', value: 0 },
  { label: '年度会员', value: 1 },
  { label: '三年会员', value: 2 },
]
/**
 * 退款状态（0:待审核,1:退款中,2:已拒绝,3:已取消,4:退款成功）
 */
export const refundStatusList = [
  { label: '退款待审核', value: 0, type: 'primary' },
  { label: '退款中', value: 1, type: 'success' },
  { label: '已拒绝', value: 2, type: 'danger' },
  { label: '已取消', value: 3, type: 'info' },
  { label: '退款成功', value: 4, type: 'success' },
]
/**
 * 退款类型（1:补偿、2:取消订单、3:取消选配）
 */
export const refundTypeList = [
  {
    label: '补偿',
    value: 1,
    type: 'warning',
  },
  {
    label: '取消订单',
    value: 2,
    type: 'danger',
  },
  {
    label: '取消选配',
    value: 3,
    type: 'warning',
  },
]
/**
 * @Excel(name = "发起方", readConverterExp = "1:商家,2:平台")
 */
export const refundSourceList = [
  {
    label: '商家',
    value: 1,
  },
  {
    label: '平台',
    value: 2,
  },
]
/**
 * 提现审批状态
 */
export const withdrawDepositStatus = [
  { label: '待处理', value: 0 },
  { label: '已提现', value: 1 },
  { label: '已取消', value: 2 },
]
export const withdrawDepositStatus1 = [
  { label: '已提现', value: 1 },
  { label: '已取消', value: 2 },
]
/**
 * 分销渠道结算状态
 */
export const settleAccountsStatus = [
  { label: '待入账', value: 0, type: 'warning' },
  { label: '可提现', value: 1, type: 'warning' },
  { label: '提现审核中', value: 2, type: 'warning' },
  { label: '待打款', value: 3, type: 'warning' },
  { label: '已打款', value: 4, type: 'success' },
  { label: '审核不通过', value: 5, type: 'danger' },
  { label: '打款异常', value: 6, type: 'danger' },
  { label: '无需提现', value: 7, type: 'info' },
  { label: '入账失败', value: 99, type: 'danger' },
  { label: '暂不可提现', value: 999, type: 'danger' },
]

/**
 * 全币种支付方式
 *
 */
export const payMoneyTypeAllList = [
  { label: '万里汇', value: 702 },
  { label: '其他平台/银行', value: 701 },
]
/**
 * 支付方式
 */
const payMoneyTypeMap = {}
payMoneyTypeAllList.forEach(item => {
  payMoneyTypeMap[item.label] = item.value
  payMoneyTypeMap[item.value] = item.label
})
export {
  payMoneyTypeMap
}