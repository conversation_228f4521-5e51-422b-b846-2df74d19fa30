<template>
  <div style="text-align: left">
    <div>收款方式：{{ channelWithdrawTypeMap[row.withdrawalAccountType] || '-' }}</div>
    <div v-if="channelWithdrawTypeMap['支付宝'] == row.withdrawalAccountType">
      支付宝账号：{{ row.payeeAccount }}
    </div>
    <div v-if="channelWithdrawTypeMap['银行卡'] == row.withdrawalAccountType">
      银行卡账号：{{ row.payeeAccount }}
    </div>
    <div v-if="channelWithdrawTypeMap['公户收款'] == row.withdrawalAccountType">
      <div>收款公司名称：{{ row.payeeName }}</div>
      <div>收款银行账号：{{ row.payeeAccount }}</div>
      <div>开户行名称：{{ row.bankName }}</div>
    </div>
  </div>
</template>

<script setup>
import { channelWithdrawTypeMap } from '@/views/finance/zhongcao/data'

defineProps({
  row: {
    type: Object,
    required: true
  }
})
</script>