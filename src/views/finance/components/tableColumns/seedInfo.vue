<template>
  <div style="text-align: left">
    <div>种草官ID：{{ row.channelSeedId }}</div>
    <template v-if="row.channelType == 2">
      <div>渠道名称：{{ row.channelName || '-' }}</div>
    </template>
    <template v-else-if="row.channelType == 7">
      <div>微信：{{ row.applicantNickName || '-' }}</div>
      <div>姓名：{{ row.applicantName || '-' }}</div>
      <div v-if="row.applicantBusinessName">
        {{ row.applicantBusinessName + (row.applicantMemberCode ? `(${row.applicantMemberCode})` : '') }}
      </div>
    </template>
  </div>
</template>

<script setup>
defineProps({
  row: {
    type: Object,
    required: true
  }
})
</script>