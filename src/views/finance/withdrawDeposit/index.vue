<template>
  <div>
    <el-tabs v-model="activeName" class="tabs" @tab-change="handleTabChange">
      <el-tab-pane label="待审批" name="0">
        <template #label>待审批({{ number }})</template>
        <Deposit ref="DepositOne" @updateStatistics="handleNumberChange" />
      </el-tab-pane>
      <el-tab-pane label="已审批" name="all" >
        <DepositSuccess ref="DepositTwo" @updateStatistics="handleNumberChange"/>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script setup>
import Deposit from '@/views/finance/withdrawDeposit/components/deposit.vue'
import DepositSuccess from '@/views/finance/withdrawDeposit/components/depositSuccess.vue'
import { onMounted } from 'vue';

const activeName = ref('0')

const DepositOne = ref()
const DepositTwo = ref()
const isActive = ref(0)

function handleTabChange() {
  isActive.value = activeName.value
  if (activeName.value == '0') {
    DepositOne.value?.handleQuery()
  } else {
    DepositTwo.value?.handleQuery()
  }
}
const number = ref(0)
function handleNumberChange(num) {
  number.value = num
}
onMounted(() => {
  DepositOne.value?.handleQuery()
})
</script>

<style scoped lang="scss">
.tabs {
  padding-left: 20px;

  :deep(.el-tabs__item:focus-visible) {
    box-shadow: none;
  }
}
</style>
