<template>
  <div style="margin: 20px">
    <ElTablePage
      ref="tableRef"
      :columns="columns"
      :data="tableData"
      :loading="tableLoading"
      :currentPage="currentPage"
      :pageSize="pageSize"
      :total="total"
      :tableAction="{
        width: '190',
        fixed: 'right',
      }"
      @page-change="pageChange"
      row-key="id"
    >
      <template #tableHeader>
        <div>
          <el-form :inline="true" :model="queryParams" @submit.prevent>
            <el-form-item label="搜索">
              <el-input
                v-model="queryParams.searchNameMemberCodeAccount"
                style="width: 300px"
                clearable
                placeholder="请输入"
              ></el-input>
            </el-form-item>
            <el-form-item label="状态">
              <el-select
                v-model="queryParams.auditStatus"
                placeholder="请选择"
                clearable
                style="width: 150px"
              >
                <el-option
                  v-for="dict in withdrawDepositStatus1"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="审批时间" style="width: 350px">
              <el-date-picker
                v-model="queryParams.auditTimes"
                format="YYYY/M/D HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="datetimerange"
                range-separator="-"
                unlink-panels
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
                :shortcuts="shortcuts"
              ></el-date-picker>
            </el-form-item>
            <el-form-item label="发起时间" style="width: 350px">
              <el-date-picker
                v-model="queryParams.times"
                format="YYYY/M/D HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="datetimerange"
                range-separator="-"
                unlink-panels
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
                :shortcuts="shortcuts"
              ></el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button v-btn type="primary" icon="Search" native-type="submit" @click="handleQuery">
                搜索
              </el-button>
              <el-button v-btn icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
          <!-- <div style="margin-bottom: 10px">待处理提现：{{ statistics }}条</div> -->
        </div>
      </template>
      <template #info="{ row }">
        <div>
          <el-row>
            {{
              row.businessAccountDetailVO?.name
                ? row.businessAccountDetailVO?.name
                : row.businessAccountDetailVO?.nickName
            }}
            &nbsp;
            <el-tag
              type="warning"
              size="small"
              v-if="row.businessAccountDetailVO?.isProxy == 1"
              style="border-radius: 10px"
            >
              代理
            </el-tag>
          </el-row>
          <el-row>公司名称: {{ row.businessAccountDetailVO?.businessName }}</el-row>
          <el-row>会员开通时间: {{ row.businessAccountDetailVO?.businessCreateTime }}</el-row>
        </div>
      </template>
      <template #remark="{ row }">
        <el-button v-btn link type="primary" @click="openRemarkDialog(row)">查看</el-button>
      </template>
      <template #tableAction="{ row }">
        <el-button
          v-btn
          v-hasPermi="['withdraw:deposit:records']"
          link
          type="primary"
          @click="openRecordsDialog(row.id)"
        >
          查看记录
        </el-button>
      </template>
      <template #pageLeft>
        <DownloadBtn
          v-hasPermi="['withdraw:deposit:export']"
          type="success"
          plain
          icon="Download"
          url="/biz/business/backend/businessBalanceAuditFlow/export"
          :params="getQueryParams()"
          fileName="提现已审批列表.xlsx"
        />
      </template>
    </ElTablePage>
    <!-- 查看记录弹窗 -->
    <el-dialog
      :close-on-press-escape="false"
      v-model="dialogRecordsVisible"
      width="900px"
      title="提现记录"
      align-center
      :showClose="false"
    >
      <div>
        <el-table
          ref="withdrawTableRef"
          border
          :data="withdrawList"
          style="max-height: 300px; overflow: auto; margin-bottom: 10px"
        >
          <el-table-column label="单号" align="center" prop="number"></el-table-column>
          <el-table-column label="视频编码" prop="videoCode" align="center">
            <template v-slot="{ row }">
              <div>
                {{ row.videoCode || '-' }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="下单运营" prop="createOrderUserName" align="center">
            <template v-slot="{ row }">
              <div>
                {{ row.createOrderUserName || '-' }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="类型" prop="origin" align="center">
            <template v-slot="{ row }">
              {{ originTypeList.find(item => item.value == row.origin)?.label || '' }}
            </template>
          </el-table-column>
          <el-table-column label="备注" prop="remark" align="center" width="110px">
            <template v-slot="{ row }">
              <div
                v-if="
                  (row.remark && stringLength(row.remark) > 10) ||
                  (row.containPresentedAmount &&
                    row.containPresentedAmount > 0 &&
                    stringLength(row.remark) > 6)
                "
              >
                <el-tooltip effect="dark" :hide-after="0" placement="top">
                  <template #content>
                    <div style="max-width: 600px; white-space: pre-wrap; max-height: 45vh; overflow-y: auto">
                      <span style="color: #f59a23">
                        {{ row.containPresentedAmount ? `赠送${row.containPresentedAmount}元` : '' }}
                      </span>
                      {{
                        row.containPresentedAmount
                          ? row.remark
                            ? row.remark
                            : ''
                          : row.remark
                          ? row.remark
                          : '-'
                      }}
                      <!-- 赠送9元{{ row.remark }} -->
                    </div>
                  </template>
                  <div class="one-ell" style="text-align: center">
                    <span style="color: #f59a23">
                      {{ row.containPresentedAmount ? `赠送${row.containPresentedAmount}元` : '' }}
                    </span>
                    {{ row.remark }}
                  </div>
                </el-tooltip>
              </div>
              <span v-else>
                <span style="color: #f59a23">
                  {{ row.containPresentedAmount ? `赠送${row.containPresentedAmount}元` : '' }}
                </span>
                {{
                  row.containPresentedAmount ? (row.remark ? row.remark : '') : row.remark ? row.remark : '-'
                }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="余额增加时间" prop="createTime" align="center"></el-table-column>
          <el-table-column label="已用金额(元)" prop="useBalance" align="center"></el-table-column>
          <el-table-column label="可提现金额(元)" prop="validBalance" align="center"></el-table-column>
        </el-table>
        <div style="max-height: 500px; overflow-y: auto">
          <el-timeline style="max-width: 800px" v-loading="detailLoading">
            <el-timeline-item
              placement="top"
              v-for="(item, index) in auditFlowData"
              :key="index"
              :timestamp="parseTime(item.createTime, '{y}-{m}-{d} {h}:{i}')"
            >
              <div v-if="index == 0">
                <div class="records-title">申请提现{{ item.amount }}元</div>
                <div style="margin: 5px 0">申请人：{{ item.createUserName }}</div>
                <div class="records-line">
                  提现方式：{{ withdrawTypeList.find(data => data.value == item.withdrawWay)?.label || '-' }}
                </div>
                <div style="margin: 5px 0; line-break: anywhere; word-break: break-all">
                  备注：{{ item.applyRemark || '-' }}
                </div>
                <div style="display: flex">
                  <div style="flex-shrink: 0">图片：</div>
                  <ViewerImageList
                    v-if="item.payoutResourceUrlList && item.payoutResourceUrlList.length > 0"
                    :data="item.payoutResourceUrlList"
                    is-preview-all
                    :show-delete-btn="false"
                  />
                  <span v-else>-</span>
                </div>
              </div>
              <div class="records-two" v-else>
                <!-- <div class="records-left"></div> -->
                <div class="records-right">
                  <template v-if="item.auditStatus == 1">
                    <div class="records-title">成功提现{{ item.realAmount }}元</div>
                    <div class="records-line">处理人：{{ item.auditUserName }}</div>
                    <div class="records-line">
                      提现方式：{{
                        withdrawTypeList.find(data => data.value == item.withdrawWay)?.label || '-'
                      }}
                    </div>
                    <div class="records-line">
                      支付时间：{{ parseTime(item.payTime, '{y}-{m}-{d} {h}:{i}') }}
                    </div>
                    <div style="display: flex">
                      支付凭证：
                      <el-image
                        v-if="item.resourceUrl"
                        :src="$picUrl + item.resourceUrl + '!1x1compress'"
                        style="width: 100px; height: 100px"
                        @click="showViewer([$picUrl + item.resourceUrl], { index: 1, raw: true })"
                      />
                      <div v-else>暂无凭证</div>
                    </div>
                    <div class="records-line" style="display: flex">
                      <div style="min-width: 42px">备注：</div>
                      <div>{{ item.remark }}</div>
                    </div>
                  </template>
                  <template v-if="item.auditStatus == 2">
                    <div class="records-title">取消提现</div>
                    <div>处理人：{{ item.auditUserName }}</div>
                  </template>
                </div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
      <template #footer>
        <div class="flex-center">
          <el-button round type="primary" @click="closeDialogRecords">确认</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 查看remark弹窗 -->
    <el-dialog
      :close-on-press-escape="false"
      v-model="dialogRemarkVisible"
      width="500px"
      title="备注"
      align-center
    >
      <div>
        <div class="remark-row">
          <div class="remark-label">提现金额：</div>
          <div>{{ dialogRemarkRow.amount }}</div>
        </div>
        <div class="remark-row">
          <div class="remark-label">提现方式：</div>
          <div>{{ withdrawTypeList.find(item => item.value == dialogRemarkRow.withdrawWay)?.label }}</div>
        </div>
        <div class="remark-row">
          <div class="remark-label">备注：</div>
          <div style="word-break: break-word; white-space: pre-wrap">{{ dialogRemarkRow.applyRemark }}</div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script setup>
import ElTablePage from '@/components/Table/ElTablePage.vue'
import ViewerImageList from '@/components/ImagePreview/viewerImageList.vue'
import { stringLength } from '@/utils/index'
import { withdrawDepositStatus1 } from '@/views/finance/data.js'
import { withdrawTypeList } from '@/views/task/data.js'
import { originTypeList } from '@/views/merchant/data.js'
import {
  businessBalanceAuditFlow,
  businessBalanceAuditFlowStatistics,
  businessBalanceAuditFlowAuditPayOut,
  businessBalanceAuditFlowDetail,
} from '@/api/finance/balence.js'
import DownloadBtn from '@/components/Button/DownloadBtn.vue'
import { parseTime } from '@/utils/ruoyi'
import { useViewer } from '@/hooks/useViewer'
const { showViewer } = useViewer()

const { proxy } = getCurrentInstance()
const router = useRouter()

const statistics = ref(0)
const tableData = ref([])
const tableLoading = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const queryParams = ref({
  searchNameMemberCodeAccount: '',
  auditStatus: '',
  times: [],
  auditTimes: [],
})

const shortcuts = [
  {
    text: '今天',
    value: () => {
      const start = new Date()
      start.setHours(0, 0, 0, 0)
      const end = new Date()
      end.setHours(23, 59, 59, 59)
      return [start, end]
    },
  },
]

const dialogRemarkVisible = ref(false)

const props = defineProps({
  type: String,
})

const columns = ref([
  {
    label: '提现单号',
    width: '100',
    prop: 'withdrawNumber',
  },
  //   {
  //     label: '商家信息',
  //     prop: 'ownerAccount',
  //     width: '200',
  //     handle: (val, row) => {
  //       return row.businessAccountDetailVO.account
  //     },
  //   },
  {
    label: '商家信息',
    minWidth: '200',
    slot: 'info',
    align: 'left',
  },
  {
    label: '会员编码',
    prop: 'memberCode',
    width: '200',
    handle: (val, row) => {
      return row.businessAccountDetailVO.memberCode
    },
  },
  {
    label: '合计提现金额',
    prop: 'amount',
    width: '150',
  },
  {
    label: '实际提现金额',
    prop: 'realAmount',
    width: '150',
  },
  {
    label: '来源',
    prop: 'numbers',
    width: '150',
    handle: (val, row) => {
      if (row.numbers && row.numbers.length > 0) {
        return row.numbers.join('、')
      } else {
        return '-'
      }
    },
  },
  {
    label: '提现方式',
    prop: 'withdrawWay',
    width: '150',
    handle: (val, row) => {
      return withdrawTypeList.find(item => item.value == row.withdrawWay)?.label
    },
  },
  {
    label: '备注',
    slot: 'remark',
    width: '150',
  },
  {
    label: '状态',
    prop: 'auditStatus',
    width: '200',
    handle: status => {
      let s = withdrawDepositStatus1.find(item => item.value == status)
      return s ? s.label : '待处理'
    },
  },
  {
    label: '发起人',
    width: '150',
    prop: 'createUserName',
  },
  {
    label: '发起时间',
    width: '200',
    prop: 'createTime',
  },
  {
    label: '审批人',
    width: '150',
    prop: 'auditUserName',
  },
  {
    label: '审批时间',
    width: '200',
    prop: 'auditTime',
  },
])

const dialogConfirmVisible = ref(false)
const dialogForm = ref({
  id: undefined,
  businessId: undefined,
  useBalance: 0,
  amount: 0,
  payTime: '',
  file: [],
  remark: '',
})

const dialogRecordsVisible = ref(false)

const rules = {
  useBalance: [{ validator: checkBalance, trigger: 'change' }],
  payTime: [{ required: true, message: '请选择支付时间', trigger: 'blur' }],
  file: [{ required: true, message: '请上传支付凭证', trigger: 'change' }],
}

function checkBalance(rule, value, callback) {
  if (value != dialogForm.value.amount) {
    return callback(new Error('输入金额与提现金额不一致！'))
  }
  return callback()
}

const handleFileChange = () => {
  proxy.$refs['dialogFormRef'].validateField('file')
}

const handleClose = () => {
  proxy.$refs['dialogFormRef'].resetFields()
}

function getQueryParams() {
  let params = {
    auditTimeBegin: '',
    auditTimeEnd: '',
    startTimeBegin: '',
    startTimeEnd: '',
    searchNameMemberCodeAccount: queryParams.value.searchNameMemberCodeAccount,
    auditStatus: queryParams.value.auditStatus,
  }
  if (queryParams.value.times != [] && queryParams.value.times != null) {
    params.startTimeBegin = queryParams.value.times[0]
    params.startTimeEnd = queryParams.value.times[1]
  }
  if (queryParams.value.auditTimes != [] && queryParams.value.auditTimes != null) {
    params.auditTimeBegin = queryParams.value.auditTimes[0]
    params.auditTimeEnd = queryParams.value.auditTimes[1]
  }
  return params
}

const confirmWithDraw = () => {
  // console.log(dialogForm.value)
  proxy.$refs['dialogFormRef'].validate(valid => {
    if (valid) {
      proxy.$modal.loading('执行中...')
      businessBalanceAuditFlowAuditPayOut({
        id: dialogForm.value.id,
        auditStatus: 1,
        businessId: dialogForm.value.businessId,
        realAmount: dialogForm.value.useBalance,
        payTime: dialogForm.value.payTime,
        remark: dialogForm.value.remark,
        resourceUrl: dialogForm.value.file[0].picUrl,
      })
        .then(res => {
          proxy.$modal.msgSuccess('操作成功')
          dialogConfirmVisible.value = false
          handleQuery()
        })
        .finally(() => proxy.$modal.closeLoading())
    }
  })
}

const handleQuery = () => {
  fetchData()
  getStatistics()
}
defineExpose({ handleQuery })

const resetQuery = () => {
  queryParams.value = {
    searchNameMemberCodeAccount: '',
    auditStatus: 1,
    times: [],
    auditTimes: [],
  }
  handleQuery()
}
const fetchData = () => {
  tableLoading.value = true
  const params = { ...queryParams.value }
  if (params.times && params.times.length > 0) {
    params.startTimeBegin = params.times[0]
    params.startTimeEnd = params.times[1]
    params.times = null
  }
  if (params.auditTimes && params.auditTimes.length > 0) {
    params.auditTimeBegin = params.auditTimes[0]
    params.auditTimeEnd = params.auditTimes[1]
    params.auditTimes = null
  }
  businessBalanceAuditFlow({
    pageNum: currentPage.value,
    pageSize: pageSize.value,
    ...params,
  }).then(res => {
    tableData.value = res.data.rows
    total.value = res.data.total
    tableLoading.value = false
  })
}
const emits = defineEmits(['updateStatistics'])
const getStatistics = () => {
  businessBalanceAuditFlowStatistics().then(res => {
    statistics.value = res.data.preApproveNum
    emits('updateStatistics', res.data.preApproveNum)
  })
}

const pageChange = page => {
  currentPage.value = page.currentPage
  pageSize.value = page.pageSize
  handleQuery()
}

const openConfirmDialog = row => {
  dialogConfirmVisible.value = true
  dialogForm.value = {
    id: row.id,
    businessId: row.businessId,
    useBalance: undefined,
    amount: row.amount,
    payTime: '',
    file: [],
    remark: '',
  }
}

const auditFlowData = ref([])
const detailLoading = ref(false)
const withdrawList = ref([])

const openRecordsDialog = id => {
  dialogRecordsVisible.value = true
  detailLoading.value = true
  businessBalanceAuditFlowDetail(id)
    .then(res => {
      const createData = {
        createTime: res.data.createTime,
        createUserName: res.data.createUserName,
        amount: res.data.amount,
        withdrawWay: res.data.withdrawWay || '',
        payoutResourceUrlList: res.data.payoutResourceUrlList || [],
        applyRemark: res.data.applyRemark || '',
      }
      const auditData = {
        createTime: res.data.auditTime,
        auditUserName: res.data.auditUserName,
        auditStatus: res.data.auditStatus,
        realAmount: res.data.realAmount,
        payTime: res.data.payTime,
        auditStatus: res.data.auditStatus,
        remark: res.data.remark,
        resourceUrl: res.data.resourceUrl,
        withdrawWay: res.data.withdrawWay || '',
      }
      withdrawList.value = res.data?.businessBalanceDetails || []
      auditFlowData.value = [createData, auditData]
    })
    .finally(() => {
      detailLoading.value = false
    })
}
const closeDialogRecords = () => {
  dialogRecordsVisible.value = false
  auditFlowData.value = []
  withdrawList.value = []
}

const dialogRemarkRow = ref(null)
function openRemarkDialog(row) {
  dialogRemarkRow.value = row
  dialogRemarkVisible.value = true
}

// handleQuery()
</script>

<style scoped lang="scss">
.records-two {
  // display: flex;
  // .records-left {
  //   position: relative;
  //   border-left: 2px solid #e6e6e6;
  //   left: -24px;
  //   top: -11px;
  // }
  .records-right::before {
    content: ' ';
    border-left: 2px solid #e6e6e6;
    position: absolute;
    height: 100%;
    left: 4px;
    top: 13px;
  }
  .records-line {
    margin: 5px 0;
  }
}

.records-title {
  font-size: 18px;
}

.remark-row {
  display: flex;
  align-items: baseline;
}

.remark-label {
  min-width: 100px;
  color: #7f7f7f;
  text-align: right;
}
</style>
