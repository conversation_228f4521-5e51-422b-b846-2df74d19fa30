<template>
  <el-dialog
    v-model="dialogVisible"
    :close-on-press-escape="false"
    width="650px"
    :title="title"
    align-center
    @close="handleClose"
  >
    <div class="flex-column dialog-content" v-loading="dialogLoading">
      <FormItem v-if="dialogType === 'markPayment' || dialogType === 'markAnomaly'" :data="dialogData" />

      <div class="batch-box" v-if="dialogType === 'batchPayment' || dialogType === 'batchAnomaly'">
        <div>
          已选择
          <span>{{ dialogList.length }}</span>
          笔待打款订单
        </div>
        <div>
          已选择待打款订单金额总计：
          <span>{{ totalAmount }}</span>
          元
        </div>
      </div>

      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        style="width: 100%; margin-top: 20px"
      >
        <template v-if="dialogType === 'markPayment' || dialogType === 'batchPayment'">
          <el-form-item label="打款账户" prop="acc">
            <el-select v-model="form.acc" placeholder="请选择" clearable style="width: 80%">
              <el-option v-for="(item, i) in accList" :key="i" :label="item" :value="item" />
            </el-select>
          </el-form-item>
          <el-form-item label="打款时间" prop="time">
            <el-date-picker
              style="width: 80%"
              v-model="form.time"
              format="YYYY/M/D HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              type="datetime"
              placeholder="请选择时间"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="打款凭证" prop="file">
            <PasteUpload
              style="width: 80%"
              v-model="form.file"
              :limit="5"
              :multiple="true"
              :size="3"
              :fileType="['pdf', 'jpg', 'jpeg', 'png']"
              show-file-list
            />
            <div style="width: 100%" v-if="!form.file || form.file.length == 0">
              请上传5份大小不超过
              <span style="color: #d9001b">3M</span>
              ，格式为
              <span style="color: #d9001b">pdf/png/jpg/jpeg</span>
              的文件
            </div>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input
              style="width: 80%"
              v-model="form.remark"
              type="textarea"
              show-word-limit
              maxlength="150"
              placeholder="请输入"
            />
          </el-form-item>
        </template>

        <el-form-item
          v-if="dialogType === 'markAnomaly' || dialogType === 'batchAnomaly'"
          label="异常原因"
          prop="reason"
        >
          <el-input
            v-model="form.reason"
            type="textarea"
            show-word-limit
            maxlength="150"
            :rows="4"
            placeholder="请输入异常原因"
          />
          <div style="color: red">注意：异常原因会展示在种草端</div>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div class="flex-center">
        <el-button v-btn class="btn-width" round plain @click="close">取消</el-button>
        <el-button v-btn class="btn-width" round type="primary" @click="confirm">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import FormItem from '@/views/finance/zhongcao/components/formItem.vue'
import PasteUpload from '@/components/FileUpload/pasteUpload'
import { zhongcaoStatusMap } from '@/views/finance/zhongcao/data'
import {
  getMemberSeedRecordWithdrawalDetail,
  auditMemberSeedRecord,
  getPayAccountList,
} from '@/api/channel/zhongcao.js'

defineExpose({
  open,
  close,
})

const { proxy } = getCurrentInstance()
const emits = defineEmits(['success'])

const accList = ref([])

const dialogVisible = ref(false)
const dialogLoading = ref(false)
const dialogType = ref('')
const dialogData = ref({})
const dialogList = ref([])
const form = ref({
  acc: '',
  time: '',
  file: [],
  remark: '',
  reason: '',
})
const formRef = ref(null)
const rules = {
  acc: [{ required: true, message: '请选择打款账户', trigger: 'change' }],
  time: [{ required: true, message: '请选择打款时间', trigger: 'change' }],
  file: [
    { required: true, message: '请选择打款凭证', trigger: 'change' },
    { type: 'array', min: 1, max: 5, message: '请上传打款凭证', trigger: 'change' },
  ],
  reason: [{ required: true, message: '请输入异常原因', trigger: 'change' }],
}
const title = computed(() => {
  if (dialogType.value === 'markPayment') {
    return '确认已完成打款吗？'
  }
  if (dialogType.value === 'batchPayment') {
    return '确认批量已完成打款吗？'
  }
  if (dialogType.value === 'markAnomaly') {
    return '打款异常？'
  }
  if (dialogType.value === 'batchAnomaly') {
    return '批量打款异常？'
  }
  return ''
})
const totalAmount = computed(() => {
  if (dialogList.value.length) {
    let total = 0
    dialogList.value.forEach(item => {
      if (item.settleAmount) {
        total += item.settleAmount * 100
      }
    })
    return (total / 100).toFixed(2)
  }
  return 0
})

function open(type, data) {
  dialogType.value = type
  if (Array.isArray(data)) {
    dialogList.value = data
  } else {
    getDetail(data.id)
  }
  if (dialogType.value === 'markPayment' || dialogType.value === 'batchPayment') {
    getAccList()
  }
  dialogVisible.value = true
}

function close() {
  dialogVisible.value = false
}
function handleClose() {
  dialogType.value = ''
  dialogData.value = {}
  dialogList.value = []
  form.value = {
    acc: '',
    time: '',
    file: [],
    remark: '',
    reason: '',
  }
}

function getDetail(id) {
  dialogLoading.value = true
  getMemberSeedRecordWithdrawalDetail(id)
    .then(res => {
      if (res.data) {
        dialogData.value = res.data
      }
    })
    .finally(() => {
      dialogLoading.value = false
    })
}
function getAccList() {
  getPayAccountList().then(res => {
    if (res.data) {
      accList.value = res.data
    }
  })
}

function confirm() {
  formRef.value?.validate(valid => {
    if (valid) {
      let hint = ''
      let status
      if (dialogType.value === 'markPayment') {
        hint = '确认已完成打款吗？'
        status = zhongcaoStatusMap['已打款']
      }
      if (dialogType.value === 'batchPayment') {
        hint = '确认批量已完成打款吗？'
        status = zhongcaoStatusMap['已打款']
      }
      if (dialogType.value === 'markAnomaly') {
        hint = '确认打款异常？'
        status = zhongcaoStatusMap['打款异常']
      }
      if (dialogType.value === 'batchAnomaly') {
        hint = '确认批量打款异常？'
        status = zhongcaoStatusMap['打款异常']
      }
      proxy.$modal.confirm(hint, '提示').then(() => {
        let params = {
          ids:
            dialogType.value == 'batchPayment' || dialogType.value == 'batchAnomaly'
              ? dialogList.value.map(item => item.id)
              : [dialogData.value.id],
          remark:
            dialogType.value == 'markAnomaly' || dialogType.value == 'batchAnomaly'
              ? form.value.reason
              : form.value.remark,
          status,
        }
        if (dialogType.value === 'markPayment' || dialogType.value === 'batchPayment') {
          params.payAccount = form.value.acc
          params.payoutTime = form.value.time
          params.resourceUrlList = form.value.file.map(item => item.picUrl)
        }
        // console.log(params)
        proxy.$modal.loading('提交中...')
        auditMemberSeedRecord(params)
          .then(res => {
            proxy.$modal.msgSuccess('操作成功')
            emits('success')
            close()
          })
          .finally(() => {
            proxy.$modal.closeLoading()
          })
      })
    }
  })
}
</script>

<style scoped lang="scss">
.dialog-content {
  padding: 0 20px 20px 0;
  min-height: 280px;
  justify-content: center;

  .batch-box {
    line-height: 32px;
    text-align: center;
    margin-bottom: 20px;

    span {
      color: red;
    }
  }

  .dialog-form-item {
    width: 100%;
    margin-top: 18px;
  }
}
.btn-width {
  width: 100px;
}
</style>
