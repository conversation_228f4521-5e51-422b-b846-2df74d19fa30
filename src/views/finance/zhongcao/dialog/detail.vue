<template>
  <el-dialog
    v-model="dialogVisible"
    :close-on-press-escape="false"
    width="650px"
    :title="title"
    align-center
    @close="handleClose"
  >
    <div class="dialog-content" v-loading="dialogLoading">
      <FormItem :data="dialogData" />

      <el-row v-if="dialogData.status == zhongcaoStatusMap['待打款']">
        <el-col class="flex-start dialog-item" :span="24">
          <div class="item-label">备注：</div>
          <div class="item-content">
            {{ dialogData.auditRemark || '-' }}
          </div>
        </el-col>
      </el-row>
      <el-row v-else-if="dialogData.status == zhongcaoStatusMap['审核不通过']">
        <el-col class="flex-start dialog-item" :span="24">
          <div class="item-label">拒绝理由：</div>
          <div class="item-content">
            {{ dialogData.auditRemark || '-' }}
          </div>
        </el-col>
      </el-row>
      <el-row v-else-if="dialogData.status == zhongcaoStatusMap['打款异常']">
        <el-col class="flex-start dialog-item" :span="24">
          <div class="item-label">异常原因：</div>
          <div class="item-content">
            {{ dialogData.withdrawalRemark || '-' }}
          </div>
        </el-col>
      </el-row>
      <template v-else-if="dialogData.status == zhongcaoStatusMap['已打款']">
        <el-row>
          <el-col class="flex-start dialog-item" :span="12">
            <div class="item-label">打款时间：</div>
            <div class="item-content">
              {{ dialogData.payoutTime || '-' }}
            </div>
          </el-col>
          <el-col class="flex-start dialog-item" :span="12">
            <div class="item-label">打款凭证：</div>
            <div class="item-content">
              <el-button
                v-if="dialogData.resourceUrlList?.length"
                v-btn
                link
                type="primary"
                @click="view"
              >
                查看
              </el-button>
              <span v-else>-</span>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col class="flex-start dialog-item" :span="24">
            <div class="item-label">备注：</div>
            <div class="item-content">
              {{ dialogData.withdrawalRemark || '-' }}
            </div>
          </el-col>
        </el-row>
      </template>
    </div>
    <template #footer>
      <div class="flex-center">
        <el-button v-btn class="btn-width" round plain @click="close">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import FormItem from '@/views/finance/zhongcao/components/formItem.vue'
import { zhongcaoStatusMap } from '@/views/finance/zhongcao/data'
import { useViewer } from '@/hooks/useViewer'
import { getMemberSeedRecordWithdrawalDetail } from '@/api/channel/zhongcao.js'

const { showViewer } = useViewer()

defineExpose({
  open,
  close,
})

const dialogVisible = ref(false)
const dialogLoading = ref(false)
const dialogData = ref({})

const title = computed(() => {
  return `${zhongcaoStatusMap[dialogData.value.status] || ''}详情`
})
function open(data) {
  if (data.id) {
    getDetail(data.id)
    dialogVisible.value = true
  }
}

function close() {
  dialogVisible.value = false
}

function handleClose() {
  dialogData.value = {}
}

function getDetail(id) {
  dialogLoading.value = true
  getMemberSeedRecordWithdrawalDetail(id)
    .then(res => {
      if (res.data) {
        dialogData.value = res.data
      }
    })
    .finally(() => {
      dialogLoading.value = false
    })
}

function view() {
  showViewer(dialogData.value.resourceUrlList)
}
</script>

<style scoped lang="scss">
.dialog-content {
  padding: 0 20px 20px 0;

  .dialog-item {
    min-height: 32px;
    align-items: baseline;

    .item-label {
      color: var(--el-text-color-regular);
      font-size: 14px;
      line-height: 32px;
      flex-shrink: 0;
      width: 120px;
      text-align: right;
      font-weight: 700;
    }
    .item-content {
      font-size: 14px;
      word-break: break-all;
    }
  }
}
.btn-width {
  width: 100px;
}
</style>
