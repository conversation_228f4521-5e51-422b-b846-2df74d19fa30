<template>
  <el-dialog
    v-model="dialogVisible"
    :close-on-press-escape="false"
    width="650px"
    :title="title"
    align-center
    @close="handleClose"
  >
    <div class="flex-column dialog-content" v-loading="dialogLoading">
      <FormItem v-if="dialogType === 'through' || dialogType === 'refuse'" :data="dialogData" />

      <div class="batch-box" v-if="dialogType === 'batchThrough' || dialogType === 'batchRefuse'">
        <div>
          已选择
          <span>{{ dialogList.length }}</span>
          笔待审核订单
        </div>
        <div>
          已选择待审核订单金额总计：
          <span>{{ totalAmount }}</span>
          元
        </div>
      </div>

      <el-form-item
        ref="formItemRef"
        class="dialog-form-item"
        :label="formLabel"
        label-width="120px"
        :required="dialogType === 'refuse' || dialogType === 'batchRefuse'"
      >
        <el-input
          v-model="remark"
          type="textarea"
          show-word-limit
          maxlength="150"
          :rows="4"
          placeholder="请输入"
        />
        <div style="color: red" v-if="dialogType === 'refuse' || dialogType === 'batchRefuse'">
          注意：拒绝理由会展示在种草端
        </div>
      </el-form-item>
    </div>
    <template #footer>
      <div class="flex-center">
        <el-button v-btn class="btn-width" round plain @click="close">取消</el-button>
        <el-button
          v-btn
          v-if="dialogType === 'through' || dialogType === 'batchThrough'"
          class="btn-width"
          round
          type="primary"
          @click="approveThrough"
        >
          审核通过
        </el-button>
        <el-button
          v-btn
          v-if="dialogType === 'refuse' || dialogType === 'batchRefuse'"
          class="btn-width"
          round
          type="primary"
          @click="approveRefuse"
        >
          审核拒绝
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import FormItem from '@/views/finance/zhongcao/components/formItem.vue'
import { zhongcaoStatusMap } from '@/views/finance/zhongcao/data'
import { getMemberSeedRecordWithdrawalDetail, auditMemberSeedRecord } from '@/api/channel/zhongcao.js'

defineExpose({
  open,
  close,
})

const { proxy } = getCurrentInstance()
const emits = defineEmits(['success'])

const dialogVisible = ref(false)
const dialogLoading = ref(false)
const dialogType = ref('')
const dialogData = ref({})
const dialogList = ref([])
const remark = ref('')
const formItemRef = ref(null)
const title = computed(() => {
  if (dialogType.value === 'through' || dialogType.value === 'refuse') {
    return '提现申请审核'
  }
  if (dialogType.value === 'batchThrough' || dialogType.value === 'batchRefuse') {
    return '批量提现申请审核'
  }
  return ''
})
const formLabel = computed(() => {
  if (dialogType.value === 'through' || dialogType.value === 'batchThrough') {
    return '备注'
  }
  if (dialogType.value === 'refuse' || dialogType.value === 'batchRefuse') {
    return '拒绝理由'
  }
  return ''
})
const totalAmount = computed(() => {
  if (dialogList.value.length) {
    let total = 0
    dialogList.value.forEach(item => {
      if (item.settleAmount) {
        total += item.settleAmount * 100
      }
    })
    return (total / 100).toFixed(2)
  }
  return 0
})

function open(type, data) {
  dialogType.value = type
  if (Array.isArray(data)) {
    dialogList.value = data
  } else {
    getDetail(data.id)
  }
  dialogVisible.value = true
}

function close() {
  dialogVisible.value = false
}
function handleClose() {
  formItemRef.value.validateState = ''
  dialogVisible.value = false
  remark.value = ''
  dialogType.value = ''
  dialogData.value = {}
  dialogList.value = []
}

function getDetail(id) {
  dialogLoading.value = true
  getMemberSeedRecordWithdrawalDetail(id)
    .then(res => {
      if (res.data) {
        dialogData.value = res.data
      }
    })
    .finally(() => {
      dialogLoading.value = false
    })
}

function approveThrough() {
  proxy.$modal.confirm('确认审核通过？', '提示').then(() => {
    let params = {
      ids: dialogType.value == 'batchThrough' ? dialogList.value.map(item => item.id) : [dialogData.value.id],
      remark: remark.value,
      status: zhongcaoStatusMap['待打款'],
    }
    proxy.$modal.loading('提交中...')
    auditMemberSeedRecord(params)
      .then(res => {
        proxy.$modal.msgSuccess('操作成功')
        emits('success')
        close()
      })
      .finally(() => {
        proxy.$modal.closeLoading()
      })
  })
}

function approveRefuse() {
  if (!remark.value) {
    formItemRef.value.validateMessage = '请输入拒绝理由'
    formItemRef.value.validateState = 'error'
    return
  }
  proxy.$modal.confirm('确认审核拒绝？', '提示').then(() => {
    let params = {
      ids: dialogType.value == 'batchRefuse' ? dialogList.value.map(item => item.id) : [dialogData.value.id],
      remark: remark.value,
      status: zhongcaoStatusMap['审核不通过'],
    }
    proxy.$modal.loading('提交中...')
    auditMemberSeedRecord(params)
      .then(res => {
        proxy.$modal.msgSuccess('操作成功')
        emits('success')
        close()
      })
      .finally(() => {
        proxy.$modal.closeLoading()
      })
  })
}
</script>

<style scoped lang="scss">
.dialog-content {
  padding: 0 20px 20px 0;
  min-height: 280px;
  justify-content: center;

  .batch-box {
    line-height: 32px;
    text-align: center;
    margin-bottom: 20px;

    span {
      color: red;
    }
  }

  .dialog-form-item {
    width: 100%;
    margin-top: 18px;
  }
  :deep(.el-form-item) {
    &.dialog-form-item {
      .el-form-item__label {
        justify-content: flex-end;
      }
    }
  }
}
.btn-width {
  width: 100px;
}
</style>
