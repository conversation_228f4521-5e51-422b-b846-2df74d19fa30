
const zhongcaoStatus = [
  { label: '待审核', value: 2 },
  { label: '待打款', value: 3 },
  { label: '已打款', value: 4 },
  { label: '审核不通过', value: 5 },
  { label: '打款异常', value: 6 },
]
const zhongcaoStatusMap = {}
zhongcaoStatus.forEach(item => {
  zhongcaoStatusMap[item.value] = item.label
  zhongcaoStatusMap[item.label] = item.value
})

// 渠道端提现方式
const channelWithdrawTypeList = [
  // { label: '微信', value: 1 },
  { label: '支付宝', value: 2 },
  { label: '银行卡', value: 3 },
  // { label: '境外汇款', value: 4 },
  { label: '公户收款', value: 6 },
  // { label: '其他', value: 5 },
]
const channelWithdrawTypeMap = {}
channelWithdrawTypeList.forEach(item => {
  channelWithdrawTypeMap[item.value] = item.label
  channelWithdrawTypeMap[item.label] = item.value
})

export { zhongcaoStatus, zhongcaoStatusMap, channelWithdrawTypeList, channelWithdrawTypeMap }