<template>
  <div class="flex-center statistic-box">
    <div v-for="item in statisticList" :key="item.label" class="flex-column statistic-item">
      <el-statistic :value="item.number" :precision="2"></el-statistic>
      <div class="flex-center tips">
        {{ item.label }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { useTransition } from '@vueuse/core'
import { getFissionStatisticsVO } from '@/api/channel/zhongcao'

const underReviewAmount = ref(0)
const pendingTransferAmount = ref(0)
const withdrawSuccessAmount = ref(0)
const duration = 0

const statisticList = ref([
  { label: '待审核', number: useTransition(underReviewAmount, { duration }) },
  { label: '待打款（元）', number: useTransition(pendingTransferAmount, { duration }) },
  {
    label: '已打款（元）',
    number: useTransition(withdrawSuccessAmount, { duration }),
  },
])

defineExpose({
  load,
})

function load() {
  getFissionStatisticsVO().then(res => {
    if (res.data) {
      underReviewAmount.value = res.data.underReviewAmount || 0
      pendingTransferAmount.value = res.data.pendingTransferAmount || 0
      withdrawSuccessAmount.value = res.data.withdrawSuccessAmount || 0
    }
  })
}

load()
</script>

<style scoped lang="scss">
.statistic-box {
  margin: 20px auto 15px;
  flex-wrap: wrap;
  width: 100%;
  min-width: 950px;
  gap: 20px;

  .statistic-item {
    flex-shrink: 0;
    gap: 8px;
    width: 300px;
    padding: 25px 0px;
    border-radius: 10px;
    box-shadow: var(--el-box-shadow-light);

    .tips {
      font-size: 14px;
      color: #7f7f7f;
    }
  }
  :deep(.el-statistic) {
    .el-statistic__content {
      font-weight: 600;
    }
  }
}
</style>
