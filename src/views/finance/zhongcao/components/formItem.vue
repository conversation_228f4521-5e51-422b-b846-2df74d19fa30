<template>
  <div class="form-item-box">
    <el-row>
      <el-col class="flex-start item" :span="24">
        <div class="item-label">{{ data.status != zhongcaoStatusMap['已打款'] ? '申请' : '' }}结算金额：</div>
        <div class="item-content" style="color: red">{{ data.settleAmount || '-' }}元</div>
      </el-col>
    </el-row>
    <el-row>
      <el-col class="flex-start item" :span="12">
        <div class="item-label">申请人：</div>
        <div class="item-content" v-if="data.channelType == 2">
          {{ data.channelName || '-' }}
        </div>
        <div class="item-content" v-else-if="data.channelType == 7">
          微信：{{ data.applicantNickName || '-' }}&emsp;员工姓名：{{ data.applicantName || '-' }}
        </div>
      </el-col>
      <el-col class="flex-start item" :span="12" v-if="data.channelType == 7">
        <div class="item-label">公司名称：</div>
        <div class="item-content" v-if="data.applicantBusinessName">
          {{ data.applicantBusinessName + (data.applicantMemberCode ? `(${data.applicantMemberCode})` : '') }}
        </div>
        <div class="item-content" v-else>-</div>
      </el-col>
    </el-row>
    <el-row>
      <el-col class="flex-start item" :span="12">
        <div class="item-label">收款方式：</div>
        <div class="item-content">
          {{ channelWithdrawTypeMap[data.withdrawalAccountType] || '-' }}
        </div>
      </el-col>
      <el-col class="flex-start item" :span="12" v-if="channelWithdrawTypeMap['公户收款'] == data.withdrawalAccountType">
        <div class="item-label">收款方手机号：</div>
        <div class="item-content">
          {{ data.payeePhone || '-' }}
        </div>
      </el-col>
      <el-col class="flex-start item" :span="12" v-else>
        <div class="item-label">{{ channelWithdrawTypeMap['支付宝'] == data.withdrawalAccountType ? '支付宝账号' : '银行卡账号' }}：</div>
        <div class="item-content">
          {{ data.payeeAccount || '-' }}
        </div>
      </el-col>
    </el-row>
    <el-row  v-if="channelWithdrawTypeMap['公户收款'] == data.withdrawalAccountType">
      <el-col class="flex-start item" :span="12">
        <div class="item-label">收款方公司名称：</div>
        <div class="item-content">
          {{ data.payeeName || '-' }}
        </div>
      </el-col>
      <el-col class="flex-start item" :span="12">
        <div class="item-label">收款方银行账号：</div>
        <div class="item-content">
          {{ data.payeeAccount || '-' }}
        </div>
      </el-col>
    </el-row>
    <el-row v-else>
      <el-col class="flex-start item" :span="12">
        <div class="item-label">收款方姓名：</div>
        <div class="item-content">
          {{ data.payeeName || '-' }}
        </div>
      </el-col>
      <el-col class="flex-start item" :span="12">
        <div class="item-label">收款方手机号：</div>
        <div class="item-content">
          {{ data.payeePhone || '-' }}
        </div>
      </el-col>
    </el-row>
    <el-row>
      <el-col class="flex-start item" :span="24" v-if="channelWithdrawTypeMap['公户收款'] == data.withdrawalAccountType ">
        <div class="item-label">开户行：</div>
        <div class="item-content">
          {{ data.bankName || '-' }}
        </div>
      </el-col>
      <el-col class="flex-start item" :span="24" v-else>
        <div class="item-label">收款方身份证号：</div>
        <div class="item-content">
          {{ data.payeeIdentityCard || '-' }}
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { zhongcaoStatusMap, channelWithdrawTypeMap } from '@/views/finance/zhongcao/data'

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
})
</script>

<style lang="scss" scoped>
.form-item-box {
  width: 100%;

  .item {
    min-height: 32px;
    align-items: baseline;
  
    .item-label {
      color: var(--el-text-color-regular);
      font-size: 14px;
      line-height: 32px;
      flex-shrink: 0;
      width: 120px;
      text-align: right;
      font-weight: 700;
    }
    .item-content {
      font-size: 14px;
      word-break: break-all;
    }
  }
}
</style>
