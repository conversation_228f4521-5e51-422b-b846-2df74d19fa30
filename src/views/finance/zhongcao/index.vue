<template>
  <div class="zhongcao-page" style="padding-bottom: 45px;">
    <Statistic :key="statisticKey" />
    <el-tabs v-model="curTab" class="tabs" @tab-change="resetQuery">
      <el-tab-pane v-for="(tab, i) in tabList" :key="tab.value" :name="tab.value">
        <template #label>
          <div>
            {{ tab.label }}
            {{ '(' }}
            <span style="color: red">{{ tab.number }}</span>
            {{ ')' }}
          </div>
        </template>
      </el-tab-pane>
    </el-tabs>

    <ElTablePage
      style="padding: 20px"
      ref="tableRef"
      :columns="columns"
      :data="tableData"
      :loading="tableLoading"
      :currentPage="pageNum"
      :pageSize="pageSize"
      :total="total"
      :tableAction="{
        width: '170',
        fixed: 'right',
      }"
      :tableOptions="{
        border: true,
      }"
      row-key="id"
      :key="tableKey"
      @page-change="pageChange"
      @selection-change="selectionChange"
    >
      <template #tableHeader>
        <div>
          <el-form
            class="flex-start"
            style="flex-wrap: wrap"
            :model="queryParams"
            ref="queryRef"
            :inline="true"
            label-width="68px"
            @submit.prevent
          >
            <el-form-item label="搜索" label-width="40px">
              <el-input
                v-model="queryParams.keyword"
                clearable
                style="width: 280px"
                placeholder="请输入微信名/姓名/公司名称/会员编码等"
              />
            </el-form-item>
            <el-form-item label="会员类型" prop="setMealType">
              <el-select
                v-model="queryParams.setMealType"
                placeholder="请选择会员类型"
                clearable
                style="width: 180px"
              >
                <el-option
                  v-for="item in setMealTypeList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="结算来源" prop="channelType">
              <el-select
                v-model="queryParams.channelType"
                placeholder="请选择"
                clearable
                style="width: 180px"
              >
                <el-option
                  v-for="item in settlementChannelTypeList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="提现状态" prop="withdrawStatus" v-if="curTab == -1">
              <el-select
                v-model="queryParams.withdrawStatus"
                placeholder="请选择"
                multiple
                collapse-tags
                collapse-tags-tooltip
                clearable
                style="width: 180px"
              >
                <el-option
                  v-for="item in zhongcaoStatus"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="收款方式" v-if="curTab == -1">
              <el-select
                v-model="queryParams.withdrawalAccountType"
                placeholder="请选择"
                clearable
                style="width: 180px"
              >
                <el-option
                  v-for="item in channelWithdrawTypeList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="申请时间" style="width: 400px">
              <el-date-picker
                v-model="queryParams.applyTime"
                format="YYYY/M/D HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="datetimerange"
                range-separator="-"
                unlink-panels
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
              ></el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button v-btn type="primary" icon="Search" native-type="submit" @click="onQuery">
                搜索
              </el-button>
              <el-button v-btn icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>

          <div class="flex-start" style="margin-bottom: 10px">
            <el-button
              v-btn
              v-if="
                checkPermi(['finance:zhongcao:batchThrough']) &&
                (curTab == -1 || curTab == zhongcaoStatusMap['待审核'])
              "
              type="primary"
              :disabled="batchDisabled1"
              @click="openApproveDialog('batchThrough', multipleSelection)"
            >
              批量通过
            </el-button>
            <el-button
              v-btn
              v-if="
                checkPermi(['finance:zhongcao:batchRefuse']) &&
                (curTab == -1 || curTab == zhongcaoStatusMap['待审核'])
              "
              type="primary"
              :disabled="batchDisabled1"
              @click="openApproveDialog('batchRefuse', multipleSelection)"
            >
              批量拒绝
            </el-button>
            <el-button
              v-btn
              v-if="
                checkPermi(['finance:zhongcao:batchPayment']) &&
                (curTab == -1 || curTab == zhongcaoStatusMap['待打款'])
              "
              type="primary"
              :disabled="batchDisabled2"
              @click="openMarkDialog('batchPayment', multipleSelection)"
            >
              批量打款
            </el-button>
            <el-button
              v-btn
              v-if="
                checkPermi(['finance:zhongcao:batchAnomaly']) &&
                (curTab == -1 || curTab == zhongcaoStatusMap['待打款'])
              "
              type="primary"
              :disabled="batchDisabled2"
              @click="openMarkDialog('batchAnomaly', multipleSelection)"
            >
              批量异常
            </el-button>
          </div>
        </div>
      </template>
      <template #info="{ row }">
        <SeedInfo :row="row" />
      </template>
      <template #orderNums="{ row }">
        <div v-if="row.orderNumList?.length">
          <div v-for="item in row.orderNumList.slice(0, 5)">{{ item }}</div>
          <el-tooltip v-if="row.orderNumList?.length > 5" effect="dark" placement="top" trigger="click">
            <template #content>
              <div v-for="item in row.orderNumList">{{ item }}</div>
            </template>
            <el-button v-btn link type="primary">查看更多</el-button>
          </el-tooltip>
        </div>
        <div v-else>-</div>
      </template>
      <template #receiptInfo="{ row }">
        <ReceiptInfo :row="row" />
      </template>
      <template #statusInfo="{ row }">
        <div>{{ zhongcaoStatusMap[row.status] || '-' }}</div>
        <div
          v-if="row.status == zhongcaoStatusMap['待打款'] || row.status == zhongcaoStatusMap['审核不通过']"
        >
          {{ row.auditUserName }}
        </div>
        <div v-if="row.status == zhongcaoStatusMap['已打款'] || row.status == zhongcaoStatusMap['打款异常']">
          {{ row.withdrawalUserName }}
        </div>
        <div>{{ row.updateTime }}</div>
      </template>

      <template #tableAction="{ row }">
        <template v-if="row.status == zhongcaoStatusMap['待审核']">
          <el-button
            v-btn
            v-if="checkPermi(['finance:zhongcao:through'])"
            link
            type="primary"
            @click="openApproveDialog('through', row)"
          >
            通过
          </el-button>
          <el-button
            v-btn
            v-if="checkPermi(['finance:zhongcao:refuse'])"
            link
            type="primary"
            @click="openApproveDialog('refuse', row)"
          >
            拒绝
          </el-button>
        </template>
        <template v-if="row.status == zhongcaoStatusMap['待打款']">
          <el-button
            v-btn
            v-if="checkPermi(['finance:zhongcao:markPayment'])"
            link
            type="primary"
            @click="openMarkDialog('markPayment', row)"
          >
            标记打款
          </el-button>
          <el-button
            v-btn
            v-if="checkPermi(['finance:zhongcao:markAnomaly'])"
            link
            type="primary"
            @click="openMarkDialog('markAnomaly', row)"
          >
            标记异常
          </el-button>
        </template>
        <el-button
          v-btn
          v-if="row.status != zhongcaoStatusMap['待审核'] && checkPermi(['finance:zhongcao:detail'])"
          link
          type="primary"
          @click="openDetailDialog(row)"
        >
          查看详情
        </el-button>
      </template>
      <template #pageLeft>
        <div style="margin-right: 15px;">
          <DownloadBtn
            type="success"
            plain
            icon="Download"
            url="/biz/member-seed-record/backend/queryMemberSeedRecordWithdrawalList/export"
            :params="handleParams"
            v-hasPermi="['finance:zhongcao:export']"
          />
        </div>
      </template>
    </ElTablePage>

    <ApproveDialog ref="ApproveDialogRef" @success="onQuery" />
    <DetailDialog ref="DetailDialogRef" />
    <MarkDialog ref="MarkDialogRef" @success="onQuery" />
  </div>
</template>

<script setup>
import ElTablePage from '@/components/Table/ElTablePage'
import DownloadBtn from '@/components/Button/DownloadBtn'
import Statistic from '@/views/finance/zhongcao/components/statistic.vue'
import ApproveDialog from '@/views/finance/zhongcao/dialog/approve.vue'
import DetailDialog from '@/views/finance/zhongcao/dialog/detail.vue'
import MarkDialog from '@/views/finance/zhongcao/dialog/mark.vue'
import SeedInfo from '@/views/finance/components/tableColumns/seedInfo.vue'
import ReceiptInfo from '@/views/finance/components/tableColumns/receiptInfo.vue'
import { setMealTypeList } from '@/views/finance/data.js'
import { settlementChannelTypeList } from '@/views/finance/incomeInfo/data'
import { zhongcaoStatus, zhongcaoStatusMap, channelWithdrawTypeList, channelWithdrawTypeMap } from '@/views/finance/zhongcao/data'
import { checkPermi } from '@/utils/permission'
import { getZhongCaoWithdrawalList, getFissionCountStatisticsVO } from '@/api/channel/zhongcao.js'

const statisticKey = ref(0)
const curTab = ref(-1)
const tabList = ref([
  { label: '所有', value: -1, number: 0 },
  { label: '待审核', value: zhongcaoStatusMap['待审核'], number: 0 },
  { label: '待打款', value: zhongcaoStatusMap['待打款'], number: 0 },
  { label: '已打款', value: zhongcaoStatusMap['已打款'], number: 0 },
  { label: '审核不通过', value: zhongcaoStatusMap['审核不通过'], number: 0 },
  { label: '打款异常', value: zhongcaoStatusMap['打款异常'], number: 0 },
])

const columns = [
  {
    type: 'selection',
    'reserve-selection': true,
    width: '55',
    selectable: (row, index) =>
      row.status == zhongcaoStatusMap['待审核'] || row.status == zhongcaoStatusMap['待打款'],
  },
  { prop: 'withdrawalNum', label: '提现单号', width: '150' },
  { slot: 'info', prop: 'info', label: '申请人', minWidth: '250' },
  { prop: 'channelType', label: '结算来源', width: '110', handle: data => {
    let item = settlementChannelTypeList.find(item => item.value == data)
    return item ? item.label : '-'
  }},
  { slot: 'orderNums', prop: 'orderNumList', label: '包含订单', width: '250' },
  { prop: 'settleAmountGroup', label: '申请结算金额', width: '150' },
  { prop: 'createTime', label: '申请时间', width: '180' },
  { slot: 'receiptInfo', prop: 'receiptInfo', label: '收款信息', minWidth: '220' },
  { slot: 'statusInfo', prop: 'status', label: '提现状态', width: '180' },
]

const tableRef = ref()
const tableKey = ref(0)
const tableData = ref([])
const pageNum = ref(1)
const pageSize = ref(20)
const total = ref(0)
const tableLoading = ref(false)
const multipleSelection = ref([])

const ApproveDialogRef = ref()
const DetailDialogRef = ref()
const MarkDialogRef = ref()

const batchDisabled1 = computed(() => {
  if (
    multipleSelection.value.length &&
    multipleSelection.value.every(item => item.status == zhongcaoStatusMap['待审核'])
  ) {
    return false
  }
  return true
})
const batchDisabled2 = computed(() => {
  if (
    multipleSelection.value.length &&
    multipleSelection.value.every(item => item.status == zhongcaoStatusMap['待打款'])
  ) {
    return false
  }
  return true
})

const queryParams = ref({
  keyword: '',
  setMealType: '',
  channelType: '',
  withdrawStatus: [],
  applyTime: [],
  withdrawalAccountType: '',
})

function handleParams() {
  let params = {
    keyword: queryParams.value.keyword.trim(),
    memberPackageType: queryParams.value.setMealType,
    channelType: queryParams.value.channelType,
    withdrawalAccountType: queryParams.value.withdrawalAccountType,
  }

  params.memberSeedRecordWithdrawalStatusList =
    curTab.value == -1 ? queryParams.value.withdrawStatus : [curTab.value]

  if (queryParams.value.applyTime && queryParams.value.applyTime.length == 2) {
    params.startTime = queryParams.value.applyTime[0]
    params.endTime = queryParams.value.applyTime[1]
  }
  return params
}

function onQuery() {
  tableRef.value?.clearSelection()
  multipleSelection.value = []
  pageNum.value = 1
  handleQuery()
}

function resetQuery() {
  tableRef.value?.clearSelection()
  tableKey.value++
  multipleSelection.value = []
  queryParams.value = {
    keyword: '',
    setMealType: '',
    channelType: '',
    withdrawStatus: [],
    applyTime: [],
    withdrawalAccountType: '',
  }
  onQuery()
}

function handleQuery() {
  tableLoading.value = true
  const params = {
    pageNum: pageNum.value,
    pageSize: pageSize.value,
    ...handleParams(),
  }
  getZhongCaoWithdrawalList(params)
    .then(res => {
      if (res.data) {
        tableData.value = res.data.rows
        total.value = res.data.total
      }
    })
    .finally(() => {
      tableLoading.value = false
    })
  getStatistics()
  statisticKey.value++
}

function getStatistics() {
  getFissionCountStatisticsVO().then(res => {
    if (res.data) {
      setStatistics(res.data)
    }
  })
}

function setStatistics(data) {
  tabList.value[0].number = data.totalCount
  tabList.value[1].number = data.underReviewCount
  tabList.value[2].number = data.pendingTransferCount
  tabList.value[3].number = data.withdrawSuccessCount
  tabList.value[4].number = data.reviewRejectedCount
  tabList.value[5].number = data.transferExceptionCount
}

function selectionChange(val) {
  multipleSelection.value = val
}

function pageChange(page) {
  pageNum.value = page.currentPage
  pageSize.value = page.pageSize
  handleQuery()
}
// 审核
function openApproveDialog(type, row) {
  ApproveDialogRef.value?.open(type, row)
}
// 详情
function openDetailDialog(row) {
  DetailDialogRef.value?.open(row)
}
// 标记打款/异常
function openMarkDialog(type, row) {
  MarkDialogRef.value?.open(type, row)
}

function init() {
  onQuery()
}
init()
</script>

<style scoped lang="scss">
.zhongcao-page {
  .tabs {
    padding: 0 20px;
  }
}
</style>
