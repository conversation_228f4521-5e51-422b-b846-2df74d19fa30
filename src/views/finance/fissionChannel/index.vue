<template>
  <div style="margin: 20px">
    <ElTablePage
      ref="tableRef"
      :columns="columns"
      :data="tableData"
      :loading="tableLoading"
      :currentPage="currentPage"
      :pageSize="pageSize"
      :total="total"
      :tableAction="{
        width: '130',
        fixed: 'right',
      }"
      @page-change="pageChange"
      row-key="id"
    >
      <template #tableHeader>
        <div>
          <el-form :inline="true" :model="queryParams" @submit.prevent>
            <el-form-item label="搜索">
              <el-input
                v-model="queryParams.keyword"
                style="width: 300px"
                clearable
                placeholder="请输入微信名/姓名/公司名称/会员编码"
              ></el-input>
            </el-form-item>
            <el-form-item label="会员类型">
              <el-select v-model="queryParams.memberPackageType" placeholder="请选择" clearable>
                <el-option
                  v-for="dict in vipTypeList"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="状态">
              <el-select
                v-model="queryParams.settleStatus"
                multiple
                collapse-tags
                collapse-tags-tooltip
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="dict in settleAccountsStatus.filter(item => item.value != 7 && item.value != 999)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="打款时间" prop="withdrawalTime">
              <el-date-picker
                style="width: 350px"
                v-model="queryParams.withdrawalTime"
                format="YYYY/M/D HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="datetimerange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
              ></el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button v-btn type="primary" icon="Search" native-type="submit" @click="onQuery">
                搜索
              </el-button>
              <el-button v-btn icon="Refresh" @click="resetQuery">重置</el-button>
              <!-- <DownloadBtn
                v-hasPermi="['fission:channel:export']"
                type="success"
                plain
                icon="Download"
                url="biz/member-channel/fission/member-channel-list/export"
                :params="queryParams"
              /> -->
            </el-form-item>
          </el-form>
        </div>
      </template>
      <template #nickName="{ row }">
        <div>
          <div>微信：{{ row.fissionNickname || '-' }}/姓名：{{ row.fissionAccountName || '-' }}</div>
          <div>{{ row.fissionBusinessName || '-' }}({{ row.fissionMemberCode || '-' }})</div>
        </div>
      </template>
      <template #bizUserId="{ row }">
        <div>
          <div>微信：{{ row.bizUserNickName || '-' }}</div>
          <div>{{ row.businessName || '-' }}({{ row.memberCode || '-' }})</div>
        </div>
      </template>
      <template #status="{ row }">
        <div v-if="row.settleStatus == null">-</div>
        <template v-if="row.settleStatus == 999">
          <el-tag type="warning">可提现</el-tag>
          <div style="font-size: 12px;">暂不可提现</div>
        </template>
        <template v-for="item in settleAccountsStatus">
          <el-tag v-if="row.settleStatus == item.value && item.value != 999" :type="item.type">{{ item.label }}</el-tag>
        </template>
        <div v-if="row.settleStatus == 4">{{ row.settleTime }}</div>
      </template>
      <template #realSettleAmount="{ row }">
        <div v-if="(row.realSettleAmount || row.realSettleAmount == 0) && row.settleStatus == 4">
          {{ row.realSettleAmount }}
        </div>
        <div v-else>-</div>
      </template>
      <template #tableAction="{ row }">
        <el-button
          v-btn
          v-hasPermi="['fission:channel:records']"
          link
          type="primary"
          @click="openRecordsDialog(row.id)"
        >
          查看
        </el-button>
        <!-- <el-button
          v-btn
          v-if="row.settleStatus == 0"
          v-hasPermi="['fission:channel:settlement']"
          link
          type="primary"
          @click="openConfirmDialog(row)"
        >
          结算
        </el-button> -->
      </template>
      <template #pageLeft>
        <DownloadBtn
          v-hasPermi="['fission:channel:export']"
          type="success"
          plain
          icon="Download"
          url="biz/member-channel/fission/member-channel-list/export"
          :params="handleParams"
        />
      </template>
    </ElTablePage>
    <!-- 渠道结算弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      width="500px"
      :close-on-press-escape="false"
      title="裂变结算"
      align-center
      @close="handleClose"
    >
      <el-form
        ref="dialogFormRef"
        :model="dialogForm"
        :rules="rules"
        label-width="120px"
        style="margin-top: 10px"
      >
        <el-form-item label="应结算金额:" prop="amount" required>
          <div>{{ dialogForm.amount }}元</div>
        </el-form-item>
        <el-form-item label="实际结算金额:" prop="realSettleAmount">
          <el-input-number
            title=""
            placeholder="请输入实际结算金额"
            v-model="dialogForm.realSettleAmount"
            :min="0"
            :max="999999"
            :precision="2"
            :step="1"
            :controls="false"
            style="width: 300px"
            @keydown="channelInputLimit"
          ></el-input-number>
        </el-form-item>
        <el-form-item label="结算时间" prop="settleTime">
          <el-date-picker
            v-model="dialogForm.settleTime"
            style="width: 300px"
            type="datetime"
            placeholder="选择日期"
            format="YYYY-M-D HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="结算凭证" prop="file">
          <!-- <PictureCardUpload
              v-model="dialogForm.file"
              :limit="1"
              @change="handleFileChange"
              bucketType="credit"
            /> -->
          <ViewerImageList
            v-if="dialogForm.file.length > 0"
            :data="dialogForm.file"
            is-preview-all
            :show-delete-btn="true"
            :urlName="'picUrl'"
            @delete="handleFileDelete"
          />
          <PasteUpload
            v-else
            :limit="1"
            :bucketType="'credit'"
            style="width: 300px"
            :size="5"
            @success="handleFileChange"
          />
          <div style="width: 100%" v-if="!dialogForm.file || dialogForm.file.length == 0">
            请上传1张大小不超过
            <span style="color: #d9001b">5M</span>
            ，格式为
            <span style="color: #d9001b">png/jpg/jpeg</span>
            的图片
          </div>
        </el-form-item>
        <el-form-item label="备注:" prop="remark">
          <el-input
            v-model="dialogForm.remark"
            style="width: 300px"
            :rows="4"
            maxlength="100"
            show-word-limit
            type="textarea"
            placeholder=""
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="flex-center gap-10">
          <el-button v-btn class="btn-width" round @click="dialogVisible = false">取消</el-button>
          <el-button v-btn class="btn-width" round type="primary" @click="confirmSettlement">确定</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 查看结算记录弹窗 -->
    <el-dialog
      v-model="dialogRecordsVisible"
      :close-on-press-escape="false"
      width="600px"
      title="结算记录"
      align-center
    >
      <el-timeline style="max-width: 600px; min-height: 250px" v-loading="detailLoading">
        <el-timeline-item
          placement="top"
          v-for="(item, index) in recordsData"
          :key="index"
          :timestamp="parseTime(item.operationTime, '{y}-{m}-{d} {h}:{i}')"
        >
          <div v-if="index == 0">
            <!-- <div class="records-title">
              会员编码：{{ item.memberCode }}，{{ item.businessName }}
              <span v-if="item.businessName">，</span>
              支付{{ item.realPayAmount }}元购买
              <template v-for="s in vipTypeList">
                <span v-if="s.value == item.memberPackageType">{{ s.label }}</span>
              </template>
            </div> -->
            <div class="records-title">
              会员编码：{{ item.memberCode }}，{{ item.businessName }}
              <span v-if="item.businessName">，</span>
              <span v-if="item.payType == 7 || item.payType == 17">
                支付{{ item.realPayAmount }}元({{
                  item.currency == '1'
                    ? '人民币'
                    : sys_money_type.find(data => data.value == item.currency)?.label
                }}支付{{ item.realPayAmountCurrency }})购买
              </span>
              <span v-else>支付{{ item.realPayAmount }}元购买</span>
              <template v-for="s in vipTypeList">
                <span v-if="s.value == item.memberPackageType">{{ s.label }}</span>
              </template>
            </div>
            <div style="margin: 5px 0">裂变渠道：{{ item.channelName }}</div>
          </div>
          <div class="records-two" v-else>
            <div class="records-right">
              <div class="records-title">成功结算{{ item.realSettleAmount }}元</div>
              <div class="records-line">处理人：{{ item.settleUserName }}</div>
              <div class="records-line">
                结算时间：{{ parseTime(item.settleTime, '{y}-{m}-{d} {h}:{i}') }}
              </div>
              <div style="display: flex">
                结算凭证：
                <el-image
                  v-if="item.settleResourceUrl"
                  :src="$picUrl + item.settleResourceUrl + '!1x1compress'"
                  style="width: 100px; height: 100px"
                  @click="showViewer([$picUrl + item.settleResourceUrl])"
                />
                <div v-else>暂无凭证</div>
              </div>
              <div class="records-line" style="display: flex">
                <div style="min-width: 42px">备注：</div>
                <div class="more-ell" style="--l: 9">{{ item.remark }}</div>
              </div>
            </div>
          </div>
        </el-timeline-item>
      </el-timeline>
      <template #footer>
        <div class="flex-center">
          <el-button v-btn class="btn-width" round type="primary" @click="closeDialogRecords">确认</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup>
import ElTablePage from '@/components/Table/ElTablePage.vue'
import DownloadBtn from '@/components/Button/DownloadBtn.vue'
import { settleAccountsStatus } from '@/views/finance/data.js'
import {
  getMemberChannelLists,
  getMemberChannelDetailss,
  memberChannelSettlement,
} from '@/api/channel/fission.js'
import PasteUpload from '@/components/FileUpload/pasteUpload.vue'
import ViewerImageList from '@/components/ImagePreview/viewerImageList.vue'
import { parseTime } from '@/utils/ruoyi'
import { useViewer } from '@/hooks/useViewer'
const { showViewer } = useViewer()

const { proxy } = getCurrentInstance()
const { sys_money_type } = proxy.useDict('sys_money_type')

const tableData = ref([])
const tableLoading = ref(false)
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const queryParams = ref({
  keyword: '',
  memberPackageType: [],
  settleStatus: [],
  withdrawalTime: [],
  isFiltrationZero: 1,
})
const vipTypeList = [
  { label: '年度会员', value: 1 },
  { label: '三年会员', value: 2 },
]
const columns = ref([
  {
    label: '种草官信息',
    slot: 'nickName',
    width: '250',
  },
  {
    label: '邀请商家信息',
    slot: 'bizUserId',
    minWidth: '200',
  },
  {
    label: '会员类型',
    prop: 'memberPackageType',
    width: '150',
    handle: val => {
      let label = vipTypeList.find(item => item.value == val)?.label
      return label || '-'
    },
  },
  {
    label: '购买时间',
    prop: 'createTime',
    minWidth: '200',
  },
  {
    label: '结算方案',
    prop: 'settleRage',
    width: '150',
    handle: (val, row) => {
      if (row.settleType == 1) {
        return `${val}元（固定金额）`
      }
      if (row.settleType == 2) {
        return `${val}%（固定比例）`
      }
      return '-'
    },
  },
  {
    label: '结算金额',
    prop: 'settleAmount',
    width: '130',
  },
  {
    label: '实际结算金额',
    width: '150',
    slot: 'realSettleAmount',
  },
  {
    slot: 'status',
    label: '状态',
    width: '140',
    prop: 'settleStatus',
  },
])

const dialogVisible = ref(false)
const dialogForm = ref({
  realSettleAmount: '',
  ids: [],
  amount: '',
  settleTime: '',
  file: [],
  remark: '',
})

const dialogRecordsVisible = ref(false)

const rules = {
  realSettleAmount: [{ required: true, message: '请输入实际结算金额', trigger: 'blur' }],
  settleTime: [{ required: true, message: '请选择结算时间', trigger: 'change' }],
  file: [{ required: true, message: '请上传凭证', trigger: 'change' }],
}

// const handleFileChange = () => {
//   proxy.$refs['dialogFormRef'].validateField('file')
// }
const handleFileChange = data => {
  if (data && data.length > 0) {
    dialogForm.value.file = [data[0].data || '']
  }
  // proxy.$refs['dialogFormRef'].validateField('file')
}

const handleFileDelete = index => {
  dialogForm.value.file.splice(index, 1)
}

const handleClose = () => {
  proxy.$refs['dialogFormRef'].resetFields()
}

const confirmSettlement = () => {
  proxy.$refs['dialogFormRef'].validate(valid => {
    if (valid) {
      proxy.$modal.loading('执行中...')
      memberChannelSettlement({
        ids: dialogForm.value.ids,
        settleTime: dialogForm.value.settleTime,
        remark: dialogForm.value.remark,
        settleResourceUrl: dialogForm.value.file[0].picUrl,
        realSettleAmount: dialogForm.value.realSettleAmount,
      })
        .then(res => {
          proxy.$modal.msgSuccess('操作成功')
          dialogVisible.value = false
          handleQuery()
        })
        .finally(() => proxy.$modal.closeLoading())
    }
  })
}

const onQuery = () => {
  currentPage.value = 1
  handleQuery()
}
function handleParams() {
  let { withdrawalTime, ...params } = queryParams.value
  if (withdrawalTime && withdrawalTime.length == 2) {
    params.withdrawalTimeStart = withdrawalTime[0]
    params.withdrawalTimeEnd = withdrawalTime[1]
  }
  return params
}
const handleQuery = () => {
  tableLoading.value = true
  getMemberChannelLists({
    ...handleParams(),
    pageNum: currentPage.value,
    pageSize: pageSize.value,
  }).then(res => {
    tableData.value = res.data.rows
    total.value = res.data.total
    tableLoading.value = false
  })
}
const resetQuery = () => {
  queryParams.value = {
    keyword: '',
    memberPackageType: [],
    settleStatus: [],
    withdrawalTime: [],
    isFiltrationZero: 1,
  }
  currentPage.value = 1
  handleQuery()
}

const pageChange = page => {
  currentPage.value = page.currentPage
  pageSize.value = page.pageSize
  handleQuery()
}

const openConfirmDialog = row => {
  dialogVisible.value = true
  dialogForm.value = {
    ids: [row.id],
    amount: row.settleAmount,
    settleTime: '',
    file: [],
    remark: '',
  }
}

const recordsData = ref([])
const detailLoading = ref(false)

const openRecordsDialog = id => {
  dialogRecordsVisible.value = true
  detailLoading.value = true
  getMemberChannelDetailss(id)
    .then(res => {
      const data1 = {
        memberCode: res.data.memberCode,
        businessName: res.data.businessName,
        realPayAmount: res.data.realPayAmount,
        memberPackageType: res.data.memberPackageType,
        channelName: res.data.channelName,
        operationTime: res.data.createTime,
        payType: res.data.payType,
        currency: res.data.currency,
        realPayAmountCurrency: res.data.realPayAmountCurrency,
        realSettleAmount: res.data.realSettleAmount,
      }
      recordsData.value = [data1]
      if (res.data.settleStatus == 1) {
        recordsData.value.push(res.data)
      }
    })
    .finally(() => {
      detailLoading.value = false
    })
}
const closeDialogRecords = () => {
  dialogRecordsVisible.value = false
  recordsData.value = []
}
const channelInputLimit = e => {
  const key = e.key
  if (key === 'Enter') e.preventDefault()
  const notAllowList = ['e', '+', '-']
  if (notAllowList.includes(key)) {
    e.returnValue = false
    return false
  }
  return true
}

handleQuery()
</script>

<style scoped lang="scss">
:deep(.el-input-number) {
  .el-input__inner {
    text-align: left;
  }
}
.btn-width {
  width: 100px;
}
.records-two {
  .records-right::before {
    content: ' ';
    border-left: 2px solid #e6e6e6;
    position: absolute;
    height: 100%;
    left: 4px;
    top: 13px;
  }
  .records-line {
    margin: 5px 0;
  }
}

.records-title {
  font-size: 18px;
}
</style>
