<template>
  <div style="padding: 20px">
    <el-tabs v-model="curTab" class="tabs" @tab-change="handleTabChange">
      <el-tab-pane :name="1" label="订单明细" v-if="checkPermi(['finance:order:list'])">
        <OrderPage ref="OrderPageRef" @action="handleAction" />
      </el-tab-pane>
      <el-tab-pane :name="2" label="视频明细" v-if="checkPermi(['finance:video:list'])">
        <VideoPage ref="VideoPageRef" />
      </el-tab-pane>
      <el-tab-pane :name="3" label="退款记录" v-if="checkPermi(['finance:refund:list'])">
        <RefundPage ref="RefundPageRef" />
      </el-tab-pane>
      <el-tab-pane :name="4" label="入驻会员" v-if="checkPermi(['finance:enter:list'])">
        <EnterPage ref="EnterPageRef" @action="handleAction" />
      </el-tab-pane>
      <el-tab-pane :name="5" label="提现明细" v-if="checkPermi(['finance:withdraw:list'])">
        <WithdrawPage ref="WithdrawPageRef" />
      </el-tab-pane>
      <el-tab-pane :name="6" label="种草结算记录" v-if="checkPermi(['finance:fissionRecord:list'])">
        <FissonPage ref="FissonPageRef" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import VideoPage from '@/views/finance/incomeInfo/components/videoPage.vue'
import OrderPage from '@/views/finance/incomeInfo/components/orderPage.vue'
import RefundPage from '@/views/finance/incomeInfo/components/refundPage.vue'
import EnterPage from '@/views/finance/incomeInfo/components/enterPage.vue'
import WithdrawPage from '@/views/finance/incomeInfo/components/withdrawPage.vue'
import FissonPage from '@/views/finance/incomeInfo/components/fissionPage.vue'
import { onMounted } from 'vue'
import { checkPermi } from '@/utils/permission'

const curTab = ref(1)

const OrderPageRef = ref()
const VideoPageRef = ref()
const RefundPageRef = ref()
const EnterPageRef = ref()
const WithdrawPageRef = ref()
const FissonPageRef = ref()

// tab切换
function handleTabChange() {
  if (curTab.value == 1) {
    OrderPageRef.value?.onQuery()
  } else if (curTab.value == 2) {
    VideoPageRef.value?.handleTabChangeQuery()
  } else if (curTab.value == 3) {
    RefundPageRef.value?.handleTabChangeQuery()
  } else if (curTab.value == 4) {
    EnterPageRef.value?.handleTabChangeQuery()
  } else if (curTab.value == 5) {
    WithdrawPageRef.value?.handleTabChangeQuery()
  } else if (curTab.value == 6) {
    FissonPageRef.value?.onQuery()
  }
}

function handleAction(data, type) {
  curTab.value = 2
  VideoPageRef.value?.handleTabChangeQuery(data, type)
}

onMounted(() => {
  if (OrderPageRef.value) {
    curTab.value = 1
    OrderPageRef.value.onQuery()
  } else if (VideoPageRef.value) {
    curTab.value = 2
    VideoPageRef.value.onQuery()
  } else if (RefundPageRef.value) {
    curTab.value = 3
    RefundPageRef.value.onQuery()
  } else if (EnterPageRef.value) {
    curTab.value = 4
    EnterPageRef.value.onQuery()
  } else if (WithdrawPageRef.value) {
    curTab.value = 5
    WithdrawPageRef.value.onQuery()
  } else if (FissonPageRef.value) {
    curTab.value = 6
    FissonPageRef.value.onQuery()
  }
})
</script>

<style scoped lang="scss">
.tabs {
  :deep(.el-tabs__item:focus-visible) {
    box-shadow: none;
  }
}
</style>
