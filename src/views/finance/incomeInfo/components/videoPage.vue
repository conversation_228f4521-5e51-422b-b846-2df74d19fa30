<!--订单收支明细-->
<template>
  <div style="padding: 20px 20px 0 20px">
    <ElTablePage
      ref="tableRef"
      :columns="columns"
      :data="pageData"
      :loading="tableLoading"
      :currentPage="currentPage"
      :pageSize="pageSize"
      :total="total"
      @page-change="pageChange"
      row-key="id"
    >
      <template #tableHeader>
        <div>
          <el-form :inline="true" :model="queryParams" class="demo-form-inline" @submit.prevent>
            <el-form-item label="搜索" prop="keyword">
              <el-input
                v-model="queryParams.keyword"
                clearable
                style="width: 260px"
                placeholder="请输入关键字搜索"
              >
                <!-- <template #prepend>
                  <el-select v-model="queryParams.select" placeholder="请选择" clearable style="width: 100px">
                    <el-option
                      v-for="dict in selectOptionList"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </template> -->
              </el-input>
            </el-form-item>
            <!-- <el-form-item label="订单类型" prop="type">
              <el-select v-model="queryParams.orderType" placeholder="请选择" clearable style="width: 140px">
                <el-option
                  v-for="dict in orderTypeList"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item> -->
            <el-form-item label="支付方式" prop="payType">
              <el-select
                v-model="queryParams.payTypes"
                placeholder="请选择"
                multiple
                collapse-tags
                collapse-tags-tooltip
                clearable
                style="width: 140px"
              >
                <el-option
                  v-for="dict in payTypeSelectList"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="全币种支付类型" prop="payTypeDetails" label-width="110px">
              <el-select
                v-model="queryParams.payTypeDetails"
                placeholder="请选择"
                multiple
                collapse-tags
                collapse-tags-tooltip
                clearable
                style="width: 200px"
              >
                <el-option
                  v-for="item in payMoneyTypeAllList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <!-- <el-form-item label="入账状态" prop="isRecord">
              <el-select v-model="queryParams.isRecord" placeholder="请选择" clearable style="width: 140px">
                <el-option
                  v-for="dict in isEntryList"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item> -->
            <el-form-item label="支付时间" style="width: 350px">
              <el-date-picker
                v-model="queryParams.times"
                format="YYYY/M/D HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="datetimerange"
                range-separator="-"
                unlink-panels
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
                :shortcuts="shortcuts"
              ></el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button v-btn type="primary" icon="Search" native-type="submit" @click="onQuery">
                搜索
              </el-button>
              <el-button v-btn icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </template>
      <template #payTime="{ row }">
        <div>支付:{{ row.payTime || ' - ' }}</div>
        <div>审核:{{ row.auditTime || ' - ' }}</div>
      </template>
      <template #orderNum="{ row }">
        <div style="text-align: left">
          <div>订单号：</div>
          <div>{{ row.orderNum }}</div>
          <div>支付号：</div>
          <div>{{ row.payNum || '-' }}</div>
          <div v-if="row.mchntOrderNo">交易流水号：</div>
          <div v-if="row.mchntOrderNo">{{ row.mchntOrderNo }}</div>
        </div>
      </template>
      <template #payType="{ row }">
        <div>
          <div>{{ payTypeMapNew[row.payType] }}</div>
          <el-tag v-if="row.payTypeDetail && (row.payType == 7 || row.payType == 17)" type="warning" round>
            {{ payMoneyTypeMap[row.payTypeDetail] || '-' }}
          </el-tag>
        </div>
      </template>
      <template #orderInfo="{ row }">
        <div style="text-align: left" v-if="row.orderType == 0">
          <div>视频编码：{{ row.videoCode }}</div>
          <div>中文名称：{{ row.productChinese }}</div>
          <div>英文名称：{{ row.productEnglish }}</div>
          <div v-if="row.shootModelName">
            拍摄模特：{{ `${row.shootModelName}  (ID ${row.shootModelAccount})` }}
          </div>
          <!-- <el-row v-if="row.merchantCode">商家编码:{{ row.merchantCode }}</el-row> -->
          <!-- <el-row v-if="row.businessName">商家名称:{{ row.businessName }}</el-row> -->
        </div>
        <div style="text-align: left" v-if="row.orderType == 1">
          <div v-if="row.businessAccount">商家编码：{{ row.businessAccount }}</div>
          <div v-if="row.businessName">商家名称：{{ row.businessName }}</div>
          <div v-if="row.packageType">
            会员类型：{{ setMealTypeList.find(item => item.value === row.packageType)?.label }}
          </div>
        </div>
      </template>
      <template #videoStatus="{ row }">
        <div style="text-align: left" v-if="row.orderType == 0">
          <div>订单状态：{{ orderStatusMap[row.status] }}</div>
          <div>首次反馈商家素材时间：{{ row.needConfirmTime || '-' }}</div>
          <div>最新提交模特时间：{{ row.lastModelSubmitTime || '-' }}</div>
        </div>
      </template>
      <template #payAmount="{ row }">
        <div>
          <div>{{ row.amount }}</div>
          <el-button
            v-btn
            link
            type="primary"
            v-hasPermi="['finance:video:detail:info']"
            @click="openDetailLog(row)"
          >
            <div style="display: flex; flex-direction: column">
              <div>明细</div>
            </div>
          </el-button>
        </div>
      </template>
      <template #pageLeft>
        <div style="margin-right: 15px">
          <DownloadBtn
            type="success"
            plain
            icon="Download"
            v-hasPermi="['finance:order:detail:export']"
            url="/order/finance/orderPayDetailVideoList/export"
            :params="getExportQueryParams()"
            fileName="视频明细.xlsx"
          />
        </div>
      </template>
    </ElTablePage>
    <el-dialog
      v-model="dialogDetailVisible"
      title="视频订单明细"
      align-center
      center
      width="400px"
      :close-on-press-escape="false"
      style="border-radius: 6px"
    >
      <el-descriptions :column="1">
        <el-descriptions-item label="视频佣金:">${{ dialogData.videoPrice }}</el-descriptions-item>
        <el-descriptions-item label="照片佣金:">${{ dialogData.picPrice }}</el-descriptions-item>
        <el-descriptions-item label="佣金代缴税费:">
          ${{ dialogData.commissionPaysTaxes }}
        </el-descriptions-item>
        <el-descriptions-item label="PayPal代付手续费:">${{ dialogData.exchangePrice }}</el-descriptions-item>
        <el-descriptions-item label="蜗牛服务费:">${{ dialogData.servicePrice }}</el-descriptions-item>
        <el-descriptions-item label="百度汇率:">{{ dialogData.currentExchangeRate }}</el-descriptions-item>
        <el-descriptions-item
          label="限时满减活动:"
          v-if="handleShowDiscount(dialogData.orderDiscountDetailVOS, '1')"
        >
          ￥{{ handleDiscount(dialogData.orderDiscountDetailVOS, '1') }}
        </el-descriptions-item>
        <el-descriptions-item
          label="每月首单立减:"
          v-if="handleShowDiscount(dialogData.orderDiscountDetailVOS, '4')"
        >
          {{ handleDiscount(dialogData.orderDiscountDetailVOS, '4') }}
        </el-descriptions-item>
        <!-- <el-descriptions-item label="优惠金额:">￥{{ dialogData.videoPromotionAmount }}</el-descriptions-item> -->
        <el-descriptions-item label="差额:">
          {{ handleDifferenceAmount(dialogData.differenceAmount) }}
        </el-descriptions-item>
        <el-descriptions-item label="订单总价:">￥{{ dialogData.amount }}</el-descriptions-item>
        <el-descriptions-item label="实付金额:">￥{{ dialogData.realPayAmount }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script setup>
import ElTablePage from '@/components/Table/ElTablePage.vue'
import DownloadBtn from '@/components/Button/DownloadBtn.vue'
// import { isEntryList, orderTypeList, selectOptionList } from '@/views/finance/incomeInfo/data.js'
import { orderPayDetailVideoList } from '@/api/finance/detail.js'
import { payTypeSelectList, payTypeMapNew } from '@/utils/dict'
import { setMealTypeList, payMoneyTypeAllList, payMoneyTypeMap } from '../../data.js'
import { orderStatusMap } from '@/views/order/list/data.js'

const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const tableLoading = ref(false)
const queryParams = ref({
  keyword: '',
  orderNum: '',
  businessId: '',
  val: '',
  select: '',
  payTypes: [],
  orderType: '',
  isRecord: '',
  times: [],
  payTypeDetails: [],
})
const pageData = ref([])

const shortcuts = [
  {
    text: '今天',
    value: () => {
      const start = new Date()
      start.setHours(0, 0, 0, 0)
      const end = new Date()
      end.setHours(23, 59, 59, 59)
      return [start, end]
    },
  },
]

const columns = reactive([
  { slot: 'payTime', label: '时间', width: '190' },
  { slot: 'orderNum', prop: 'orderNum', label: '订单号', width: '300' },
  { slot: 'orderInfo', label: '下单信息', minWidth: '200' },
  // {
  //   prop: 'orderType',
  //   label: '订单类型',
  //   width: '100',
  //   handle: status => {
  //     return orderTypeList.find(item => item.value === status)?.label
  //   },
  // },
  { slot: 'videoStatus', label: '视频状态', minWidth: '200' },
  {
    slot: 'payType',
    prop: 'payType',
    label: '支付方式',
    width: '150',
  },
  { slot: 'payAmount', label: '订单总价', width: '150' },
  // {
  //   prop: 'isRecord',
  //   label: '状态',
  //   width: '200',
  //   handle: isRecord => {
  //     return isRecord == 1 ? '已入账' : '未入账'
  //   },
  // },
])

function getQueryParams() {
  let params = {
    startTime: '',
    endTime: '',
    keyword: queryParams.value.keyword,
    pageNum: currentPage.value,
    pageSize: pageSize.value,
    isRecord: queryParams.value.isRecord,
    payTypes: queryParams.value.payTypes.join(','),
    orderType: queryParams.value.orderType,
    payTypeDetails: queryParams.value.payTypeDetails,
  }
  if (queryParams.value.orderNum) {
    params.orderNum = queryParams.value.orderNum
  }
  if (queryParams.value.businessId) {
    params.businessId = queryParams.value.businessId
  }
  if (queryParams.value.times != [] && queryParams.value.times != null) {
    params.startTime = queryParams.value.times[0]
    params.endTime = queryParams.value.times[1]
  }
  if (queryParams.value.select) {
    params[queryParams.value.select] = queryParams.value.val
  }
  return params
}

function getExportQueryParams() {
  let params = {
    startTime: '',
    endTime: '',
    keyword: queryParams.value.keyword,
    pageNum: currentPage.value,
    pageSize: 10000,
    isRecord: queryParams.value.isRecord,
    payTypes: queryParams.value.payTypes.join(','),
    orderType: queryParams.value.orderType,
    payTypeDetails: queryParams.value.payTypeDetails,
  }
  if (queryParams.value.orderNum) {
    params.orderNum = queryParams.value.orderNum
  }
  if (queryParams.value.businessId) {
    params.businessId = queryParams.value.businessId
  }
  if (queryParams.value.times != [] && queryParams.value.times != null) {
    params.startTime = queryParams.value.times[0]
    params.endTime = queryParams.value.times[1]
  }
  if (queryParams.value.select) {
    params[queryParams.value.select] = queryParams.value.val
  }
  return params
}

function onQuery() {
  currentPage.value = 1
  getList(getQueryParams())
}

function handleQuery() {
  getList(getQueryParams())
}
function handleTabChangeQuery(orderNum, type = '') {
  if (type) {
    queryParams.value.businessId = orderNum || ''
    queryParams.value.orderNum = ''
  } else {
    queryParams.value.orderNum = orderNum || ''
    queryParams.value.businessId = ''
  }
  pageData.value = []
  onQuery()
}

function resetQuery() {
  queryParams.value = {
    keyword: '',
    orderNum: '',
    businessId: '',
    val: '',
    select: '',
    payTypes: [],
    orderType: '',
    isRecord: '',
    times: [],
    payTypeDetails: [],
  }
  currentPage.value = 1
  handleQuery()
}

function getList(param) {
  tableLoading.value = true
  orderPayDetailVideoList(param)
    .then(res => {
      pageData.value = res.data.rows
      total.value = res.data.total
    })
    .finally(() => (tableLoading.value = false))
}

function pageChange(page) {
  currentPage.value = page.currentPage
  pageSize.value = page.pageSize
  handleQuery()
}

const dialogDetailVisible = ref(false)
const dialogData = ref({
  title: '',
  orderType: '',
  memberPrice: '',
  taxPointCost: '',
  currentExchangeRate: '',
  servicePrice: '',
  exchangePrice: '',
  picPrice: '',
  videoPrice: '',
  videoAmount: '',
  useBalance: '',
  payAmount: '',
  realPayAmount: '',
  amount: '',
  orderDiscountDetailVOS: [],
})

const openDetailLog = row => {
  dialogData.value = row
  dialogDetailVisible.value = true
}

function handleDifferenceAmount(val) {
  if (val) {
    return '-￥' + Math.abs(val)
  }
  return '￥0'
}

const handleDiscount = (list, type) => {
  if (type == 1) {
    return list.find(item => item.type == 1).discountAmount
  } else if (type == 4) {
    let data = list.find(item => item.type == 4)
    return '￥' + data.discountAmount + '（$' + data.amount + '）'
  }
}
const handleShowDiscount = (list, type) => {
  return list && list.length > 0 ? list.some(item => item.type == type) : false
}

defineExpose({
  handleTabChangeQuery,
  onQuery,
})
</script>
