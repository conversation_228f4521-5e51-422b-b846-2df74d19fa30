<!--退款记录-->
<template>
  <div style="padding: 20px 20px 0 20px;">
    <ElTablePage
      ref="tableRef"
      :columns="columns"
      :data="pageData"
      :loading="tableLoading"
      :currentPage="currentPage"
      :pageSize="pageSize"
      :total="total"
      @page-change="pageChange"
      row-key="id"
    >
      <template #tableHeader>
        <div>
          <el-form :inline="true" :model="queryParams" class="demo-form-inline" @submit.prevent>
            <el-form-item label="搜索" prop="keyword">
              <el-input
                v-model="queryParams.keyword"
                clearable
                style="width: 260px"
                placeholder="请输入关键字搜索"
              ></el-input>
            </el-form-item>
            <el-form-item label="退款类型" prop="refundType">
              <el-select
                v-model="queryParams.refundType"
                placeholder="请选择"
                multiple
                collapse-tags
                collapse-tags-tooltip
                clearable
                style="width: 140px"
              >
                <el-option
                  v-for="dict in refundTypeList"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="时间" style="width: 350px">
              <el-date-picker
                v-model="queryParams.times"
                format="YYYY/M/D HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="datetimerange"
                range-separator="-"
                unlink-panels
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
                :shortcuts="shortcuts"
              ></el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button v-btn type="primary" icon="Search" native-type="submit" @click="onQuery">
                搜索
              </el-button>
              <el-button v-btn icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </template>
      <template #payTime="{ row }">
        <div>{{ row.payTime || ' - ' }}</div>
      </template>
      <template #orderNum="{ row }">
        <div style="text-align: left">
          <div>订单号：</div>
          <div>{{ row.orderNum }}</div>
          <div v-if="row.refundNum">退款单号：</div>
          <div v-if="row.refundNum">{{ row.refundNum }}</div>
        </div>
      </template>
      <template #orderInfo="{ row }">
        <div style="text-align: left">
          <div>视频编码：{{ row.videoCode }}</div>
          <div>中文名称：{{ row.productChinese }}</div>
          <div>英文名称：{{ row.productEnglish }}</div>
          <div>会员编码：{{ row.merchantCode }}</div>
        </div>
      </template>
      <template #refundCause="{ row }">
        <div>发起人：{{ row.initiatorSource == 1 ? '商家' : '平台' }}({{ row.initiatorName }})</div>
        <div>退款原因：{{ row.refundCause }}</div>
        <div v-if="row.refundPicCount">退款方案:{{row.refundPicCount}}张</div>
      </template>
      <template #operateBy="{ row }">
        <div>审核人：{{ row.operateBy }}</div>
        <div>审核时间：{{ row.operateTime }}</div>
      </template>
      <template #pageLeft>
        <div style="margin-right: 15px">
          <DownloadBtn
            type="success"
            plain
            icon="Download"
            v-hasPermi="['finance:refund:export']"
            url="/order/finance/refund-success-list/export"
            :params="getQueryParams('export')"
            fileName="退款记录.xlsx"
          />
        </div>
      </template>
    </ElTablePage>
  </div>
</template>

<script setup>
import ElTablePage from '@/components/Table/ElTablePage.vue'
import DownloadBtn from '@/components/Button/DownloadBtn.vue'
import { getRefundSuccessList } from '@/api/finance/refund.js'
import { refundTypeList } from '@/views/finance/data.js'

const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const tableLoading = ref(false)
const queryParams = ref({
  keyword: '',
  refundType: [],
  times: [],
})
const pageData = ref([])

const shortcuts = [
  {
    text: '今天',
    value: () => {
      const start = new Date()
      start.setHours(0, 0, 0, 0)
      const end = new Date()
      end.setHours(23, 59, 59, 59)
      return [start, end]
    },
  },
]


const columns = reactive([
  { prop: 'applyTime', label: '申请时间', width: '160' },
  { slot: 'orderNum', prop: 'orderNum', label: '单号', width: '300' },
  { slot: 'orderInfo', label: '订单信息', minWidth: '200' },
  {
    prop: 'refundType',
    label: '退款类型',
    width: '150',
    handle: refundType => {
      if (refundType == 1) {
        return '补偿订单'
      }
      let item = refundTypeList.find(item => item.value == refundType)
      if (item) {
        return item.label
      }
      return '-'
    },
  },
  { slot: 'refundCause', label: '退款信息', width: '200' },
  { slot: 'operateBy', label: '操作人', width: '230' },
  {
    prop: 'refundAmount',
    label: '退款金额',
    width: '100',
  },
])

function getQueryParams(type = '') {
  let params = {
    timeBegin: '',
    timeEnd: '',
    keyword: queryParams.value.keyword,
    pageNum: type == 'export' ? '' : currentPage.value,
    pageSize: type == 'export' ? '' : pageSize.value,
    refundType: queryParams.value.refundType.join(','),
  }
  if (queryParams.value.times != [] && queryParams.value.times != null) {
    params.timeBegin = queryParams.value.times[0]
    params.timeEnd = queryParams.value.times[1]
  }
  return params
}

function onQuery() {
  currentPage.value = 1
  getList(getQueryParams())
}

function handleQuery() {
  getList(getQueryParams())
}
function handleTabChangeQuery() {
  pageData.value = []
  onQuery()
}

function resetQuery() {
  queryParams.value = {
    keyword: '',
    refundType: [],
    times: [],
  }
  handleQuery()
}

function getList(param) {
  tableLoading.value = true
  // timeBegin
  // timeEnd
  getRefundSuccessList(param)
    .then(res => {
      pageData.value = res.data.rows
      total.value = res.data.total
    })
    .finally(() => (tableLoading.value = false))
}

function pageChange(page) {
  currentPage.value = page.currentPage
  pageSize.value = page.pageSize
  handleQuery()
}

defineExpose({
  handleTabChangeQuery,
  onQuery,
})
</script>
