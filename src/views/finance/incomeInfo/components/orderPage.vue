<!--订单收支明细-->
<template>
  <div style="padding: 20px 20px 0 20px">
    <ElTablePage
      ref="tableRef"
      :columns="columns"
      :data="pageData"
      :loading="tableLoading"
      :currentPage="currentPage"
      :pageSize="pageSize"
      :total="total"
      :tableAction="{
        width: '170',
        fixed: 'right',
      }"
      @page-change="pageChange"
      row-key="id"
    >
      <template #tableHeader>
        <div>
          <el-form :inline="true" :model="queryParams" @submit.prevent>
            <el-form-item label="搜索" prop="keyword">
              <el-input
                v-model="queryParams.keyword"
                clearable
                style="width: 250px"
                placeholder="请输入关键字搜索"
              />
            </el-form-item>
            <el-form-item label="来源" prop="orderTypeList">
              <el-select
                v-model="queryParams.orderTypeList"
                placeholder="请选择"
                multiple
                collapse-tags
                collapse-tags-tooltip
                clearable
                style="width: 180px"
              >
                <el-option
                  v-for="dict in orderTypeList"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="支付方式" prop="payTypes">
              <el-select
                v-model="queryParams.payTypes"
                placeholder="请选择"
                multiple
                collapse-tags
                collapse-tags-tooltip
                clearable
                style="width: 180px"
              >
                <el-option
                  v-for="dict in payTypeSelectList"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
                <el-option label="其他" :value="99" :key="99">其他</el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="全币种支付类型" prop="payTypeDetails" label-width="110px">
              <el-select
                v-model="queryParams.payTypeDetails"
                placeholder="请选择"
                multiple
                collapse-tags
                collapse-tags-tooltip
                clearable
                style="width: 200px"
              >
                <el-option
                  v-for="item in payMoneyTypeAllList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="支付时间" style="width: 380px">
              <el-date-picker
                v-model="queryParams.times"
                format="YYYY/M/D HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="datetimerange"
                range-separator="-"
                unlink-panels
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
                :shortcuts="shortcuts"
              ></el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button v-btn type="primary" icon="Search" native-type="submit" @click="onQuery">
                搜索
              </el-button>
              <el-button v-btn icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </template>
      <template #orderNum="{ row }">
        <div style="text-align: left">
          <div>订单号：</div>
          <div>{{ row.orderNum }}</div>
          <div>支付号：</div>
          <div>{{ row.payNum || '-' }}</div>
          <div v-if="row.mchntOrderNo">交易流水号：</div>
          <div v-if="row.mchntOrderNo">{{ row.mchntOrderNo }}</div>
        </div>
      </template>
      <!-- <template #payTime="{ row }">
        <div>支付时间：{{ row.payTime }}</div>
        <div>入账时间：{{ row.recordTime ? row.recordTime : ' - ' }}</div>
      </template> -->
      <template #orderInfo="{ row }">
        <div>
          <div v-if="row.merchantCode">会员编码：{{ row.merchantCode }}</div>
          <div v-if="row.businessName">商家名称：{{ row.businessName }}</div>
        </div>
      </template>
      <template #payType="{ row }">
        <div>
          <div>{{ payTypeMapNew[row.payType] }}</div>
          <el-tag v-if="row.payTypeDetail && (row.payType == 7 || row.payType == 17)" type="warning" round>
            {{ payMoneyTypeMap[row.payTypeDetail] || '-' }}
          </el-tag>
        </div>
      </template>
      <template #account="{ row }">
        <template v-if="row.payeeAccountConfig">
          <PayeeAccountVO :data="row.payeeAccountConfig" :payType="row.payType" />
          <!-- <div
            v-if="row.payType == payTypeMap['银行卡'] || row.payType == payTypeMap['银行卡+余额']"
            style="text-align: left"
          >
            <div>银行卡号：{{ row.payeeAccountConfig.bankAccount || ' - ' }}</div>
            <div>姓名：{{ row.payeeAccountConfig.accountName || ' - ' }}</div>
            <div>开户行：{{ row.payeeAccountConfig.bankName || ' - ' }}</div>
          </div>
          <div
            v-else-if="row.payType == payTypeMap['公对公'] || row.payType == payTypeMap['公对公+余额']"
            style="text-align: left"
          >
            <div>
              收款公司名称：
              <br />
              {{ row.payeeAccountConfig.accountName || ' - ' }}
            </div>
            <div>
              开户行名称：
              <br />
              {{ row.payeeAccountConfig.bankName || ' - ' }}
            </div>
            <div>
              收款银行账号：
              <br />
              {{ row.payeeAccountConfig.bankAccount || ' - ' }}
            </div>
          </div>
          <div
            v-else-if="row.payType == payTypeMap['全币种'] || row.payType == payTypeMap['全币种+余额']"
            style="text-align: left"
          >
            <div>
              收款账户类型：
              <br />
              {{ row.payeeAccountConfig.companyAccountType || ' - ' }}
            </div>
            <div>
              账号名：
              <br />
              {{ row.payeeAccountConfig.accountName || ' - ' }}
            </div>
          </div>
          <div v-else>-</div> -->
        </template>
        <div v-else>-</div>
      </template>
      <template #tableAction="{ row }">
        <el-button
          v-if="row.orderType != 3 && row.orderType != 5"
          v-btn
          link
          type="primary"
          v-hasPermi="['finance:order:detail']"
          @click="openDetailLog(row)"
        >
          明细
        </el-button>
        <el-button
          v-if="row.orderType == 0"
          v-hasPermi="['finance:order:link-order']"
          v-btn
          link
          type="primary"
          @click="$emit('action', row.orderNum)"
        >
          查看关联订单
        </el-button>
      </template>
      <template #pageLeft>
        <div style="margin-right: 15px">
          <DownloadBtn
            type="success"
            plain
            icon="Download"
            url="/order/finance/orderPayDetailList/export"
            :params="getExportQueryParams()"
            fileName="订单收支明细.xlsx"
            v-hasPermi="['finance:order:export']"
          />
        </div>
      </template>
    </ElTablePage>
    <el-dialog
      v-model="dialogDetailVisible"
      :title="dialogData.title"
      align-center
      center
      width="400px"
      style="border-radius: 6px"
      :close-on-press-escape="false"
    >
      <el-descriptions v-if="dialogData.orderType == 0" :column="1">
        <el-descriptions-item label="视频佣金:">
          ${{ dialogData.videoAmount?.videoPrice }}
        </el-descriptions-item>
        <el-descriptions-item label="照片佣金:">${{ dialogData.videoAmount?.picPrice }}</el-descriptions-item>
        <el-descriptions-item label="佣金代缴税费:">
          ￥{{ dialogData.videoAmount?.commissionPaysTaxes }}
        </el-descriptions-item>
        <el-descriptions-item label="paypal代付手续费:">
          ${{ dialogData.videoAmount?.exchangePrice }}
        </el-descriptions-item>
        <el-descriptions-item label="蜗牛服务费:">
          ${{ dialogData.videoAmount?.servicePrice }}
        </el-descriptions-item>
        <el-descriptions-item label="百度汇率:">
          {{ dialogData.videoAmount?.currentExchangeRate }}
        </el-descriptions-item>
        <el-descriptions-item label="开票费用:" v-if="dialogData.videoAmount?.taxPointCost > 0">
          ￥{{ dialogData.videoAmount?.taxPointCost }}
        </el-descriptions-item>
        <el-descriptions-item label="">
          <el-divider border-style="dashed" style="margin: -10px 0 10px" />
        </el-descriptions-item>
        <el-descriptions-item label="订单总价:">
          ￥{{ dialogData.videoAmount?.orderAmount }}
        </el-descriptions-item>
        <el-descriptions-item
          label="限时满减活动:"
          v-if="handleShowDiscount(dialogData.orderDiscountDetailVOS, '1')"
        >
          ￥{{ handleDiscount(dialogData.orderDiscountDetailVOS, '1') }}
        </el-descriptions-item>
        <el-descriptions-item
          label="每月首单立减:"
          v-if="handleShowDiscount(dialogData.orderDiscountDetailVOS, '4')"
        >
          {{ handleDiscount(dialogData.orderDiscountDetailVOS, '4') }}
        </el-descriptions-item>
        <!-- <el-descriptions-item label="优惠金额:">
          ￥{{ dialogData.videoAmount?.orderPromotionAmount }}
        </el-descriptions-item> -->
        <el-descriptions-item label="钱包支付:">
          ￥{{ dialogData.videoAmount?.useBalance }}
        </el-descriptions-item>
        <el-descriptions-item label="剩余支付:">
          ￥{{ dialogData.videoAmount?.surplusAmount }}
        </el-descriptions-item>
        <el-descriptions-item label="实付金额:">
          {{ dialogData.realPayAmountCurrency }}
        </el-descriptions-item>
        <el-descriptions-item label="差额:">
          {{ handleDifferenceAmount(dialogData.differenceAmount) }}
        </el-descriptions-item>
        <el-descriptions-item label="币种:">
          <span v-if="dialogData.currency && dialogData.currency == 1">人民币</span>
          <span v-else>
            {{ sys_money_type.find(item => item.value == dialogData.currency)?.label || '-' }}
          </span>
        </el-descriptions-item>
      </el-descriptions>
      <el-descriptions v-if="dialogData.orderType == 1" :column="1">
        <el-descriptions-item label="会员类型:">
          {{
            dialogData.memberAmount?.packageType == 0
              ? '季度会员'
              : dialogData.memberAmount?.packageType == 1
              ? '年度会员'
              : dialogData.memberAmount?.packageType == 2
              ? '三年会员'
              : ''
          }}
        </el-descriptions-item>
        <el-descriptions-item label="会员费用:">
          ${{ dialogData.memberAmount?.memberPrice }}
        </el-descriptions-item>
        <el-descriptions-item label="折扣类型:">
          {{ dialogData.memberAmount?.discountType == 1 ? '种草折扣' : '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="折扣金额:">
          ￥{{ dialogData.memberAmount?.discountAmount }}
        </el-descriptions-item>
        <el-descriptions-item label="百度汇率:">
          {{ dialogData.currentExchangeRate }}
        </el-descriptions-item>
        <el-descriptions-item label="开票费用:" v-if="dialogData.memberAmount?.taxPointCost > 0">
          ￥{{ dialogData.memberAmount.taxPointCost }}
        </el-descriptions-item>
        <el-descriptions-item label="">
          <el-divider border-style="dashed" style="margin: -10px 0 10px" />
        </el-descriptions-item>
        <el-descriptions-item label="订单总价:">
          ￥{{ dialogData.memberAmount?.payAmount }}
        </el-descriptions-item>
        <el-descriptions-item label="钱包抵扣:">
          ￥{{ dialogData.memberAmount.useBalance }}
        </el-descriptions-item>
        <el-descriptions-item label="剩余支付:">
          ￥{{ dialogData.memberAmount?.surplusAmount }}
        </el-descriptions-item>
        <el-descriptions-item label="实付人民币:">
          ￥{{ dialogData.memberAmount?.realPayAmount }}
        </el-descriptions-item>
        <el-descriptions-item label="实付金额:">
          {{ dialogData.realPayAmountCurrency }}
        </el-descriptions-item>
        <el-descriptions-item label="币种:">
          <span v-if="dialogData.currency && dialogData.currency == 1">人民币</span>
          <span v-else>
            {{ sys_money_type.find(item => item.value == dialogData.currency)?.label || '-' }}
          </span>
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
    <el-dialog
      v-model="dialogMemberDetailVisible"
      title="会员订单金额明细"
      align-center
      center
      width="500px"
      style="border-radius: 6px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div class="member-detail-box">
        <div class="member-title">基础信息</div>
        <el-row>
          <el-col :span="12">
            会员类型：
            {{
              dialogData.memberAmount?.packageType == 0
                ? '季度会员'
                : dialogData.memberAmount?.packageType == 1
                ? '年度会员'
                : dialogData.memberAmount?.packageType == 2
                ? '三年会员'
                : ''
            }}
          </el-col>
          <el-col :span="12">会员费用：${{ dialogData.memberAmount?.memberPrice }}</el-col>
        </el-row>
        <el-row>
          <el-col :span="12">百度汇率：{{ dialogData.currentExchangeRate }}</el-col>
        </el-row>
        <el-divider border-style="dashed" style="margin: 10px 0 10px" />
        <div class="member-title" style="margin-bottom: 10px">优惠信息</div>
        <el-table :data="dialogData.orderDiscountDetailVOS" border>
          <el-table-column prop="type" label="优惠类型" align="center">
            <template v-slot="{ row }">
              {{ disountTypeList.find(item => item.value == row.type)?.label || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="discountAmount" label="优惠金额" align="center">
            <template v-slot="{ row }">￥{{ row.discountAmount }}</template>
          </el-table-column>
        </el-table>
        <div class="flex-between" style="margin-top: 10px">
          <div>总优惠金额：</div>
          <div>{{ handleDiscountAmount(dialogData.orderDiscountDetailVOS) }}</div>
        </div>
        <el-divider border-style="dashed" style="margin: 10px 0 10px" />
        <div class="member-title">支付信息</div>
        <el-row>
          <el-col :span="12">订单总价：￥{{ dialogData.memberAmount?.payAmount }}</el-col>
          <el-col :span="12">钱包抵扣：￥{{ dialogData.memberAmount.useBalance }}</el-col>
        </el-row>
        <el-row>
          <el-col :span="12">剩余支付：￥{{ dialogData.memberAmount?.surplusAmount }}</el-col>
          <el-col :span="12">实付人民币：￥{{ dialogData.memberAmount?.realPayAmount }}</el-col>
        </el-row>
        <el-row>
          <el-col :span="12">实付金额：{{ dialogData.realPayAmountCurrency }}</el-col>
          <el-col :span="12">
            币种：
            <span v-if="dialogData.currency && dialogData.currency == 1">人民币</span>
            <span v-else>
              {{ sys_money_type.find(item => item.value == dialogData.currency)?.label || '-' }}
            </span>
          </el-col>
        </el-row>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import ElTablePage from '@/components/Table/ElTablePage.vue'
import DownloadBtn from '@/components/Button/DownloadBtn.vue'
import { orderTypeList } from '@/views/finance/incomeInfo/data.js'
import { orderPayDetailList } from '@/api/finance/detail.js'
import { payTypeSelectList, payTypeMap, payTypeMapNew } from '@/utils/dict'
import PayeeAccountVO from '@/views/finance/receivableApprove/components/payeeAccountVO.vue'
import * as math from 'mathjs'
import { disountTypeList } from '@/views/order/vip/data.js'
import { payMoneyTypeAllList, payMoneyTypeMap } from '@/views/finance/data.js'
const { proxy } = getCurrentInstance()
const { sys_money_type } = proxy.useDict('sys_money_type')

const emits = defineEmits(['action'])

const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const tableLoading = ref(false)
const queryParams = ref({
  keyword: '',
  orderTypeList: [],
  payTypes: [],
  times: [],
  payTypeDetails: [],
})
const pageData = ref([])

const shortcuts = [
  {
    text: '今天',
    value: () => {
      const start = new Date()
      start.setHours(0, 0, 0, 0)
      const end = new Date()
      end.setHours(23, 59, 59, 59)
      return [start, end]
    },
  },
]

const columns = reactive([
  { prop: 'payTime', label: '支付时间', width: '170' },
  {
    slot: 'orderNum',
    prop: 'orderNum',
    label: '订单号',
    width: '250',
  },
  { slot: 'orderInfo', label: '下单商家', minWidth: '200' },
  {
    prop: 'orderType',
    label: '来源',
    width: '80',
    handle: data => {
      let item = orderTypeList.find(item => item.value == data)
      return item ? item.label.replace('订单', '') : ''
    },
  },
  {
    slot: 'payType',
    prop: 'payType',
    label: '支付方式',
    width: '150',
  },
  {
    prop: 'payAccount',
    label: '付款账户',
    width: '250',
    handle: (data, row) => {
      if (row.payType == payTypeMapNew['对公'] || row.payType == payTypeMapNew['对公+余额']) {
        return data || '-'
      }
      return data || '-'
    },
  },
  {
    slot: 'account',
    label: '收款账号',
    width: '250',
    align: 'left',
  },
  { prop: 'orderAmount', label: '订单总价', width: '150', handle: data => `￥${data}` },
  { prop: 'useBalance', label: '钱包支付', width: '150', handle: data => (data ? `￥${data}` : '-') },
  { prop: 'surplusAmount', label: '剩余支付', width: '150', handle: data => `￥${data}` },
  { prop: 'realPayAmount', label: '实付人民币', width: '150', handle: data => `￥${data}` },
  {
    prop: 'currency',
    label: '支付币种',
    width: '150',
    handle: (data, row) => {
      let s = sys_money_type.value.find(item => item.value == data)
      if (s) {
        return s.label
      } else {
        return '-'
      }
    },
  },
  {
    prop: 'realPayAmountCurrency',
    label: '实付金额',
    width: '150',
    handle: (data, row) => {
      if (row.payType == payTypeMapNew['全币种'] || row.payType == payTypeMapNew['全币种+余额']) {
        return data || '-'
      } else {
        return '-'
      }
    },
  },
])

function getQueryParams() {
  let params = {
    payTimeBegin: '',
    payTimeEnd: '',
    searchName: queryParams.value.keyword,
    pageNum: currentPage.value,
    pageSize: pageSize.value,
    orderTypeList: queryParams.value.orderTypeList,
    payTypes: queryParams.value.payTypes.join(','),
    payTypeDetails: queryParams.value.payTypeDetails,
  }
  if (queryParams.value.times?.length) {
    params.payTimeBegin = queryParams.value.times[0]
    params.payTimeEnd = queryParams.value.times[1]
  }
  if (queryParams.value.select) {
    params[queryParams.value.select] = queryParams.value.val
  }
  return params
}

function getExportQueryParams() {
  let params = {
    payTimeBegin: '',
    payTimeEnd: '',
    searchName: queryParams.value.keyword,
    pageNum: currentPage.value,
    pageSize: 10000,
    orderTypeList: queryParams.value.orderTypeList,
    payTypes: queryParams.value.payTypes.join(','),
    payTypeDetails: queryParams.value.payTypeDetails,
  }
  if (queryParams.value.times?.length) {
    params.payTimeBegin = queryParams.value.times[0]
    params.payTimeEnd = queryParams.value.times[1]
  }
  if (queryParams.value.select) {
    params[queryParams.value.select] = queryParams.value.val
  }
  return params
}

function onQuery() {
  currentPage.value = 1
  getList(getQueryParams())
}

function handleQuery() {
  getList(getQueryParams())
}

function resetQuery() {
  queryParams.value = {
    keyword: '',
    orderTypeList: [],
    payTypes: [],
    times: [],
    payTypeDetails: [],
  }
  currentPage.value = 1
  handleQuery()
}

function getList(param) {
  tableLoading.value = true
  orderPayDetailList(param)
    .then(res => {
      pageData.value = res.data.rows
      total.value = res.data.total
    })
    .finally(() => (tableLoading.value = false))
}

function pageChange(page) {
  currentPage.value = page.currentPage
  pageSize.value = page.pageSize
  handleQuery()
}

const dialogDetailVisible = ref(false)
const dialogData = ref({
  title: '',
  orderType: '',
  memberPrice: '',
  taxPointCost: '',
  currentExchangeRate: '',
  servicePrice: '',
  exchangePrice: '',
  picPrice: '',
  videoPrice: '',
  videoAmount: '',
  useBalance: '',
  payAmount: '',
  realPayAmount: '',
  realPayAmountCurrency: '',
  payAmount: '',
  currency: '',
  orderDiscountDetailVOS: [],
})

const dialogMemberDetailVisible = ref(false)
const openDetailLog = row => {
  dialogData.value = row
  dialogData.value.title = '视频大订单金额明细'
  row.orderType == 0 ? (dialogDetailVisible.value = true) : (dialogMemberDetailVisible.value = true)
}

function handleDifferenceAmount(val) {
  if (val < 0) {
    return '-￥' + Math.abs(val)
  }
  if (val > 0) {
    return '+￥' + val
  }
  return '￥0'
}

function handleDiscountAmount(list) {
  let result = 0
  if (list && list.length > 0) {
    result = list.reduce((total, item) => {
      return math.add(math.bignumber(total), math.bignumber(item.discountAmount))
    }, 0)
  }
  return '￥' + result
}

const handleDiscount = (list, type) => {
  if (type == 1) {
    return list.find(item => item.type == 1).discountAmount
  } else if (type == 4) {
    let data = list.find(item => item.type == 4)
    return '￥' + data.discountAmount + '（$' + data.amount + '）'
  }
}
const handleShowDiscount = (list, type) => {
  return list && list.length > 0 ? list.some(item => item.type == type) : false
}

defineExpose({
  onQuery,
})
</script>

<style lang="scss" scoped>
.member-detail-box {
  color: #818181;
  font-size: 14px;
  .member-title {
    color: #333;
    font-size: 18px;
  }
  :deep(.el-col-12) {
    margin-top: 10px;
  }
}
</style>
