<!--退款记录-->
<template>
  <div style="padding: 20px 20px 0 20px">
    <ElTablePage
      ref="tableRef"
      :columns="columns"
      :data="pageData"
      :loading="tableLoading"
      :currentPage="currentPage"
      :pageSize="pageSize"
      :total="total"
      @page-change="pageChange"
      row-key="id"
    >
      <template #tableHeader>
        <div>
          <el-form :inline="true" :model="queryParams" class="demo-form-inline" @submit.prevent>
            <el-form-item label="搜索" prop="keyword">
              <el-input
                v-model="queryParams.keyword"
                clearable
                style="width: 350px"
                placeholder="请输入提现单号/商家名称/会员编码搜索"
              ></el-input>
            </el-form-item>
            <el-form-item label="发起时间" style="width: 350px">
              <el-date-picker
                v-model="queryParams.times"
                format="YYYY/M/D HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="datetimerange"
                range-separator="-"
                unlink-panels
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
                :shortcuts="shortcuts"
              ></el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button v-btn type="primary" icon="Search" native-type="submit" @click="onQuery">
                搜索
              </el-button>
              <el-button v-btn icon="Refresh" @click="resetQuery">重置</el-button>
              <!-- <DownloadBtn
                type="success"
                plain
                icon="Download"
                v-hasPermi="['finance:withdraw:export']"
                url="/biz/business/backend/businessBalanceAuditFlow/auditList/export"
                :params="getQueryParams()"
                fileName="提现明细列表.xlsx"
              /> -->
            </el-form-item>
          </el-form>
        </div>
      </template>
      <template #businessAccountDetailVO="{ row }">
        <div style="text-align: left">
          <div>
            {{ row.businessAccountDetailVO?.name || row.businessAccountDetailVO?.nickName }}
            <el-tag round size="small" type="warning" v-if="row.businessAccountDetailVO?.isProxy == 1">
              代理
            </el-tag>
          </div>
          <div>公司名称：{{ row.businessAccountDetailVO?.businessName }}</div>
          <div>注册时间：{{ row.businessAccountDetailVO?.businessCreateTime }}</div>
        </div>
      </template>
      <template #memberCode="{ row }">
        <div>{{ row.businessAccountDetailVO?.memberCode || '-' }}</div>
      </template>
      <template #orderInfo="{ row }">
        <div style="text-align: left">
          <div>视频编码：{{ row.videoCode }}</div>
          <div>中文名称：{{ row.productChinese }}</div>
          <div>英文名称：{{ row.productEnglish }}</div>
          <div>会员编码：{{ row.merchantCode }}</div>
        </div>
      </template>
      <template #refundCause="{ row }">
        <div>发起人：{{ row.initiatorSource == 1 ? '商家' : '平台' }}({{ row.initiatorName }})</div>
        <div>退款原因：{{ row.refundCause }}</div>
      </template>
      <template #operateBy="{ row }">
        <div>审核人：{{ row.operateBy }}</div>
        <div>审核时间：{{ row.operateTime }}</div>
      </template>
      <template #pageLeft>
        <DownloadBtn
          type="success"
          plain
          icon="Download"
          v-hasPermi="['finance:withdraw:export']"
          url="/biz/business/backend/businessBalanceAuditFlow/auditList/export"
          :params="getQueryParams()"
          fileName="提现明细列表.xlsx"
        />
      </template>
    </ElTablePage>
  </div>
</template>

<script setup>
import ElTablePage from '@/components/Table/ElTablePage.vue'
import DownloadBtn from '@/components/Button/DownloadBtn.vue'

import { withdrawTypeList } from '@/views/task/data.js'
import { getWithdrawList } from '@/api/finance/withdraw.js'
import { getRefundSuccessList } from '@/api/finance/refund.js'

const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const tableLoading = ref(false)
const queryParams = ref({
  keyword: '',
  times: [],
})
const pageData = ref([])

const shortcuts = [
  {
    text: '今天',
    value: () => {
      const start = new Date()
      start.setHours(0, 0, 0, 0)
      const end = new Date()
      end.setHours(23, 59, 59, 59)
      return [start, end]
    },
  },
]

const columns = reactive([
  { prop: 'withdrawNumber', label: '提现单号', width: '160' },
  { slot: 'businessAccountDetailVO', label: '商家信息', width: '300' },
  { slot: 'memberCode', label: '会员编码', minWidth: '200' },
  {
    prop: 'amount',
    label: '合计提现金额',
    width: '120',
    handle: amount => {
      return amount ? amount : '-'
    },
  },
  {
    prop: 'numbers',
    label: '单号',
    width: '220',
    handle: numbers => {
      if (numbers && numbers.length > 0) {
        return numbers.join('、')
      } else {
        return '-'
      }
    },
  },
  {
    prop: 'withdrawWay',
    label: '提现方式',
    width: '100',
    handle: withdrawWay => {
      const s = withdrawTypeList.find(item => item.value === withdrawWay)
      return s ? s.label : '-'
    },
  },
  {
    prop: 'applyRemark',
    label: '备注',
    width: '200',
    handle: applyRemark => {
      return applyRemark ? applyRemark : '-'
    },
  },
  {
    prop: 'createUserName',
    label: '发起人',
    width: '100',
  },
  {
    prop: 'createTime',
    label: '发起时间',
    width: '180',
  },
])

function getQueryParams() {
  let params = {
    startTimeBegin: '',
    startTimeEnd: '',
    keyword: queryParams.value.keyword,
  }
  if (queryParams.value.times != [] && queryParams.value.times != null) {
    params.startTimeBegin = queryParams.value.times[0]
    params.startTimeEnd = queryParams.value.times[1]
  }
  return params
}

function onQuery() {
  currentPage.value = 1
  getList(getQueryParams())
}

function handleQuery() {
  getList(getQueryParams())
}
function handleTabChangeQuery() {
  pageData.value = []
  onQuery()
}

function resetQuery() {
  queryParams.value = {
    keyword: '',
    times: [],
  }
  handleQuery()
}

function getList(param) {
  tableLoading.value = true
  getWithdrawList({ ...param, pageNum: currentPage.value, pageSize: pageSize.value })
    .then(res => {
      pageData.value = res.data.rows
      total.value = res.data.total
    })
    .finally(() => (tableLoading.value = false))
}

function pageChange(page) {
  currentPage.value = page.currentPage
  pageSize.value = page.pageSize
  handleQuery()
}

defineExpose({
  handleTabChangeQuery,
  onQuery,
})
</script>
