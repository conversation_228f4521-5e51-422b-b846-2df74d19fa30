<!--入驻会员-->
<template>
  <div style="padding: 20px 20px 0 20px">
    <ElTablePage
      ref="tableRef"
      :columns="columns"
      :data="pageData"
      :loading="tableLoading"
      :currentPage="currentPage"
      :pageSize="pageSize"
      :total="total"
      @page-change="pageChange"
      row-key="id"
      :tableAction="{
        width: '170',
        fixed: 'right',
      }"
    >
      <template #tableHeader>
        <div>
          <el-form :inline="true" :model="queryParams" class="demo-form-inline" @submit.prevent>
            <el-form-item label="搜索" prop="searchName">
              <el-input
                v-model="queryParams.searchName"
                clearable
                style="width: 260px"
                placeholder="请输入关键字搜索"
              ></el-input>
            </el-form-item>
            <el-form-item label="对接客服" prop="waiterId">
              <SelectLoad
                v-model="queryParams.waiterId"
                :request="listUser"
                :requestCallback="res => res.data"
                :totalCallback="res => res.data.length"
                keyValue="userId"
                keyLabel="userName"
                keyWord="userName"
                placeholder="请选择负责对接的客服"
                style="width: 280px"
              />
            </el-form-item>
            <el-form-item label="会员状态" prop="memberStatus">
              <el-select
                v-model="queryParams.memberStatus"
                placeholder="请选择"
                clearable
                style="width: 140px"
              >
                <el-option
                  v-for="dict in memberStatusList"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="注册时间" style="width: 350px">
              <el-date-picker
                v-model="queryParams.times"
                format="YYYY/M/D HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="datetimerange"
                range-separator="-"
                unlink-panels
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
                :shortcuts="shortcuts"
              ></el-date-picker>
            </el-form-item>
            <el-form-item label="会员有效期" style="width: 350px">
              <el-date-picker
                v-model="queryParams.memberTimes"
                format="YYYY/M/D HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="datetimerange"
                range-separator="-"
                unlink-panels
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
                :shortcuts="shortcuts"
              ></el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button v-btn type="primary" icon="Search" native-type="submit" @click="onQuery">
                搜索
              </el-button>
              <el-button v-btn icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </template>
      <template #bussisInfo="{ row }">
        <div>公司名称:{{ row.name || ' - ' }}</div>
        <div>注册时间:{{ row.createTime || ' - ' }}</div>
      </template>
      <template #memberFirstType="{ row }">
        <div>{{ memberTypeList.find(item => item.value == row.memberFirstType)?.label || '-' }}</div>
        <div>{{ row.memberFirstTime }}</div>
      </template>
      <template #tableAction="{ row }">
        <div>
          <el-button
            v-btn
            link
            type="primary"
            v-hasPermi="['finance:residentBusiness:businessMemberValidityFlowList']"
            @click="openDetailLog(row)"
          >
            会员开通记录
          </el-button>
        </div>
        <el-button
          v-hasPermi="['finance:video:viewOrder']"
          v-btn
          link
          type="primary"
          @click="$emit('action', row.id, 'id')"
        >
          查看关联订单
        </el-button>
      </template>
      <template #pageLeft>
        <div style="margin-right: 15px">
          <DownloadBtn
            type="success"
            plain
            icon="Download"
            v-hasPermi="['finance:residentBusiness:list:export']"
            url="/biz/business/backend/residentBusinessList/export"
            :params="getQueryParams('export')"
            fileName="入驻会员列表.xlsx"
          />
        </div>
      </template>
    </ElTablePage>
    <el-dialog
      v-model="dialogDetailVisible"
      title="会员有效期记录"
      align-center
      width="800px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      style="border-radius: 6px"
    >
      <div class="dialog-box">
        <div class="flex-start">
          <div>会员编码：{{ dialogData.memberCode }}</div>
          <div style="margin-left: 30px">
            会员状态：{{ memberStatusList.find(item => item.value == dialogData.memberStatus)?.label }}
          </div>
        </div>
        <el-divider style="margin: 8px 0" />
        <div class="dialog-box-title">有效期历史记录</div>
        <el-table height="350" :data="curDialogList">
          <el-table-column prop="createTime" label="购买/调整时间" width="200" align="center">
            <template #default="{ row }">
              {{ parseTime(row.createTime, '{y}-{m}-{d}') }}
            </template>
          </el-table-column>
          <el-table-column prop="memberPackageType" label="会员套餐" align="center">
            <template #default="{ row }">
              {{ memberTypeList.find(item => item.value == row.memberPackageType)?.label || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="originMemberValidity" label="开始时间" align="center"></el-table-column>
          <el-table-column prop="resultMemberValidity" label="到期时间" align="center" />
          <el-table-column prop="type" label="类型" align="center" width="200">
            <template #default="{ row }">
              {{ resultMemberList.find(item => item.value == row.type)?.label || '-'
              }}{{
                // row.presentedTime && row.presentedTime > 0 ? '(加赠一月)' : ''
                handleSendTime(row.presentedTime, row.presentedTimeType)
              }}
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import SelectLoad from '@/components/Select/SelectLoad.vue'
import ElTablePage from '@/components/Table/ElTablePage.vue'
import DownloadBtn from '@/components/Button/DownloadBtn.vue'
import { parseTime } from '@/utils/ruoyi'
import { getResidentBusinessList, getBusinessMemberFlowList } from '@/api/finance/refund.js'
import { payTypeSelectList, payTypeMap } from '@/utils/dict'
import { listUser } from '@/api/system/user'
import { setMealTypeList } from '../../data.js'
import { memberStatusList, memberTypeList, resultMemberList } from '../data.js'

const emits = defineEmits(['action'])

const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const tableLoading = ref(false)
const queryParams = ref({
  waiterId: '',
  memberStatus: '',
  times: [],
  memberTimes: [],
  searchName: '',
})
const pageData = ref([])

const shortcuts = [
  {
    text: '今天',
    value: () => {
      const start = new Date()
      start.setHours(0, 0, 0, 0)
      const end = new Date()
      end.setHours(23, 59, 59, 59)
      return [start, end]
    },
  },
]

const columns = reactive([
  // { prop: 'ownerAccount', label: '商家账号', width: '150' },
  { slot: 'bussisInfo', label: '商家信息', minWidth: '250' },
  { prop: 'memberCode', label: '会员编码', width: '150' },
  { prop: 'waiterName', label: '对接客服', width: '150' },
  {
    prop: 'memberStatus',
    label: '会员状态',
    width: '150',
    handle: memberStatus => {
      return memberStatusList.find(item => item.value == memberStatus)?.label
    },
  },
  { slot: 'memberFirstType', label: '会员首次购买', width: '300' },
  {
    prop: 'memberValidity',
    label: '会员有效期',
    width: '180',
  },
])

function getQueryParams(type = '') {
  let params = {
    businessCreateBegin: '',
    businessCreateEnd: '',
    memberValidityBegin: '',
    memberValidityEnd: '',
    searchName: queryParams.value.searchName,
    pageNum: type == 'export' ? '' : currentPage.value,
    pageSize: type == 'export' ? '' : pageSize.value,
    waiterId: queryParams.value.waiterId,
    memberStatus: queryParams.value.memberStatus,
  }
  if (queryParams.value.times != [] && queryParams.value.times != null) {
    params.businessCreateBegin = queryParams.value.times[0]
    params.businessCreateEnd = queryParams.value.times[1]
  }
  if (queryParams.value.memberTimes != [] && queryParams.value.memberTimes != null) {
    params.memberValidityBegin = queryParams.value.memberTimes[0]
    params.memberValidityEnd = queryParams.value.memberTimes[1]
  }
  return params
}

function handleSendTime(time, type) {
  if (time && time > 0) {
    if (type == 1) {
      return `(加赠${time}天)`
    } else if (type == 2) {
      return `(加赠${time}月)`
    } else if (type == 3) {
      return `(加赠${time}年)`
    }
  } else {
    return ''
  }
}

function onQuery() {
  currentPage.value = 1
  getList(getQueryParams())
}

function handleQuery() {
  getList(getQueryParams())
}
function handleTabChangeQuery() {
  pageData.value = []
  onQuery()
}

function resetQuery() {
  queryParams.value = {
    waiterId: '',
    memberStatus: '',
    times: [],
    memberTimes: [],
    searchName: '',
  }
  handleQuery()
}

function getList(param) {
  tableLoading.value = true
  getResidentBusinessList(param)
    .then(res => {
      pageData.value = res.data.rows
      total.value = res.data.total
    })
    .finally(() => (tableLoading.value = false))
}

function pageChange(page) {
  currentPage.value = page.currentPage
  pageSize.value = page.pageSize
  handleQuery()
}

const dialogDetailVisible = ref(false)
const dialogData = ref({
  memberCode: '',
  memberStatus: '',
})
const curDialogList = ref([])
const openDetailLog = row => {
  dialogData.value = row
  dialogDetailVisible.value = true
  getDetailsDialog(row.id)
}

function getDetailsDialog(businessId) {
  getBusinessMemberFlowList({ businessId }).then(res => {
    curDialogList.value = res.data.rows
  })
}

defineExpose({
  handleTabChangeQuery,
  onQuery,
})
</script>

<style scoped lang="scss">
.dialog-box {
  &-title {
    font-weight: 600;
    margin-bottom: 10px;
  }
}
</style>
