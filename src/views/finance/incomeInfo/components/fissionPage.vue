<template>
  <div style="padding: 20px 20px 0 20px;">
    <ElTablePage
      ref="tableRef"
      :columns="columns"
      :data="tableData"
      :loading="tableLoading"
      :currentPage="pageNum"
      :pageSize="pageSize"
      :total="total"
      :tableOptions="{
        border: true,
      }"
      row-key="relevanceId"
      @page-change="pageChange"
    >
      <template #tableHeader>
        <div>
          <el-form
            class="flex-start"
            style="flex-wrap: wrap"
            :model="queryParams"
            ref="queryRef"
            :inline="true"
            label-width="68px"
            @submit.prevent
          >
            <el-form-item label="搜索" label-width="40px">
              <el-input
                v-model="queryParams.keyword"
                clearable
                style="width: 280px"
                placeholder="请输入微信名/姓名/公司名称/会员编码等"
              />
            </el-form-item>
            <el-form-item label="结算来源">
              <el-select
                v-model="queryParams.channelType"
                placeholder="请选择"
                clearable
                style="width: 180px"
              >
                <el-option
                  v-for="item in settlementChannelTypeList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="结算方案">
              <el-select v-model="queryParams.settleType" placeholder="请选择" clearable style="width: 180px">
                <el-option
                  v-for="item in settlementTypeList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="收款方式">
              <el-select
                v-model="queryParams.withdrawalAccountType"
                placeholder="请选择"
                clearable
                style="width: 180px"
              >
                <el-option
                  v-for="item in channelWithdrawTypeList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="结算时间" style="width: 400px">
              <el-date-picker
                v-model="queryParams.time"
                format="YYYY/M/D HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="datetimerange"
                range-separator="-"
                unlink-panels
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
              ></el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button v-btn type="primary" icon="Search" native-type="submit" @click="onQuery">
                搜索
              </el-button>
              <el-button v-btn icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </template>
      <template #info="{ row }">
        <SeedInfo :row="row" />
      </template>
      <template #inviteInfo="{ row }">
        <div style="text-align: left">
          <span>微信：{{ row.inviteBizUserNickName || '-' }}</span>
          <br />
          <span v-if="row.inviteBusinessName">
            {{ row.inviteBusinessName }}
          </span>
          <span v-if="row.inviteMemberCode">({{ row.inviteMemberCode }})</span>
        </div>
      </template>
      <template #orderNums="{ row }">
        <div v-if="row.orderNumList?.length">
          <div v-for="item in row.orderNumList.slice(0, 5)">{{ item }}</div>
          <el-tooltip v-if="row.orderNumList?.length > 5" effect="dark" placement="top" trigger="click">
            <template #content>
              <div v-for="item in row.orderNumList">{{ item }}</div>
            </template>
            <el-button v-btn link type="primary">查看更多</el-button>
          </el-tooltip>
        </div>
        <div v-else>-</div>
      </template>
      <template #receiptInfo="{ row }">
        <ReceiptInfo :row="row" />
      </template>
      <template #settlementInfo="{ row }">
        <div style="text-align: left">
          <div>打款账户：{{ row.payAccount || '-' }}</div>
          <div>打款时间：{{ row.payoutTime || '-' }}</div>
          <div>
            <span>打款凭证：</span>
            <el-button
              v-if="row.resourceUrlList?.length"
              v-btn
              link
              type="primary"
              @click="viewResource(row.resourceUrlList)"
            >
              查看
            </el-button>
            <span v-else>-</span>
          </div>
          <div>结算人：{{ row.withdrawalUserName || '-' }}</div>
          <div>结算时间：{{ row.withdrawalTime || '-' }}</div>
        </div>
      </template>

      <template #pageLeft>
        <div style="margin-right: 15px">
          <DownloadBtn
            type="success"
            plain
            icon="Download"
            url="/biz/member-seed-record/backend/queryFissionSettleRecordList/export"
            :params="handleParams"
            v-hasPermi="['finance:fissionRecord:export']"
          />
        </div>
      </template>
    </ElTablePage>
  </div>
</template>

<script setup>
import ElTablePage from '@/components/Table/ElTablePage'
import DownloadBtn from '@/components/Button/DownloadBtn'
import SeedInfo from '@/views/finance/components/tableColumns/seedInfo.vue'
import ReceiptInfo from '@/views/finance/components/tableColumns/receiptInfo.vue'
// import { checkPermi } from '@/utils/permission'
import { settlementTypeList, settlementChannelTypeList } from '@/views/finance/incomeInfo/data'
import { channelWithdrawTypeList } from '@/views/finance/zhongcao/data'
import { queryFissionSettleRecordList } from '@/api/channel/zhongcao.js'
import { useViewer } from '@/hooks/useViewer'

const { showViewer } = useViewer()

defineExpose({
  onQuery,
})

const columns = [
  { prop: 'withdrawalNum', label: '提现单号', width: '150' },
  { slot: 'info', prop: 'info', label: '种草官信息', minWidth: '250' },
  { slot: 'inviteInfo', prop: 'inviteInfo', label: '邀请商家信息', minWidth: '250' },
  { slot: 'orderNums', prop: 'orderNums', label: '订单信息', minWidth: '250' },
  { prop: 'channelType', label: '结算来源', width: '110', handle: data => {
    let item = settlementChannelTypeList.find(item => item.value == data)
    return item ? item.label : '-'
  }},
  {
    prop: 'settleType',
    label: '结算方案',
    width: '150',
    handle: data => {
      let item = settlementTypeList.find(item => item.value == data)
      return item ? item.label : '-'
    },
  },
  {
    prop: 'settleRage',
    label: '方案数据',
    width: '150',
    handle: (data, row) => {
      if (row.settleType == 1) {
        return data
      }
      if (row.settleType == 2) {
        return data + '%'
      }
      return '-'
    },
  },
  { prop: 'orderSettleAmount', label: '结算金额', width: '150' },
  { prop: 'createTime', label: '申请时间', width: '180' },
  { slot: 'receiptInfo', prop: 'receiptInfo', label: '收款信息', minWidth: '200' },
  { slot: 'settlementInfo', prop: 'settlementInfo', label: '结算信息', minWidth: '300' },
]

const tableRef = ref()
const tableData = ref([])
const pageNum = ref(1)
const pageSize = ref(20)
const total = ref(0)
const tableLoading = ref(false)

const queryParams = ref({
  keyword: '',
  channelType: '',
  settleType: '',
  withdrawalAccountType: '',
  time: [],
})

function handleParams() {
  let params = {
    keyword: queryParams.value.keyword.trim(),
    channelType: queryParams.value.channelType,
    settleType: queryParams.value.settleType,
    withdrawalAccountType: queryParams.value.withdrawalAccountType,
  }

  if (queryParams.value.time && queryParams.value.time.length == 2) {
    params.settleStartTime = queryParams.value.time[0]
    params.settleEndTime = queryParams.value.time[1]
  }
  return params
}

function onQuery() {
  pageNum.value = 1
  handleQuery()
}

function resetQuery() {
  queryParams.value = {
    keyword: '',
    channelType: '',
    settleType: '',
    withdrawalAccountType: '',
    time: [],
  }
  onQuery()
}

function handleQuery() {
  tableLoading.value = true
  const params = {
    pageNum: pageNum.value,
    pageSize: pageSize.value,
    ...handleParams(),
  }
  queryFissionSettleRecordList(params)
    .then(res => {
      if (res.data) {
        tableData.value = res.data.rows
        total.value = res.data.total
      }
    })
    .finally(() => {
      tableLoading.value = false
    })
}

function pageChange(page) {
  pageNum.value = page.currentPage
  pageSize.value = page.pageSize
  handleQuery()
}

function viewResource(list) {
  showViewer(list, { raw: true })
}
</script>

<style scoped lang="scss"></style>
