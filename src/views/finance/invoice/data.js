const invoiceStatusList = [
  { label: '待开票', value: 1 },
  { label: '待红冲', value: 2 },
  { label: '已完成', value: 3 },
]

const sourceList = [
  { label: '商家申请', value: 1 },
  { label: '红冲重开', value: 2 },
]

const invoiceTypeList = [
  { label: '增值税普通发票', value: 1 },
  { label: '形式发票', value: 2 },
]

const orderTypeList = [
  { label: '视频订单', value: 0 },
  { label: '会员订单', value: 1 },
  { label: '线上钱包充值', value: 5 },
]

const redPunshTypeList = [
  { label: '商家提现', value: 2 },
  { label: '重开发票', value: 1 },
]

const orderInvoiceOperateTypeEnum = [
  { label: '申请开票', value: 1 },
  { label: '取消开票', value: 2 },
  { label: '确认开票', value: 3 },
  { label: '修改开票信息', value: 4 },
  { label: '上传发票', value: 5 },
  { label: '重新上传发票', value: 6 },
  { label: '审核发票', value: 7 },
  { label: '红冲提醒', value: 8 },
  { label: '标记红冲', value: 9 },
  { label: '标记红冲', value: 10 },
  { label: '标记红冲', value: 11 },
  { label: '重开发票', value: 12 },
  { label: '红冲重开', value: 13 },
]

const refundTypeList = [
  { label: '补偿', value: 1 },
  { label: '取消订单', value: 2 },
  { label: '取消选配', value: 3 },
  { label: '线上钱包充值', value: 8 },
]

const invoiceStatusMap = {}
invoiceStatusList.forEach(item => {
  invoiceStatusMap[item.label] = item.value
  invoiceStatusMap[item.value] = item.label
})
export {
  invoiceStatusMap,
  invoiceStatusList,
  sourceList,
  invoiceTypeList,
  orderTypeList,
  redPunshTypeList,
  orderInvoiceOperateTypeEnum,
  refundTypeList,
}
