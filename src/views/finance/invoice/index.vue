<template>
  <div style="padding-bottom: 45px;">
    <Statistic :key="statisticKey" />
    <el-tabs v-model="curTab" class="tabs" @tab-change="handleTabChange">
      <el-tab-pane v-for="(tab, i) in tabList" :key="tab.value" :name="tab.value">
        <template #label>
          <div v-if="i != tabList.length - 1">
            {{ tab.label }}
            {{ '(' }}
            <span style="color: red">{{ tab.number }}</span>
            {{ ')' }}
          </div>
          <div v-else>{{ tab.label }}</div>
        </template>
      </el-tab-pane>
    </el-tabs>

    <WaitInvoice ref="waitInvoiceRef" v-if="curTab == '1'" @action="handleAction" />
    <WaitRedPunch ref="waitRedPunchRef" v-if="curTab == '2'" @action="handleAction" />
    <DoneInvoice ref="doneInvoiceRef" v-if="curTab == '3'" @action="handleAction" />
    <InvoiceDetailDialog ref="invoiceDetailDialogRef" />
    <CancelDialog ref="cancelDialogRef" @success="handleQuery" />
    <SignDialog ref="signDialogRef" @success="handleQuery" />
    <MakeInvoiceDialog ref="makeInvoiceDialogRef" @success="handleQuery" />
    <ConfirmDialog ref="confirmDialogRef" @success="handleQuery" />
    <UploadDialog ref="uploadDialogRef" @success="handleUpload" />
    <EditDialog ref="editDialogRef" @success="handleQuery" />

    <InvoiceDialog ref="invoiceDialog" :url="invoiceDialogUrl" />
  </div>
</template>

<script setup>
import Statistic from '@/views/finance/invoice/components/statistic'
import WaitInvoice from '@/views/finance/invoice/components/waitInvoice.vue'
import WaitRedPunch from '@/views/finance/invoice/components/waitRedPunch.vue'
import DoneInvoice from '@/views/finance/invoice/components/doneInvoice.vue'
import InvoiceDetailDialog from '@/views/finance/invoice/components/invoiceDetailDialog.vue'
import CancelDialog from '@/views/finance/invoice/components/cancelDialog.vue'
import SignDialog from '@/views/finance/invoice/components/signDialog.vue'
import MakeInvoiceDialog from '@/views/finance/invoice/components/makeInvoiceDialog.vue'
import ConfirmDialog from '@/views/finance/invoice/components/confirmDialog.vue'
import UploadDialog from '@/views/finance/invoice/components/uploadDialog.vue'
import EditDialog from '@/views/finance/invoice/components/editDialog.vue'

import InvoiceDialog from '@/components/Dialog/invoice'
import { invoiceStatusMap } from '@/views/finance/invoice/data.js'
import { useViewer } from '@/hooks/useViewer'
import { getbackInvoiceStatistics } from '@/api/finance/invoice'

const { showViewer } = useViewer()

const { proxy } = getCurrentInstance()

const route = useRoute()

const waitInvoiceRef = ref(null)
const waitRedPunchRef = ref(null)
const doneInvoiceRef = ref(null)
const invoiceDetailDialogRef = ref(null)
const cancelDialogRef = ref(null)
const signDialogRef = ref(null)
const makeInvoiceDialogRef = ref(null)
const confirmDialogRef = ref(null)
const uploadDialogRef = ref(null)
const editDialogRef = ref(null)

const curTab = ref(1)
const tabList = ref([
  { label: '待开票', value: invoiceStatusMap['待开票'], number: 0 },
  { label: '待红冲', value: invoiceStatusMap['待红冲'], number: 0 },
  { label: '已完成', value: invoiceStatusMap['已完成'] },
])

const statisticKey = ref(0)

const invoiceDialog = ref()
const invoiceDialogUrl = ref('')

function handleTabChange(val) {
  curTab.value = val
  getInvoiceStatistics()
}

function handleAction(id, btn, tab) {
  switch (btn) {
    case '查看':
      invoiceDetailDialogRef.value?.open(id, tab)
      break
    case '取消开票':
      cancelDialogRef.value?.open(id)
      break
    case '标记':
      signDialogRef.value?.open(id)
      break
    case '审核':
      makeInvoiceDialogRef.value?.open(id)
      break
    case '上传':
    case '重新上传':
      uploadDialogRef.value?.open(id, tab)
      break
    case '修改':
      editDialogRef.value?.open(id)
      break
    case '确认':
      confirmDialogRef.value?.open(id)
      break
    default:
      break
  }
}

function handleQuery() {
  getInvoiceStatistics()
  if (curTab.value == 1) {
    waitInvoiceRef.value?.handleQuery()
  } else if (curTab.value == 2) {
    waitRedPunchRef.value?.handleQuery()
  }
}
function handleUpload(id = null) {
  getInvoiceStatistics()
  if (curTab.value == 1) {
    waitInvoiceRef.value?.handleQuery()
  } else if (curTab.value == 2) {
    waitRedPunchRef.value?.handleQuery()
  }
  if (id) {
    confirmDialogRef.value?.open(id)
  }
}

function getInvoiceStatistics() {
  statisticKey.value++
  getbackInvoiceStatistics().then(res => {
    if (res.data) {
      tabList.value.forEach(item => {
        if (item.label == '待开票') {
          item.number = res.data?.quantityToBeInvoiced || 0
        } else if (item.label == '待红冲') {
          item.number = res.data?.quantityToBeFlushed || 0
        }
      })
    }
  })
}

function init() {
  if (route.query.tab) {
    let tab = tabList.value.find(item => item.value == route.query.tab)
    if (tab) {
      curTab.value = tab.value
    }
  }
}
init()
getInvoiceStatistics()
</script>

<style scoped lang="scss">
.tabs {
  padding: 0 20px;
}
.total-amount {
  color: red;
  margin: 0 5px 18px auto;
  font-size: 16px;
}
.form-box {
  .file-item {
    width: 100%;

    .text {
      max-width: 90%;
      cursor: pointer;

      &:hover {
        color: var(--el-color-primary);
      }
    }
    .el-icon {
      cursor: pointer;
      color: var(--el-color-primary);
    }
  }
}
.form-box {
  .file-item {
    width: 100%;

    .text {
      max-width: 90%;
      cursor: pointer;

      &:hover {
        color: var(--el-color-primary);
      }
    }
    .el-icon {
      cursor: pointer;
      color: var(--el-color-primary);
    }
  }
}
</style>
