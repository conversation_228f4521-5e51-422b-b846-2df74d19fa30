<template>
  <div>
    <el-dialog
      v-model="visible"
      title="标记红冲"
      align-center
      width="1100"
      destroy-on-close
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      @close="handleClose"
    >
      <div style="height: 70vh; overflow-x: hidden">
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="left-box">
              <div class="text-warp">
                <div class="fs-0">发票抬头：</div>
                <div class="more-ell" style="--l: 5">{{ detailData.orderInvoiceDetailVO?.title || '-' }}</div>
              </div>
              <div class="text-warp">
                <div class="fs-0">&emsp;&emsp;税号：</div>
                <div class="more-ell" style="--l: 5">
                  {{ detailData.orderInvoiceDetailVO?.dutyParagraph || '-' }}
                </div>
              </div>
              <div
                v-if="detailData.orderInvoiceDetailVO && detailData.orderInvoiceDetailVO.cautions"
                class="text-warp"
              >
                <div class="fs-0">注意事项：</div>
                <div class="more-ell" style="--l: 5">
                  {{ detailData.orderInvoiceDetailVO?.cautions || '-' }}
                </div>
              </div>
              <div>开票金额：{{ detailData.orderInvoiceDetailVO?.invoiceAmount || '-' }}元</div>
              <div class="text-warp">
                <div class="fs-0">&emsp;发票号：</div>
                <div class="more-ell" style="--l: 5">
                  {{ detailData.orderInvoiceDetailVO?.number || '-' }}
                </div>
              </div>
              <div style="display: flex">
                <div>&emsp;&emsp;发票：</div>
                <div style="flex: 1" v-if="detailData.orderInvoiceDetailVO?.objectKeys">
                  <PasteUpload
                    v-model="files"
                    :limit="1"
                    :multiple="false"
                    :fileType="['jpg', 'jpeg', 'png', 'pdf', 'ofd', 'xml']"
                    show-file-list
                    @view-pdf="handleViewPdf"
                    :delete-button="false"
                    :size="3"
                  />
                  <!-- <template
                    v-if="
                      detailData.orderInvoiceDetailVO?.objectKey.includes('.jpg') ||
                      detailData.orderInvoiceDetailVO?.objectKey.includes('.png') ||
                      detailData.orderInvoiceDetailVO?.objectKey.includes('.jpeg')
                    "
                  >
                    <ViewerImageList
                      :data="[detailData.orderInvoiceDetailVO.objectKey]"
                      is-preview-all
                      :show-delete-btn="false"
                    />
                  </template>
                  <template v-else>
                    <PasteUpload
                      v-model="files"
                      :limit="1"
                      :multiple="false"
                      :fileType="['jpg', 'jpeg', 'png', 'pdf', 'ofd', 'xml']"
                      show-file-list
                      @view-pdf="handleViewPdf"
                      :delete-button="false"
                      :size="3"
                    />
                  </template> -->
                </div>
                <span v-else>-</span>
              </div>
            </div>
            <div class="left-table" style="max-height: 450px; overflow: auto">
              <template v-if="detailData?.title">
                <div>
                  <div style="margin: 10px 0; font-weight: 600">商家发起-重开信息：</div>
                  <div class="left-bottom">
                    <div class="text-warp">
                      <div class="fs-0">发票抬头：</div>
                      <div class="more-ell" style="--l: 5">{{ detailData?.title || '-' }}</div>
                    </div>
                    <div class="text-warp">
                      <div class="fs-0">&emsp;&emsp;税号：</div>
                      <div class="more-ell" style="--l: 5">{{ detailData?.dutyParagraph || '-' }}</div>
                    </div>
                    <div>开票金额：{{ detailData.invoiceAmount || '-' }}元</div>
                    <div v-if="detailData.invoiceRemark" class="text-warp">
                      <div class="fs-0">&emsp;&emsp;备注：</div>
                      <div>
                        {{ detailData.invoiceRemark || '-' }}
                      </div>
                    </div>
                  </div>
                </div>
              </template>
              <template v-if="orderInvoiceRedList && orderInvoiceRedList.length > 0">
                <div class="left-table-title">提现金额：{{ withdrawalAmount || 0 }}元</div>
                <el-table :data="orderInvoiceRedList" style="max-height: 350px; overflow: auto" border>
                  <el-table-column
                    prop="orderNum"
                    label="订单号"
                    align="center"
                    width="100"
                    v-if="detailData.orderInvoiceDetailVO?.orderType == 5"
                  />
                  <el-table-column
                    prop="videoCode"
                    label="视频编码"
                    align="center"
                    width="80"
                    v-else
                  />
                  <el-table-column prop="payType" label="支付方式" align="center">
                    <template v-slot="{ row }">
                      {{ payTypeMapNew[row.payType] || '-' }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="refundType" label="退款类型" align="center" width="80">
                    <template v-slot="{ row }">
                      {{ handleRefundType(row.refundType) }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="withdrawDepositTime" label="提现时间" align="center">
                    <template v-slot="{ row }">
                      {{ row.withdrawDepositTime || '-' }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="withdrawDepositAmount" label="提现金额（元）" align="center">
                    <template v-slot="{ row }">
                      {{ row.withdrawDepositAmount || '-' }}
                    </template>
                  </el-table-column>
                </el-table>
              </template>
            </div>
          </el-col>
          <el-col :span="12">
            <el-form :model="formData" :rules="rules" ref="ruleFormRef" label-width="100px">
              <el-form-item label="是否红冲：" prop="invoiceRedStatus">
                <el-radio-group v-model="formData.invoiceRedStatus">
                  <el-radio-button value="3">红冲</el-radio-button>
                  <!-- :disabled="detailData.invoiceRedCause == 1" -->
                  <el-radio-button value="2">不红冲</el-radio-button>
                </el-radio-group>
              </el-form-item>
              <template v-if="!isNoRed">
                <el-form-item label="红冲票号：" prop="invoiceRedNumber">
                  <el-input
                    placeholder="请输入红冲票号"
                    v-model="formData.invoiceRedNumber"
                    style="width: 90%"
                  />
                </el-form-item>
                <el-form-item label="红冲金额：" prop="invoiceRedAmount">
                  <el-input-number
                    title=""
                    :controls="false"
                    clearable
                    v-model="formData.invoiceRedAmount"
                    :min="0"
                    :max="9999999"
                    @keydown="channelInputLimit"
                    :precision="2"
                    style="width: 90%"
                  />
                  &emsp;元
                </el-form-item>
                <el-form-item label="上传发票：" prop="file">
                  <PasteUpload
                    style="width: 90%"
                    v-model="formData.file"
                    :limit="1"
                    :multiple="false"
                    :fileType="['jpg', 'jpeg', 'png', 'pdf', 'ofd', 'xml']"
                    show-file-list
                    @view-pdf="handleViewPdf"
                    :size="3"
                  />
                </el-form-item>
                <el-form-item label="是否重开：" prop="isReopen">
                  <el-radio-group v-model="formData.isReopen">
                    <el-radio-button value="1">重开</el-radio-button>
                    <!-- :disabled="detailData.invoiceRedCause == 1" -->
                    <el-radio-button value="0">不重开</el-radio-button>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="重开金额：" prop="reopenAmount" v-if="formData.isReopen == '1'">
                  <el-input-number
                    title=""
                    :controls="false"
                    clearable
                    v-model="formData.reopenAmount"
                    :min="0"
                    :max="9999999"
                    @keydown="channelInputLimit"
                    :precision="2"
                    style="width: 90%"
                  />
                  &emsp;元
                </el-form-item>
              </template>
              <el-form-item label="备注：">
                <el-input
                  type="textarea"
                  show-word-limit
                  v-model="formData.remark"
                  maxlength="800"
                  :rows="6"
                  style="width: 90%"
                />
              </el-form-item>
              <el-form-item>
                <div class="flex-center" style="flex: 1">
                  <el-button @click="handleClose">取消</el-button>
                  <el-button type="primary" @click="handleConfirm">确定</el-button>
                </div>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </div>
    </el-dialog>
    <InvoiceDialog ref="invoiceDialog" :url="invoiceDialogUrl" />
  </div>
</template>

<script setup>
import { ElMessage } from 'element-plus'
import { payTypeMapNew } from '@/utils/dict'
import ViewerImageList from '@/components/ImagePreview/viewerImageList.vue'
import PasteUpload from '@/components/FileUpload/pasteUpload'
import InvoiceDialog from '@/components/Dialog/invoice'
import { refundTypeList } from '@/views/finance/invoice/data.js'
import { getRedInvoiceDetail, markRedInvoice } from '@/api/finance/invoice'

const { proxy } = getCurrentInstance()

const visible = ref(false)

const emits = defineEmits(['success'])

const formData = ref({
  invoiceRedStatus: '3',
  invoiceRedNumber: '',
  invoiceRedAmount: 0,
  file: [],
  isReopen: '1',
  reopenAmount: 0,
  remark: '',
})
const ruleFormRef = ref(null)

const isNoRed = computed(() => {
  return formData.value.invoiceRedStatus == '2'
})

const rules = {
  invoiceRedStatus: [{ required: true, message: '请选择是否红冲', trigger: 'blur' }],
  invoiceRedNumber: [
    { required: true, message: '请输入红冲票号', trigger: 'blur' },
    {
      pattern: /^[0-9a-zA-Z]+$/,
      message: '红冲票号只能包含数字和字母',
      trigger: 'change',
    },
  ],
  invoiceRedAmount: [{ required: true, message: '请输入红冲金额', trigger: 'blur' }],
  file: [{ required: true, message: '请上传发票', trigger: 'blur' }],
  isReopen: [{ required: true, message: '请选择是否重开', trigger: 'blur' }],
  reopenAmount: [{ required: true, message: '请输入重开金额', trigger: 'blur' }],
}

defineExpose({
  open,
  handleClose,
})
const files = ref([])
const invoiceId = ref('')
const detailData = ref({})
function open(id) {
  invoiceId.value = id
  getDetail()
  visible.value = true
}

function handleClose() {
  ruleFormRef.value.resetFields()
  formData.value = {
    invoiceRedStatus: '3',
    invoiceRedNumber: '',
    invoiceRedAmount: 0,
    file: [],
    isReopen: '1',
    reopenAmount: 0,
    remark: '',
  }
  files.value = []
  orderInvoiceRedList.value = []
  withdrawalAmount.value = 0
  visible.value = false
}

function handleConfirm() {
  ruleFormRef.value.validate(valid => {
    if (valid) {
      let params = { ...formData.value }
      if (params.invoiceRedStatus == '2') {
        params = {
          id: invoiceId.value,
          invoiceRedStatus: params.invoiceRedStatus,
          remark: params.remark,
        }
      } else {
        if (params.isReopen == '0') {
          params = {
            id: invoiceId.value,
            invoiceRedStatus: params.invoiceRedStatus,
            invoiceRedNumber: params.invoiceRedNumber,
            invoiceRedAmount: params.invoiceRedAmount,
            objectKey: params.file[0].picUrl,
            isReopen: params.isReopen,
            remark: params.remark,
          }
        } else {
          params.id = invoiceId.value
          params.objectKey = params.file[0].picUrl
          delete params.file
        }
      }
      markRedInvoice(params).then(res => {
        emits('success')
        ElMessage.success('标记成功')
        handleClose()
      })
    }
  })
}

const invoiceDialog = ref()
const invoiceDialogUrl = ref('')
function handleViewPdf(url) {
  invoiceDialogUrl.value = proxy.$picUrl + url
  invoiceDialog.value.open()
}

const orderInvoiceRedList = ref([])
const withdrawalAmount = ref(0)

function getDetail() {
  getRedInvoiceDetail({ invoiceRedId: invoiceId.value }).then(res => {
    detailData.value = res.data
    if (detailData.value?.invoiceRedCause == '1') {
      formData.value.invoiceRedStatus = '3'
    }
    formData.value.invoiceRedAmount = detailData.value?.orderInvoiceDetailVO?.invoiceAmount || 0
    orderInvoiceRedList.value = res.data?.orderInvoiceRedOrderVideoVOS || []
    if (orderInvoiceRedList.value.length > 0) {
      let num  = orderInvoiceRedList.value.reduce(
        (sum, item) => sum + item.withdrawDepositAmount,
        0
      )
      withdrawalAmount.value = Math.floor(num * 100) / 100
    }
    if (detailData.value.orderInvoiceDetailVO?.objectKeys) {
      detailData.value.orderInvoiceDetailVO?.objectKeys.forEach(item => {
        files.value.push({
          name: item.split('/').pop(),
          url: item,
          picUrl: item,
        })
      })
      // files.value = [
      //   {
      //     name: detailData.value.orderInvoiceDetailVO?.objectKey.split('/').pop(),
      //     url: detailData.value.orderInvoiceDetailVO?.objectKey,
      //     picUrl: detailData.value.orderInvoiceDetailVO?.objectKey,
      //   },
      // ]
    }
  })
}

const channelInputLimit = e => {
  const key = e.key
  if (key === 'Enter') e.preventDefault()
  const notAllowList = ['e', '+', '-']
  if (notAllowList.includes(key)) {
    e.returnValue = false
    return false
  }
  return true
}

function handleRefundType(type) {
  if (type == 8) {
    return '线下余额提现'
  }
  let item = refundTypeList.find(item => item.value == type)
  return item?.label || '-'
}
</script>

<style scoped lang="scss">
@import '@/assets/styles/customForm.scss';
@import '@/assets/styles/form-disabled.scss';
:deep(.el-input-number) {
  .el-input__inner {
    text-align: left;
  }
}
.left-box {
  display: grid;
  gap: 5px;
  background: #f2f2f2;
  padding: 10px 15px;
  border-radius: 10px;
}
.left-table {
  &-title {
    font-weight: 600;
    margin: 8px 0;
  }
}
.left-bottom {
  padding-left: 15px;
  display: grid;
  gap: 5px;
}
.text-warp {
  word-break: break-all;
  line-break: anywhere;
  white-space: break-spaces;
  display: flex;
  align-items: baseline;
}
</style>
