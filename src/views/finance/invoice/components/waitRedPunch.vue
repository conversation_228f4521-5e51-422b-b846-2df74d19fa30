<template>
  <div>
    <ElTablePage
      style="padding: 20px"
      ref="tableRef"
      :columns="columns"
      :data="tableData"
      :loading="tableLoading"
      :currentPage="pageNum"
      :pageSize="pageSize"
      :total="total"
      :tableAction="{
        width: '170',
        fixed: 'right',
      }"
      :tableOptions="{
        border: true,
      }"
      @page-change="pageChange"
    >
      <template #tableHeader>
        <div>
          <el-form
            class="flex-start"
            style="flex-wrap: wrap"
            :model="queryParams"
            ref="queryRef"
            :inline="true"
            label-width="68px"
            @submit.prevent
          >
            <el-form-item label="搜索">
              <el-input
                v-model="queryParams.keyword"
                clearable
                style="width: 260px"
                placeholder="请输入搜索内容"
              />
            </el-form-item>

            <el-form-item label="红冲原因" prop="invoiceRedCause">
              <el-select
                v-model="queryParams.invoiceRedCause"
                placeholder="请选择"
                clearable
                style="width: 180px"
              >
                <el-option
                  v-for="item in redPunshTypeList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="订单类型" prop="orderType">
              <el-select v-model="queryParams.orderType" placeholder="请选择" clearable style="width: 180px">
                <el-option
                  v-for="item in orderTypeList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>

            <!-- <el-form-item label="支付时间" style="width: 400px">
                <el-date-picker
                  v-model="queryParams.payTime"
                  format="YYYY/M/D HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  type="datetimerange"
                  range-separator="-"
                  unlink-panels
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
                ></el-date-picker>
              </el-form-item> -->
            <el-form-item label="申请时间" style="width: 400px">
              <el-date-picker
                v-model="queryParams.invoicingTime"
                format="YYYY/M/D HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="datetimerange"
                range-separator="-"
                unlink-panels
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
                :shortcuts="shortcuts"
              ></el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button v-btn type="primary" icon="Search" native-type="submit" @click="onQuery">
                搜索
              </el-button>
              <el-button v-btn icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
            <div class="total-amount" v-if="showInvoiceAmount">已开票金额：{{ invoiceAmount }}</div>
          </el-form>
        </div>
      </template>
      <template #videoCodes="{ row }">
        <template v-if="row.videoCodes && row.videoCodes.length > 0">
          <div v-for="item in row.videoCodes" :key="item">{{ item }}</div>
        </template>
        <span v-else>-</span>
      </template>
      <template #merchantCode="{ row }">
        <div>{{ row.merchantCode || '-' }}</div>
        <!-- <div>{{ row.waiterUser?.name }}</div> -->
      </template>
      <template #info-header="{ row }">
        <div style="margin-top: -5px">
          <div>开票信息</div>
          <div style="color: #7f7f7f; font-size: 0.7em; line-height: 8px">点击可复制内容</div>
        </div>
      </template>
      <template #info="{ row }">
        <!-- <div>商家编码：{{ row.businessInfo?.memberCode }}</div> -->
        <!-- <div>商家名称：{{ row.businessInfo?.businessName }}</div> -->
        <div style="cursor: pointer" @click="copyText(row.title, '发票抬头：')">
          发票抬头：{{ row.title }}
        </div>
        <div style="cursor: pointer" @click="copyText(row.dutyParagraph, '税号：')">
          税号：{{ row.dutyParagraph }}
        </div>
        <div
          v-if="row.cautions"
          class="more-ell"
          style="text-align: center; cursor: pointer"
          @click="copyText(row.cautions, '开票注意事项：')"
        >
          开票注意事项：{{ row.cautions }}
        </div>
      </template>

      <template #tableAction="{ row }">
        <el-button
          v-btn
          v-hasPermi="['finance:invoice:view']"
          link
          type="primary"
          @click="handleBtnAction(row.invoiceId, '查看', 2)"
        >
          查看
        </el-button>
        <el-button
          v-btn
          link
          type="primary"
          @click="handleBtnAction(row.id, '标记')"
          v-hasPermi="['finance:invoice:sign']"
        >
          标记
        </el-button>
      </template>
      <template #pageLeft>
        <div style="margin-right: 15px;">
          <DownloadBtn
            type="success"
            plain
            icon="Download"
            url="/order/invoice/to-be-red-invoice-list-export"
            :params="handleExportParams()"
            fileName="待红冲列表.xlsx"
            v-hasPermi="['finance:invoice:red-export']"
          />
        </div>
      </template>
    </ElTablePage>
  </div>
</template>

<script setup>
import ElTablePage from '@/components/Table/ElTablePage'
import DownloadBtn from '@/components/Button/DownloadBtn'
import { downUrlFile } from '@/utils/download'
import { ElMessage, ElMessageBox } from 'element-plus'
import { financeInvoiceAmount, getToBeRedInvoiceList } from '@/api/finance/invoice'
import {
  invoiceStatusMap,
  sourceList,
  invoiceTypeList,
  orderTypeList,
  redPunshTypeList,
} from '@/views/finance/invoice/data.js'
import { useViewer } from '@/hooks/useViewer'

const { showViewer } = useViewer()

const { proxy } = getCurrentInstance()

const columns = [
  { prop: 'ticketCode', label: '申票码', width: '100', handle: data => (data ? data : '-') },
  { prop: 'number', label: '红冲发票号', width: '250', handle: data => (data ? data : '-') },
  { prop: 'orderType', label: '订单类型', width: '250', handle: data => {
    let item = orderTypeList.find(i => i.value == data)
    return item ? item.label : '-'
  }},
  { slot: 'videoCodes', prop: 'videoCodes', label: '单号', width: '100' },
  {
    slot: 'merchantCode',
    label: '商家编码',
    width: '100',
  },
  { slot: 'info', prop: 'info', label: `开票信息`, labelSlot: 'info-header', minWidth: '230' },
  { prop: 'invoiceAmount', label: '开票金额(元)', width: '100', handle: data => `${data}` },
  {
    prop: 'invoiceRedCause',
    label: '红冲原因',
    width: '100',
    handle: invoiceRedCause => {
      let s = redPunshTypeList.find(item => item.value == invoiceRedCause)
      return s ? s.label : '-'
    },
  },
  { prop: 'applyTime', label: '发起时间', width: '180', handle: data => `${data ? data : '-'}` },
]

const tableData = ref([])
const pageNum = ref(1)
const pageSize = ref(20)
const total = ref(0)
const tableLoading = ref(false)

const showInvoiceAmount = ref(false)
const invoiceAmount = ref(0)
defineExpose({ handleQuery })
const emits = defineEmits(['action'])

const queryParams = ref({
  keyword: '',
  invoiceRedCause: '',
  orderType: '',
  invoicingTime: [],
})

const shortcuts = [
  {
    text: '今天',
    value: () => {
      const start = new Date()
      start.setHours(0, 0, 0, 0)
      const end = new Date()
      end.setHours(23, 59, 59, 59)
      return [start, end]
    },
  },
]

//复制剪切板
function copyText(text, type) {
  if (navigator.clipboard && text) {
    let copyText = type + text
    navigator.clipboard
      .writeText(copyText)
      .then(function () {
        ElMessage.success('内容已复制到剪贴板')
      })
      .catch(function (err) {
        console.error('Failed to copy text: ', err)
      })
  }
}

function resetQuery() {
  queryParams.value = {
    keyword: '',
    invoiceRedCause: '',
    orderType: '',
    invoicingTime: [],
  }
  pageNum.value = 1
  handleQuery()
}
function onQuery() {
  pageNum.value = 1
  handleQuery()
}
function handleExportParams() {
  let { invoicingTime, ...data } = queryParams.value

  if (invoicingTime && invoicingTime.length) {
    data.applyTimeBegin = invoicingTime[0]
    data.applyTimeEnd = invoicingTime[1]
  }
  return data
}
function handleQuery() {
  tableLoading.value = true
  let params = handleExportParams()
  // if (params.applyTimeBegin && params.applyTimeEnd) {
  //   financeInvoiceAmount(params).then(res => {
  //     showInvoiceAmount.value = true
  //     invoiceAmount.value = res.data
  //   })
  // } else {
  //   showInvoiceAmount.value = false
  // }
  params.pageNum = pageNum.value
  params.pageSize = pageSize.value
  getToBeRedInvoiceList(params)
    .then(res => {
      tableData.value = res.data.rows || []
      total.value = res.data.total
    })
    .finally(() => (tableLoading.value = false))
}
// 分页跳转
function pageChange(page) {
  pageNum.value = page.currentPage
  pageSize.value = page.pageSize
  handleQuery()
}

function handleBtnAction(row, btn, tab) {
  emits('action', row, btn, tab)
}

handleQuery()
</script>

<style scoped lang="scss">
.tabs {
  padding: 0 20px;
}
.total-amount {
  color: red;
  margin: 0 5px 18px auto;
  font-size: 16px;
}
.form-box {
  .file-item {
    width: 100%;

    .text {
      max-width: 90%;
      cursor: pointer;

      &:hover {
        color: var(--el-color-primary);
      }
    }
    .el-icon {
      cursor: pointer;
      color: var(--el-color-primary);
    }
  }
}
.form-box {
  .file-item {
    width: 100%;

    .text {
      max-width: 90%;
      cursor: pointer;

      &:hover {
        color: var(--el-color-primary);
      }
    }
    .el-icon {
      cursor: pointer;
      color: var(--el-color-primary);
    }
  }
}
</style>
