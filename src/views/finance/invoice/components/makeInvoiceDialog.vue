<template>
  <div>
    <el-dialog
      v-model="visible"
      title="开票信息"
      align-center
      width="600"
      destroy-on-close
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      @close="handleClose"
    >
      <div class="invoice-info">
        <div>
          发票类型：{{ invoiceTypeList.find(item => item.value == detailData.invoiceType)?.label || '-' }}
        </div>
        <div>商家名称：{{ detailData.merchant?.name || '-' }}</div>
        <div>商家编码：{{ detailData.merchantCode || '-' }}</div>
        <div>
          订单类型：{{ orderTypeList.find(item => item.value == detailData.orderType)?.label || '-' }}
        </div>
        <!-- <template v-if="detailData.orderType == 1">
          <div>支付方式：{{ payTypeMapNew[detailData.payType] || '-' }}</div>
          <div>订单总额：{{ detailData.payAmount || '-' }}</div>
          <div>实付金额：{{ detailData.invoiceAmount || '-' }}元</div>
        </template> -->
        <div>包含订单：{{ relevantOrderList.length }}笔</div>
        <el-table border :data="relevantOrderList" style="max-height: 200px; overflow: auto">
          <el-table-column prop="orderNum" label="订单号" align="center"></el-table-column>
          <el-table-column prop="payType" label="支付方式" align="center">
            <template v-slot="{ row }">
              {{ payTypeMapNew[row.payType] || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="payTime" label="支付时间" align="center"></el-table-column>
          <el-table-column prop="invoiceAmount" label="开票金额（元）" align="center"></el-table-column>
        </el-table>
        <div>实付合计：{{ tableAmount ? tableAmount.toFixed(2) : '0' }}元</div>
      </div>
      <el-divider style="margin: 10px 0" border-style="dashed" />
      <div v-if="detailData.invoiceType == 1">
        <el-form :model="formData" :rules="rules" ref="ruleFormRef" label-width="100px">
          <el-form-item label="开票金额：" prop="invoiceAmount">
            <el-input-number
              title=""
              :controls="false"
              clearable
              v-model="formData.invoiceAmount"
              :min="0"
              :max="9999999"
              @keydown="channelInputLimit"
              :precision="2"
              style="width: 80%"
            />
            &emsp;元
          </el-form-item>
          <el-form-item label="发票抬头：" prop="title">
            <el-input placeholder="请输入发票抬头" v-model="formData.title" style="width: 80%" />
          </el-form-item>
          <el-form-item label="税号：" prop="dutyParagraph">
            <el-input placeholder="请输入税号" v-model="formData.dutyParagraph" style="width: 80%" />
          </el-form-item>
          <el-form-item label="注意事项：" prop="cautions">
            <el-input
              type="textarea"
              show-word-limit
              v-model="formData.cautions"
              maxlength="800"
              placeholder="请输入注意事项"
              :rows="2"
              resize="none"
            />
          </el-form-item>
        </el-form>
      </div>
      <div v-else>
        <el-form :model="formTwoData" :rules="twoRules" ref="ruleTwoFormRef" label-width="100px">
          <el-form-item label="开票金额：" prop="invoiceAmount">
            <el-input-number
              title=""
              :controls="false"
              clearable
              v-model="formTwoData.invoiceAmount"
              :min="0"
              :max="9999999"
              @keydown="channelInputLimit"
              :precision="2"
              style="width: 80%"
            />
            &emsp;元
          </el-form-item>
          <el-form-item label="公司名称：" prop="companyName">
            <el-input placeholder="请输入公司名称" v-model="formTwoData.companyName" style="width: 80%" />
          </el-form-item>
          <el-form-item label="公司地址：" prop="companyAddress">
            <el-input placeholder="请输入公司地址" v-model="formTwoData.companyAddress" style="width: 80%" />
          </el-form-item>
          <el-form-item label="联系电话：" prop="companyPhone">
            <el-input
              placeholder="请输入联系电话"
              v-model="formTwoData.companyPhone"
              style="width: 80%"
              maxlength="150"
            />
          </el-form-item>
          <el-form-item label="联系人：" prop="companyContact">
            <el-input placeholder="请输入联系人" v-model="formTwoData.companyContact" style="width: 80%" />
          </el-form-item>
          <el-form-item label="附件：">
            <el-button
              v-if="detailData.attachmentObjectKey"
              v-btn
              link
              type="primary"
              @click="getFileUrl(detailData.attachmentObjectKey)"
            >
              下载
            </el-button>
            <span v-else>无</span>
          </el-form-item>
          <el-form-item label="注意事项：" prop="cautions">
            <el-input
              type="textarea"
              show-word-limit
              v-model="formTwoData.cautions"
              placeholder="请输入注意事项"
              maxlength="800"
              :rows="2"
              resize="none"
            />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer flex-center">
          <el-button style="padding: 8px 36px" @click="handleClose">取消</el-button>
          <el-button type="primary" @click="handleConfirm">确认可开票</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import DownloadBtn from '@/components/Button/DownloadBtn'
import { downloadFile, downUrlFile } from '@/utils/index'
import { payTypeMapNew } from '@/utils/dict'
import { orderTypeList, invoiceTypeList } from '@/views/finance/invoice/data.js'
import { getBackInvoiceDetail, auditInvoice } from '@/api/finance/invoice'
import { ElMessage } from 'element-plus'
const visible = ref(false)

defineExpose({
  open,
  handleClose,
})

const ruleFormRef = ref(null)
const ruleTwoFormRef = ref(null)
const tableAmount = ref(0)
const formData = ref({
  invoiceAmount: 0,
  title: '',
  dutyParagraph: '',
  cautions: '',
})
const formTwoData = ref({
  invoiceAmount: 0,
  companyName: '',
  companyAddress: '',
  companyPhone: '',
  companyContact: '',
  cautions: '',
})
const rules = {
  invoiceAmount: [{ required: true, message: '请输入发票金额', trigger: 'blur' }],
  title: [{ required: true, message: '请输入发票抬头', trigger: 'blur' }],
  dutyParagraph: [{ required: true, message: '请输入税号', trigger: 'blur' }],
}

const twoRules = {
  invoiceAmount: [{ required: true, message: '请输入发票金额', trigger: 'blur' }],
  companyName: [{ required: true, message: '请输入公司名称', trigger: 'blur' }],
  companyAddress: [{ required: true, message: '请输入公司地址', trigger: 'blur' }],
  companyPhone: [
    { required: false, message: '请输入联系电话', trigger: 'change' },
    { pattern: /^\d+$/, message: '请输入正确的联系电话', trigger: 'change' },
  ],
  // companyContact: [{ required: true, message: '请输入联系人', trigger: 'blur' }],
}

const emits = defineEmits(['success'])

const invoiceId = ref('')
function open(id) {
  invoiceId.value = id
  getDetail()
  curInvoiceType.value = ''
  visible.value = true
}

function handleClose() {
  if (curInvoiceType.value == 1) {
    ruleFormRef.value.resetFields()
    formData.value = {
      invoiceAmount: 0,
      title: '',
      dutyParagraph: '',
      cautions: '',
    }
  } else {
    ruleTwoFormRef.value.resetFields()
    formTwoData.value = {
      invoiceAmount: 0,
      companyName: '',
      companyAddress: '',
      companyPhone: '',
      companyContact: '',
      cautions: '',
      attachmentObjectKey: '',
    }
  }
  tableAmount.value = 0
  relevantOrderList.value = []
  visible.value = false
}

function handleConfirm() {
  if (curInvoiceType.value == 1) {
    ruleFormRef.value.validate(valid => {
      if (valid) {
        auditInvoice({ id: invoiceId.value, ...formData.value }).then(res => {
          handleClose()
          ElMessage.success('审核成功')
          emits('success')
        })
      }
    })
  } else {
    ruleTwoFormRef.value.validate(valid => {
      if (valid) {
        auditInvoice({ id: invoiceId.value, ...formTwoData.value }).then(res => {
          handleClose()
          ElMessage.success('审核成功')
          emits('success')
        })
      }
    })
  }
}

const detailData = ref({})
const relevantOrderList = ref([])
const curInvoiceType = ref('')
function getDetail() {
  getBackInvoiceDetail({ invoiceId: invoiceId.value }).then(res => {
    curInvoiceType.value = res.data.invoiceType
    detailData.value = res.data
    relevantOrderList.value = res.data?.orderInvoiceVideoBackVOS || []
    if (relevantOrderList.value && relevantOrderList.value.length > 0) {
      tableAmount.value = relevantOrderList.value.reduce((sum, item) => sum + item.invoiceAmount, 0)
    }
    if (res.data) {
      if (res.data.invoiceType == 1) {
        formData.value = {
          invoiceAmount: res.data.invoiceAmount,
          title: res.data.title,
          dutyParagraph: res.data.dutyParagraph,
          cautions: res.data.cautions,
        }
      } else {
        formTwoData.value = {
          invoiceAmount: res.data.invoiceAmount,
          companyName: res.data.companyName,
          companyAddress: res.data.companyAddress,
          companyPhone: res.data.companyPhone,
          companyContact: res.data.companyContact,
          cautions: res.data.cautions,
        }
      }
    }
  })
}

function getFileUrl(objectKey) {
  const fileSuffix = objectKey.substring(objectKey.lastIndexOf('/') + 1)
  downUrlFile(objectKey, fileSuffix)
}

const channelInputLimit = e => {
  const key = e.key
  if (key === 'Enter') e.preventDefault()
  const notAllowList = ['e', '+', '-']
  if (notAllowList.includes(key)) {
    e.returnValue = false
    return false
  }
  return true
}
</script>

<style scoped lang="scss">
:deep(.el-input-number) {
  .el-input__inner {
    text-align: left;
  }
}
.invoice-info {
  display: grid;
  gap: 5px;
}
</style>
