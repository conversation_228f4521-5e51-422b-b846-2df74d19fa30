<template>
  <div>
    <el-dialog
      v-model="visible"
      title="确认发票信息"
      align-center
      width="600"
      destroy-on-close
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      @close="handleClose"
      :show-close="false"
    >
      <div style="color: #f59a23">请认真核对发票内容，确认无误后将会将发票投递给商家</div>
      <div class="confirm-box">
        <div>
          发票类型：{{ invoiceTypeList.find(item => item.value === detailData.invoiceType)?.label || '-' }}
        </div>
        <div>开票金额：{{ detailData.invoiceAmount || '0' }}元</div>
        <template v-if="detailData.invoiceType == 1">
          <div class="text-warp">
            <div class="fs-0">发票抬头：</div>
            <div class="more-ell" style="--l: 5">{{ detailData.title || '-' }}</div>
          </div>
          <div class="text-warp">
            <div class="fs-0">&emsp;&emsp;税号：</div>
            <div class="more-ell" style="--l: 5">{{ detailData.dutyParagraph || '-' }}</div>
          </div>

          <div v-if="detailData.cautions" class="text-warp">
            <div class="fs-0">注意事项：</div>
            <div class="more-ell" style="--l: 5">{{ detailData.cautions || '-' }}</div>
          </div>
          <div class="text-warp">&emsp;发票号：{{ detailData.number || '-' }}</div>
          <div>开票时间：{{ detailData.invoicingTime || '-' }}</div>
        </template>
        <template v-else>
          <div class="text-warp">
            <div class="fs-0">公司名称：</div>
            <div class="more-ell" style="--l: 5">{{ detailData.companyName || '-' }}</div>
          </div>
          <div class="text-warp">
            <div class="fs-0">公司地址：</div>
            <div class="more-ell" style="--l: 5">{{ detailData.companyAddress || '-' }}</div>
          </div>
          <div>联系电话：{{ detailData.companyPhone || '无' }}</div>
          <div>&emsp;联系人：{{ detailData.companyContact || '无' }}</div>
          <div v-if="detailData.cautions" class="text-warp">
            <div class="fs-0">注意事项：</div>
            <div class="more-ell" style="--l: 5">{{ detailData.cautions || '-' }}</div>
          </div>
          <div v-if="detailData.invoiceRemark" class="text-warp">
            <div class="fs-0">商家备注：</div>
            <div class="more-ell" style="--l: 5">{{ detailData.invoiceRemark || '-' }}</div>
          </div>
        </template>
        <template v-if="detailData.invoiceType == 2">
          <el-divider border-style="dashed" style="margin: 8px 0" />
          <div class="text-warp">&emsp;发票号：{{ detailData.number || '-' }}</div>
          <div>开票时间：{{ detailData.invoicingTime || '-' }}</div>
        </template>
        <div style="display: flex; align-items: baseline">
          <div>&emsp;&emsp;发票：</div>
          <div style="flex: 1" v-if="detailData.objectKeys">
            <PasteUpload
              v-model="file"
              :limit="1"
              :multiple="false"
              :fileType="['jpg', 'jpeg', 'png', 'pdf', 'ofd', 'xml']"
              show-file-list
              @view-pdf="handleViewPdf"
              :delete-button="false"
              :size="3"
            />
          </div>
          <span v-else>-</span>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer flex-center">
          <el-button style="padding: 8px 36px" @click="handleClose">取消</el-button>
          <el-button type="primary" @click="handleConfirm">确认并投递</el-button>
        </div>
      </template>
      <InvoiceDialog ref="invoiceDialog" :url="invoiceDialogUrl" />
    </el-dialog>
  </div>
</template>

<script setup>
import InvoiceDialog from '@/components/Dialog/invoice'
import PasteUpload from '@/components/FileUpload/pasteUpload'
import ViewerImageList from '@/components/ImagePreview/viewerImageList.vue'
import { invoiceTypeList } from '@/views/finance/invoice/data.js'
import { getBackInvoiceDetail, confirmInvoice } from '@/api/finance/invoice'
import { ElMessage } from 'element-plus'
const { proxy } = getCurrentInstance()
const visible = ref(false)

defineExpose({
  open,
  handleClose,
})

const emits = defineEmits(['success'])

const invoiceDialog = ref()
const invoiceDialogUrl = ref('')
const invoiceId = ref('')
function open(id) {
  invoiceId.value = id
  getDetail()
  visible.value = true
}

function handleClose() {
  invoiceId.value = ''
  invoiceDialog.value = ''
  invoiceDialogUrl.value = ''
  detailData.value = {}
  file.value = []
  visible.value = false
}

function handleConfirm() {
  confirmInvoice({ invoiceId: invoiceId.value }).then(res => {
    ElMessage.success('投递成功')
    handleClose()
    emits('success')
  })
}

function handleViewPdf(url) {
  invoiceDialogUrl.value = proxy.$picUrl + url
  invoiceDialog.value.open()
}

const detailData = ref({})
const file = ref([])
function getDetail() {
  getBackInvoiceDetail({ invoiceId: invoiceId.value }).then(res => {
    detailData.value = res.data
    if (res.data && res.data.objectKeys && res.data.objectKeys.length > 0) {
      res.data.objectKeys.forEach(item => {
        file.value.push({
          name: item.split('/').pop(),
          url: item,
          picUrl: item,
        })
      })
    }
    // file.value = res.data?.objectKey
    //   ? [
    //       {
    //         name: res.data?.objectKey.split('/').pop(),
    //         url: res.data?.objectKey,
    //         picUrl: res.data?.objectKey,
    //       },
    //     ]
    //   : []
  })
}
</script>

<style scoped lang="scss">
.confirm-box {
  margin-top: 15px;
  padding: 10px;
  display: grid;
  gap: 20px;
}
.text-warp {
  word-break: break-all;
  line-break: anywhere;
  white-space: break-spaces;
  display: flex;
  align-items: baseline;
}
</style>
