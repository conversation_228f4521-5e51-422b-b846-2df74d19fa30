<template>
  <div>
    <el-dialog
      v-model="visible"
      title="取消开票"
      align-center
      width="600"
      destroy-on-close
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      @close="handleClose"
    >
      <div class="dialog-box">
        <div style="font-size: 14px; font-weight: 600">发票信息</div>
        <div class="dialog-box" style="max-height: 500px; overflow: auto">
          <div>
            发票类型：{{ invoiceTypeList.find(item => item.value == formData.invoiceType)?.label || '-' }}
          </div>
          <div>开票金额：{{ formData.invoiceAmount || 0 }}元</div>
          <template v-if="formData.invoiceType == '1'">
            <div class="text-warp">
              <div class="fs-0">发票抬头：</div>
              <div class="more-ell" style="--l: 5">{{ formData.title || '-' }}</div>
            </div>
            <div>
              <div class="text-warp">
                <div class="fs-0">&emsp;&emsp;税号：</div>
                <div class="more-ell" style="--l: 5">{{ formData.dutyParagraph || '-' }}</div>
              </div>
            </div>
          </template>
          <template v-if="formData.invoiceType == '2'">
            <div class="text-warp">
              <div class="fs-0">公司名称：</div>
              <div class="more-ell" style="--l: 5">{{ formData.companyName || '-' }}</div>
            </div>
            <div class="text-warp">
              <div class="fs-0">公司地址：</div>
              <div class="more-ell" style="--l: 5">{{ formData.companyAddress || '-' }}</div>
            </div>
            <div class="text-warp">
              <div class="fs-0">联系电话：</div>
              <div>{{ formData.companyPhone || '无' }}</div>
            </div>
            <div>
              <div class="text-warp">
                <div class="fs-0">&emsp;联系人：</div>
                <div class="more-ell" style="--l: 5">{{ formData.companyContact || '无' }}</div>
              </div>
            </div>
          </template>
          <div v-if="formData.cautions" class="text-warp">
            <div class="fs-0">注意事项：</div>
            <div>{{ formData.cautions || '-' }}</div>
          </div>
          <div v-if="formData.invoiceRemark" class="text-warp">
            <div class="fs-0">商家备注：</div>
            <div>{{ formData.invoiceRemark || '-' }}</div>
          </div>
        </div>
        <el-divider style="margin: 10px 0" />
        <div style="font-size: 14px; font-weight: 600">是否确认取消本次开票申请?</div>
        <div style="color: #f59a23">取消后需由商家重新发起开票申请,请谨慎操作</div>
      </div>
      <template #footer>
        <div class="dialog-footer flex-center">
          <el-button style="padding: 8px 29px" @click="handleClose">取消</el-button>
          <el-button type="primary" @click="handleConfirm">确认取消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { invoiceTypeList } from '@/views/finance/invoice/data.js'
import { cancelInvoice, getBackInvoiceDetail } from '@/api/finance/invoice'
import { ElMessage } from 'element-plus'
const visible = ref(false)

defineExpose({
  open,
  handleClose,
})

const emits = defineEmits(['success'])

const invoiceId = ref('')
const formData = ref({})
function open(id) {
  invoiceId.value = id
  getDetail()
  visible.value = true
}

function handleClose() {
  invoiceId.value = ''
  formData.value = {}
  visible.value = false
}

function handleConfirm() {
  cancelInvoice({ invoiceId: invoiceId.value }).then(() => {
    ElMessage.success('取消成功')
    emits('success')
    handleClose()
  })
}

function getDetail() {
  getBackInvoiceDetail({ invoiceId: invoiceId.value }).then(res => {
    formData.value = res.data
  })
}
</script>

<style scoped lang="scss">
.dialog-box {
  display: grid;
  gap: 5px;
  font-size: 13px;
  color: #333;
}
.text-warp {
  word-break: break-all;
  line-break: anywhere;
  white-space: break-spaces;
  display: flex;
  align-items: baseline;
}
</style>
