<template>
  <div>
    <el-dialog
      v-model="visible"
      title="开票信息"
      align-center
      width="600"
      destroy-on-close
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      @close="handleClose"
    >
      <div class="invoice-info">
        <div>
          发票类型：{{ invoiceTypeList.find(item => item.value === detailData.invoiceType)?.label || '-' }}
        </div>
        <div>开票金额：{{ detailData.invoiceAmount || '0' }}元</div>
        <template v-if="detailData.invoiceType == 1">
          <div class="text-warp">
            <div class="fs-0">发票抬头：</div>
            <div class="more-ell" style="--l: 5">{{ detailData.title || '-' }}</div>
          </div>
          <div class="text-warp">
            <div class="fs-0">&emsp;&emsp;税号：</div>
            <div class="more-ell" style="--l: 5">{{ detailData.dutyParagraph || '-' }}</div>
          </div>
          <div v-if="detailData.cautions" class="text-warp">
            <div style="flex-shrink: 0">注意事项：</div>
            <div>{{ detailData.cautions || '-' }}</div>
          </div>
          <div v-if="detailData.invoiceRemark" class="text-warp">
            <div style="flex-shrink: 0">商家备注：</div>
            <div>{{ detailData.invoiceRemark || '-' }}</div>
          </div>
        </template>
        <template v-else>
          <div>公司名称：{{ detailData.companyName || '-' }}</div>
          <div class="text-warp">
            <div style="flex-shrink: 0">公司地址：</div>
            <div class="more-ell">{{ detailData.companyAddress || '-' }}</div>
          </div>
          <div class="text-warp">
            <div class="fs-0">联系电话：</div>
            <div>{{ detailData.companyPhone || '无' }}</div>
          </div>
          <div>联系人：{{ detailData.companyContact || '无' }}</div>
          <div style="display: flex; align-items: baseline">
            附件：
            <el-button
              v-if="detailData.attachmentObjectKey"
              link
              type="primary"
              @click="getFileUrl(detailData.attachmentObjectKey)"
            >
              下载
            </el-button>
            <span v-else>无</span>
          </div>
          <div v-if="detailData.cautions" class="text-warp">
            <div style="flex-shrink: 0">注意事项：</div>
            <div>{{ detailData.cautions || '-' }}</div>
          </div>
          <div v-if="detailData.invoiceRemark" class="text-warp">
            <div style="flex-shrink: 0">商家备注：</div>
            <div>{{ detailData.invoiceRemark || '-' }}</div>
          </div>
        </template>
      </div>
      <el-divider style="margin: 10px 0" border-style="dashed" />
      <el-form :model="formData" :rules="rules" ref="ruleFormRef" label-width="100px">
        <el-form-item label="上传发票" prop="file">
          <PasteUpload
            v-model="formData.file"
            :limit="5"
            :multiple="false"
            :fileType="['jpg', 'jpeg', 'png', 'pdf', 'ofd', 'xml']"
            show-file-list
            @view-pdf="handleViewPdf"
            :size="3"
          />
          <div style="width: 100%; font-size: 12px">
            请上传5份大小不超过
            <span style="color: #d9001b">3M</span>
            ，格式为
            <span style="color: #d9001b">pdf/ofd/xml/jpg/jpeg/png</span>
            的文件
          </div>
        </el-form-item>
        <el-form-item label="发票号" prop="number">
          <el-input v-model="formData.number" placeholder="请输入发票号" maxlength="100" />
        </el-form-item>
        <el-form-item label="开票时间" prop="invoicingTime">
          <el-date-picker
            v-model="formData.invoicingTime"
            type="datetime"
            placeholder="请选择开票时间"
            format="YYYY/M/D HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer flex-center">
          <el-button style="padding: 8px 36px" @click="handleClose">取消</el-button>
          <el-button type="primary" @click="handleConfirm">确认上传</el-button>
        </div>
      </template>
      <InvoiceDialog ref="invoiceDialog" :url="invoiceDialogUrl" />
      <ConfirmDialog ref="confirmDialogRef" @success="handleSuccess" />
    </el-dialog>
  </div>
</template>

<script setup>
import DownloadBtn from '@/components/Button/DownloadBtn'
import { downloadFile, downUrlFile } from '@/utils/index'
import PasteUpload from '@/components/FileUpload/pasteUpload'
import InvoiceDialog from '@/components/Dialog/invoice'
import ConfirmDialog from '@/views/finance/invoice/components/confirmDialog.vue'
import { ElMessage } from 'element-plus'
import { payTypeMapNew } from '@/utils/dict'
import { invoiceTypeList } from '@/views/finance/invoice/data.js'
import { getBackInvoiceDetail, uploadInvoice, reUploadInvoice } from '@/api/finance/invoice'

const { proxy } = getCurrentInstance()

const visible = ref(false)

defineExpose({
  open,
  handleClose,
})
const emits = defineEmits(['success'])

const ruleFormRef = ref(null)
const confirmDialogRef = ref(null)

const formData = ref({
  file: [],
  number: '',
  invoicingTime: '',
})

const rules = {
  file: [{ required: true, message: '请上传发票', trigger: 'blur' }],
  number: [
    { required: true, message: '请输入发票号', trigger: 'blur' },
    {
      pattern: /^[0-9a-zA-Z]+$/,
      message: '发票号只能包含数字和字母',
      trigger: 'change',
    },
  ],
  invoicingTime: [{ required: true, message: '请选择开票时间', trigger: 'change' }],
}

const invoiceId = ref('')
const curType = ref('')
function open(id, tab) {
  curType.value = tab
  invoiceId.value = id
  getDetail()
  visible.value = true
}

function handleClose() {
  ruleFormRef.value.resetFields()
  formData.value = {
    file: [],
    number: '',
    invoicingTime: '',
  }
  curType.value = ''
  detailData.value = {}
  visible.value = false
}

function handleConfirm() {
  ruleFormRef.value.validate(valid => {
    if (valid) {
      const params = {
        id: invoiceId.value,
        number: formData.value.number,
        invoicingTime: formData.value.invoicingTime,
        objectKeys: formData.value.file.map(item => item.picUrl),
      }
      if (curType.value == 1) {
        uploadInvoice(params).then(res => {
          ElMessage.success('上传成功')
          emits('success', invoiceId.value)
          handleClose()
          // confirmDialogRef.value?.open(invoiceId.value)
        })
      } else if (curType.value == 2) {
        reUploadInvoice(params).then(res => {
          ElMessage.success('重新上传成功')
          emits('success', invoiceId.value)
          handleClose()
          // confirmDialogRef.value?.open(invoiceId.value)
        })
      }
    }
  })
}

function handleSuccess() {
  handleClose()
  // ElMessage.success('投递成功')
  emits('success')
}

const invoiceDialog = ref()
const invoiceDialogUrl = ref('')
function handleViewPdf(url) {
  invoiceDialogUrl.value = proxy.$picUrl + url
  invoiceDialog.value.open()
}

const detailData = ref({})
function getDetail() {
  getBackInvoiceDetail({ invoiceId: invoiceId.value }).then(res => {
    detailData.value = res.data
    formData.number = res.data.number || ''
  })
}

function getFileUrl(objectKey) {
  const fileSuffix = objectKey.substring(objectKey.lastIndexOf('/') + 1)
  downUrlFile(objectKey, fileSuffix)
}
</script>

<style scoped lang="scss">
.invoice-info {
  max-height: 400px;
  overflow: auto;
  display: grid;
  gap: 5px;
}
.text-warp {
  word-break: break-all;
  line-break: anywhere;
  white-space: break-spaces;
  display: flex;
  align-items: baseline;
}
</style>
