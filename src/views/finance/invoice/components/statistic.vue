<template>
  <div class="flex-center statistic-box">
    <div v-for="item in statisticList" :key="item.label" class="flex-column statistic-item">
      <div class="flex-center tips">
        {{ item.label }}
      </div>
      <el-statistic :value="item.number" :precision="2"></el-statistic>
    </div>
  </div>
</template>

<script setup>
import { useTransition } from '@vueuse/core'
import { getInvoiceAmountStatistics } from '@/api/finance/invoice'

const deliverTotal = ref(0)
const unInvoiceTotal = ref(0)
const amountToBeFlushedTotal = ref(0)
const duration = 1000

const statisticList = ref([
  { label: '待开票（元）', number: useTransition(unInvoiceTotal, { duration }), tips: '待开票的订单总金额' },
  {
    label: '待红冲（元）',
    number: useTransition(amountToBeFlushedTotal, { duration }),
    tips: '待红冲的订单总金额',
  },
  { label: '已开票（元）', number: useTransition(deliverTotal, { duration }), tips: '历史开票的订单总金额' },
])

defineExpose({
  load,
})

function load() {
  getInvoiceAmountStatistics().then(res => {
    if (res.data) {
      deliverTotal.value = res.data.invoicedAmount || 0
      unInvoiceTotal.value = res.data.amountToBeBilled || 0
      amountToBeFlushedTotal.value = res.data.amountToBeFlushed || 0
    }
  })
}

load()
</script>

<style scoped lang="scss">
.statistic-box {
  margin: 20px 30% 15px;
  flex-wrap: wrap;
  border-radius: 10px;
  box-shadow: var(--el-box-shadow-light);
  min-width: 600px;
  .statistic-item {
    flex-shrink: 0;
    gap: 8px;
    width: 200px;
    padding: 20px 0px;
    // margin: -1px 0 0 -1px;
    // border: 1px solid #ccc;

    .tips {
      font-size: 14px;
      color: #7f7f7f;
    }
  }
  :deep(.el-statistic) {
    .el-statistic__content {
      font-weight: 600;
    }
  }
}
</style>
