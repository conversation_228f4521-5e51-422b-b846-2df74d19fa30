<template>
  <div>
    <ElTablePage
      style="padding: 20px"
      ref="tableRef"
      :columns="columns"
      :data="tableData"
      :loading="tableLoading"
      :currentPage="pageNum"
      :pageSize="pageSize"
      :total="total"
      :tableAction="{
        width: '80',
        fixed: 'right',
      }"
      :tableOptions="{
        border: true,
      }"
      @page-change="pageChange"
    >
      <template #tableHeader>
        <div>
          <el-form
            class="flex-start"
            style="flex-wrap: wrap"
            :model="queryParams"
            ref="queryRef"
            :inline="true"
            label-width="68px"
            @submit.prevent
          >
            <el-form-item label="搜索">
              <el-input
                v-model="queryParams.keyword"
                clearable
                style="width: 260px"
                placeholder="请输入搜索内容"
              />
            </el-form-item>
            <el-form-item label="状态" prop="status">
              <el-select
                multiple
                collapse-tags
                collapse-tags-tooltip
                v-model="queryParams.status"
                placeholder="请选择"
                clearable
                style="width: 180px"
              >
                <el-option
                  v-for="item in statusList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="订单类型" prop="orderType">
              <el-select v-model="queryParams.orderType" placeholder="请选择" clearable style="width: 180px">
                <el-option
                  v-for="item in orderTypeList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="发票类型" prop="invoiceType">
              <el-select
                v-model="queryParams.invoiceType"
                placeholder="请选择"
                clearable
                style="width: 180px"
              >
                <el-option
                  v-for="item in invoiceTypeList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="来源" prop="source">
              <el-select v-model="queryParams.source" placeholder="请选择" clearable style="width: 180px">
                <el-option
                  v-for="item in sourceList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <!-- <el-form-item label="支付时间" style="width: 400px">
                <el-date-picker
                  v-model="queryParams.payTime"
                  format="YYYY/M/D HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  type="datetimerange"
                  range-separator="-"
                  unlink-panels
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
                ></el-date-picker>
              </el-form-item> -->
            <el-form-item label="申请时间" style="width: 400px">
              <el-date-picker
                v-model="queryParams.invoicingTime"
                format="YYYY/M/D HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="datetimerange"
                range-separator="-"
                unlink-panels
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
                :shortcuts="shortcuts"
              ></el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button v-btn type="primary" icon="Search" native-type="submit" @click="onQuery">
                搜索
              </el-button>
              <el-button v-btn icon="Refresh" @click="resetQuery">重置</el-button>
              <!-- <DownloadBtn
                type="success"
                plain
                icon="Download"
                url="/order/finance/export"
                :params="handleExportParams()"
                fileName="发票信息.xlsx"
                v-hasPermi="['finance:invoice:export']"
              /> -->
            </el-form-item>
            <div class="total-amount" v-if="showInvoiceAmount">已开票金额：{{ invoiceAmount }}</div>
          </el-form>
        </div>
      </template>
      <template #orderNums="{ row }">
        <div style="padding: 10px 0">
          <el-tag v-if="row.isApplyWithdrawal" type="warning" class="tag-icon">发生提现</el-tag>
          <template v-if="row.orderNums && row.orderNums.length > 0">
            <div v-for="item in row.orderNums" :key="item">{{ item }}</div>
          </template>
          <span v-else>-</span>
        </div>
      </template>
      <template #merchantCode="{ row }">
        <div>{{ row.merchantCode || '-' }}</div>
      </template>
      <template #info-header="{ row }">
        <div style="margin-top: -5px">
          <div>开票信息</div>
          <div style="color: #7f7f7f; font-size: 0.7em; line-height: 8px">点击可复制内容</div>
        </div>
      </template>
      <template #operatorByType="{ row }">
        <div v-if="row.operatorByType == '0'">{{ row.operatorBack?.name || '-' }}</div>
        <div v-if="row.operatorByType == '1'">{{ row.operatorCompany?.name || '-' }}</div>
      </template>
      <template #info="{ row }">
        <template v-if="row.invoiceType == 1">
          <div style="cursor: pointer" @click="copyText(row.title, '发票抬头：')">
            发票抬头：{{ row.title }}
          </div>
          <div style="cursor: pointer" @click="copyText(row.dutyParagraph, '税号：')">
            税号：{{ row.dutyParagraph }}
          </div>
          <div
            v-if="row.cautions"
            class="more-ell"
            style="text-align: center; cursor: pointer"
            @click="copyText(row.cautions, '开票注意事项：')"
          >
            开票注意事项：{{ row.cautions }}
          </div>
        </template>
        <template v-else>
          <div style="cursor: pointer" @click="copyText(row.companyName, '公司名称：')">
            公司名称：{{ row.companyName || '-' }}
          </div>
          <div style="cursor: pointer" @click="copyText(row.companyAddress, '公司地址：')">
            公司地址：{{ row.companyAddress || '-' }}
          </div>
          <div style="cursor: pointer" @click="copyText(row.companyPhone, '联系电话：')">
            联系电话：{{ row.companyPhone || '无' }}
          </div>
          <div style="cursor: pointer" @click="copyText(row.companyContact, '联系人：')">
            联系人：{{ row.companyContact || '无' }}
          </div>
          <div v-if="row.attachmentObjectKey">
            附件：
            <el-button v-btn link type="primary" @click="getFileUrl(row.attachmentObjectKey)">下载</el-button>
          </div>
        </template>
        <!-- <div style="cursor: pointer" v-if="row.number" @click="copyText(row.number, '发票号：')">
            发票号：{{ row.number }}
          </div> -->
      </template>

      <template #tableAction="{ row }">
        <el-button
          v-btn
          v-hasPermi="['finance:invoice:view']"
          link
          type="primary"
          @click="handleBtnAction(row.id, '查看')"
        >
          查看
        </el-button>
      </template>
      <template #pageLeft>
        <div style="margin-right: 15px;">
          <DownloadBtn
            type="success"
            plain
            icon="Download"
            url="/order/invoice/invoice-finish-list-export"
            :params="handleExportParams()"
            fileName="发票已完成列表.xlsx"
            v-hasPermi="['finance:invoice:done-export']"
          />
        </div>
      </template>
    </ElTablePage>
  </div>
</template>

<script setup>
import ElTablePage from '@/components/Table/ElTablePage'
import DownloadBtn from '@/components/Button/DownloadBtn'
import { downloadFile, downUrlFile } from '@/utils/index'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getInvoiceFinishList, getDownloadUrl } from '@/api/finance/invoice'
import { invoiceStatusMap, sourceList, invoiceTypeList, orderTypeList } from '@/views/finance/invoice/data.js'
import { useViewer } from '@/hooks/useViewer'

const { showViewer } = useViewer()

const { proxy } = getCurrentInstance()

const shortcuts = [
  {
    text: '今天',
    value: () => {
      const start = new Date()
      start.setHours(0, 0, 0, 0)
      const end = new Date()
      end.setHours(23, 59, 59, 59)
      return [start, end]
    },
  },
]

const statusList = [
  { label: '已完成', value: '3' },
  { label: '已取消', value: '6' },
  { label: '已红冲', value: '4' },
]
const columns = [
  { prop: 'ticketCode', label: '申票码', width: '100', handle: data => (data ? data : '-') },
  { prop: 'number', label: '发票号', width: '200', handle: data => (data ? data : '-') },
  { slot: 'orderNums', prop: 'orderNums', label: '包含订单', width: '250' },
  {
    slot: 'merchantCode',
    label: '商家编码',
    width: '100',
  },
  {
    prop: 'invoiceType',
    label: '发票类型',
    width: '130',
    handle: invoiceType => {
      let s = invoiceTypeList.find(item => item.value == invoiceType)
      if (invoiceType == '7') return '已完成'
      return s ? s.label : '-'
    },
  },
  { slot: 'info', prop: 'info', label: `开票信息`, labelSlot: 'info-header', minWidth: '230' },
  { prop: 'invoiceAmount', label: '开票金额(元)', width: '100', handle: data => `${data}` },

  {
    prop: 'status',
    label: '状态',
    minWidth: '100',
    handle: (data, row) => {
      let item = statusList.find(item => item.value == data)
      if (item) {
        return item.label
      } else if (data == 7) {
        return '已完成'
      }
      return '-'
    },
  },
  {
    slot: 'operatorByType',
    label: '操作人',
    minWidth: '100',
  },
  {
    prop: 'operatorTime',
    label: '操作时间',
    minWidth: '180',
    handle: (data, row) => {
      return data ? data : '-'
    },
  },
]
const emits = defineEmits(['action'])

const tableData = ref([])
const pageNum = ref(1)
const pageSize = ref(20)
const total = ref(0)
const tableLoading = ref(false)

const showInvoiceAmount = ref(false)
const invoiceAmount = ref(0)

const invoiceDialog = ref()
const invoiceDialogUrl = ref('')

const queryParams = ref({
  keyword: '',
  status: [],
  orderType: '',
  invoicingTime: [],
  invoiceType: '',
  source: '',
})

//复制剪切板
function copyText(text, type) {
  if (navigator.clipboard && text) {
    let copyText = type + text
    navigator.clipboard
      .writeText(copyText)
      .then(function () {
        ElMessage.success('内容已复制到剪贴板')
      })
      .catch(function (err) {
        console.error('Failed to copy text: ', err)
      })
  }
}

function resetQuery() {
  queryParams.value = {
    keyword: '',
    status: [],
    orderType: '',
    invoicingTime: [],
    invoiceType: '',
    source: '',
  }
  pageNum.value = 1
  handleQuery()
}
function onQuery() {
  pageNum.value = 1
  handleQuery()
}
function handleExportParams() {
  let { invoicingTime, ...data } = queryParams.value
  if (invoicingTime && invoicingTime.length) {
    data.operatorTimeBegin = invoicingTime[0]
    data.operatorTimeEnd = invoicingTime[1]
  }
  return data
}
function handleQuery() {
  tableLoading.value = true
  let params = handleExportParams()
  // if (params.operatorTimeBegin && params.operatorTimeEnd) {
  //   financeInvoiceAmount(params).then(res => {
  //     showInvoiceAmount.value = true
  //     invoiceAmount.value = res.data
  //   })
  // } else {
  //   showInvoiceAmount.value = false
  // }
  params.pageNum = pageNum.value
  params.pageSize = pageSize.value
  getInvoiceFinishList(params)
    .then(res => {
      tableData.value = res.data.rows
      total.value = res.data.total
    })
    .finally(() => (tableLoading.value = false))
}
// 分页跳转
function pageChange(page) {
  pageNum.value = page.currentPage
  pageSize.value = page.pageSize
  handleQuery()
}

function handleBtnAction(row, btn) {
  emits('action', row, btn)
}

function getFileUrl(objectKey) {
  const fileSuffix = objectKey.substring(objectKey.lastIndexOf('/') + 1)
  downUrlFile(objectKey, fileSuffix)
}

handleQuery()
</script>

<style scoped lang="scss">
.tabs {
  padding: 0 20px;
}
.total-amount {
  color: red;
  margin: 0 5px 18px auto;
  font-size: 16px;
}
.form-box {
  .file-item {
    width: 100%;

    .text {
      max-width: 90%;
      cursor: pointer;

      &:hover {
        color: var(--el-color-primary);
      }
    }
    .el-icon {
      cursor: pointer;
      color: var(--el-color-primary);
    }
  }
}
.form-box {
  .file-item {
    width: 100%;

    .text {
      max-width: 90%;
      cursor: pointer;

      &:hover {
        color: var(--el-color-primary);
      }
    }
    .el-icon {
      cursor: pointer;
      color: var(--el-color-primary);
    }
  }
}
</style>
