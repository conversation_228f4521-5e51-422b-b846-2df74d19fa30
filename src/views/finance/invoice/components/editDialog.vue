<template>
  <div>
    <el-dialog
      v-model="visible"
      title="开票信息"
      align-center
      width="600"
      destroy-on-close
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      @close="handleClose"
    >
      <div v-if="curInvoiceType == 1">
        <el-form :model="FormData" :rules="rules" ref="ruleFormRef" label-width="100px">
          <el-form-item label="开票金额：" prop="invoiceAmount">
            <el-input-number
              title=""
              :controls="false"
              clearable
              v-model="FormData.invoiceAmount"
              :min="0"
              :max="9999999"
              @keydown="channelInputLimit"
              :precision="2"
              style="width: 80%"
            />
            &emsp;元
          </el-form-item>
          <el-form-item label="发票抬头：" prop="title">
            <el-input placeholder="请输入发票抬头" v-model="FormData.title" style="width: 80%" />
          </el-form-item>
          <el-form-item label="税号：" prop="dutyParagraph">
            <el-input placeholder="请输入税号" v-model="FormData.dutyParagraph" style="width: 80%" />
          </el-form-item>
        </el-form>
      </div>
      <div v-else>
        <el-form :model="FormTwoData" :rules="twoRules" ref="ruleTwoFormRef" label-width="100px">
          <el-form-item label="开票金额：" prop="invoiceAmount">
            <el-input-number
              title=""
              :controls="false"
              clearable
              v-model="FormTwoData.invoiceAmount"
              :min="0"
              :max="9999999"
              @keydown="channelInputLimit"
              :precision="2"
              style="width: 80%"
            />
            &emsp;元
          </el-form-item>
          <el-form-item label="公司名称：" prop="companyName">
            <el-input placeholder="请输入公司名称" v-model="FormTwoData.companyName" style="width: 80%" />
          </el-form-item>
          <el-form-item label="公司地址：" prop="companyAddress">
            <el-input placeholder="请输入公司地址" v-model="FormTwoData.companyAddress" style="width: 80%" />
          </el-form-item>
          <el-form-item label="联系电话：" prop="companyPhone">
            <el-input placeholder="请输入联系电话" v-model="FormTwoData.companyPhone" style="width: 80%" />
          </el-form-item>
          <el-form-item label="联系人：" prop="companyContact">
            <el-input placeholder="请输入联系人" v-model="FormTwoData.companyContact" style="width: 80%" />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer flex-center">
          <el-button style="padding: 8px 29px" @click="handleClose">取消</el-button>
          <el-button type="primary" @click="handleConfirm">确认修改</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { updateInvoice, getBackInvoiceDetail } from '@/api/finance/invoice'
import { ElMessage } from 'element-plus'
const visible = ref(false)

defineExpose({
  open,
  handleClose,
})
const emits = defineEmits(['success'])
const ruleFormRef = ref(null)
const ruleTwoFormRef = ref(null)
const FormData = ref({
  invoiceAmount: 0,
  title: '',
  dutyParagraph: '',
})
const FormTwoData = ref({
  invoiceAmount: 0,
  companyName: '',
  companyContact: '',
  companyAddress: '',
  companyPhone: '',
})
const rules = {
  invoiceAmount: [{ required: true, message: '请输入发票金额', trigger: 'blur' }],
  title: [{ required: true, message: '请输入发票抬头', trigger: 'blur' }],
  dutyParagraph: [{ required: true, message: '请输入税号', trigger: 'blur' }],
}

const twoRules = {
  invoiceAmount: [{ required: true, message: '请输入发票金额', trigger: 'blur' }],
  companyName: [{ required: true, message: '请输入公司名称', trigger: 'blur' }],
  companyAddress: [{ required: true, message: '请输入公司地址', trigger: 'blur' }],
  companyPhone: [
    { required: false, message: '请输入联系电话', trigger: 'change' },
    { pattern: /^\d+$/, message: '请输入正确的联系电话', trigger: 'change' },
  ],
  // companyContact: [{ required: true, message: '请输入联系人', trigger: 'blur' }],
}

const invoiceId = ref('')
function open(id) {
  invoiceId.value = id
  getDetail()
  visible.value = true
}

function handleClose() {
  if (curInvoiceType.value == 1) {
    ruleFormRef.value.resetFields()
    FormData.value = {
      invoiceAmount: 0,
      title: '',
      dutyParagraph: '',
    }
  } else {
    ruleTwoFormRef.value.resetFields()
    FormTwoData.value = {
      invoiceAmount: 0,
      companyName: '',
      companyContact: '',
      companyAddress: '',
      companyPhone: '',
    }
  }
  invoiceId.value = ''
  curInvoiceType.value = ''
  visible.value = false
}

function handleConfirm() {
  if (curInvoiceType.value == 1) {
    ruleFormRef.value.validate(valid => {
      if (valid) {
        updateInvoice({ id: invoiceId.value, ...FormData.value }).then(res => {
          ElMessage.success('修改成功')
          emits('success')
          handleClose()
        })
      }
    })
  } else {
    ruleTwoFormRef.value.validate(valid => {
      if (valid) {
        updateInvoice({ id: invoiceId.value, ...FormTwoData.value }).then(res => {
          ElMessage.success('修改成功')
          emits('success')
          handleClose()
        })
      }
    })
  }
}

const curInvoiceType = ref('')
function getDetail() {
  getBackInvoiceDetail({ invoiceId: invoiceId.value }).then(res => {
    curInvoiceType.value = res.data.invoiceType
    if (res.data && res.data.invoiceType == 1) {
      FormData.value = {
        invoiceAmount: res.data.invoiceAmount,
        title: res.data.title,
        dutyParagraph: res.data.dutyParagraph,
      }
    }
    if (res.data && res.data.invoiceType == 2) {
      FormTwoData.value = {
        invoiceAmount: res.data.invoiceAmount,
        companyName: res.data.companyName,
        companyContact: res.data.companyContact,
        companyAddress: res.data.companyAddress,
        companyPhone: res.data.companyPhone,
      }
    }
  })
}

const channelInputLimit = e => {
  const key = e.key
  if (key === 'Enter') e.preventDefault()
  const notAllowList = ['e', '+', '-']
  if (notAllowList.includes(key)) {
    e.returnValue = false
    return false
  }
  return true
}
</script>

<style scoped lang="scss">
:deep(.el-input-number) {
  .el-input__inner {
    text-align: left;
  }
}

.text-warp {
  word-break: break-all;
  line-break: anywhere;
}
</style>
