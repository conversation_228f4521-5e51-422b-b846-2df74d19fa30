<template>
  <div>
    <el-dialog
      v-model="visible"
      title="发票详情"
      align-center
      width="700"
      destroy-on-close
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      @close="handleClose"
    >
      <div v-loading="loading" style="height: 800px; overflow: auto">
        <div class="detail-head" v-if="curTab != '1'">
          <template v-if="formData.invoiceType == '1'">
            <div class="text-warp">
              <div class="fs-0">发票抬头：</div>
              <div class="more-ell" style="--l: 5">{{ formData.title || '-' }}</div>
            </div>
            <div class="text-warp">
              <div class="fs-0">&emsp;&emsp;税号：</div>
              <div class="more-ell" style="--l: 5">{{ formData.dutyParagraph || '-' }}</div>
            </div>
            <div v-if="formData.invoiceRemark" style="display: flex; align-items: baseline" class="text-warp">
              <div class="fs-0">商家备注：</div>
              <div>{{ formData.invoiceRemark }}</div>
            </div>
            <div v-if="formData.cautions" style="display: flex; align-items: baseline" class="text-warp">
              <div class="fs-0">注意事项：</div>
              <div>{{ formData.cautions }}</div>
            </div>
          </template>
          <template v-else>
            <div class="text-warp">
              <div class="fs-0">公司名称：</div>
              <div class="more-ell" style="--l: 5">{{ formData.companyName || '-' }}</div>
            </div>
            <div class="text-warp">
              <div class="fs-0">公司地址：</div>
              <div class="more-ell" style="--l: 5">{{ formData.companyAddress || '-' }}</div>
            </div>
            <div>联系电话：{{ formData.companyPhone || '无' }}</div>
            <div>&emsp;联系人：{{ formData.companyContact || '无' }}</div>
            <div class="text-warp">
              <div>&emsp;&emsp;附件：</div>
              <el-button
                style="padding: 0"
                v-if="formData.attachmentObjectKey"
                v-btn
                link
                type="primary"
                @click="getFileUrl(formData.attachmentObjectKey)"
              >
                下载
              </el-button>
              <span v-else>-</span>
            </div>
          </template>
        </div>

        <el-tabs v-model="tab">
          <el-tab-pane name="1" label="开票信息" v-if="curTab != '1'">
            <template v-if="invoiceRecordInfoList && invoiceRecordInfoList.length > 0">
              <div class="detail-info" v-for="item in invoiceRecordInfoList" :key="item.id">
                <div>{{ item.type == 1 ? '发票' : '红冲' }}</div>
                <div>{{ item.type == 1 ? '开票金额' : '红冲金额' }}：{{ item.invoiceAmount || 0 }}</div>
                <div class="text-warp">
                  <div class="fs-0">{{ item.type == 1 ? '发票号' : '红冲票号' }}：</div>
                  <div>{{ item.number || '-' }}</div>
                </div>
                <div>开票时间：{{ item.invoicingTime || '-' }}</div>
                <div style="display: flex; align-items: baseline">
                  <div>发票：</div>
                  <div style="flex: 1" v-if="item.objectKeys && item.objectKeys.length > 0">
                    <PasteUpload
                      v-model="item.file"
                      :limit="1"
                      :multiple="false"
                      :fileType="['jpg', 'jpeg', 'png', 'pdf', 'ofd', 'xml']"
                      show-file-list
                      @view-pdf="handleViewPdf"
                      :delete-button="false"
                      :size="3"
                    />
                    <!-- <template v-if="item.isImg">
                      <ViewerImageList :data="[item.objectKey]" is-preview-all :show-delete-btn="false" />
                    </template>
                    <template v-else>
                      <PasteUpload
                        v-model="item.file"
                        :limit="1"
                        :multiple="false"
                        :fileType="['jpg', 'jpeg', 'png', 'pdf', 'ofd', 'xml']"
                        show-file-list
                        @view-pdf="handleViewPdf(item.objectKey)"
                        :delete-button="false"
                        :size="3"
                      />
                    </template> -->
                  </div>
                  <!-- <ViewerImageList :data="[item.objectKey]" is-preview-all :show-delete-btn="false" /> -->
                </div>
                <div style="display: flex; align-items: baseline" class="text-warp">
                  <div style="flex-shrink: 0">备注：</div>
                  <div>{{ item.remark || '-' }}</div>
                </div>
              </div>
            </template>
            <div v-else style="text-align: center">暂无开票记录</div>
          </el-tab-pane>
          <el-tab-pane name="2" label="关联订单">
            <template v-if="curTab != '1' && curTab != '2' && formData.invoiceType == 1">
              <div class="flex-between" style="margin-bottom: 10px">
                <div>红冲关联订单</div>
                <div>合计{{ withdrawalAmount }}元</div>
              </div>
              <el-table border :data="orderInvoiceRedList" style="max-height: 250px; overflow: auto">
                <el-table-column
                  prop="orderNum"
                  label="订单号 "
                  align="center"
                  v-if="formData.orderType == 5"
                ></el-table-column>
                <el-table-column prop="videoCode" label="视频编码" align="center" v-else></el-table-column>
                <el-table-column prop="payType" label="支付方式" align="center">
                  <template v-slot="{ row }">
                    {{ payTypeMapNew[row.payType] || '-' }}
                  </template>
                </el-table-column>
                <el-table-column prop="refundType" label="退款类型" align="center">
                  <template v-slot="{ row }">
                    {{ refundTypeList.find(item => item.value == row.refundType)?.label || '-' }}
                  </template>
                </el-table-column>
                <el-table-column prop="withdrawDepositTime" label="提现时间" align="center"></el-table-column>
                <el-table-column
                  prop="withdrawDepositAmount"
                  label="提现金额（元）"
                  align="center"
                ></el-table-column>
              </el-table>
            </template>
            <div class="flex-between" style="margin: 10px 0" v-if="curTab != '1' && curTab != '2'">
              <div>开票关联订单</div>
              <div>合计{{ invoiceAmount }}元</div>
            </div>
            <el-table
              border
              :data="relevantOrderList"
              :style="{ maxHeight: curTab == 1 ? '550px' : '250px' }"
              style="overflow: auto"
            >
              <el-table-column prop="orderNum" label="订单号" align="center"></el-table-column>
              <el-table-column prop="payType" label="支付方式" align="center">
                <template v-slot="{ row }">
                  {{ payTypeMapNew[row.payType] || '-' }}
                </template>
              </el-table-column>
              <el-table-column prop="payTime" label="支付时间" align="center"></el-table-column>
              <el-table-column prop="invoiceAmount" label="开票金额（元）" align="center"></el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane name="3" label="流转记录">
            <template v-if="invoiceRecordList && invoiceRecordList.length > 0">
              <div style="max-height: 600px; overflow: auto">
                <el-timeline style="margin: 0 0 0 50px; max-width: 600px">
                  <el-timeline-item size="large" v-for="item in invoiceRecordList" :key="item.id">
                    <template #default>
                      <div class="time">
                        <div>
                          <span class="year">
                            {{ item.createTime.substring(10, item.createTime.length - 3) }}
                          </span>
                        </div>
                        <div class="day">{{ item.createTime.substring(0, 10) }}</div>
                      </div>
                      <div style="margin-left: 10px">
                        <div style="font-weight: 600; font-size: 16px">
                          {{ orderInvoiceOperateTypeEnum.find(it => it.value == item.type)?.label || '-' }}
                        </div>
                        <div class="text-warp">{{ item.content }}</div>
                        <div v-if="item.objectKeys">
                          <PasteUpload
                            v-model="item.file"
                            :limit="1"
                            :multiple="false"
                            :fileType="['jpg', 'jpeg', 'png', 'pdf', 'ofd', 'xml']"
                            show-file-list
                            @view-pdf="handleViewPdf"
                            :delete-button="false"
                            :size="3"
                          />
                          <!-- <template v-if="item.isImg">
                            <ViewerImageList
                              :data="[item.objectKey]"
                              is-preview-all
                              :show-delete-btn="false"
                            />
                          </template>
                          <template v-else>
                            <PasteUpload
                              v-model="item.file"
                              :limit="1"
                              :multiple="false"
                              :fileType="['jpg', 'jpeg', 'png', 'pdf', 'ofd', 'xml']"
                              show-file-list
                              @view-pdf="handleViewPdf(item.objectKey)"
                              :delete-button="false"
                              :size="3"
                            />
                          </template> -->
                        </div>
                      </div>
                    </template>
                    <template #dot>
                      <el-icon color="#409eff" size="26"><CircleCheck /></el-icon>
                    </template>
                  </el-timeline-item>
                </el-timeline>
              </div>
            </template>
            <div v-else style="text-align: center; margin: 100px 0">暂无流转记录</div>
          </el-tab-pane>
        </el-tabs>
      </div>
      <InvoiceDialog ref="invoiceDialog" :url="invoiceDialogUrl" />
    </el-dialog>
  </div>
</template>

<script setup>
import InvoiceDialog from '@/components/Dialog/invoice'
import PasteUpload from '@/components/FileUpload/pasteUpload'
import ViewerImageList from '@/components/ImagePreview/viewerImageList.vue'

import { downloadFile, downUrlFile } from '@/utils/index'
import { CircleCheck } from '@element-plus/icons-vue'
import { payTypeMapNew } from '@/utils/dict'
import { orderInvoiceOperateTypeEnum, refundTypeList } from '@/views/finance/invoice/data.js'
import {
  getBackInvoiceDetail,
  getInvoiceOperateRecord,
  getInvoiceRecord,
  getDownloadUrl,
} from '@/api/finance/invoice'

const { proxy } = getCurrentInstance()
const visible = ref(false)

defineExpose({
  open,
  close,
})

const curTab = ref('')
const invoiceId = ref('')
function open(id, type) {
  curTab.value = type
  invoiceId.value = id
  getInvoiceDetail()
  getInvoiceReverseRecord()
  if (type != 1) {
    getRecordInfo()
  } else {
    tab.value = '2'
  }
  visible.value = true
}
const tab = ref('1')
const invoiceDialog = ref()
const invoiceDialogUrl = ref('')
function handleClose() {
  tab.value = '1'
  curTab.value = ''
  invoiceDialog.value = ''
  invoiceDialogUrl.value = ''
  invoiceId.value = ''
  relevantOrderList.value = []
  formData.value = {}
  invoiceRecordList.value = []
  orderInvoiceRedList.value = []
  invoiceRecordInfoList.value = []
  invoiceAmount.value = 0
  withdrawalAmount.value = 0
  visible.value = false
}

function handleViewPdf(url) {
  invoiceDialogUrl.value = proxy.$picUrl + url
  invoiceDialog.value.open()
}

const formData = ref({})
const loading = ref(false)
const relevantOrderList = ref([])
const orderInvoiceRedList = ref([])
const withdrawalAmount = ref(0)
const invoiceAmount = ref(0)
function getInvoiceDetail() {
  loading.value = true
  getBackInvoiceDetail({ invoiceId: invoiceId.value })
    .then(res => {
      formData.value = res.data
      relevantOrderList.value = res.data?.orderInvoiceVideoBackVOS || []
      orderInvoiceRedList.value = res.data?.orderInvoiceRedOrderVideoVOS || []
      if (orderInvoiceRedList.value.length > 0) {
        withdrawalAmount.value = orderInvoiceRedList.value.reduce(
          (sum, item) => sum + item.withdrawDepositAmount,
          0
        )
        withdrawalAmount.value = withdrawalAmount.value.toFixed(2) || 0
      }
      if (relevantOrderList.value.length > 0) {
        invoiceAmount.value = relevantOrderList.value.reduce((sum, item) => sum + item.invoiceAmount, 0)
        invoiceAmount.value = invoiceAmount.value.toFixed(2) || 0
      }
    })
    .finally(() => {
      loading.value = false
    })
}

const invoiceRecordInfoList = ref([])
function getRecordInfo() {
  getInvoiceRecord({ invoiceId: invoiceId.value }).then(res => {
    invoiceRecordInfoList.value = res.data || []
    if (invoiceRecordInfoList.value.length > 0) {
      invoiceRecordInfoList.value.forEach(item => {
        item.file = []
        if (item.objectKeys && item.objectKeys.length > 0) {
          item.objectKeys.forEach(data => {
            item.file.push({ picUrl: data, name: data.split('/').pop(), url: data })
          })
          // item.file = [{ picUrl: item, name: item.split('/').pop() }]
          // item.isImg =
          //   item.objectKey.includes('.jpg') ||
          //   item.objectKey.includes('.png') ||
          //   item.objectKey.includes('.jpeg')
        }
        // if (item.objectKey) {
        //   item.file = [{ picUrl: item.objectKey, name: item.objectKey?.split('/').pop() }]
        //   item.isImg =
        //     item.objectKey.includes('.jpg') ||
        //     item.objectKey.includes('.png') ||
        //     item.objectKey.includes('.jpeg')
        // }
      })
    }
  })
}

const invoiceRecordList = ref([])

function getInvoiceReverseRecord() {
  const params = {}
  if (curTab.value == 2) {
    params.invoiceRedId = invoiceId.value
  } else {
    params.invoiceId = invoiceId.value
  }
  getInvoiceOperateRecord({ invoiceId: invoiceId.value }).then(res => {
    invoiceRecordList.value = res.data || []
    if (invoiceRecordList.value.length > 0) {
      invoiceRecordList.value.forEach(item => {
        item.file = []
        if (item.objectKeys && item.objectKeys.length > 0) {
          item.objectKeys?.forEach(data => {
            item.file.push({ picUrl: data, name: data.split('/').pop(), url: data })
          })
        }
        // if (item.objectKey) {
        //   item.file = [{ picUrl: item.objectKey, name: item.objectKey?.split('/').pop() }]
        //   item.isImg =
        //     item.objectKey.includes('.jpg') ||
        //     item.objectKey.includes('.png') ||
        //     item.objectKey.includes('.jpeg')
        // }
      })
    }
  })
}

function getFileUrl(objectKey) {
  const fileSuffix = objectKey.substring(objectKey.lastIndexOf('/') + 1)
  downUrlFile(objectKey, fileSuffix)
}
</script>

<style scoped lang="scss">
:deep(.el-timeline) {
  .el-timeline-item {
    .el-timeline-item__tail {
      left: 12px;
      top: 11px;
    }
    .el-timeline-item__dot {
      z-index: 9;
      background: #fff;
    }
  }
}

//左侧时间
.time {
  color: #409eff;
  position: absolute;
  left: -90px;
  top: 1px;
  text-align: right;
  .year {
    font-size: 18px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #20354a;
  }
  .day {
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #596878;
    text-align: center;
    margin-top: 10px;
  }
}
.text-warp {
  word-break: break-all;
  line-break: anywhere;
  white-space: break-spaces;
  display: flex;
  align-items: baseline;
}
.detail-head {
  display: grid;
  gap: 5px;
  max-height: 250px;
  overflow: auto;
}
.detail-info {
  display: grid;
  gap: 5px;
  background: #f2f2f2;
  border-radius: 10px;
  padding: 10px 15px;
  margin-bottom: 15px;
}
</style>
