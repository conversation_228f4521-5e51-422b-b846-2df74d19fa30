<template>
  <div>
    <el-tabs v-model="activeName" class="tabs" @tab-change="handleTabChange">
      <el-tab-pane label="待审核" name="0">
        <Refund v-if="activeName == '0'" ref="Refund1" type="0" @hover="handleModelHover" />
      </el-tab-pane>
      <el-tab-pane label="全部" name="all">
        <Refund v-if="activeName == 'all'" ref="Refund2" @hover="handleModelHover" />
      </el-tab-pane>
    </el-tabs>

    <ModelInfoPopover ref="ModelInfoPopoverRef" />
  </div>
</template>
<script setup>
import Refund from '@/views/finance/refundApproval/components/Refund.vue'
import ModelInfoPopover from '@/views/model/ModelInfoPopover.vue'

const ModelInfoPopoverRef = ref()

const activeName = ref('0')

const Refund1 = ref()
const Refund2 = ref()

function handleTabChange() {
  if (activeName.value == '0') {
    Refund1.value?.handleQuery()
  } else {
    Refund2.value?.handleQuery()
  }
}
function handleModelHover(el, id, show) {
  if (show) {
    ModelInfoPopoverRef.value?.open(el, id)
  } else {
    ModelInfoPopoverRef.value?.close()
  }
}
</script>

<style scoped lang="scss">
.tabs {
  padding-left: 20px;

  :deep(.el-tabs__item:focus-visible) {
    box-shadow: none;
  }
}
</style>
