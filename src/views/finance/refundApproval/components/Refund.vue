<template>
  <div style="padding-bottom: 20px">
    <ElTablePage
      ref="tableRef"
      :columns="columns"
      :data="pageData"
      :loading="tableLoading"
      :currentPage="currentPage"
      :pageSize="pageSize"
      :total="total"
      :tableAction="{
        width: '190',
        fixed: 'right',
      }"
      @page-change="pageChange"
      row-key="id"
    >
      <template #tableHeader>
        <div>
          <el-form :inline="true" :model="queryParams" class="demo-form-inline" @submit.prevent>
            <el-form-item label="搜索" prop="keyword">
              <el-input
                v-model="queryParams.keyword"
                clearable
                style="width: 260px"
                placeholder="请输入关键字搜索"
              />
            </el-form-item>
            <el-form-item label="退款类型" prop="type">
              <el-select
                multiple
                collapse-tags
                v-model="queryParams.refundType"
                placeholder="请选择"
                clearable
                style="width: 150px"
              >
                <el-option
                  v-for="dict in refundTypeList"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="退款状态" prop="refundStatus" v-if="props.type != '0'">
              <el-select
                multiple
                collapse-tags
                v-model="queryParams.refundStatus"
                placeholder="请选择"
                clearable
                style="width: 150px"
              >
                <el-option
                  v-for="dict in refundStatusList"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="时间" style="width: 350px">
              <el-date-picker
                v-model="queryParams.times"
                format="YYYY/M/D HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="datetimerange"
                range-separator="-"
                unlink-panels
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
                :shortcuts="shortcuts"
              ></el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button v-btn type="primary" icon="Search" native-type="submit" @click="onQuery">
                搜索
              </el-button>
              <el-button v-btn icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </template>
      <template #orderNumInfo="{ row }">
        <div style="text-align: left">
          <div class="flex-start gap-5">
            订单号:
            <!-- <el-tag type="danger" size="small" round>{{ matchOrderStatus(row.status) }}</el-tag> -->
          </div>
          <div style="text-align: left">{{ row.orderNum }}</div>
          <div style="display: flex; text-align: left; flex-direction: column">
            <div>退款审批号：</div>
            <div style="text-align: left">{{ row.refundNum }}</div>
          </div>
        </div>
      </template>
      <template #orderInfo="{ row }">
        <div style="text-align: left">
          <div>视频编码:{{ row.videoCode }}</div>
          <div class="more-ell" v-ellipsis-tooltips="row.productChinese">
            中文名称:{{ row.productChinese }}
          </div>
          <div class="more-ell" v-ellipsis-tooltips="row.productEnglish">
            英文名称:{{ row.productEnglish }}
          </div>
          <div class="one-ell productLink">
            产品链接：
            <el-link :underline="false" target="_blank" type="primary" :href="row.productLink">
              {{ row.productLink }}
            </el-link>
          </div>
          <!-- <div v-if="row.shootModel">
            拍摄模特:{{ `${row.shootModel.name}  (ID ${row.shootModel.account})` }}
          </div> -->
          <!-- <div v-if="row.merchant">商家编码:{{ row.merchant.memberCode }}</div>
          <div v-if="row.merchant">商家名称:{{ row.merchant.businessName }}</div> -->
          <biz-model-platform :value="row.platform" />
          <biz-nation :value="row.shootingCountry" />
        </div>
      </template>
      <template #status="{ row }">
        <div>
          {{ orderStatusMap[row.status] }}
        </div>
        <el-button
          v-btn
          link
          type="primary"
          v-hasPermi="['finance:refundApproval:detail']"
          @click="routerNewWindow('/order/details/' + row.videoId)"
        >
          订单详情
        </el-button>
      </template>
      <template #shootModel="{ row }">
        <div
          v-if="row.shootModel"
          @mouseenter="handleMouseEnter($event, row.shootModel.id)"
          @mouseleave="handleMouseLeave($event, row.shootModel.id)"
        >
          <el-avatar
            class="model-avatar"
            icon="UserFilled"
            :src="$picUrl + row.shootModel.modelPic + '!1x1compress'"
          />
          <div>{{ row.shootModel.name }}</div>
          <div>{{ row.shootModel?.account ? `(ID:${row.shootModel.account})` : '-' }}</div>
          <BizModelTypeNew :value="row.shootModel.type" />
          <!-- <biz-model-type :value="row.shootModel.type" /> -->
        </div>
        <div v-else>-</div>
      </template>

      <template #refundAmount="{ row }">
        <span :style="{ color: row.refundAmount > row.realAmount ? 'red' : '' }">{{ row.refundAmount }}</span>
      </template>
      <template #refundType="{ row }">
        <template v-for="item in refundTypeList" :key="item.value">
          <el-tag v-if="row.refundType == item.value" :type="item.type">
            {{ item.label }}
          </el-tag>
        </template>
      </template>
      <template #refundInfo="{ row }">
        <div style="display: flex; text-align: left; flex-direction: column">
          <div v-if="row.initiatorSource">
            发起方:{{ refundSourceList.find(item => item.value == row.initiatorSource).label }}
          </div>
          <div v-if="row.initiatorName">发起人:{{ row.initiatorName }}</div>
          <div v-if="row.refundCause" class="more-ell" v-ellipsis-tooltips="row.refundCause">
            退款原因:{{ row.refundCause }}
          </div>
          <div v-if="row.refundPicCount">退款方案:{{ row.refundPicCount }}张</div>
        </div>
      </template>
      <template #refundStatus="{ row }">
        <template v-for="item in refundStatusList" :key="item.value">
          <el-tag v-if="row.refundStatus == item.value" :type="item.type">
            {{ item.label }}
          </el-tag>
        </template>
        <div v-if="row.operateTime">
          {{ row.operateTime }}
        </div>
        <div v-if="row.rejectCause">拒绝理由:{{ row.rejectCause }}</div>
      </template>
      <template #tableAction="{ row }">
        <el-button
          v-btn
          v-hasPermi="['finance:refundApproval:confirm']"
          v-if="row.refundStatus == 0"
          link
          type="primary"
          @click="openApproveDialog(row)"
        >
          审核
        </el-button>
        <!-- <el-button
          v-btn
          v-hasPermi="['finance:refundApproval:confirm']"
          v-if="row.refundStatus == 0"
          link
          type="primary"
          @click="openConfirmDialog(row)"
        >
          同意
        </el-button>
        <el-button
          v-btn
          v-hasPermi="['finance:refundApproval:confirm']"
          v-if="row.refundStatus == 0"
          link
          type="primary"
          @click="openRefuseDialog(row)"
        >
          拒绝
        </el-button> -->
      </template>
      <template #pageLeft>
        <DownloadBtn
          v-hasPermi="['finance:refundApproval:export']"
          type="success"
          plain
          icon="Download"
          :isAsnyc="true"
          url="/order/refund/export"
          :params="getQueryParams"
          fileName="退款信息.xlsx"
        />
      </template>
    </ElTablePage>

    <el-dialog
      v-model="dialogVisible"
      width="500px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      title="退款审核"
      align-center
      center
      @close="handleDialogClose"
    >
      <el-descriptions :column="1">
        <el-descriptions-item label="视频编码">
          {{ dialogVideoData.videoCode }}
        </el-descriptions-item>
        <el-descriptions-item label="产品中文名">
          <span class="more-ell" v-ellipsis-tooltips="dialogVideoData.productChinese">
            {{ dialogVideoData.productChinese }}
          </span>
        </el-descriptions-item>
        <el-descriptions-item label="订单实付金额">{{ dialogVideoData.realAmount }}元</el-descriptions-item>
        <el-descriptions-item label="已退款金额">
          {{ dialogVideoData.refundAmountTotal }}元
        </el-descriptions-item>
        <el-descriptions-item label="订单状态">
          {{ orderStatusMap[dialogVideoData.videoStatus] }}
        </el-descriptions-item>
        <el-descriptions-item label="">
          <el-divider border-style="dashed" style="margin: -10px 0 10px" />
        </el-descriptions-item>
        <el-descriptions-item label="">
          <span style="font-weight: bold; position: relative; left: -16px">退款申请</span>
        </el-descriptions-item>
        <el-descriptions-item label="退款金额">{{ dialogVideoData.refundAmount }}元</el-descriptions-item>
        <el-descriptions-item label="退款类型">
          {{ handleRefundType(dialogVideoData.refundType, dialogVideoData.isCancelOrder) }}
        </el-descriptions-item>
        <el-descriptions-item label="退款原因" class-name="asdf">
          <span class="more-ell" v-ellipsis-tooltips="dialogVideoData.refundCause">
            {{ dialogVideoData.refundCause }}
          </span>
        </el-descriptions-item>
        <el-descriptions-item label="申请人">
          {{ dialogVideoData.initiatorName }}
        </el-descriptions-item>
      </el-descriptions>
      <el-form ref="approveDialogForm" :model="dialogForm" :rules="rules" label-width="90px">
        <el-form-item label="审核结果:" prop="action">
          <el-radio-group v-model="dialogForm.action">
            <el-radio-button :value="1">同意</el-radio-button>
            <el-radio-button :value="0">拒绝</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注:" prop="remark" v-if="dialogForm.action == 1">
          <el-col :span="21">
            <el-input
              v-model="dialogForm.remark"
              :rows="4"
              maxlength="500"
              placeholder="请输入备注"
              show-word-limit
              type="textarea"
            />
          </el-col>
        </el-form-item>
        <el-form-item label="拒绝理由:" prop="comment" v-if="dialogForm.action == 0">
          <el-col :span="21">
            <el-input
              v-model="dialogForm.comment"
              :rows="4"
              maxlength="500"
              placeholder="请输入拒绝理由"
              show-word-limit
              type="textarea"
            />
          </el-col>
        </el-form-item>
      </el-form>
      <el-dialog v-model="dialogConfirmVisible" width="30%" align-center center>
        <template #header="{ titleClass }">
          <div :class="titleClass" style="transform: translateX(16px)">
            确认{{ dialogForm.action == 1 ? '同意' : '拒绝' }}退款吗？
          </div>
        </template>
        <div v-if="dialogForm.action == 1" style="text-align: center; color: red">
          同意后，退款金额将退至商家账号余额中，是否继续？
        </div>
        <div v-if="dialogForm.action == 0" style="text-align: center; color: red">
          是否拒绝当前订单的退款？
        </div>
        <template #footer>
          <div class="dialog-footer">
            <el-button v-btn @click="dialogConfirmVisible = false">取消</el-button>
            <el-button v-btn type="primary" @click="confirmRefund">确定</el-button>
          </div>
        </template>
      </el-dialog>
      <template #footer>
        <div class="dialog-footer">
          <el-button v-btn @click="dialogVisible = false">取消</el-button>
          <el-button v-btn type="primary" @click="onConfirm">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import ElTablePage from '@/components/Table/ElTablePage.vue'
import DownloadBtn from '@/components/Button/DownloadBtn.vue'
import { refundTypeList, refundStatusList, refundSourceList } from '@/views/finance/data.js'
import { orderStatusMap, orderStatusList, picCountOptions } from '@/views/order/list/data.js'
import { refundApprovalList, consentRefund, rejectRefund } from '@/api/finance/refundApproval'
import { ElLoading, ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'

const router = useRouter()
const route = useRoute()

const { proxy } = getCurrentInstance()

const emits = defineEmits(['hover'])

const shortcuts = [
  {
    text: '今天',
    value: () => {
      const start = new Date()
      start.setHours(0, 0, 0, 0)
      const end = new Date()
      end.setHours(23, 59, 59, 59)
      return [start, end]
    },
  },
]

const dialogVisible = ref(false)
const dialogConfirmVisible = ref(false)
const dialogVideoData = ref({})
const dialogForm = ref({
  id: '',
  action: 1,
  remark: '',
  comment: '',
})
const rules = ref({
  action: [{ required: true, message: '请选择审核结果', trigger: 'change' }],
  comment: [
    { required: true, message: '请输入理由', trigger: 'blur' },
    { min: 1, max: 64, message: '请输入1到64字理由', trigger: 'blur' },
  ],
})
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const tableLoading = ref(false)
const queryParams = ref({
  keyword: '',
  val: '',
  select: 'refundNum',
  refundType: [],
  refundStatus: [],
  times: [],
  orderNum: '',
  refundNum: '',
  videoCode: '',
  merchantName: '',
  productName: '',
})
const pageData = ref([])

const props = defineProps({
  type: String,
})

defineExpose({
  handleQuery,
})

const selectList = [
  { label: '订单号', value: 'orderNum' },
  { label: '退款单号', value: 'refundNum' },
  { label: '视频编码', value: 'videoCode' },
  { label: '商家名称', value: 'merchantName' },
  { label: '产品名称', value: 'productName' },
]

function initStatus() {
  if (!queryParams.value.type && props.type !== undefined) {
    queryParams.value.refundStatus = [Number(props.type)]
  }
}

const columns = [
  { prop: 'applyTime', label: '申请时间', width: '200' },
  { slot: 'orderNumInfo', prop: 'orderNumInfo', label: '单号', minWidth: '200' },
  { slot: 'orderInfo', prop: 'orderInfo', label: '产品信息', minWidth: '300' },
  {
    prop: 'picCount',
    label: '照片数量',
    width: '80',
    handle: data => {
      let str = picCountOptions.find(item => item.value == data)
      return str ? str.label.substring(0, 2) : '-'
    },
  },
  { slot: 'status', prop: 'status', label: '订单状态', width: '150' },
  { prop: 'refundAmountTotal', label: '已退款金额', width: '90' },
  { slot: 'shootModel', prop: 'shootModel', label: '拍摄模特', width: '130' },
  {
    prop: 'contactUser',
    label: '中文部/英文部',
    width: '120',
    handle: (data, row) => {
      let str = '-'
      if (data?.name || row.issueUser?.name) {
        str = (data?.name || '-') + '/' + (row.issueUser?.name || '-')
      }
      return str
    },
  },
  { prop: 'realAmount', label: '订单金额', width: '100' },
  { slot: 'refundAmount', prop: 'refundAmount', label: '退款金额', width: '100' },
  { slot: 'refundType', prop: 'refundType', label: '退款类型', width: '100' },
  { slot: 'refundInfo', prop: 'refundInfo', label: '退款信息', minWidth: '160' },
  { slot: 'refundStatus', prop: 'refundStatus', width: '110', label: '退款状态' },
]

function getQueryParams() {
  let params = {
    timeBegin: '',
    timeEnd: '',
    keyword: queryParams.value.keyword,
    refundStatus: queryParams.value.refundStatus,
    refundType: queryParams.value.refundType,
  }
  if (props.type == '0') {
    params.refundStatus = [Number(props.type)]
  }
  if (queryParams.value.times != [] && queryParams.value.times != null) {
    params.timeBegin = queryParams.value.times[0]
    params.timeEnd = queryParams.value.times[1]
  }
  if (queryParams.value.select == 'orderNum') {
    params.orderNum = queryParams.value.val
  }
  if (queryParams.value.select == 'refundNum') {
    params.refundNum = queryParams.value.val
  }
  if (queryParams.value.select == 'videoCode') {
    params.videoCode = queryParams.value.val
  }
  if (queryParams.value.select == 'merchantName') {
    params.merchantName = queryParams.value.val
  }
  if (queryParams.value.select == 'productName') {
    params.productName = queryParams.value.val
  }
  return params
}

function onQuery() {
  currentPage.value = 1
  getList(getQueryParams())
}
function handleQuery() {
  getList(getQueryParams())
}
function resetQuery() {
  queryParams.value = {
    keyword: '',
    val: '',
    select: 'refundNum',
    refundType: [],
    refundStatus: [],
    times: [],
    orderNum: '',
    refundNum: '',
    videoCode: '',
    merchantName: '',
    productName: '',
  }
  initStatus()
  handleQuery()
}

function toDetails(row) {
  router.push(`/order/details/${row.videoId}`)
}

function routerNewWindow(path) {
  const { href } = router.resolve({ path })
  window.open(href, '_blank')
}

function handleRefundType(type, isCancelOrder) {
  let item = refundTypeList.find(item => item.value == type)
  if (item) {
    if (item.value == 1 && isCancelOrder) {
      return item.label + '+取消订单'
    }
    return item.label
  }
  return ''
}

function handleDialogClose() {
  dialogForm.value = {
    id: '',
    action: 1,
    remark: '',
    comment: '',
  }
}
function openApproveDialog(data) {
  dialogVideoData.value = data
  dialogForm.value.id = data.id
  dialogVisible.value = true
}

function onConfirm() {
  proxy.$refs['approveDialogForm'].validate(valid => {
    if (valid) {
      dialogConfirmVisible.value = true
    }
  })
}
// 确认审核
function confirmRefund() {
  dialogConfirmVisible.value = false
  const el_loading = ElLoading.service({
    lock: true,
    text: '正在提交',
    background: 'rgba(0, 0, 0, 0.7)',
  })
  if (dialogForm.value.action == 1) {
    consentRefund({
      id: [dialogForm.value.id],
      remark: dialogForm.value.remark,
    })
      .then(res => {
        if (res.code == 200) {
          ElMessage({
            message: `退款成功！`,
            type: 'success',
          })
          handleQuery()
          dialogVisible.value = false
        }
      })
      .finally(() => el_loading.close())
  } else if (dialogForm.value.action == 0) {
    rejectRefund({
      id: [dialogForm.value.id],
      rejectCause: dialogForm.value.comment,
    })
      .then(res => {
        if (res.code == 200) {
          ElMessage({
            message: `拒绝退款成功！`,
            type: 'success',
          })
          handleQuery()
          dialogVisible.value = false
        }
      })
      .finally(() => el_loading.close())
  }
}

function getList(param) {
  tableLoading.value = true
  refundApprovalList({ ...param, pageNum: currentPage.value, pageSize: pageSize.value })
    .then(res => {
      pageData.value = res.data.rows
      total.value = res.data.total
    })
    .finally(() => (tableLoading.value = false))
}

function pageChange(page) {
  currentPage.value = page.currentPage
  pageSize.value = page.pageSize
  handleQuery()
}

function handleMouseEnter(e, id) {
  emits('hover', e.target, id, true)
}
function handleMouseLeave(e, id) {
  emits('hover', e.target, id, false)
}

function init() {
  if (route.query.r_type) {
    let item = refundTypeList.find(item => item.value == route.query.r_type)
    if (item) {
      queryParams.value.refundType = [item.value]
    }
  }
  initStatus()
  handleQuery()
}
init()
</script>

<style lang="scss" scoped>
:deep(.el-radio-button) {
  margin: 0 10px 10px 0;

  &.is-active {
    box-shadow: none;
  }

  .el-radio-button__inner {
    border-left: var(--el-border);
    border-radius: var(--el-border-radius-base);
  }
}
:deep(.el-radio-group) {
  .el-radio-button.is-active {
    .el-radio-button__inner {
      box-shadow: none;
    }
  }
}
:deep(.el-descriptions) {
  padding-left: 16px;
  margin-bottom: 16px;

  .el-descriptions__body .el-descriptions__table:not(.is-bordered) {
    .el-descriptions__cell {
      padding-bottom: 4px;
      display: flex;

      .el-descriptions__label {
        flex-shrink: 0;
      }
    }
  }
}
.productLink {
  position: relative;
  padding-right: 5px;

  :deep(.el-link) {
    display: contents;

    .el-link__inner {
      display: inline;
    }
  }
}
</style>
