<template>
  <div class="login">
    <el-form ref="loginRef" :model="loginForm" :rules="loginRules" class="login-form">
      <h3 class="title">蜗牛运营管理系统</h3>
      <div class="loginTabs">
        <!-- <div class="tab" :class="{ active: curTabs == 0 }" @click="changeLogin(0)">账号密码登录</div>
        <div class="tab" :class="{ active: curTabs == 1 }" @click="changeLogin(1)">手机号登录</div> -->
      </div>
      <div v-if="!curTabs">
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            type="text"
            size="large"
            auto-complete="off"
            placeholder="账号"
          >
            <template #prefix>
              <svg-icon icon-class="user" class="el-input__icon input-icon" />
            </template>
          </el-input>
        </el-form-item>
        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            size="large"
            auto-complete="off"
            placeholder="密码"
            show-password
            @keyup.enter="handleLogin"
          >
            <template #prefix>
              <svg-icon icon-class="password" class="el-input__icon input-icon" />
            </template>
          </el-input>
        </el-form-item>
        <div class="flex-start">
          <el-checkbox v-model="loginForm.rememberMe" style="margin: 0px 0px 25px 0px">记住密码</el-checkbox>
        </div>
      </div>
      <div v-else>
        <el-form-item prop="phonenumber">
          <el-input
            v-model="loginForm.phonenumber"
            type="text"
            size="large"
            auto-complete="off"
            placeholder="手机号"
          >
            <template #prefix>
              <svg-icon icon-class="user" class="el-input__icon input-icon" />
            </template>
          </el-input>
        </el-form-item>
        <el-form-item prop="code">
          <el-input
            v-model="loginForm.code"
            size="large"
            auto-complete="off"
            placeholder="验证码"
            style="width: 63%"
            @keyup.enter="handleLogin"
          >
            <template #prefix>
              <svg-icon icon-class="validCode" class="el-input__icon input-icon" />
            </template>
          </el-input>
          <div class="login-code" style="margin-left: 13px">
            <div v-if="codeTime > 0" class="time">
              {{ codeTime + codeText }}
            </div>
            <el-button v-btn v-else :disabled="codeTime > 0" @click="getPhoneCode">
              {{ codeText }}
            </el-button>
          </div>
        </el-form-item>
      </div>
      <el-form-item style="width: 100%">
        <el-button v-btn
          :loading="loading"
          size="large"
          type="primary"
          style="width: 100%"
          @click.prevent="handleLogin"
        >
          <span v-if="!loading">登 录</span>
          <span v-else>登 录 中...</span>
        </el-button>
        <div style="float: right" v-if="register">
          <router-link class="link-type" :to="'/register'">立即注册</router-link>
        </div>
      </el-form-item>
    </el-form>
    <!--  底部  -->
    <div class="el-login-footer">
      <span>闽ICP备2024057555号 - 泉州润一进出口贸易有限公司</span>
    </div>
  </div>
</template>

<script setup>
import { getCodeImg } from '@/api/login'
import Cookies from 'js-cookie'
import { encrypt, decrypt } from '@/utils/jsencrypt'
import useUserStore from '@/store/modules/user'

const userStore = useUserStore()
const route = useRoute()
const router = useRouter()
const { proxy } = getCurrentInstance()

const curTabs = ref(0)

const loginForm = ref({
  username: '',
  password: '',
  rememberMe: false,
  code: '',
  uuid: '',
})

const loginRules = {
  username: [{ required: true, trigger: 'blur', message: '请输入您的账号' }],
  phonenumber: [
    { required: true, message: '请输入正确的手机号码', trigger: 'blur' },
    { pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: '请输入正确的手机号码', trigger: 'change' },
  ],
  password: [{ required: true, trigger: 'blur', message: '请输入您的密码' }],
  code: [{ required: true, trigger: 'change', message: '请输入验证码' }],
}

const codeUrl = ref('')
const loading = ref(false)
// 验证码开关
const captchaEnabled = ref(true)
// 注册开关
const register = ref(false)
const redirect = ref(undefined)

const codeTime = ref(0)
const codeText = ref('获取验证码')

watch(
  route,
  newRoute => {
    redirect.value = newRoute.query && newRoute.query.redirect
  },
  { immediate: true },
)

function handleLogin() {
  proxy.$refs.loginRef.validate(valid => {
    if (valid) {
      loading.value = true
      if (curTabs.value === 0) {
        // 勾选了需要记住密码设置在 cookie 中设置记住用户名和密码
        if (loginForm.value.rememberMe) {
          Cookies.set('username', loginForm.value.username, { expires: 30 })
          Cookies.set('password', encrypt(loginForm.value.password), { expires: 30 })
          Cookies.set('rememberMe', loginForm.value.rememberMe, { expires: 30 })
        } else {
          // 否则移除
          Cookies.remove('username')
          Cookies.remove('password')
          Cookies.remove('rememberMe')
        }
      }
      // 调用action的登录方法
      userStore
        .login(loginForm.value, curTabs.value)
        .then(() => {
          const query = route.query
          const otherQueryParams = Object.keys(query).reduce((acc, cur) => {
            if (cur !== 'redirect') {
              acc[cur] = query[cur]
            }
            return acc
          }, {})
          const path = redirect.value && redirect.value != '/index' ? redirect.value : '/'
          router.push({ path, query: otherQueryParams })
        })
        .catch(() => {
          loading.value = false
          // 重新获取验证码
          if (captchaEnabled.value) {
            getCode()
          }
        })
    }
  })
}

function getCode() {
  // getCodeImg().then(res => {
  //   captchaEnabled.value = res.captchaEnabled === undefined ? true : res.captchaEnabled;
  //   if (captchaEnabled.value) {
  //     codeUrl.value = "data:image/gif;base64," + res.img;
  //     loginForm.value.uuid = res.uuid;
  //   }
  // });
}

function getCookie() {
  const username = Cookies.get('username')
  const password = Cookies.get('password')
  const rememberMe = Cookies.get('rememberMe')
  loginForm.value = {
    username: username === undefined ? loginForm.value.username : username,
    password: password === undefined ? loginForm.value.password : decrypt(password),
    rememberMe: rememberMe === undefined ? false : Boolean(rememberMe),
  }
}

function getPhoneCode() {
  if (codeTime.value > 0) return
  proxy.$refs.loginRef.validateField('phonenumber', valid => {
    if (valid) {
      codeTime.value = 60
      codeText.value = '秒后重新获取'
      // sendPhoneCode(form.value.username)
      Countdown()
    }
  })
}

function Countdown() {
  const t = setInterval(() => {
    codeTime.value--
    if (codeTime.value == 0) {
      codeText.value = '获取验证码'
      clearInterval(t)
    }
  }, 1000)
}

function changeLogin(val) {
  loginForm.value.code = ''
  curTabs.value = val
}

getCode()
getCookie()
</script>

<style lang="scss" scoped>
.login {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-image: url('https://pstatic.woniu.video/static/bg-manage-login.webp');
  background-size: cover;
}

.login-form {
  border-radius: 6px;
  background: #ffffff;
  width: 400px;
  padding: 25px 25px 5px 25px;
  position: relative;

  .el-input {
    height: 40px;

    input {
      height: 40px;
    }
  }

  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 0px;
  }

  .title, h3 {
    position: absolute;
    left: 0;
    top: -30%;
    width: 100%;
    font-size: 30px;
    text-align: center;
    color: rgba(0, 0, 0, 0.7);
  }
}

.login-tip {
  font-size: 13px;
  text-align: center;
  color: rgba(0, 0, 0, 0.7);
}

.login-code {
  width: 33%;
  height: 40px;
  float: right;

  img {
    cursor: pointer;
    vertical-align: middle;
  }

  button {
    width: 100%;
    height: 100%;
  }

  .time {
    width: 100%;
    height: 100%;
    color: rgba(0, 0, 0, 0.7);
    cursor: not-allowed;
    background-image: none;
    background-color: rgba(0, 0, 0, 0.7);
    border-color: rgba(0, 0, 0, 0.7);
    border: 1px solid;
    border-radius: 4px;
    text-align: center;
    line-height: 40px;
  }
}

.el-login-footer {
  background: rgba(255, 255, 255, 0.7);
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: rgba(0, 0, 0, 0.7);
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
}

.login-code-img {
  height: 40px;
  padding-left: 12px;
}

.loginTabs {
  display: flex;
  justify-content: center;
  text-align: center;
  margin: 0 0 20px 0;

  .tab {
    width: 100%;
    cursor: pointer;
    height: 38px;
    line-height: 34px;
    font-size: 16px;
    // border: 1px solid #ffffff99;
    border-bottom: 2px solid #ddd;
    color: #000;

    &:hover {
      color: #3b99fc;
    }
  }

  .active {
    // cursor: auto;
    color: #3b99fc;
    border-bottom: 2px solid #3b99fc;
    // background-color: #3b99fc;
  }
}

.text-btn {
  color: #3b99fc;
  cursor: pointer;
  font-size: 14px;
  margin: 0px 0px 25px;
}
</style>
