<template>
  <div>
    <el-tabs v-model="activeName" @tab-change="resetQuery">
      <el-tab-pane label="未处理" name="0">
        <template #label>
          <div>
            未处理 (
            {{ waitNum }}
            )
          </div>
        </template>
      </el-tab-pane>
      <el-tab-pane label="已处理" name="1"></el-tab-pane>
    </el-tabs>
    <ElTablePage
      style="padding: 20px 20px 45px 20px"
      ref="tableRef"
      :columns="columns"
      :data="tableData"
      :loading="tableLoading"
      :currentPage="pageNum"
      :pageSize="pageSize"
      :total="total"
      :tableAction="
        activeName == '0'
          ? {
              width: '150',
              fixed: 'right',
            }
          : false
      "
      @page-change="pageChange"
      row-key="id"
    >
      <template #tableHeader>
        <div style="margin-bottom: 10px">
          <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="80px" @submit.prevent>
            <el-form-item label="类型">
              <el-select v-model="queryParams.object" clearable placeholder="请选择类型" style="width: 200px">
                <el-option
                  v-for="item in textTypeList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="中文部客服" prop="chineseWaiterIds" label-width="120px">
              <el-select
                :reserve-keyword="false"
                v-model="queryParams.chineseWaiterIds"
                placeholder="请选择"
                multiple
                filterable
                collapse-tags
                collapse-tags-tooltip
                clearable
                style="width: 200px"
              >
                <el-option v-for="item in contactList" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
            <el-form-item label="会员编码" label-width="120px">
              <el-input
                placeholder="请输入"
                clearable
                v-model="queryParams.memberCode"
                style="width: 200px"
              />
            </el-form-item>
            <el-form-item>
              <el-button v-btn type="primary" native-type="submit" @click="handleQuery">搜索</el-button>
              <el-button v-btn @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </template>
      <template #content="{ row }">
        <div class="more-ell" style="--l: 3">{{ row.content || '-' }}</div>
      </template>
      <template #businessName="{ row }">
        <div style="text-align: left">
          <div>微信名：{{ row.roastUserNickName }} / 姓名：{{ row.roastUserName || '-' }}</div>
          <div>{{ row.businessName || '-' }}({{ row.memberCode || '-' }})</div>
        </div>
      </template>
      <template #tableAction="{ row }">
        <el-button
          v-if="row.handleStatus == 0"
          v-btn
          link
          type="primary"
          @click="handleOpenDialog('edit', row)"
          v-hasPermi="['system:tease:edit']"
        >
          处理
        </el-button>
      </template>
    </ElTablePage>
    <el-dialog
      v-model="dialogVisible"
      title="吐槽处理"
      width="800px"
      align-center
      append-to-body
      destroy-on-close
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="handleCloseDialog"
    >
      <el-form
        :disabled="dialogDisabled"
        ref="formRef"
        :model="dialogData"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="处理结果" prop="handleResult">
          <el-input
            placeholder="请输入处理结果"
            type="textarea"
            v-model="dialogData.handleResult"
            maxlength="1000"
            resize="none"
            :rows="10"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <template #footer v-if="!dialogDisabled">
        <div class="dialog-footer">
          <el-button @click="handleCloseDialog">取消</el-button>
          <el-button type="primary" @click="handleConfirm">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import ElTablePage from '@/components/Table/ElTablePage'
import { textTypeList, statusTypeList } from '@/views/tease/data.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import { getRoastList, handleRoast, getContactSelect } from '@/api/roast/roast.js'

const activeName = ref('0')
const props = defineProps({
  waitNum: {
    type: [Number, String],
    default: 0,
  },
})

const emits = defineEmits(['getNum'])

const columns = computed(() => {
  let list = []
  if (activeName.value == '0') {
    list = [
      {
        prop: 'content',
        label: '内容',
        ellipsis: true,
        line: 2,
        tooltipPlacement: 'right',
        handle: data => {
          return data || '-'
        },
      },
      {
        width: 100,
        prop: 'object',
        label: '类型',
        handle: data => {
          let item = textTypeList.find(item => item.value == data)
          if (item) {
            return item.label
          }
          return '-'
        },
      },
      {
        prop: 'updateTime',
        label: '提交时间',
        width: 180,
      },
      {
        slot: 'businessName',
        prop: 'businessName',
        label: '商家信息',
      },
      {
        prop: 'waiterName',
        label: '中文部客服',
        width: 120,
        handle: data => {
          return data || '-'
        },
      },
    ]
  } else {
    list = [
      {
        prop: 'content',
        label: '内容',
        ellipsis: true,
        line: 2,
        tooltipPlacement: 'right',
        handle: data => {
          return data || '-'
        },
      },
      {
        width: 100,
        prop: 'object',
        label: '类型',
        handle: data => {
          let item = textTypeList.find(item => item.value == data)
          if (item) {
            return item.label
          }
          return '-'
        },
      },
      {
        prop: 'updateTime',
        label: '提交时间',
        width: 180,
      },
      {
        slot: 'businessName',
        prop: 'businessName',
        label: '商家信息',
      },
      {
        prop: 'waiterName',
        label: '中文部客服',
        width: 120,
        handle: data => {
          return data || '-'
        },
      },
      {
        prop: 'handleUserName',
        label: '处理人',
        width: 100,
        handle: data => {
          return data || '-'
        },
      },
      {
        prop: 'handleTime',
        label: '处理时间',
        width: 170,
        handle: data => {
          return data || '-'
        },
      },
      {
        prop: 'handleResult',
        label: '处理结果',
        ellipsis: true,
        line: 3,
        tooltipPlacement: 'left',
        handle: data => {
          return data || '-'
        },
      },
    ]
  }
  return list
})

const tableData = ref([])
const pageNum = ref(1)
const pageSize = ref(20)
const total = ref(0)
const tableLoading = ref(false)

const contactList = ref([])

const queryParams = ref({
  handleStatus: '',
  chineseWaiterIds: [],
  object: '',
  memberCode: '',
  handleStatus: activeName.value,
})

function resetQuery() {
  queryParams.value = {
    handleStatus: '',
    object: '',
    chineseWaiterIds: [],
    memberCode: '',
    handleStatus: activeName.value,
  }
  init()
}

function init() {
  handleQuery()
  getContactList()
}

function getContactList() {
  getContactSelect()
    .then(res => {
      contactList.value = res.data || []
    })
    .catch(err => {
      console.log(err)
    })
}

function handleQuery() {
  tableLoading.value = true
  getRoastList({
    pageNum: pageNum.value,
    pageSize: pageSize.value,
    roastType: 1,
    ...queryParams.value,
  })
    .then(res => {
      tableData.value = res.data.rows || []
      total.value = res.data.total || 0
      emits('getNum')
    })
    .finally(() => {
      tableLoading.value = false
    })
}
// 分页跳转
function pageChange(page) {
  pageNum.value = page.currentPage
  pageSize.value = page.pageSize
  init()
}

init()

//弹窗
const formRef = ref(null)
const dialogVisible = ref(false)
const dialogDisabled = ref(false)
const rules = {
  handleResult: [{ required: true, message: '请输入处理结果', trigger: 'blur' }],
}
const dialogData = ref({
  handleResult: '',
  id: '',
})
function handleOpenDialog(type, row) {
  dialogData.value.id = row.id
  if (type == 'detail') {
    dialogDisabled.value = true
    dialogData.value.handleResult = row.handleResult
  }
  dialogVisible.value = true
}
function handleCloseDialog() {
  formRef.value.resetFields()
  dialogData.value = {
    handleResult: '',
    id: '',
  }
  dialogDisabled.value = false
  dialogVisible.value = false
}

function handleConfirm() {
  formRef.value.validate(valid => {
    if (valid) {
      handleRoast(dialogData.value)
        .then(res => {
          handleCloseDialog()
          ElMessage.success('处理成功')
          init()
        })
        .catch(err => {
          console.log(err)
        })
    }
  })
}
</script>

<style scoped lang="scss"></style>
