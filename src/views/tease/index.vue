<template>
  <div class="task-container">
    <el-radio-group v-model="curTab" size="large" @change="handleTabChange()">
      <el-radio-button :value="1">订单吐槽 ( {{ teaseNumbers.videoCount || 0 }} )</el-radio-button>
      <el-radio-button :value="2">系统吐槽 ( {{ teaseNumbers.systemCount || 0 }} )</el-radio-button>
    </el-radio-group>

    <OrderTease v-if="curTab === 1" :waitNum="teaseNumbers.videoUnHandleCount" @getNum="getStatics()" />
    <SystemTease v-if="curTab === 2" :waitNum="teaseNumbers.systemUnHandleCount" @getNum="getStatics()" />
  </div>
</template>
<script setup>
import OrderTease from '@/views/tease/components/orderTease.vue'
import SystemTease from '@/views/tease/components/systemTease.vue'
import { checkPermi } from '@/utils/permission'
import { getRoastStatistics } from '@/api/roast/roast.js'

const teaseNumbers = ref({
  videoCount: 0,
  systemCount: 0,
  videoUnHandleCount: 0,
  systemUnHandleCount: 0,
})

const curTab = ref(1)

function getStatics() {
  getRoastStatistics().then(res => {
    if (res.data) {
      teaseNumbers.value = res.data
    }
  })
}

function handleTabChange() {
  // getStatics()
}
</script>

<style lang="scss" scoped>
.task-container {
  padding: 20px;
}
</style>
