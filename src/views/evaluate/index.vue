<template>
  <div class="clip-table">
    <div class="search-box">
      <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="88px" @submit.prevent>
        <el-form-item label="搜索">
          <el-input
            v-model="queryParams.keyword"
            placeholder="支持视频编码/产品信息等进行搜索"
            clearable
            style="width: 330px"
          />
        </el-form-item>
        <el-form-item label="拍摄模特">
          <el-select
            :reserve-keyword="false"
            v-model="queryParams.shootModelIds"
            placeholder="请选择"
            multiple
            filterable
            collapse-tags
            collapse-tags-tooltip
            clearable
            style="width: 190px"
          >
            <el-option
              v-for="item in selectShootModelList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="模特类型">
          <el-select v-model="queryParams.modelType" placeholder="请选择" clearable style="width: 190px">
            <el-option
              v-for="item in biz_model_type"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
            <el-option label="影/素都可以" value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="评分人">
          <el-select
            v-model="queryParams.videoScoreByIds"
            placeholder="请选择"
            multiple
            filterable
            collapse-tags
            collapse-tags-tooltip
            clearable
            style="width: 190px"
          >
            <el-option
              v-for="item in videoScorePersonList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="评星">
          <el-select
            v-model="queryParams.videoScores"
            placeholder="请选择"
            multiple
            collapse-tags
            collapse-tags-tooltip
            clearable
            style="width: 190px"
          >
            <el-option v-for="item in ratingList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button v-btn type="primary" icon="Search" native-type="submit" @click="handleQuery">
            搜索
          </el-button>
          <el-button v-btn icon="Refresh" plain @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-table ref="tableRef" :data="tableData" style="width: 100%" border row-key="id">
      <el-table-column
        prop="productPic"
        label="产品图"
        align="center"
        width="130"
        class-name="product-img-box"
      >
        <template v-slot="{ row }">
          <div class="corner-mark-hint" v-if="row.productPicChange">调</div>
          <div class="flex-start top-tag">
            <el-tag v-if="row.isCare" type="danger" size="small" round>照顾单</el-tag>
          </div>
          <el-image
            style="width: 90px; height: 90px; cursor: pointer"
            :style="{ 'margin-top': row.isCare ? '15px' : '0' }"
            :src="
              row.productPic
                ? $picUrl +
                  row.productPic +
                  '?imageMogr2/format/webp/interlace/1/quality/100/interlace/0/thumbnail/200x'
                : ''
            "
            fit="scale-down"
            preview-teleported
            @click="() => row.productPic && showViewer([$picUrl + row.productPic])"
          >
            <template #error>
              <img :src="$picUrl + 'static/assets/no-img.png'" alt="" style="width: 100%; height: 100%" />
            </template>
          </el-image>
        </template>
      </el-table-column>
      <el-table-column prop="msg" label="产品信息" minWidth="320">
        <template v-slot="{ row }">
          <div class="product-info-box">
            <div class="flex-between">
              <span>视频编码：{{ row.videoCode }}</span>
              <div>
                <el-button
                  class="btn"
                  size="small"
                  v-btn
                  plain
                  @click="() => ProductMoreInfoRef.open(row.videoId)"
                >
                  更多
                </el-button>
              </div>
            </div>
            <div class="one-ell">中文名称：{{ row.productChinese }}</div>
            <div class="one-ell" style="word-break: break-all">英文名称：{{ row.productEnglish }}</div>
            <div class="one-ell productLink">
              产品链接：
              <el-link :underline="false" target="_blank" type="primary" :href="row.productLink">
                {{ row.productLink }}
              </el-link>
            </div>
            <biz-model-platform :value="row.platform" />
            <biz-model-type :value="row.modelType" />
            <biz-nation :value="row.shootingCountry" />
            <template v-for="op in videoFormatOptions" :key="op.value">
              <el-tag class="tag" v-if="op.value == row.videoFormat" type="warning" size="small" round>
                {{ op.label }}
              </el-tag>
            </template>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="picCount" label="照片数量" align="center" width="110">
        <template v-slot="{ row }">
          <div>{{ handleSelectiveAssembly(row.picCount) }}</div>
          <el-button
            v-btn
            v-if="row.referencePic && row.referencePic?.length"
            link
            type="primary"
            @click="showViewer(row.referencePic)"
          >
            查看
          </el-button>
          <el-tag type="info" size="small" v-if="handleOrderVideoRefundList(row.orderVideoRefundList, 1)">
            已退款{{ row.refundPicCount > 0 ? `(${row.refundPicCount}张)` : '' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="model2" label="拍摄模特" align="center" width="130" class-name="shoot-model-box">
        <template v-slot="{ row }">
          <div style="padding: 5px 0">
            <div
              v-if="row.shootModel"
              @mouseenter="handleMouseEnter($event, row.shootModel.id)"
              @mouseleave="handleMouseLeave($event, row.shootModel.id)"
            >
              <div
                class="hint-box"
                style="pointer-events: none"
                v-if="row.intentionModel?.account && row.intentionModel?.account != row.shootModel?.account"
              >
                <div class="exchange">
                  <span>换</span>
                </div>
              </div>
              <el-avatar
                class="model-avatar"
                icon="UserFilled"
                :src="$picUrl + row.shootModel.modelPic + '!1x1compress'"
              />
              <div>{{ row.shootModel.name }}</div>
              <div>{{ row.shootModel?.account ? `(ID:${row.shootModel.account})` : '-' }}</div>
              <biz-model-type :value="row.shootModel.type" />
            </div>
            <div v-else>-</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="videoUrl" label="评价来源" align="center" width="300">
        <template v-slot="{ row }">
          <div v-if="row.videoUrl || row.picUrl">
            <div class="one-ell productLink" v-if="row.videoUrl">
              视频:
              <el-link v-btn :underline="false" target="_blank" type="primary" :href="row.videoUrl">
                {{ row.videoUrl }}
              </el-link>
            </div>
            <div v-if="row.picUrl" class="one-ell productLink">
              照片:
              <el-link v-btn :underline="false" target="_blank" type="primary" :href="row.picUrl">
                {{ row.picUrl }}
              </el-link>
            </div>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="videoScore" label="评星" align="center" width="110">
        <template v-slot="{ row }">
          <div>
            <div>{{ row.videoScore }}星</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="videoScoreContent" label="评价内容" align="center" width="250">
        <template v-slot="{ row }">
          <div>
            <div
              class="more-ell"
              v-has-ellipsis:videoScoreContentMore="row"
              :key="Math.random()"
              style="--l: 2"
            >
              {{ row.videoScoreContent || '-' }}
            </div>
            <el-button v-btn v-if="row.videoScoreContentMore" link type="primary" @click="doShowMore(row)">
              更多
            </el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="videoScoreBy" label="评价人" align="center" width="110">
        <template v-slot="{ row }">
          <div>
            <div>{{ row.videoScoreBy || '-' }}</div>
            <div>{{ row.videoScoreTime }}</div>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <div class="flex-end" style="margin: 10px 0 60px">
      <PaginationFloatBar :current-page="pageNum" :page-size="pageSize" @update:current-page="handlePageChange" :total="total" />

      <!-- <el-pagination
        background
        @size-change="pageChange({ pageNum: 1, pageSize: $event })"
        @current-change="pageChange({ pageNum: $event, pageSize })"
        :current-page="pageNum"
        :page-size="pageSize"
        :page-sizes="[10, 20, 30, 50]"
        layout="total, prev, pager, next, jumper"
        :total="total"
      /> -->
    </div>
    <ModelInfoPopover ref="ModelInfoPopoverRef" />
    <ProductMoreInfo ref="ProductMoreInfoRef" />
  </div>
</template>

<script setup>
import { ElMessageBox } from 'element-plus'
import ProductMoreInfo from '@/views/order/components/dialog/productMoreInfo.vue'
import ModelInfoPopover from '@/views/model/ModelInfoPopover.vue'
import {
  videoScoreList,
  videoScoreListSelectEvaluatePerson,
  videoScoreListSelectShootModel,
} from '@/api/clip'
import { picCountOptions, orderRefundStatusMap, videoFormatOptions } from '@/views/order/list/data.js'
import { useViewer } from '@/hooks/useViewer'
const router = useRouter()
const { proxy } = getCurrentInstance()
const { biz_model_type } = proxy.useDict('biz_model_type')

function routerNewWindow(path) {
  const { href } = router.resolve({ path })
  window.open(href, '_blank')
}

const { showViewer } = useViewer()
const ModelInfoPopoverRef = ref()
const ProductMoreInfoRef = ref()
const tableRef = ref()
const pageNum = ref(1)
const pageSize = 20
const total = ref(0)
const loading = ref(false)
const selectShootModelList = ref([])
const videoScorePersonList = ref([])
const queryParams = ref({
  keyword: '',
  modelType: '',
  shootModelIds: [],
  videoScoreByIds: [],
  videoScores: [],
})

const ratingList = [
  { label: '1星', value: 1 },
  { label: '1.5星', value: 1.5 },
  { label: '2星', value: 2 },
  { label: '2.5星', value: 2.5 },
  { label: '3星', value: 3 },
  { label: '3.5星', value: 3.5 },
  { label: '4星', value: 4 },
  { label: '4.5星', value: 4.5 },
  { label: '5星', value: 5 },
  { label: '5.5星', value: 5.5 },
  { label: '6星', value: 6 },
  { label: '6.5星', value: 6.5 },
  { label: '7星', value: 7 },
  { label: '7.5星', value: 7.5 },
  { label: '8星', value: 8 },
  { label: '8.5星', value: 8.5 },
  { label: '9星', value: 9 },
  { label: '9.5星', value: 9.5 },
  { label: '10星', value: 10 },
]

const tableData = ref([])

function handleMouseEnter(e, id) {
  ModelInfoPopoverRef.value?.open(e.target, id)
}
function handleMouseLeave(e, id) {
  ModelInfoPopoverRef.value?.close()
}

function pageChange(params) {
  pageNum.value = params.pageNum
  // pageSize = params.pageSize
  handleQuery()
}
function handlePageChange(num) {
  pageNum.value = num
  handleQuery()
}

function handleSelectiveAssembly(val) {
  let str = picCountOptions.find(item => item.value == val)
  return str ? str.label.substring(0, 2) : '-'
}

function handleOrderVideoRefundList(list, type) {
  let s = false
  if (type == '2' && list && list.length > 0) {
    list.find(item => {
      if (item.refundStatus == orderRefundStatusMap['退款成功'] && item.refundType == 1) {
        s = true
      }
    })
  }
  if (type == '1' && list && list.length > 0) {
    list.find(item => {
      if (item.refundStatus == orderRefundStatusMap['退款成功'] && item.refundType == 3) {
        s = true
      }
    })
  }
  return s
}

function resetQuery() {
  queryParams.value = {
    keyword: '',
    modelType: '',
    shootModelIds: [],
    videoScoreByIds: [],
    videoScores: [],
  }
  handleQuery()
}

function handleQuery() {
  initSelectQuery()
  videoScoreList({ ...queryParams.value, pageNum: pageNum.value, pageSize: pageSize }).then(res => {
    tableData.value = res.data.rows || []
    total.value = res.data.total || 0
  })
}

function doShowMore(row) {
  if (row.videoScoreContent) {
    let contentDom = row.videoScoreContent
    let dom = h('div', [
      h(
        'div',
        {
          style: {
            'max-height': '500px',
            'overflow-y': 'auto',
            'margin-bottom': '10px',
            'font-weight': '500',
            'line-break': 'anywhere',
            'white-space': 'pre-wrap',
          },
        },
        contentDom
      ),
    ])

    ElMessageBox.alert(dom, '评价内容	', {
      customStyle: {
        '--el-messagebox-width': '680px',
        'font-weight': '600',
      },
      dangerouslyUseHTMLString: true,
      showConfirmButton: false,
      showCancelButton: false,
      closeOnClickModal: true,
    })
  }
}
function initSelectQuery() {
  videoScoreListSelectEvaluatePerson().then(res => {
    videoScorePersonList.value = res.data
  })
  videoScoreListSelectShootModel().then(res => {
    selectShootModelList.value = res.data.map(item => {
      return {
        label: item.name + '(' + item.account + ')',
        value: item.id,
      }
    })
  })
}

handleQuery()
</script>

<style lang="scss" scoped>
.clip-table {
  padding: 20px 20px 0 20px;
  .productLink {
    position: relative;
    padding-right: 5px;

    :deep(.el-link) {
      display: contents;

      .el-link__inner {
        display: inline;
      }
    }
  }
  .head-box {
    &-left {
      display: flex;
      align-items: center;
    }
    &-right {
      display: flex;
      gap: 0 5px;
    }
  }
  .productLink {
    position: relative;
    padding-right: 5px;

    :deep(.el-link) {
      display: contents;

      .el-link__inner {
        display: inline;
      }
    }
  }
  :deep(.el-table) {
    .link-box {
      position: relative;
      .tag {
        position: absolute;
        top: 0;
        right: 0;
      }
      .el-link {
        display: block;
        .el-link__inner {
          display: block;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
    .el-table__header-wrapper {
      th {
        height: 30px !important;
      }
    }
    th {
      text-align: center;
    }

    .el-button + .el-button {
      margin: 0 3px;
    }
    .el-table__header-wrapper {
      .seltAllbtnDis {
        .cell {
          visibility: hidden;
        }
      }
    }
    .product-info-box {
      .btn {
        padding: 2px 4px;
        height: auto;
        font-size: 12px;
      }
    }
    .product-img-box {
      position: relative;

      .top-tag {
        z-index: 9;
        position: absolute;
        top: 2px;
        left: 1px;

        .el-tag + .el-tag {
          margin-left: 5px;
        }
      }

      .productPic-img {
        width: 100%;
        height: 100%;
        position: relative;

        .productPic-btn {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }
      }
    }
    .shoot-model-box {
      // padding-top: 8px;
      position: relative;
      overflow: hidden;

      .hint-box {
        z-index: 999;
        position: absolute;
        left: -6px;
        top: -2px;
        // overflow: hidden;
        width: 32px;
        height: 25px;

        .exchange {
          background: var(--el-color-danger);
          color: #fff;
          font-size: 13px;
          width: 60px;
          transform: translate(-17px, -3px) scale(0.8) rotateZ(-45deg);
        }
      }
    }
    .task-tag-box {
      position: absolute;
      top: 0;
      right: 0;

      .el-button {
        padding: 2px 8px;
        height: 18px;
        border: 1px solid var(--el-color-info-light-7);
        margin: 0 !important;
      }
    }
    .task-button-box {
      flex-direction: column;
    }
  }
}
.btn-left {
  margin-left: 10px;
}
</style>
