<template>
  <div class="member-box">
    <TitleHead title="基础数据" />
    <MemberBase />

    <TitleHead title="分析数据" style="margin: 10px 0" />
    <MemberTrend />
    <BarData />
    <MemberOrder />
    <MemberLast />
  </div>
</template>

<script setup>
import TitleHead from '@/views/statistics/components/TitleHead.vue'
import MemberBase from '@/views/statistics/member/components/memberBase.vue'
import MemberTrend from '@/views/statistics/member/components/memberTrend.vue'
import BarData from '@/views/statistics/member/components/barData.vue'
import MemberOrder from '@/views/statistics/member/components/memberOrder.vue'
import MemberLast from '@/views/statistics/member/components/memberLast.vue'
</script>

<style lang="scss" scoped>
.member-box {
  padding: 10px 20px;
}
</style>
