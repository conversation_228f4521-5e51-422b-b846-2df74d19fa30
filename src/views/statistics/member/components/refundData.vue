<template>
  <div class="refund-box">
    <div class="flex-between" style="padding-right: 20px">
      <div class="flex-start">
        <TitleContent style="margin: 0 0 0 0" contentTip="选定时间段内，会员订单从不同渠道下单的优惠信息">
          优惠分析
        </TitleContent>
        <span class="time-tip" v-if="updateTime">最后更新时间：{{ updateTime }}</span>
      </div>
      <SelectDateBtn :showBtn="false" @change="handleChangeMathTimes" />
    </div>
    <!-- <ModelEchar :option="option" height="160px" /> -->
    <div class="chart-container">
      <div class="chart-scroll" v-loading="loading">
        <template v-if="data && data.length > 0">
          <div v-for="(item, index) in data" :key="index" class="chart-item">
            <div class="discount-name" style="display: flex">
              <div>{{ item.label }}</div>
              <div style="margin-left: 10px" v-if="!calculateBarWidth1(item.count)">{{ item.count }}单</div>
            </div>
            <div class="bar-container">
              <div class="bar" :style="{ width: calculateBarWidth(item.count) }">
                <div class="order-count" v-if="calculateBarWidth1(item.count)">{{ item.count }}单</div>
              </div>
            </div>
          </div>
        </template>
        <div v-else class="flex-center" style="height: 170px">暂无数据</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import TitleContent from '@/views/statistics/components/TitleContent.vue'
import ModelEchar from '@/views/statistics/components/modelEchar.vue'
import SelectDateBtn from '@/views/statistics/components/SelectDateBtn.vue'
import { getNowMonth } from '@/utils/index.js'
import { getMemberDiscountStatistics } from '@/api/statistics/member.js'

const params = ref({
  beginTime: '',
  endTime: '',
})
const updateTime = ref('')
const loading = ref(false)
const total = ref(0)

const handleChangeMathTimes = (val, type) => {
  if (val && val.length > 0) {
    params.value.beginTime = val[0] + ' 00:00:00'
    params.value.endTime = val[1] + ' 23:59:59'
  } else {
    params.value.beginTime = ''
    params.value.endTime = ''
  }
  getData()
}
const data = ref([])

const calculateBarWidth = value => {
  const maxValue = Math.max(...data.value.map(item => item.count))
  return `${(value / maxValue) * 100}%`
  // return `${(value / total.value) * 100}%`
}
const calculateBarWidth1 = value => {
  const maxValue = Math.max(...data.value.map(item => item.count))
  if ((value / maxValue) * 100 > 10) {
    return true
  } else return false
  // if ((value / total.value) * 100 > 10) {
  //   return true
  // } else return false
}

// const option = {
//   dataZoom: [
//     {
//       type: 'slider',
//       filterMode: 'empty', // 禁止自动填充空白
//       moveOnMouseMove: false, // 禁用鼠标拖拽自动滚动
//       preventDefaultMouseMove: true, // 阻止默认滑动行为
//       yAxisIndex: 0,
//       show: data.length > 4, // 动态控制显示
//       startValue: 0,
//       endValue: Math.min(3, data.length - 1),
//       fillerColor: 'rgba(200,200,200,0.3)',
//       borderColor: 'transparent',
//       handleStyle: {
//         color: '#666',
//         borderWidth: 0,
//       },
//       height: data.length > 4 ? 120 : 0,
//       bottom: 15,
//       width: 0,
//       zoomLock: true,
//       showDetail: false,
//     },
//   ],
//   grid: {
//     left: '3%',
//     top: '10%',
//     right: '6%',
//     bottom: '0%',
//     containLabel: true,
//   },
//   xAxis: {
//     type: 'value',
//     show: false,
//   },
//   yAxis: {
//     type: 'category',
//     inverse: true, // 新增这行实现反向显示
//     data: data.map(item => item.name),
//     axisLine: { show: false },
//     axisTick: { show: false },
//     axisLabel: {
//       formatter: '{value|{value}}', // 使用富文本格式
//       rich: {
//         value: {
//           width: 120, // 固定标签区域宽度
//           align: 'right', // 右对齐
//           padding: [0, 10], // 右侧内边距
//         },
//       },
//     },
//   },
//   series: [
//     {
//       name: '数量',
//       type: 'bar',
//       data: data.map(item => item.value),
//       barWidth: '18',
//       label: {
//         show: true,
//         position: 'right',
//         formatter: '{c}单',
//         offset: [5, 0], // 向右偏移5px
//       },
//       itemStyle: {
//         borderRadius: 10,
//       },
//     },
//   ],
// }

function getData() {
  loading.value = true
  getMemberDiscountStatistics(params.value)
    .then(res => {
      data.value = res.data?.businessOrderPieChartVOS || []
      updateTime.value = res.data?.updateTime || ''
      total.value = res.data?.businessOrderTotal || 0
    })
    .finally(() => {
      loading.value = false
    })
}

getData()
</script>

<style lang="scss" scoped>
.refund-box {
  border: 1px solid #ffffff;
  padding: 15px 0 0 20px;
  box-sizing: border-box;
  border-radius: 10px;
  box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.12);
  .time-tip {
    color: #848484;
    font-size: 12px;
    margin-left: 10px;
  }
}
.chart-container {
  height: 170px;
  overflow-y: auto;
  padding-right: 10px;
  .chart-scroll {
    min-height: 100%;
  }

  .chart-item {
    margin-top: 4px;
  }

  .discount-name {
    font-size: 14px;
    color: #666;
    margin-bottom: 4px;
  }

  .bar-container {
    display: flex;
    align-items: center;
    .bar {
      height: 14px;
      background-color: #409eff;
      border-radius: 12px;
      transition: width 0.5s ease;
      position: relative;
    }
  }

  .order-count {
    position: absolute;
    right: 0; // 调整值以避免遮挡 bar
    top: -23px; // 调整值以控制文本垂直位置
    font-size: 14px;
    color: #666;
    white-space: nowrap;
  }
}
.chart-container::-webkit-scrollbar {
  width: 8px;
}

.chart-container::-webkit-scrollbar-track {
  background: #fff;
  border-radius: 4px;
}

.chart-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.chart-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
