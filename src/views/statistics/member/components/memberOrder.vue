<template>
  <div class="member-order">
    <PieData
      title="会员排单数占比"
      tip="选定时间段内，会员各排单数分类占比分析"
      :updateTime="updateTimeLeft"
      :seriesDate="takeOrderList"
      :total="businessOrderTotal"
      @changeTime="getMemberTakeOrder"
      isLastTime
    />
    <PieData
      title="会员退款单数占比分析"
      tip="选定时间段内，所有退款的单数中七天无理由退会和非七天无理由退会的占比和数量分析"
      :seriesDate="exitOrderList"
      :updateTime="updateTimeRight"
      :total="exitOrderTotal"
      @changeTime="getMemberExitData"
      :itemGap="26"
      top="40%"
    />
  </div>
</template>

<script setup>
import PieData from '@/views/statistics/member/components/pieData.vue'
import { getMemberStatistics, getMemberExitStatistics } from '@/api/statistics/member.js'

const takeOrderList = ref([])
const updateTimeLeft = ref('')
const businessOrderTotal = ref(0)
const updateTimeRight = ref('')
const exitOrderList = ref([])
const exitOrderTotal = ref(0)

function handleList(list) {
  if (list && list.length > 0) {
    return list.map(item => {
      return {
        name: item.label,
        value: item.count,
        ratio: item.ratio,
      }
    })
  } else {
    return []
  }
}

function getMemberTakeOrder(val) {
  getMemberStatistics(val ? val : {}).then(res => {
    updateTimeLeft.value = res.data.updateTime
    businessOrderTotal.value = res.data.businessOrderTotal
    takeOrderList.value = handleList(res.data.businessOrderPieChartVOS)
  })
}

function getMemberExitData(val) {
  getMemberExitStatistics(val ? val : {}).then(res => {
    updateTimeRight.value = res.data.updateTime
    exitOrderTotal.value = res.data.businessOrderTotal
    exitOrderList.value = handleList(res.data.businessOrderPieChartVOS)
  })
}

getMemberTakeOrder()
getMemberExitData()
</script>

<style lang="scss" scoped>
.member-order {
  margin-top: 15px;
  display: grid;
  grid-template-columns: repeat(2, minmax(500px, 1fr));
  gap: 0 15px;
}
</style>
