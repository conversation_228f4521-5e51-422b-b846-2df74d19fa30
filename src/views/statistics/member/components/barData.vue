<template>
  <div class="bar-box">
    <div class="flex-between">
      <div class="flex-start">
        <TitleContent style="margin: 0" contentTip="不同时间状态下，新会员和老会员的排单数量，和当月的总数">
          会员排单数统计
        </TitleContent>
        <span class="time-tip">最后更新时间：{{ updateTime }}</span>
      </div>
      <SelectDateBtn :showDate="false" @change="handleChangeMathTimes" />
    </div>
    <ModelEchar :option="option" />
  </div>
</template>

<script setup>
import TitleContent from '@/views/statistics/components/TitleContent.vue'
import ModelEchar from '@/views/statistics/components/modelEchar.vue'
import SelectDateBtn from '@/views/statistics/components/SelectDateBtn.vue'
import { getMemberTypeOrderStatistics } from '@/api/statistics/member.js'
import { dayjs } from 'element-plus'

const emit = defineEmits(['changeTime'])
const updateTime = ref('')

const params = ref({
  beginTime: '',
  endTime: '',
})
const curType = ref('')
const handleChangeMathTimes = (val,type) => {
  params.value.beginTime = val[0] + ' 00:00:00'
  params.value.endTime = val[1] + ' 23:59:59'
  curType.value = type
  init()
}

const commonItemStyle = {
  borderRadius: 4,
  borderWidth: 1,
  borderColor: 'rgba(255,255,255,0.3)',
}

const option = ref({
  //   graphic: {
  //     type: 'text',
  //     invisible: false,
  //     left: 'center',
  //     top: 'middle',
  //     style: {
  //       text: '暂无数据',
  //       fontSize: 16,
  //       fill: '#909399',
  //     },
  //   },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'none',
    },
    formatter: function (params) {
      let xAxisValue = params[0].axisValue
      // let xAxisValue = params[0].data.day[params[0].dataIndex] || params[0].axisValue
      // let total = params.reduce((sum, item) => sum + item.value, 0)
      let total = params[0].data.total || 0
      return (
        `<div style="text-align: center">${xAxisValue}<br/>总计: ${total}<br/></div>` +
        params
          .map(
            item => `${item.seriesName}: ${item.value == null || item.value == undefined ? 0 : item.value}`
          )
          .join('<br/>')
      )
    },
  },
  grid: {
    left: '3%',
    right: '10%',
    bottom: '7%',
    containLabel: true,
  },
  legend: {
    orient: 'horizontal',
    selectedMode: false,
    right: '20%',
    icon: 'circle',
    itemWidth: 12,
    itemHeight: 12,
    itemGap: 30,
    textStyle: {
      color: '#666',
    },
    data: ['新会员', '老会员'],
  },
  xAxis: [
    {
      type: 'category',
      name: '单位：日',
      axisLabel: {
        fontSize: 12,
        interval: 0,
      },
      nameGap: 10,
      nameTextStyle: {
        padding: [7, 0, 0, 0],
        verticalAlign: 'top',
      },
      axisTick: {
        show: false,
      },
      // prettier-ignore
      data: [],
    },
  ],
  yAxis: [
    {
      type: 'value',
      name: '单位：单',
      position: 'left',
      alignTicks: true,
      splitLine: {
        show: false,
      },
      nameTextStyle: {
        padding: [0, 0, 0, 30],
      },
      axisLine: {
        show: true,
      },
    },
  ],
  series: [
    {
      name: '新会员',
      type: 'bar',
      stack: 'stack1',
      data: [],
      itemStyle: commonItemStyle,
      barWidth: 40,
    },
    {
      name: '老会员',
      type: 'bar',
      stack: 'stack1',
      data: [],
      itemStyle: commonItemStyle,
      barWidth: 40,
    },
  ],
})

const handleData = (list, isDay = false) => {
  if (list && list.length > 0) {
    return list.map(item => item.recordTime)
    // if (isDay) {
    //   return list.map(item => item.recordTime)
    // } else {
    //   return list.map(item => item.recordTime.split('.')[1])
    // }
  } else {
    return []
  }
}
function init() {
  getMemberTypeOrderStatistics(params.value).then(res => {
    option.value.xAxis[0].name = curType.value == '3' ? '单位：月' : '单位：日'
    updateTime.value = res.data.updateTime
    option.value.xAxis[0].data = handleData(res.data.memberTypeOrderCountVOS)
    option.value.series[0].data = res.data.memberTypeOrderCountVOS?.map(item => {
      return {
        value: item.newMemberOrderCount === 0 ? null : item.newMemberOrderCount,
        total: item.totalOrderCount,
        // day: handleData(res.data, true),
      }
    })
    option.value.series[1].data = res.data.memberTypeOrderCountVOS?.map(item => {
      return {
        value: item.oldMemberOrderCount === 0 ? null : item.oldMemberOrderCount,
      }
    })
  })
}
</script>

<style lang="scss" scoped>
.bar-box {
  margin-top: 15px;
  border: 1px solid #ffffff;
  padding: 12px 20px;
  box-sizing: border-box;
  border-radius: 10px;
  box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.12);
  .time-tip {
    color: #848484;
    font-size: 12px;
    margin-left: 10px;
  }
}
</style>
