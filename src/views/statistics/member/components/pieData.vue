<template>
  <div class="pie-box">
    <div class="flex-between">
      <div class="flex-start">
        <TitleContent style="margin: 0 0 0 0" :contentTip="tip">
          {{ title }}
        </TitleContent>
        <span class="time-tip" v-if="updateTime">
          {{ isLastTime ? '最后更新时间：' : '实时更新数据：' }}{{ updateTime }}
        </span>
      </div>
      <SelectDateBtn :showBtn="false" @change="handleChangeMathTimes">
        <template #picker v-if="pickerLabel">
          <div class="time-tip">{{ pickerLabel }}</div>
        </template>
      </SelectDateBtn>
    </div>
    <div v-if="showGrade" class="flex-end grade-box">
      <el-input-number
        v-model="params.beginScore"
        controls-position="right"
        :controls="false"
        :precision="1"
        :min="0"
        :max="10"
        @keydown="channelInputLimit"
        @change="handleStartTime"
      />
      <span>分 -</span>
      <el-input-number
        v-model="params.endScore"
        controls-position="right"
        :controls="false"
        :precision="1"
        :min="params.beginScore || 0"
        :max="10"
        @keydown="channelInputLimit"
      />
      <span>分</span>
      <el-button v-btn @click="handleQuery">搜索</el-button>
      <el-button v-btn @click="handleReset">重置</el-button>
    </div>
    <ModelEchar ref="modelEcharRef" height="160px" :option="echartsData" />
  </div>
</template>

<script setup>
import TitleContent from '@/views/statistics/components/TitleContent.vue'
import ModelEchar from '@/views/statistics/components/modelEchar.vue'
import SelectDateBtn from '@/views/statistics/components/SelectDateBtn.vue'
import { getNowMonth } from '@/utils/index.js'

const props = defineProps({
  title: {
    type: String,
    default: '',
  },
  tip: {
    type: String,
    default: '',
  },
  seriesDate: {
    type: Array,
    default: () => [],
  },
  itemGap: {
    type: Number,
    default: 12,
  },
  top: {
    type: String,
    default: 'center',
  },
  updateTime: {
    type: String,
    default: '',
  },
  total: {
    type: Number,
    default: 0,
  },
  isLastTime: {
    type: Boolean,
    default: false,
  },
  pickerLabel: {
    type: String,
    default: '',
  },
  showGrade: {
    type: Boolean,
    default: false,
  },
  showTotal: {
    type: Boolean,
    default: true,
  },
  siteOptions: {
    type: Object,
    default: () => ({}),
  },
  legendArray: {
    type: Boolean,
    default: false,
  },
})

const params = ref({
  beginTime: '',
  endTime: '',
  beginScore: null,
  endScore: null,
})

const modelEcharRef = ref(null)
watch(
  () => props.seriesDate,
  () => {
    echartsData.value.series[0].data = props.seriesDate
    if (props.legendArray) {
      initDoubleLegend()
    }
    // modelEcharRef?.value.draw()
  }
)
const time = ref(getNowMonth())
const emit = defineEmits(['changeTime'])
const handleChangeTime = () => {
  emit('changeTime', params.value)
}

const handleChangeMathTimes = (val, type) => {
  if (val && val.length > 0) {
    params.value.beginTime = val[0] + ' 00:00:00'
    params.value.endTime = val[1] + ' 23:59:59'
  } else {
    params.value.beginTime = ''
    params.value.endTime = ''
  }
  handleChangeTime()
}

const echartsData = ref({
  tooltip: {
    trigger: 'item',
  },
  legend: {
    type: 'scroll',
    selectedMode: false,
    formatter: name => {
      const dataItem = echartsData.value.series[0].data.find(item => item.name === name)
      return `{name|${name}}{value|${dataItem.value || 0}个}{percent|${dataItem.ratio || 0}%}`
    },
    orient: 'vertical',
    right: props.siteOptions.legendRight || '15%',
    top: props.top,
    icon: 'circle',
    itemWidth: 12,
    itemHeight: 12,
    itemGap: props.itemGap,
    padding: [0, 0, 0, 0],
    textStyle: {
      color: '#666', // 基础文字颜色
      rich: {
        name: {
          width: 95,
          padding: [0, 5, 0, 0],
        },
        value: {
          width: 70,
          align: 'left',
          padding: [0, 5, 0, 0],
        },
        percent: {
          width: 60,
          align: 'left',
        },
      },
    },
  },
  series: [
    {
      type: 'pie',
      radius: ['40%', '70%'],
      center: props.siteOptions.seriesCenter || ['15%', '55%'],
      avoidLabelOverlap: false,
      label: {
        show: props.showTotal,
        position: 'center',
        formatter: () => {
          // const total = props.seriesDate.reduce((sum, item) => sum + (item.value || 0), 0)
          return `{label|总数}\n{total|${props.total}}`
        },
        rich: {
          total: {
            fontSize: 16,
            padding: [5, 0, 0, 0],
          },
        },
        fontSize: 16,
        color: '#333',
      },
      labelLine: {
        show: false,
      },
      data: props.seriesDate,
    },
  ],
})

function handleStartTime() {
  if (params.value.endScore < params.value.beginScore) {
    params.value.endScore = params.value.beginScore
  }
}
const handleQuery = () => {
  if (
    (params.value.beginScore === '' || params.value.beginScore == null) &&
    (params.value.endScore === '' || params.value.endScore == null)
  )
    return
  if (
    (params.value.beginScore === '' || params.value.beginScore == null) &&
    (params.value.endScore || params.value.endScore === 0)
  ) {
    params.value.beginScore = params.value.endScore
  }
  if (
    (params.value.endScore === '' || params.value.endScore == null) &&
    (params.value.beginScore || params.value.beginScore === 0)
  ) {
    params.value.endScore = params.value.beginScore
  }
  handleChangeTime()
}
const handleReset = () => {
  params.value.endScore = null
  params.value.beginScore = null
  handleChangeTime()
}

const channelInputLimit = e => {
  const key = e.key
  if (key === 'Enter') e.preventDefault()
  const notAllowList = ['e', '+', '-']
  if (notAllowList.includes(key)) {
    e.returnValue = false
    return false
  }
  return true
}

const initDoubleLegend = () => {
  const leftData = props.seriesDate.slice(0, 4)
  const rightData = props.seriesDate.slice(4)
  const leftNames = leftData.map(item => item.name)
  const rightNames = rightData.map(item => item.name)
  echartsData.value.legend = [
    {
      data: leftNames,
      selectedMode: false,
      formatter: name => {
        const dataItem = leftData.find(item => item.name === name)
        return `{name|${name}}{value|${dataItem?.value || 0}个}{percent|${dataItem?.ratio || 0}%}`
      },
      orient: 'vertical',
      right: props.siteOptions.legendRight || '38%',
      top: props.top,
      icon: 'circle',
      itemWidth: 12,
      itemHeight: 12,
      itemGap: props.itemGap,
      padding: [0, 0, 0, 0],
      textStyle: {
        color: '#666',
        rich: {
          name: { width: 95, padding: [0, 5, 0, 0] },
          value: { width: 70, align: 'left', padding: [0, 5, 0, 0] },
          percent: { width: 60, align: 'left' },
        },
      },
    },
    {
      data: rightNames,
      selectedMode: false,
      formatter: name => {
        const dataItem = rightData.find(item => item.name === name)
        return `{name|${name}}{value|${dataItem?.value || 0}个}{percent|${dataItem?.ratio || 0}%}`
      },
      orient: 'vertical',
      right: props.siteOptions.legendRight || '5%',
      top: props.top,
      icon: 'circle',
      itemWidth: 12,
      itemHeight: 12,
      itemGap: props.itemGap,
      padding: [0, 0, 0, 0],
      textStyle: {
        color: '#666',
        rich: {
          name: { width: 95, padding: [0, 5, 0, 0] },
          value: { width: 70, align: 'left', padding: [0, 5, 0, 0] },
          percent: { width: 60, align: 'left' },
        },
      },
    },
  ]
}
</script>

<style lang="scss" scoped>
:deep(.el-input-number) {
  .el-input__inner {
    text-align: left;
  }
}
.pie-box {
  border: 1px solid #ffffff;
  padding: 15px 20px;
  box-sizing: border-box;
  border-radius: 10px;
  box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.12);
  .time-tip {
    color: #848484;
    font-size: 12px;
    margin-left: 10px;
  }
  .grade-box {
    margin-top: 10px;
    .el-input-number {
      width: 60px;
    }
    span {
      margin: 0 10px;
    }
  }
}
</style>
