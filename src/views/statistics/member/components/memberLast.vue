<template>
  <div class="last-box">
    <PieData
      title="会员来源分析"
      tip="选定时间段内，所有的会员订单的来源渠道"
      :seriesDate="menberSourceList"
      :updateTime="updateTime"
      :total="menberSourceTotal"
      @changeTime="getMemberSourceData"
      :itemGap="26"
      top="28%"
    />
    <RefundData />
  </div>
</template>

<script setup>
import PieData from '@/views/statistics/member/components/pieData.vue'
import RefundData from '@/views/statistics/member/components/refundData.vue'
import { getMemberSourceStatistics } from '@/api/statistics/member.js'

const menberSourceList = ref([])
const menberSourceTotal = ref(0)
const updateTime = ref(0)

function handleList(list) {
  if (list && list.length > 0) {
    return list.map(item => {
      return {
        name: item.label,
        value: item.count,
        ratio: item.ratio,
      }
    })
  } else {
    return []
  }
}

function getMemberSourceData(val) {
  getMemberSourceStatistics(val ? val : {}).then(res => {
    updateTime.value = res.data.updateTime
    menberSourceTotal.value = res.data.businessOrderTotal
    menberSourceList.value = handleList(res.data.businessOrderPieChartVOS)
  })
}

getMemberSourceData()
</script>

<style lang="scss" scoped>
.last-box {
  margin-top: 15px;
  display: grid;
  //   grid-template-columns: repeat(2, 1fr);
  grid-template-columns: 2fr 3fr;
  gap: 0 15px;
}
</style>
