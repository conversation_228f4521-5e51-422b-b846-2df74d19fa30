<template>
  <div class="base-head">
    <div class="base-head-item">
      <div class="head-title">
        服务中会员数
        <span class="time-tip">实时更新数据：{{ baseRechargeData.updateTime }}</span>
      </div>
      <div class="head-value">
        {{ baseRechargeData.memberCount || 0 }}
        <span class="head-value-ic">个</span>
      </div>
      <el-divider class="head-divider" />
      <div class="head-text">
        <span>昨日新会员数{{ baseRechargeData.yesterdayMemberCount || 0 }}</span>
        <span class="head-text-second">
          昨日续费会员数{{ baseRechargeData.yesterdayRenewMemberCount || 0 }}
        </span>
      </div>
      <div class="head-text" style="margin-top: 5px">
        <span>昨日退会数{{ baseRechargeData.yesterdayExitMemberCount || 0 }}</span>
      </div>
    </div>
    <div class="base-head-item">
      <div class="head-title">
        已到期会员数
        <span class="time-tip">实时更新数据：{{ baseRechargeData.updateTime }}</span>
      </div>
      <div class="head-value">
        {{ baseExporeData.overMemberCount || 0 }}
        <span class="head-value-ic">个</span>
      </div>
      <el-divider class="head-divider" />
      <div class="head-text">
        <span>30天内到期{{ baseExporeData.noExpireMemberCount || 0 }}</span>
        <span class="head-text-second">昨日到期{{ baseExporeData.yesterdayExpireMemberCount || 0 }}</span>
      </div>
    </div>
    <div class="base-head-item">
      <div class="flex-start">
        <TitleContent
          style="margin: 0"
          contentTip="拥有的会员数下，首次充值会员、首次续费会员和多次续费会员分别的占比和数量"
        >
          会员类型
        </TitleContent>
        <span class="time-tip" v-if="memberTypeTime">实时更新数据 {{ memberTypeTime }}</span>
      </div>
      <ModelEchar height="100px" :option="echartsData" />
    </div>
  </div>
</template>

<script setup>
import {
  getMemberTypeStatistics,
  getMemberBaseRechargeStatistics,
  getMemberBaseExporeStatistics,
} from '@/api/statistics/member'
import TitleContent from '@/views/statistics/components/TitleContent.vue'
import ModelEchar from '@/views/statistics/components/modelEchar.vue'

const memberTypeTime = ref('')

const echartsData = ref({
  tooltip: {
    trigger: 'item',
  },
  legend: {
    type: 'scroll',
    selectedMode: false,
    formatter: name => {
      const dataItem = echartsData.value.series[0].data.find(item => item.name === name)
      return `{name|${name}}{value|${dataItem.value || 0}个}{percent|${dataItem.ratio || 0}%}`
    },
    orient: 'vertical',
    right: '21%',
    top: '25%',
    icon: 'circle',
    itemWidth: 12,
    itemHeight: 12,
    itemGap: 16,
    padding: [0, 0, 0, 0],
    textStyle: {
      color: '#666', // 基础文字颜色
      rich: {
        name: {
          width: 110,
          padding: [0, 5, 0, 0],
        },
        value: {
          width: 70,
          align: 'left',
          padding: [0, 5, 0, 0],
        },
        percent: {
          width: 60,
          align: 'left',
        },
      },
    },
  },
  series: [
    {
      type: 'pie',
      radius: ['50%', '85%'],
      center: ['20%', '55%'],
      avoidLabelOverlap: false,
      label: {
        show: false,
        position: 'center',
      },
      labelLine: {
        show: false,
      },
      data: [],
    },
  ],
})

const getOptionsData = () => {
  getMemberTypeStatistics().then(res => {
    memberTypeTime.value = res.data.updateTime
    if (res.data.businessOrderPieChartVOS && res.data.businessOrderPieChartVOS.length > 0) {
      echartsData.value.series[0].data = res.data.businessOrderPieChartVOS.map(item => {
        return {
          name: item.label,
          value: item.count,
          ratio: item.ratio,
        }
      })
    }
  })
}

const baseRechargeData = ref({})
const baseExporeData = ref({})
const getBaseRecharge = () => {
  getMemberBaseRechargeStatistics().then(res => {
    baseRechargeData.value = res.data
  })
}
const getBaseExpore = () => {
  getMemberBaseExporeStatistics().then(res => {
    baseExporeData.value = res.data
  })
}

getOptionsData()
getBaseRecharge()
getBaseExpore()
</script>

<style lang="scss" scoped>
.base-head {
  margin-top: 10px;
  display: grid;
  grid-template-columns: 2fr 2fr 3fr;
  gap: 0 20px;
  .time-tip {
    color: #848484;
    font-size: 12px;
    margin-left: 10px;
  }
  &-item {
    border: 1px solid #ffffff;
    padding: 15px 20px;
    box-sizing: border-box;
    border-radius: 10px;
    box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.12);
  }
  .head-title {
    margin-top: 7px;
    color: #0000006d;
    font-size: 14px;
  }
  .head-value {
    margin-top: 8px;
    font-size: 30px;
    color: #000000d8;
    &-ic {
      font-size: 14px;
      margin-left: 5px;
    }
  }
  .head-divider {
    margin: 5px 0 8px 0;
    border-top-color: #0000006d;
  }
  .head-text {
    font-size: 13px;
    color: #000000a5;
    &-second {
      margin-left: 25px;
    }
  }
}
</style>
