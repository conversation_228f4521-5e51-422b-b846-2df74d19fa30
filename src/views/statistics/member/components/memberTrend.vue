<template>
  <div class="trend-box">
    <div class="left-head flex-between">
      <div class="flex-start">
        <TitleContent style="margin: 0" contentTip="不同时间状态下到期会员和续费/新会员的数量趋势">
          会员趋势
        </TitleContent>
        <span class="time-tip">最后更新时间：{{ updateTime }}</span>
      </div>

      <SelectDateBtn :showDate="false" @change="handleChangeMathTimes" />
    </div>
    <ModelEchar style="margin-top: 10px" :option="option" />
  </div>
</template>

<script setup>
import SelectDateBtn from '@/views/statistics/components/SelectDateBtn.vue'
import TitleContent from '../../components/TitleContent.vue'
import ModelEchar from '@/views/statistics/components/modelEchar.vue'
import { getMemberTrendStatistics } from '@/api/statistics/member.js'
import { dayjs } from 'element-plus'

const updateTime = ref('')
const curType = ref('')

const params = ref({
  beginTime: '',
  endTime: '',
})
const handleChangeMathTimes = (val, type) => {
  params.value.beginTime = val[0] + ' 00:00:00'
  params.value.endTime = val[1] + ' 23:59:59'
  curType.value = type
  init()
}

const colors = ['#D7D7D7', '#169BD5']
const option = ref({
  color: colors,
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'none',
    },
    formatter: function (params) {
      let xAxisValue = params[0].axisValue
      let value1 = params[0].value
      let value2 = params[1].value
      //   let sum = (value1 * 100 + value2 * 100) / 100
      return `
          <div style="text-align: center">
              <p>${xAxisValue}</p>
              <p>${params[0].seriesName}: ${value1}</p>
              <p>${params[1].seriesName}: ${value2}</p>
          </div>
         `
    },
  },
  grid: {
    left: '6%',
    right: '10%',
    bottom: '13%',
    containLabel: true,
  },
  legend: {
    orient: 'horizontal',
    selectedMode: false,
    top: 0,
    right: 200,
    icon: 'circle',
    itemWidth: 8,
    itemHeight: 8,
    itemGap: 12,
    textStyle: {
      color: '#333',
    },
    data: ['到期', '续费/新会员'],
  },
  xAxis: [
    {
      type: 'category',
      name: '单位：日',
      axisLabel: {
        fontSize: 12,
        interval: 0,
      },
      nameGap: 10,
      nameTextStyle: {
        padding: [7, 0, 0, 0],
        verticalAlign: 'top',
      },
      axisTick: {
        show: false,
      },
      // prettier-ignore
      data: [],
    },
  ],
  yAxis: [
    {
      type: 'value',
      name: '单位：个',
      position: 'left',
      nameGap: 25,
      axisLabel: {
        margin: 30,
      },
      nameTextStyle: {
        padding: [0, 0, 0, -105],
      },
      alignTicks: true,
      splitLine: {
        show: false,
      },
      axisLine: {
        show: true,
      },
    },
  ],
  series: [
    {
      name: '到期',
      type: 'line',
      data: [],
    },
    {
      name: '续费/新会员',
      type: 'line',
      data: [],
    },
  ],
})

const init = () => {
  getMemberTrendStatistics(params.value).then(res => {
    option.value.xAxis[0].name = curType.value == '3' ? '单位：月' : '单位：日'
    updateTime.value = res.data.updateTime
    option.value.xAxis[0].data = res.data.dateArray || []
    option.value.series[0].data = res.data.expireMemberCountArray || []
    option.value.series[1].data = res.data.rechargeMemberCountArray || []
  })
}
</script>

<style lang="scss" scoped>
.trend-box {
  border: 1px solid #ffffff;
  padding: 15px 20px;
  box-sizing: border-box;
  border-radius: 10px;
  box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.12);
  .time-tip {
    color: #848484;
    font-size: 12px;
    margin-left: 10px;
  }
}
</style>
