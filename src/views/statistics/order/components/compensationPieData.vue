<template>
  <div class="compensation-pie-data">
    <div class="flex-between">
      <div class="flex-start">
        <TitleContent style="margin: 0" contentTip="不同月份下补偿订单的单数、金额和补偿金额类型">
          补偿订单情况
        </TitleContent>
        <span class="time-tip">最后更新时间：{{ updateTime }}</span>
      </div>
      <el-date-picker
        v-model="time"
        type="month"
        placeholder="请选择月份"
        format="YYYY-MM"
        value-format="YYYY-MM"
        @change="init"
        :clearable="false"
      />
    </div>
    <div class="pie-info">
      <div class="info-left">
        <div class="info-left-date">
          <div>补偿单数（单）</div>
          <div class="date-num">{{ orderCount }}</div>
        </div>
        <div class="info-left-date" style="margin-top: 10px">
          <div>补偿金额（￥）</div>
          <div class="date-num">{{ amount }}</div>
        </div>
      </div>
      <div class="info-right">
        <ModelEchar height="180px" :option="option" />
      </div>
    </div>
  </div>
</template>

<script setup>
import TitleContent from '@/views/statistics/components/TitleContent.vue'
import ModelEchar from '@/views/statistics/components/modelEchar.vue'
import { getNowMonth } from '@/utils/index.js'
import { orderVideoCompensationOrderSituation } from '@/api/statistics/order.js'

const time = ref(getNowMonth())
const emit = defineEmits(['changeTime'])

const option = ref({
  tooltip: {
    trigger: 'item',
    hideDelay: 100,
    enterable: true,
    transitionDuration: 0.3,
    formatter: params => {
      if (params.name === '其他' && params.data.otherList && params.data.otherList.length > 0) {
        const tableRows = params.data.otherList
          .map(
            item => `
          <tr>
          <td  style="min-width: 70px">${item.name}</td>
          <td style="text-align: right;width: 55px">${item.value}个</td> 
          <td style="text-align: right;width: 45px">${item.ratio}%</td>
        </tr>
      `
          )
          .join('')
        return `
          <div style="font-size: 14px; font-weight: bold; max-width: 400px" class="flex-between">
            <div>${params.marker}其他</div>
            <div>${params.value}</div>
          </div>
            <table style="width:170px;border-collapse:collapse; text-align: left; max-height:250px; 
          overflow-y:auto">
              <tbody style="display:block;max-height:250px;overflow-y:auto;">
              ${tableRows}
            </tbody>
            </table>
        `
      } else {
        return `${params.marker} ${params.name}&nbsp;&nbsp;&nbsp;&nbsp; ${params.value}`
      }
    },
  },
  legend: {
    selectedMode: false,
    formatter: name => {
      const dataItem = option.value.series[0].data.find(item => item.name === name)
      return `{name|${name}}{value|${dataItem.value || 0}个}{percent|${dataItem.ratio || 0}%}`
    },
    orient: 'vertical',
    right: '10%',
    top: 'center',
    icon: 'circle',
    itemWidth: 12,
    itemHeight: 12,
    itemGap: 18,
    textStyle: {
      rich: {
        name: {
          width: 80,
          padding: [0, 10, 0, 0],
        },
        value: {
          width: 70,
          align: 'left',
          padding: [0, 10, 0, 0],
        },
        percent: {
          width: 60,
          align: 'left',
        },
      },
    },
  },
  series: [
    {
      type: 'pie',
      radius: ['40%', '70%'],
      center: ['20%', '50%'],
      avoidLabelOverlap: false,
      label: {
        show: false,
        position: 'center',
      },
      labelLine: {
        show: false,
      },
      data: [],
    },
  ],
})

const updateTime = ref('')
const orderCount = ref(0)
const amount = ref(0)

function handleQueryData(arr) {
  if (arr && arr.length > 0) {
    return arr.map(item => {
      return {
        value: item.count,
        name: item.label,
        ratio: item.ratio,
        otherList:
          item.pieChartVOS && item.pieChartVOS.length > 0
            ? item.pieChartVOS.map(item => {
                return {
                  value: item.count,
                  name: item.label,
                  ratio: item.ratio,
                }
              })
            : [],
      }
    })
  } else {
    return []
  }
}
const init = () => {
  orderVideoCompensationOrderSituation({ date: time.value }).then(res => {
    updateTime.value = res.data.writeTimeEnd
    orderCount.value = res.data.compensationOrderCount
    amount.value = res.data.compensationAmount
    option.value.series[0].data = handleQueryData(res.data.pieChartVOS)
    // if (res.data.pieChartVOS && res.data.pieChartVOS.length > 0) {
    //   option.value.series[0].data = res.data.pieChartVOS.map(item => ({
    //     value: item.count,
    //     name: item.label,
    //     ratio: item.ratio,
    //   }))
    // } else {
    //   option.value.series[0].data = []
    // }
  })
}

init()
</script>

<style lang="scss" scoped>
.compensation-pie-data {
  display: flex;
  flex-direction: column;
  border: 1px solid #ffffff;
  padding: 12px 20px;
  box-sizing: border-box;
  border-radius: 10px;
  box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.12);
}
.time-tip {
  color: #848484;
  font-size: 12px;
  margin-left: 10px;
}
.pie-info {
  flex: 1;
  display: flex;
  align-items: center;
  .info-left {
    width: 30%;
    margin-right: 20px;
    flex-shrink: 0;
    &-date {
      font-size: 14px;
      color: #626262;
      background: #f2f2f2;
      border-radius: 4px;
      padding: 15px 10px;
    }
    .date-num {
      margin-top: 15px;
    }
  }
  .info-right {
    width: 70%;
  }
}
</style>
