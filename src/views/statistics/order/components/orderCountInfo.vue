<template>
  <div class="main-box">
    <div class="left-head flex-between">
      <div class="flex-start">
        <TitleContent contentTip="不同时间状态下新增和取消的订单数量">订单趋势</TitleContent>
        <span class="time-tip">最后更新时间：{{ dateTime }}</span>
      </div>

      <SelectDateBtn :showDate="false" @change="handleChangeMathTimes" />
    </div>
    <modelEchar style="margin-top: 10px" :option="option" />
  </div>
</template>

<script setup>
import SelectDateBtn from '@/views/statistics/components/SelectDateBtn.vue'
import TitleContent from '../../components/TitleContent.vue'
import modelEchar from '@/views/statistics/components/modelEchar.vue'
import { modelNumberTrendAnalysis } from '@/api/statistics/model'
import { orderVideoTrend } from '@/api/statistics/order'
import { getBeforeDate } from '@/utils/index'

const params = ref({
  beginTime: '',
  endTime: '',
})

const dateTime = ref('')
const handleChangeMathTimes = (val, type) => {
  if (type == '3') {
    option.value.xAxis[0].name = '单位：月'
  } else {
    option.value.xAxis[0].name = '单位：日'
  }
  params.value.beginTime = val[0] + ' 00:00:00'
  params.value.endTime = val[1] + ' 23:59:59'
  init()
}

const colors = ['#D57D16', '#D7D7D7', '#169BD5']
const option = ref({
  color: colors,
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'none',
    },
    formatter: function (params) {
      let xAxisValue = params[0].axisValue
      let value1 = params[0].value
      let value2 = params[1].value
      let value3 = params[2].value
      //   let sum = (value1 * 100 + value2 * 100) / 100
      return `
          <div style="text-align: center">
              <p>${params[0].seriesName}: ${value1}</p>
              <p>${params[1].seriesName}: ${value2}</p>
              <p>${params[2].seriesName}: ${value3}</p>
          </div>
         `
    },
  },
  grid: {
    left: '6%',
    right: '10%',
    bottom: '13%',
    containLabel: true,
  },
  legend: {
    orient: 'horizontal',
    selectedMode: false,
    top: 0,
    right: 200,
    icon: 'circle',
    itemWidth: 8,
    itemHeight: 8,
    itemGap: 12,
    textStyle: {
      color: '#333',
    },
    data: ['新增订单数', '取消订单数', '排单数'],
  },
  xAxis: [
    {
      type: 'category',
      name: '单位：日',
      axisLabel: {
        fontSize: 12,
        interval: 0,
      },
      nameGap: 10,
      nameTextStyle: {
        padding: [7, 0, 0, 0],
        verticalAlign: 'top',
      },
      axisTick: {
        show: false,
      },
      // prettier-ignore
      data: [],
    },
  ],
  yAxis: [
    {
      type: 'value',
      name: '单位：单',
      position: 'left',
      nameGap: 25,
      axisLabel: {
        margin: 30,
      },
      nameTextStyle: {
        padding: [0, 0, 0, -105],
      },
      alignTicks: true,
      splitLine: {
        show: false,
      },
      axisLine: {
        show: true,
      },
    },
  ],
  series: [
    {
      name: '新增订单数',
      type: 'line',
      data: [],
    },
    {
      name: '取消订单数',
      type: 'line',
      data: [],
    },
    {
      name: '排单数',
      type: 'line',
      data: [],
    },
  ],
})

const init = () => {
  orderVideoTrend(params.value).then(res => {
    dateTime.value = res.data.writeTimeEnd
    option.value.xAxis[0].data = res.data.dateArray || []
    option.value.series[0].data = res.data.orderNewCountArray || []
    option.value.series[1].data = res.data.orderCancelCountArray || []
    option.value.series[2].data = res.data.orderScheduledCountArray || []
  })
}

// init()
</script>

<style lang="scss" scoped>
.main-box {
  margin-top: 10px;
  border: 1px solid #ffffff;
  padding: 10px 20px;
  box-sizing: border-box;
  border-radius: 10px;
  box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.12);
}
.time-tip {
  color: #848484;
  font-size: 12px;
  margin-left: 10px;
}
.left-head {
  display: flex;
}
.left-brokerage {
  font-size: 12px;
  color: #626262;
  background: #f2f2f2;
  width: 200px;
  padding: 4px 16px;
  border-radius: 4px;
  align-items: baseline;
}
</style>
