<template>
  <div class="statistics">
    <div class="time-tip">实时更新数据 {{ dataInfo.dateTime || '' }}</div>
    <div class="statistics-box">
      <div class="statistics-box-item flex-start">
        <div class="item-left">
          <div>平均审单时长（H）</div>
          <div class="number">{{ dataInfo.averageApprovalDuration || 0 }}</div>
        </div>
      </div>
      <div class="statistics-box-item flex-start">
        <div class="item-left">
          <div>平均服务时长（天）</div>
          <div class="number">{{ dataInfo.averageServiceDuration || 0 }}</div>
        </div>
      </div>
      <!-- <div v-for="(item, i) in curData.pieChartVOS" :key="i" class="matche-source-item flex-between">
          <div class="item-left">
            <div>{{ item.label }}</div>
            <div class="number">{{ item.count }}</div>
          </div>
          <div class="progress-wrap">
            <el-progress type="circle" :percentage="item.ratio" :width="60" :stroke-width="7" />
          </div>
        </div> -->
      <div class="statistics-box-item flex-between">
        <div class="item-left">
          <div>任务单数</div>
          <div class="number">{{ dataInfo.taskOrderCount || 0 }}</div>
        </div>
        <div class="progress-wrap">
          <el-progress
            type="circle"
            :percentage="dataInfo.taskOrderRate || 0"
            :width="70"
            :stroke-width="7"
          />
        </div>
      </div>
      <div class="statistics-box-item flex-between">
        <div class="item-left">
          <div>拖单数</div>
          <div class="number">{{ dataInfo.dragOrderCount || 0 }}</div>
        </div>
        <div class="progress-wrap">
          <el-progress
            type="circle"
            :percentage="dataInfo.dragOrderRate || 0"
            :width="70"
            :stroke-width="7"
          />
        </div>
      </div>
      <div class="statistics-box-item flex-between">
        <div class="item-left">
          <div>烂单数</div>
          <div class="number">{{ dataInfo.badOrderCount || 0 }}</div>
        </div>
        <div class="progress-wrap">
          <el-progress type="circle" :percentage="dataInfo.badOrderRate || 0" :width="70" :stroke-width="7" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { orderVideoAverageDurationData } from '@/api/statistics/order'

const dataInfo = ref({})
const init = () => {
  orderVideoAverageDurationData().then(res => {
    dataInfo.value = res.data
  })
}

init()
</script>

<style lang="scss" scoped>
.progress-wrap {
  transform: rotate(180deg);
  position: relative;
}

.progress-wrap :deep(.el-progress__text) {
  transform: translate(-50%, -50%) rotate(-180deg);
  position: absolute;
  left: 50%;
  top: 50%;
  margin: 0 !important;
  font-size: 16px !important;
}

.statistics {
  padding: 15px;
  border: 1px solid #ffffff;
  box-sizing: border-box;
  border-radius: 10px;
  box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.12);
  margin-top: 15px;
  min-width: 780px;
  .time-tip {
    color: #848484;
    font-size: 12px;
  }
}

.statistics-box {
  margin-top: 15px;
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  grid-gap: 15px;
  &-item {
    border: 1px solid #ffffff;
    padding: 12px 20px;
    box-sizing: border-box;
    border-radius: 10px;
    box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.12);
    font-weight: bold;
    color: #333;
  }
  .item-left {
    font-weight: bold;
    .number {
      margin-top: 10px;
    }
  }
}
</style>
