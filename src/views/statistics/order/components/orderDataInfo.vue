<template>
  <div class="order-data-info">
    <div class="head-info">
      <div class="head-info-item" v-loading="dataLoading">
        <div class="title">
          视频订单总数
          <span>实时更新数据 {{ data.dateTime }}</span>
        </div>
        <div class="flex-start">
          <div class="total">{{ data.orderCount || 0 }}</div>
          <div class="flex-center text-gray">
            昨日新增:{{ data.addedOrderVideoCount || 0 }}单
            <el-icon :size="14"><Top /></el-icon>
          </div>
          <span style="margin-left: 10px">最后更新时间：{{ data.writeTimeEnd }}</span>
        </div>
      </div>
      <div class="head-info-item" v-loading="dataLoading">
        <div class="title">
          视频任务单数
          <span>实时更新数据 {{ data.dateTime }}</span>
        </div>
        <div class="flex-start">
          <div class="total">{{ data.taskCount || 0 }}</div>
          <div class="flex-center text-gray">
            昨日新增:{{ data.addedOrderVideoTaskCount || 0 }}单
            <el-icon :size="14"><Top /></el-icon>
          </div>
          <span style="margin-left: 10px">最后更新时间：{{ data.writeTimeEnd }}</span>
        </div>
      </div>
    </div>
    <div class="order-service" v-loading="serviceLoading">
      <div class="flex-start">
        <TitleContent style="margin: 0" contentTip="服务中不同阶段的订单数量和其中的照顾单数量">
          服务中订单数
        </TitleContent>
        <span class="time-tip">实时更新数据&ensp;{{ videoServiceInfo.dateTime || '' }}</span>
      </div>

      <div class="flex-around order-service-info">
        <div class="info-v1 info-items">
          <div>待确认数</div>
          <div>{{ videoServiceInfo.waitConfirmOrderCount || 0 }}单</div>
          <div style="font-weight: 400">其中照顾单数</div>
          <div>{{ videoServiceInfo.waitConfirmOrderCareCount || 0 }}单</div>
        </div>
        <div class="info-v1 info-items">
          <div>待匹配数</div>
          <div>{{ videoServiceInfo.waitMatchOrderCount || 0 }}单</div>
          <div>&nbsp;</div>
          <div>{{ videoServiceInfo.waitMatchOrderCareCount || 0 }}单</div>
        </div>
        <div class="info-v2 info-items">
          <div>超7天未匹配模特数</div>
          <div>{{ videoServiceInfo.overSevenDaysNoMatchOrderCount || 0 }}单</div>
          <div style="height: 21px">&nbsp;</div>
          <div>{{ videoServiceInfo.overSevenDaysNoMatchOrderCareCount || 0 }}单</div>
        </div>
        <div class="info-v1 info-items">
          <div>待发货数</div>
          <div>{{ videoServiceInfo.waitDeliveryOrderCount || 0 }}单</div>
          <div>&nbsp;</div>
          <div>{{ videoServiceInfo.waitDeliveryOrderCareCount || 0 }}单</div>
        </div>
        <div class="info-v2 info-items">
          <div>超7天未发货数</div>
          <div>{{ videoServiceInfo.overSevenDaysNoDeliveryOrderCount || 0 }}单</div>
          <div style="height: 21px">&nbsp;</div>
          <div>{{ videoServiceInfo.overSevenDaysNoDeliveryOrderCareCount || 0 }}单</div>
        </div>
        <div class="info-v1 info-items">
          <div>待完成数</div>
          <div>{{ videoServiceInfo.waitFinishOrderCount || 0 }}单</div>
          <div>&nbsp;</div>
          <div>{{ videoServiceInfo.waitFinishOrderCareCount || 0 }} 单</div>
        </div>
        <div class="info-v2 info-items">
          <div>签收后超20天未反馈素材数</div>
          <div>{{ videoServiceInfo.signOverTwentyDaysNoFeedbackOrderCount || 0 }}单</div>
          <div style="height: 21px">&nbsp;</div>
          <div>{{ videoServiceInfo.signOverTwentyDaysNoFeedbackOrderCareCount || 0 }}单</div>
        </div>
        <div class="info-v1 info-items">
          <div>需确认数</div>
          <div>{{ videoServiceInfo.needConfirmOrderCount || 0 }}单</div>
          <div>&nbsp;</div>
          <div>{{ videoServiceInfo.needConfirmOrderCareCount || 0 }}单</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import TitleContent from '@/views/statistics/components/TitleContent.vue'
import { orderVideoBaseBoard, orderVideoServiceCount } from '@/api/statistics/order'

const dataLoading = ref(false)

const data = ref({
  addedOrderVideoCount: 0,
  addedOrderVideoTaskCount: 0,
  orderCount: 0,
  taskCount: 0,
})

const videoServiceInfo = ref({})
const serviceLoading = ref(false)
const getVideoServiceInfo = () => {
  serviceLoading.value = true
  orderVideoServiceCount()
    .then(res => {
      videoServiceInfo.value = res.data
    })
    .finally(() => {
      serviceLoading.value = false
    })
}

const init = () => {
  dataLoading.value = true
  orderVideoBaseBoard()
    .then(res => {
      data.value = res.data
    })
    .finally(() => {
      dataLoading.value = false
    })
  getVideoServiceInfo()
}

init()
</script>

<style lang="scss" scoped>
.order-data-info {
  margin-top: 15px;
}
.head-info {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0 15px;
  &-item {
    border: 1px solid #ffffff;
    padding: 20px;
    box-sizing: border-box;
    border-radius: 10px;
    box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.12);
    span {
      font-size: 12px;
      color: #848484;
      margin-left: 5px;
    }
  }
  .title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 15px;
    span {
      font-weight: 400;
    }
  }
  .total {
    // margin-right: 100px;
    width: 145px;
    font-size: 20px;
    font-weight: bold;
  }
  .text-gray {
    font-size: 13px;
    padding: 2px 18px;
    background: #f2f2f2;
    color: #169bd5;
    border-radius: 73px;
  }
}
.order-service {
  border: 1px solid #ffffff;
  padding: 20px;
  margin-top: 15px;
  box-sizing: border-box;
  border-radius: 10px;
  box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.12);
  .time-tip {
    color: #848484;
    font-size: 12px;
    margin-left: 10px;
  }
  &-info {
    margin-top: 20px;
    background: #f2f2f2;
    border-radius: 8px;
    padding: 10px 15px;
  }
  .info-items {
    color: #333;
    display: grid;
    gap: 10px 0;
    // border-right: 1px solid #ccc;
    position: relative; /* 添加定位 */
    padding-right: 3%;
  }
  .info-items:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 50%;
    right: 0;
    height: 67px;
    width: 2px;
    background-color: #ccc;
    transform: translateY(-50%);
  }

  .info-v1 {
    font-size: 16px;
    height: 114px;
    font-weight: bold;
  }
  .info-v2 {
    height: 114px;
    padding-top: 3px;
    font-size: 14px;
  }
}
</style>
