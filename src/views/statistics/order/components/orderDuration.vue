<template>
  <div class="duration-box">
    <div class="duration-box-item">
      <PieDate title="订单匹配时长数据分析" @changeTime="initMatch" :seriesDate="matchList">
        <div class="time-tip">最后更新时间：{{ matchTime }}</div>
        <div class="flex-between time-box">
          <div class="time-box-title">平均匹配时长（天）</div>
          <div class="time-box-num">{{ matchDay }}</div>
        </div>
      </PieDate>
    </div>
    <div class="duration-box-item">
      <PieDate title="订单发货时长数据分析" @changeTime="initDelivery" :seriesDate="deliveryList">
        <div class="time-tip">最后更新时间：{{ deliveryTime }}</div>
        <div class="flex-between time-box">
          <div class="time-box-title">平均发货时长（天）</div>
          <div class="time-box-num">{{ deliveryDay }}</div>
        </div>
      </PieDate>
    </div>
    <div class="duration-box-item">
      <PieDate title="订单素材反馈时长数据分析" @changeTime="initFeedback" :seriesDate="feedbackList">
        <div class="time-tip">最后更新时间：{{ feedbackTime }}</div>
        <div class="flex-between time-box">
          <div class="time-box-title">素材平均反馈时长（天）</div>
          <div class="time-box-num">{{ feedbackDay }}</div>
        </div>
      </PieDate>
    </div>
  </div>
</template>

<script setup>
import PieDate from './pieDate.vue'
import {
  orderVideoMatchDurationData,
  orderVideoFeedbackDurationData,
  orderVideoDeliveryDurationData,
} from '@/api/statistics/order.js'

const matchTime = ref('')
const deliveryTime = ref('')
const feedbackTime = ref('')
const matchDay = ref('')
const deliveryDay = ref('')
const feedbackDay = ref('')
const matchList = ref([])
const deliveryList = ref([])
const feedbackList = ref([])

function handleQueryData(arr) {
  if (arr && arr.length > 0) {
    return arr.map(item => {
      return {
        value: item.count,
        name: item.label,
        ratio: item.ratio,
        otherList:
          item.pieChartVOS && item.pieChartVOS.length > 0
            ? item.pieChartVOS.map(item => {
                return {
                  value: item.count,
                  name: item.label,
                  ratio: item.ratio,
                }
              })
            : [],
      }
    })
  } else {
    return []
  }
}
const initMatch = date => {
  orderVideoMatchDurationData({ date }).then(res => {
    matchTime.value = res.data.writeTimeEnd
    matchDay.value = res.data.averageDuration
    matchList.value = handleQueryData(res.data.pieChartVOS)
  })
}
const initDelivery = date => {
  orderVideoDeliveryDurationData({ date }).then(res => {
    deliveryTime.value = res.data.writeTimeEnd
    deliveryDay.value = res.data.averageDuration
    deliveryList.value = handleQueryData(res.data.pieChartVOS)
  })
}
const initFeedback = date => {
  orderVideoFeedbackDurationData({ date }).then(res => {
    feedbackTime.value = res.data.writeTimeEnd
    feedbackDay.value = res.data.averageDuration
    feedbackList.value = handleQueryData(res.data.pieChartVOS)
  })
}
</script>

<style lang="scss" scoped>
.duration-box {
  margin-top: 15px;
  display: grid;
  grid-template-columns: repeat(3, minmax(450px, 1fr));
  gap: 0 15px;
  &-item {
    border: 1px solid #ffffff;
    padding: 15px 20px 8px 20px;
    box-sizing: border-box;
    border-radius: 10px;
    box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.12);
  }
  .time-box {
    margin: 10px 20px 0 20px;
    padding: 20px;
    background: #f2f2f2;
    border-radius: 4px;
    &-title {
      font-size: 13px;
      color: #4f4f4f;
    }
    &-num {
      font-size: 20px;
      font-weight: bold;
      color: #333;
    }
  }
  .time-tip {
    color: #848484;
    font-size: 12px;
  }
}
</style>
