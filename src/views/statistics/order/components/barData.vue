<template>
  <div class="bar-box">
    <div class="flex-between">
      <div class="flex-start">
        <TitleContent style="margin: 0" contentTip="不同月份下不同售后类型的占比和数量">
          售后类型分析
        </TitleContent>
        <span class="time-tip">最后更新时间：{{ updateTime }}</span>
      </div>
      <el-date-picker
        v-model="time"
        type="month"
        placeholder="请选择月份"
        format="YYYY-MM"
        value-format="YYYY-MM"
        @change="init"
        :clearable="false"
      />
    </div>
    <ModelEchar :option="option" />
  </div>
</template>

<script setup>
import TitleContent from '@/views/statistics/components/TitleContent.vue'
import ModelEchar from '@/views/statistics/components/modelEchar.vue'
import { getNowMonth } from '@/utils/index.js'
import { orderVideoAfterSaleTypeAnalysis } from '@/api/statistics/order.js'

const time = ref(getNowMonth())
const emit = defineEmits(['changeTime'])

const commonItemStyle = {
  borderRadius: 4,
  borderWidth: 1,
  borderColor: 'rgba(255,255,255,0.3)',
}

const option = ref({
  //   graphic: {
  //     type: 'text',
  //     invisible: false,
  //     left: 'center',
  //     top: 'middle',
  //     style: {
  //       text: '暂无数据',
  //       fontSize: 16,
  //       fill: '#909399',
  //     },
  //   },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'none',
    },
    formatter: function (params) {
      // let total = params.reduce(
      //   (sum, item) => sum + (item.value === null || item.value == undefined ? 0 : item.value),
      //   0
      // )
      // let xAxisValue = params[0].axisValue
      let xAxisValue = params[0].data.day[params[0].dataIndex] || params[0].axisValue
      return (
        `
        ${xAxisValue}<br/> 
        <div style="display:flex"><div style="width:80px">总计:</div> ${params[0].data.total}</div>
        ` +
        params
          .map(
            item =>
              `<div class="flex-start" style="width: 200px">
                <div style="width:80px">${item.seriesName}:</div>
                <div style="display:flex"><div style="width:70px;">${
                  item.value == null || item.value == undefined ? 0 : item.value
                }</div><div style="width:50px">${item.data.ratio}%</div></div>
                </div> `
          )
          .join('')
      )
    },
  },
  grid: {
    left: '3%',
    right: '10%',
    bottom: '7%',
    containLabel: true,
  },
  legend: {
    orient: 'horizontal',
    selectedMode: false,
    right: 10,
    icon: 'circle',
    itemWidth: 12,
    itemHeight: 12,
    itemGap: 30,
    textStyle: {
      color: '#666',
    },
    data: ['售后单', '工单', '补偿', '回退'],
  },
  xAxis: [
    {
      type: 'category',
      name: '日期',
      axisLabel: {
        fontSize: 12,
        interval: 0,
      },
      nameGap: 10,
      nameTextStyle: {
        padding: [7, 0, 0, 0],
        verticalAlign: 'top',
      },
      axisTick: {
        show: false,
        // alignWithLabel: true,
      },
      // prettier-ignore
      data: [],
    },
  ],
  //筛选控件可选范围
  // dataZoom: [
  //   {
  //     type: 'slider',
  //     show: true,
  //     start: 0,
  //     end: 40,
  //     height: 15,
  //     bottom: '5%',
  //   },
  // ],
  yAxis: [
    {
      type: 'value',
      name: '单位：单',
      position: 'left',
      alignTicks: true,
      splitLine: {
        show: false,
      },
      nameTextStyle: {
        padding: [0, 0, 0, 30],
      },
      axisLine: {
        show: true,
      },
    },
  ],
  series: [
    {
      name: '售后单',
      type: 'bar',
      stack: 'stack1',
      data: [],
      itemStyle: commonItemStyle,
    },
    {
      name: '工单',
      type: 'bar',
      stack: 'stack1',
      data: [],
      itemStyle: commonItemStyle,
    },
    {
      name: '补偿',
      type: 'bar',
      stack: 'stack1',
      data: [],
      itemStyle: commonItemStyle,
    },
    {
      name: '回退',
      type: 'bar',
      stack: 'stack1',
      data: [],
      itemStyle: commonItemStyle,
    },
  ],
})

const updateTime = ref('')

const handleData = (list, isDay = false) => {
  if (list && list.length > 0) {
    if (isDay) {
      return list.map(item => item.date)
    } else {
      return list.map(item => item.date.split('.')[1])
    }
  } else {
    return []
  }
}

function handleQueryData(arr) {
  if (arr && arr.length > 0) {
    return arr.map(item => {
      return {
        value: item.count,
        name: item.label,
        ratio: item.ratio,
      }
    })
  } else {
    return []
  }
}
const init = () => {
  orderVideoAfterSaleTypeAnalysis({ date: time.value }).then(res => {
    updateTime.value = res.data.writeTimeEnd
    option.value.xAxis[0].data = handleData(res.data.orderVideoAfterSaleTypeAnalysisDetailVOS)
    option.value.series[0].data = res.data.orderVideoAfterSaleTypeAnalysisDetailVOS.map(item => {
      return {
        value: item.afterSaleCount === 0 ? null : item.afterSaleCount,
        ratio: item.afterSaleRate,
        total: item.totalCount,
        day: handleData(res.data.orderVideoAfterSaleTypeAnalysisDetailVOS, true),
      }
    })
    option.value.series[1].data = res.data.orderVideoAfterSaleTypeAnalysisDetailVOS.map(item => {
      return {
        value: item.workOrderCount === 0 ? null : item.workOrderCount,
        ratio: item.workOrderRate,
      }
    })
    option.value.series[2].data = res.data.orderVideoAfterSaleTypeAnalysisDetailVOS.map(item => {
      return {
        value: item.reparationCount === 0 ? null : item.reparationCount,
        ratio: item.reparationRate,
      }
    })
    option.value.series[3].data = res.data.orderVideoAfterSaleTypeAnalysisDetailVOS.map(item => {
      return {
        value: item.returnCount === 0 ? null : item.returnCount,
        ratio: item.returnRate,
      }
    })
  })
}

init()
</script>

<style lang="scss" scoped>
.bar-box {
  border: 1px solid #ffffff;
  padding: 12px 20px;
  box-sizing: border-box;
  border-radius: 10px;
  box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.12);
  .time-tip {
    color: #848484;
    font-size: 12px;
    margin-left: 10px;
  }
}
</style>
