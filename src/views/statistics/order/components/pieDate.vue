<template>
  <div class="pie-box">
    <div class="flex-between">
      <TitleContent style="margin: 0 0 0 0" :showTip="false">
        {{ title }}
      </TitleContent>
      <el-date-picker
        v-model="time"
        type="month"
        placeholder="请选择月份"
        format="YYYY-MM"
        value-format="YYYY-MM"
        @change="handleChangeTime"
        :clearable="false"
      />
    </div>
    <slot></slot>
    <modelEchar ref="modelEcharRef" height="160px" :option="echartsData" />
  </div>
</template>

<script setup>
import TitleContent from '@/views/statistics/components/TitleContent.vue'
import modelEchar from '@/views/statistics/components/modelEchar.vue'
import { getNowMonth } from '@/utils/index.js'

const props = defineProps({
  title: {
    type: String,
    default: '',
  },
  seriesDate: {
    type: Array,
    default: () => [],
  },
  itemGap: {
    type: Number,
    default: 12,
  },
  top: {
    type: String,
    default: 'center',
  },
})

const modelEcharRef = ref(null)
watch(
  () => props.seriesDate,
  () => {
    echartsData.value.series[0].data = props.seriesDate
    // modelEcharRef?.value.draw()
  }
)
const time = ref(getNowMonth())
const emit = defineEmits(['changeTime'])
const handleChangeTime = () => {
  emit('changeTime', time.value)
}

const echartsData = ref({
  tooltip: {
    trigger: 'item',
  },
  legend: {
    type: 'scroll',
    selectedMode: false,
    formatter: name => {
      const dataItem = echartsData.value.series[0].data.find(item => item.name === name)
      return `{name|${name}}{value|${dataItem.value || 0}个}{percent|${dataItem.ratio || 0}%}`
    },
    orient: 'vertical',
    right: '15%',
    top: props.top,
    icon: 'circle',
    itemWidth: 12,
    itemHeight: 12,
    itemGap: props.itemGap,
    padding: [0, 0, 0, 0],
    textStyle: {
      color: '#666', // 基础文字颜色
      rich: {
        name: {
          width: 80,
          padding: [0, 5, 0, 0],
        },
        value: {
          width: 70,
          align: 'left',
          padding: [0, 5, 0, 0],
        },
        percent: {
          width: 60,
          align: 'left',
        },
      },
    },
  },
  series: [
    {
      type: 'pie',
      radius: ['40%', '70%'],
      center: ['18%', '55%'],
      avoidLabelOverlap: false,
      label: {
        show: false,
        position: 'center',
      },
      labelLine: {
        show: false,
      },
      data: props.seriesDate,
    },
  ],
})

function init() {
  emit('changeTime', time.value)
}

init()
</script>

<style lang="scss" scoped></style>
