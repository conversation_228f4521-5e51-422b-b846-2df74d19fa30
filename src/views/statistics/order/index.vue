<template>
  <div class="order-box">
    <TitleHead title="视频订单数据" />
    <OrderDataInfo />
    <TitleHead style="margin-top: 10px" title="视频订单趋势分析" />
    <OrderCountInfo />
    <OrderDuration />
    <OrderStatistics />
    <OrderAfterSale />
  </div>
</template>

<script setup>
import TitleHead from '@/views/statistics/components/TitleHead.vue'
import OrderDataInfo from '@/views/statistics/order/components/orderDataInfo.vue'
import OrderCountInfo from '@/views/statistics/order/components/orderCountInfo.vue'
import OrderDuration from '@/views/statistics/order/components/orderDuration.vue'
import OrderStatistics from '@/views/statistics/order/components/orderStatistics.vue'
import OrderAfterSale from '@/views/statistics/order/components/orderAfterSale.vue'
</script>

<style lang="scss" scoped>
.order-box {
  padding: 15px 20px;
}
</style>
