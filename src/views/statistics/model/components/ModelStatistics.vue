<template>
  <div class="container">
    <div class="left-panel flex-around">
      <div style="width: 50%">
        <div class="flex-between" style="padding: 0 30px">
          <div class="cooperation-box">
            <div>合作中模特数</div>
            <div style="margin-top: 15px">{{ modelBasicsVo.activeModelNumber || 0 }}</div>
          </div>
          <div class="month-box">
            <div>上月</div>
            <div class="mt-10 flex-center">
              新增 {{ modelBasicsVo.newAdditionsLastMonthModelNumber || 0 }}&nbsp;
              <el-icon color="#000"><Top /></el-icon>
            </div>
          </div>
        </div>
        <div class="left-panel-bottom">
          <div class="flex-between">
            <div>正常合作</div>
            <div>{{ modelBasicsVo.normalModelNumber || 0 }}</div>
          </div>
          <div class="flex-between mt-10">
            <div>行程中</div>
            <div>{{ modelBasicsVo.journeyModelNumber || 0 }}</div>
          </div>
        </div>
      </div>
      <div style="width: 50%">
        <div class="flex-between" style="padding: 0 30px">
          <div class="cooperation-box">
            <div>已淘汰模特数</div>
            <div style="margin-top: 15px">{{ modelBasicsVo.oustModelNumber || 0 }}</div>
          </div>
          <div class="month-box">
            <div>上月</div>
            <div class="mt-10 flex-center">
              淘汰 {{ modelBasicsVo.eliminatedLastMonthModelNumber || 0 }}&nbsp;
              <el-icon color="#000"><Bottom /></el-icon>
            </div>
          </div>
        </div>
        <div class="left-panel-bottom">
          <div class="flex-between">
            <div>暂停合作</div>
            <div>{{ modelBasicsVo.pauseModelNumber || 0 }}</div>
          </div>
          <div class="flex-between mt-10">
            <div>取消合作</div>
            <div>{{ modelBasicsVo.cancelModelNumber || 0 }}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="right-panel">
      <div class="statistic-item">
        <div class="statistic-item__title">首次匹配成功率</div>
        <div class="statistic-item__value">{{ modelBasicsVo.successRateOfTheFirstMatch || 0 }}%</div>
      </div>
      <div class="statistic-item">
        <div class="statistic-item__title">模特售后率</div>
        <div class="statistic-item__value">{{ modelBasicsVo.modelAfterSalesRate || 0 }}%</div>
      </div>
      <div class="statistic-item">
        <div class="statistic-item__title">模特超时率</div>
        <div class="statistic-item__value">{{ modelBasicsVo.modelOvertimeRate || 0 }}%</div>
      </div>
      <div class="statistic-item">
        <div class="statistic-item__title">意向匹配成功率</div>
        <div class="statistic-item__value">{{ modelBasicsVo.successRateOfIntentionMatching || 0 }}%</div>
      </div>
      <div class="statistic-item">
        <div class="statistic-item__title">平均匹配时长 (天)</div>
        <div class="statistic-item__value">{{ modelBasicsVo.averageMatchingDuration || 0 }}</div>
      </div>
      <div class="statistic-item">
        <div class="statistic-item__title">平均反馈时长 (天)</div>
        <div class="statistic-item__value">{{ modelBasicsVo.averageFeedbackDuration || 0 }}</div>
      </div>
    </div>
  </div>
  <div></div>
</template>

<script setup>
import { modelBasicsData } from '@/api/statistics/model.js'

const modelBasicsVo = ref({})
const emit = defineEmits(['change'])

const initQuery = () => {
  modelBasicsData().then(res => {
    modelBasicsVo.value = res.data
    if (res.data && res.data.dateTime) {
      emit('change', res.data.dateTime)
    }
  })
}

initQuery()
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  justify-content: space-between;
  gap: 0 20px;
  margin-top: 10px;
  .left-panel,
  .right-panel {
    width: 50%;
    border: 1px solid #ffffff;
    padding: 20px;
    box-sizing: border-box;
    border-radius: 10px;
    box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.12);
  }
  .left-panel {
    gap: 0 20px;
    &-bottom {
      background: #e8eaec;
      border-radius: 4px;
      padding: 15px 30px;
      margin-top: 15px;
    }
    .cooperation-box {
      //   margin-top: 20px;
      font-size: 18px;
    }
    .month-box {
      //   margin-top: 20px;
      color: #848484;
      font-size: 14px;
    }
  }
  .right-panel {
    padding: 20px 30px;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px 20px;
    .statistic-item__title {
      font-weight: 800;
      font-size: 15px;
      //   text-align: center;
    }
    .statistic-item__value {
      font-weight: 600;
      font-size: 22px;
      margin-top: 10px;
      //   text-align: center;
    }
  }
}
.mt-10 {
  margin-top: 10px;
}
</style>
