<template>
  <div class="matche-box">
    <div class="matche-num">
      <div class="flex-start">
        <div class="flex-start">
          成功匹配次数
          <el-tooltip popper-class="public-white-tooltips" :hide-after="0" placement="top">
            <template #content>
              <div class="tip-box">截至某天当前总共匹配成功的次数</div>
            </template>
            <el-icon class="icon" size="18" color="#c5c8ce"><QuestionFilled /></el-icon>
          </el-tooltip>
        </div>
        <div class="title-time">实时更新数据：{{curData.dateTime}}</div>
      </div>
      <div class="number">{{ curData.successMatchCount || 0 }}</div>
    </div>
    <div>
      <TitleContent :contentTip="'匹配成功的模特的来源和占比'">匹配拍摄模特来源</TitleContent>
      <div class="matche-source">
        <div v-for="(item, i) in curData.pieChartVOS" :key="i" class="matche-source-item flex-between">
          <div class="item-left">
            <div>{{ item.label }}</div>
            <div class="number">{{ item.count }}</div>
          </div>
          <div class="progress-wrap">
            <el-progress type="circle" :percentage="item.ratio" :width="60" :stroke-width="7" />
          </div>
        </div>
        <!-- <div class="matche-source-item flex-between">
          <div class="item-left">
            <div>客服自选</div>
            <div class="number">123456</div>
          </div>
          <div class="progress-wrap">
            <el-progress type="circle" :percentage="50" :width="60" :stroke-width="7" />
          </div>
        </div>
        <div class="matche-source-item flex-between">
          <div class="item-left">
            <div>模特自选</div>
            <div class="number">123456</div>
          </div>
          <div class="progress-wrap">
            <el-progress type="circle" :percentage="25" :width="60" :stroke-width="7" />
          </div>
        </div> -->
      </div>
    </div>
  </div>
</template>

<script setup>
import TitleContent from '@/views/statistics/components/TitleContent.vue'
import { modelSuccessMatchCount } from '@/api/statistics/model.js'

const curData = ref({})
const init = () => {
  modelSuccessMatchCount().then(res => {
    curData.value = res.data
  })
}
init()
</script>

<style lang="scss" scoped>
.progress-wrap {
  transform: rotate(180deg);
  position: relative;
}

.progress-wrap :deep(.el-progress__text) {
  transform: translate(-50%, -50%) rotate(-180deg);
  position: absolute;
  left: 50%;
  top: 50%;
  margin: 0 !important;
  font-size: 12px !important;
}

.matche-box {
  margin-top: 10px;
  border: 1px solid #ffffff;
  padding: 20px 30px;
  box-sizing: border-box;
  border-radius: 10px;
  box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.12);
}
.matche-num {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  .icon {
    margin-left: 8px;
  }
  .number {
    font-size: 20px;
    margin-top: 10px;
  }
}
.matche-source {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 0 10px;
  &-item {
    padding: 10px 20px;
    background: #f2f2f2;
    border-radius: 4px;
    font-size: 14px;
    color: #333;
  }
  .item-left {
    font-weight: bold;
    .number {
      margin-top: 10px;
    }
  }
}
.title-time {
  font-size: 12px;
  color: #848484;
  font-weight: normal;
  margin-left: 10px;
}
</style>
