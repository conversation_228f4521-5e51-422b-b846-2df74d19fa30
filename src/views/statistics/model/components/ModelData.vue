<template>
  <div class="month-box">
    <div class="left">
      <PieData
        title="开发模特合作状态分布"
        tip="不同月份新增的模特截止最新统计日期时的合作状态情况"
        :updateTime="addDateUpdateTime"
        :seriesDate="addDate"
        pickerLabel="模特新增时间"
        showGrade
        :showTotal="false"
        :itemGap="26"
        top="15%"
        :siteOptions="{ legendRight: '25%', seriesCenter: ['30%', '55%'] }"
        @changeTime="handleChangeAddTime"
      />
    </div>
    <div class="right">
      <PieData
        title="模特排单情况"
        tip="不同月份新增的模特截止最新统计日期时的排单情况"
        :updateTime="reduceDateUpdateTime"
        pickerLabel="模特新增时间"
        showGrade
        legendArray
        :siteOptions="{ seriesCenter: ['18%', '50%'] }"
        :itemGap="20"
        :showTotal="false"
        :seriesDate="reduceDate"
        @changeTime="handleChangeReduceTime"
      />
    </div>
  </div>
</template>

<script setup>
//   import pieDate from './pieDate.vue'
import PieData from '@/views/statistics/member/components/pieData.vue'
import { modelOrderScheduledData, modelStatusData } from '@/api/statistics/model'

const reduceDate = ref([])
const addDate = ref([])
const addDateUpdateTime = ref('')
const reduceDateUpdateTime = ref('')

function handleList(list) {
  if (list && list.length > 0) {
    return list.map(item => {
      return {
        name: item.label,
        value: item.count,
        ratio: item.ratio,
      }
    })
  } else {
    return []
  }
}

function handleChangeReduceTime(val) {
  modelOrderScheduledData(val).then(res => {
    reduceDateUpdateTime.value = res.data.dateTime
    reduceDate.value = handleList(res.data.pieChartVOS)
  })
}

function handleChangeAddTime(val) {
  modelStatusData(val).then(res => {
    addDateUpdateTime.value = res.data.dateTime
    addDate.value = handleList(res.data.pieChartVOS)
  })
}

handleChangeReduceTime()
handleChangeAddTime()
</script>

<style lang="scss" scoped>
.month-box {
  display: grid;
  grid-template-columns: repeat(2, minmax(750px, 1fr));
  gap: 0 10px;
  margin-top: 10px;
  .left,
  .right {
    // width: 50%;
    // border: 1px solid #ffffff;
    // padding: 10px 20px;
    // box-sizing: border-box;
    // border-radius: 10px;
    // box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.12);
  }
}
</style>
