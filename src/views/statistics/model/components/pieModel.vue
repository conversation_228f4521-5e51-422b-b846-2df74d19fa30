<template>
  <div class="pie-box">
    <TitleContent style="margin: 0 0 0 0; position: absolute" :showTip="false">{{ title }}</TitleContent>
    <modelEchar ref="modelEcharRef" :height="echartsHeight" :option="echartsData" />
  </div>
</template>

<script setup>
import TitleContent from '@/views/statistics/components/TitleContent.vue'
import modelEchar from '@/views/statistics/components/modelEchar.vue'

const props = defineProps({
  title: {
    type: String,
    default: '',
  },
  seriesDate: {
    type: Array,
    default: () => [],
  },
  itemGap: {
    type: Number,
    default: 12,
  },
  top: {
    type: String,
    default: 'center',
  },
  enterable: {
    type: Boolean,
    default: false,
  },
  hideDelay: {
    type: Number,
    default: 0,
  },
  vertical: {
    type: Boolean,
    default: false,
  },
  echartsHeight: {
    type: String,
    default: '160px',
  },
})

const modelEcharRef = ref(null)
watch(
  () => props.seriesDate,
  () => {
    echartsData.value.series[0].data = props.seriesDate
    if (props.vertical) {
      initVerticalLegend()
    }
    // modelEcharRef?.value.draw()
  }
)

const echartsData = ref({
  tooltip: {
    trigger: 'item',
    hideDelay: props.hideDelay,
    enterable: props.enterable,
    transitionDuration: 0.3,
    formatter: params => {
      if (params.name === '其他' && params.data.otherList && params.data.otherList.length > 0) {
        const tableRows = params.data.otherList
          .map(
            item => `
          <tr>
          <td  style="min-width: 70px">${item.name}</td>
          <td style="text-align: right;width: 55px">${item.value}个</td>
          <td style="text-align: right;width: 45px">${item.ratio}%</td>
        </tr>
      `
          )
          .join('')
        return `
          <div style="font-size: 14px; font-weight: bold; max-width: 400px" class="flex-between">
            <div>${params.marker}其他</div>
            <div>${params.value}</div>
          </div>
            <table style="width:170px;border-collapse:collapse; text-align: left; max-height:250px;
          overflow-y:auto">
              <tbody style="display:block;max-height:250px;overflow-y:auto;">
              ${tableRows}
            </tbody>
            </table>
        `
      } else {
        return `${params.marker} ${params.name}&nbsp;&nbsp;&nbsp;&nbsp; ${params.value}`
      }
    },
  },
  legend: {
    type: 'scroll',
    selectedMode: false,
    formatter: name => {
      const dataItem = echartsData.value.series[0].data.find(item => item.name === name)
      return `{name|${name}}{value|${dataItem.value || 0}个}{percent|${dataItem.ratio || 0}%}`
    },
    orient: 'vertical',
    right: '6%',
    top: props.top,
    icon: 'circle',
    itemWidth: 12,
    itemHeight: 12,
    itemGap: props.itemGap,
    padding: [0, 0, 0, 0],
    textStyle: {
      color: '#666', // 基础文字颜色
      rich: {
        name: {
          width: 80,
          padding: [0, 5, 0, 0],
        },
        value: {
          width: 70,
          align: 'left',
          padding: [0, 5, 0, 0],
        },
        percent: {
          width: 60,
          align: 'left',
        },
      },
    },
  },
  series: [
    {
      type: 'pie',
      radius: ['35%', '60%'],
      center: ['15%', '55%'],
      avoidLabelOverlap: false,
      label: {
        show: false,
        position: 'center',
      },
      labelLine: {
        show: false,
      },
      data: props.seriesDate,
    },
  ],
})

const initVerticalLegend = () => {
  echartsData.value.series[0].radius = ['30%', '50%']
  echartsData.value.series[0].center = ['50%', '27%']
  echartsData.value.legend.right = '4%'
  echartsData.value.legend.textStyle = {
    color: '#666', // 基础文字颜色
    rich: {
      name: {
        width: 80,
        padding: [0, 5, 0, 0],
      },
      value: {
        width: 60,
        align: 'left',
        padding: [0, 5, 0, 0],
      },
      percent: {
        width: 60,
        align: 'left',
      },
    },
  }
}
</script>

<style lang="scss" scoped>
.pie-box {
  height: 100%;
  border: 1px solid #ffffff;
  padding: 15px 20px 8px 20px;
  box-sizing: border-box;
  border-radius: 10px;
  box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.12);
}
</style>
