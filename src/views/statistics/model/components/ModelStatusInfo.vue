<template>
  <div class="status-box">
    <div class="flex-between">
      <div class="flex-center">
        <TitleContent
          contentTip="不同合作状态下的模特类型分析（数量、类型、性别、模特等级、国家、佣金、年龄层）"
        >
          模特类型分析
        </TitleContent>
        <div class="title-time">实时更新数据：{{ dateTime }}</div>
      </div>
      <el-radio-group v-model="status" @change="onQuery">
        <el-radio-button :value="0">正常合作</el-radio-button>
        <el-radio-button :value="2">行程中</el-radio-button>
        <el-radio-button :value="1">暂停合作</el-radio-button>
        <el-radio-button :value="3">取消合作</el-radio-button>
      </el-radio-group>
    </div>
    <div class="info-box flex-center">
      <div class="info-box-left">
        <div class="left-item flex-between">
          <div>
            {{
              status == 0 ? '正常合作' : status == 1 ? '暂停合作' : status == 2 ? '行程中' : '取消合作'
            }}模特
          </div>
          <div>{{ curModelCount }}</div>
        </div>
        <div class="left-item flex-grid">
          <div>模特类型</div>
          <template v-if="curTypeData.length > 0">
            <div v-for="(item, i) in curTypeData" :key="i" class="flex-between left-item-text">
              <div>{{ item.label }}</div>
              <div class="flex-center">
                <div style="margin-right: 2vw">{{ item.count }}</div>
                <div>{{ item.ratio }}%</div>
              </div>
            </div>
          </template>
        </div>
        <div class="left-item flex-grid">
          <div>性别</div>
          <template v-if="curSexData.length > 0">
            <div v-for="(item, i) in curSexData" :key="i" class="flex-between left-item-text">
              <div>{{ item.label }}</div>
              <div class="flex-center">
                <div style="margin-right: 2vw">{{ item.count }}</div>
                <div>{{ item.ratio }}%</div>
              </div>
            </div>
          </template>
        </div>
      </div>
      <div class="info-box-right">
        <PieModel title="模特等级" :seriesDate="cooperationList" :itemGap="20" top="40%" />
        <PieModel title="国家" :seriesDate="nationList" />
        <PieModel title="佣金" :seriesDate="brokerageList" top="21%" enterable :hideDelay="100" />
        <PieModel title="年龄层" :seriesDate="ageList" top="28%" />
      </div>
      <div class="info-box-end">
        <PieModel title="排单情况" vertical :seriesDate="orderScheduled" echartsHeight="100%" top="50%" />
      </div>
    </div>
  </div>
</template>

<script setup>
import TitleContent from '@/views/statistics/components/TitleContent.vue'
import PieModel from './pieModel.vue'
import { modelTypeAnalysis } from '@/api/statistics/model'

const status = ref(0)
const cooperationList = ref([])
const nationList = ref([])
const brokerageList = ref([])
const ageList = ref([])
const orderScheduled = ref([])

function handleQueryData(arr) {
  if (arr && arr.length > 0) {
    return arr.map(item => {
      return {
        value: item.count,
        name: item.label,
        ratio: item.ratio,
        otherList:
          item.pieChartVOS && item.pieChartVOS.length > 0
            ? item.pieChartVOS.map(item => {
                return {
                  value: item.count,
                  name: item.label,
                  ratio: item.ratio,
                }
              })
            : [],
      }
    })
  } else {
    return []
  }
}

const curSexData = ref([])
const curTypeData = ref([])
const curModelCount = ref(0)
const dateTime = ref('')
const onQuery = () => {
  modelTypeAnalysis({ status: status.value }).then(res => {
    cooperationList.value = handleQueryData(res.data.modelCooperationPieChartVOS)
    nationList.value = handleQueryData(res.data.modelNationPieChartVOS)
    brokerageList.value = handleQueryData(res.data.modelCommissionPieChartVOS)
    ageList.value = handleQueryData(res.data.modelAgeGroupPieChartVOS)
    orderScheduled.value = handleQueryData(res.data.modelOrderScheduledPieChartVOS)
    curSexData.value = res.data.modelSexPieChartVOS || []
    curTypeData.value = res.data.modelTypePieChartVOS || []
    curModelCount.value = res.data.modelCount || 0
    dateTime.value = res.data.dateTime
  })
}

onQuery()
</script>

<style lang="scss" scoped>
.status-box {
  border: 1px solid #ffffff;
  padding: 5px 20px 20px;
  box-sizing: border-box;
  border-radius: 10px;
  box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.12);
}

.info-box {
  gap: 0 10px;
  align-items: normal;
  &-left {
    width: 18%;
    min-width: 180px;
    display: grid;
    gap: 10px 0;
  }
  &-right {
    flex: 1;
    display: grid;
    grid-template-columns: repeat(2, minmax(400px, 1fr));
    gap: 10px;
  }
  &-end {
    // width: 30%;
    min-width: 250px;
  }
  .left-item {
    background: #f2f2f2;
    border-radius: 4px;
    color: #626262;
    font-size: 14px;
    padding: 15px;
    &-text {
      font-size: 12px;
    }
  }
  .flex-grid {
    display: grid;
    gap: 15px 0;
  }
}
.title-time {
  font-size: 12px;
  color: #848484;
  margin-left: 10px;
}
</style>
