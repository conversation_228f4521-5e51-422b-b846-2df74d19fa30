<template>
  <div class="rankling-box">
    <div class="flex-between">
      <TitleContent contentTip="月度时间下模特接单的排行">接单排行榜</TitleContent>
      <el-date-picker
        style="width: 150px"
        v-model="time"
        type="month"
        placeholder="请选择月份"
        format="YYYY-MM"
        value-format="YYYY-MM"
        :disabled-date="disabledDate"
        @change="handleChangeTime"
      />
    </div>
    <div class="title-time">最后更新时间：{{ writeTimeEnd }}</div>
    <div v-if="modelList.length" class="model-item" v-loading="loading">
      <div v-for="(item, index) in modelList" :key="index" class="flex-between">
        <div class="flex-center">
          <div
            class="rank flex-center"
            :class="{ first: index === 0, second: index === 1, third: index === 2 }"
          >
            {{ index + 1 }}
          </div>
          <!-- <el-image  class="img" :src="$picUrl + item.modelAvatar + '!3x4compress'" alt="" /> -->
          <img class="img" :src="$picUrl + item.modelAvatar + '!1x1compress'" alt="" />
          <div class="name one-ell">{{ item.modelName }}</div>
          <model-score
            v-if="item.cooperationScore || item.cooperationScore === 0"
            :score="item.cooperationScore"
            style="margin: 2px 0 0 5px"
          />
        </div>
        <div>{{ item.orderCount }}单</div>
      </div>
    </div>
    <template v-else>
      <el-empty description="暂无数据" />
    </template>
  </div>
</template>

<script setup>
import TitleContent from '@/views/statistics/components/TitleContent.vue'
import { modelOrderRanking } from '@/api/statistics/model'
import { getNowMonth } from '@/utils/index.js'
const time = ref(getNowMonth(1))
const loading = ref(false)

const writeTimeEnd = ref('')
const modelList = ref([])
const handleChangeTime = val => {
  time.value = val
  init()
}

const disabledDate = time => {
  const now = new Date()
  const currentMonthStart = new Date(now.getFullYear(), now.getMonth(), 1)
  return time >= currentMonthStart
}

const init = () => {
  loading.value = true
  modelOrderRanking({ date: time.value })
    .then(res => {
      writeTimeEnd.value = res.data.writeTimeEnd || ''
      modelList.value = res.data?.modelOrderRankingListVOS || []
    })
    .finally(() => {
      loading.value = false
    })
}
init()
</script>

<style lang="scss" scoped>
.rankling-box {
  // width: 20%;
  min-width: 300px;
  border: 1px solid #ffffff;
  padding: 5px 10px;
  box-sizing: border-box;
  border-radius: 10px;
  box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.12);
}

.model-item {
  display: grid;
  gap: 15px 0;
  color: #4f4f4f;
  font-size: 14px;
  // padding: 20px 0 10px 0;
  padding-bottom: 10px;
  .rank {
    width: 20px;
    height: 20px;
    border-radius: 4px;
    background: #e8eaec;
    font-size: 12px;
    margin-right: 8px;
  }
  .img {
    width: 46px;
    height: 46px;
    border-radius: 4px;
  }
  .name {
    margin-left: 5px;
    max-width: 120px;
  }
  .first {
    background: #808695;
    color: #ffffff;
  }
  .second {
    background: #c5c8ce;
    color: #ffffff;
  }
  .third {
    background: #dcdee2;
    color: #ffffff;
  }
}
.title-time {
  font-size: 12px;
  color: #848484;
  margin: 0 0 3px 10px;
}
</style>
