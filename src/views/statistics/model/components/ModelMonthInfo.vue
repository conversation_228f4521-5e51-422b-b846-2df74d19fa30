<template>
  <div class="month-box">
    <div class="left">
      <pieDate
        :options="leftObj"
        :data="addDate"
        :echartsData="leftEchartsData"
        @changeTime="handleChangeAddTime"
      />
    </div>
    <div class="right">
      <pieDate
        :options="rightObj"
        :data="reduceDate"
        type="reduce"
        :echartsData="rightEchartsData"
        @changeTime="handleChangeReduceTime"
      />
    </div>
  </div>
</template>

<script setup>
import pieDate from './pieDate.vue'
import { newModelAnalysis, oustModelAnalysis } from '@/api/statistics/model'

const leftObj = ref({
  tip: '不同月度新增模特分析（数量、平均佣金、等级占比）',
  title: '每月新增模特分析',
})
const rightObj = ref({
  tip: '不同月度淘汰模特分析（数量、平均佣金、等级占比）',
  title: '每月淘汰模特分析',
})

const leftEchartsData = ref({
  tooltip: {
    trigger: 'item',
  },
  legend: {
    selectedMode: false,
    formatter: name => {
      const dataItem = leftEchartsData.value.series[0].data.find(item => item.name === name)
      return `{name|${name}}{value|${dataItem.value || 0}个}{percent|${dataItem.ratio || 0}%}`
    },
    orient: 'vertical',
    right: '10%',
    top: 'center',
    icon: 'circle',
    itemWidth: 12,
    itemHeight: 12,
    itemGap: 18,
    textStyle: {
      rich: {
        name: {
          width: 80,
          padding: [0, 10, 0, 0],
        },
        value: {
          width: 70,
          align: 'left',
          padding: [0, 10, 0, 0],
        },
        percent: {
          width: 60,
          align: 'left',
        },
      },
    },
  },
  series: [
    {
      type: 'pie',
      radius: ['40%', '70%'],
      center: ['25%', '50%'],
      avoidLabelOverlap: false,
      label: {
        show: false,
        position: 'center',
      },
      labelLine: {
        show: false,
      },
      data: [],
    },
  ],
})
const rightEchartsData = ref({
  tooltip: {
    trigger: 'item',
  },
  legend: {
    selectedMode: false,
    formatter: name => {
      // const total = rightEchartsData.value.series[0].data.reduce((sum, item) => sum + item.value, 0)
      const dataItem = rightEchartsData.value.series[0].data.find(item => item.name === name)
      // const percent = ((dataItem.value / total) * 100).toFixed(2)
      // return `{name|${name}}{value|${dataItem}个}{percent|${dataItem.modelRatio}%}`
      return `{name|${name}}{value|${dataItem.value || 0}个}{percent|${dataItem.ratio || 0}%}`
    },
    orient: 'vertical',
    right: '10%',
    top: 'center',
    icon: 'circle',
    itemWidth: 12,
    itemHeight: 12,
    itemGap: 18,
    textStyle: {
      rich: {
        name: {
          width: 80,
          padding: [0, 10, 0, 0],
        },
        value: {
          width: 70,
          align: 'left',
          padding: [0, 10, 0, 0],
        },
        percent: {
          width: 60,
          align: 'left',
        },
      },
    },
  },
  series: [
    {
      type: 'pie',
      radius: ['40%', '70%'],
      center: ['25%', '50%'],
      avoidLabelOverlap: false,
      label: {
        show: false,
        position: 'center',
      },
      labelLine: {
        show: false,
      },
      data: [],
    },
  ],
})

const reduceDate = ref({})
const addDate = ref({})

function handleChangeReduceTime(val) {
  oustModelAnalysis({ date: val }).then(res => {
    reduceDate.value = res.data
    if (res.data.pieChartVOS && res.data.pieChartVOS.length > 0) {
      rightEchartsData.value.series[0].data = res.data.pieChartVOS.map(item => ({
        value: item.count,
        name: item.label,
        ratio: item.ratio,
      }))
    } else {
      rightEchartsData.value.series[0].data = []
    }
  })
}

function handleChangeAddTime(val) {
  newModelAnalysis({ date: val }).then(res => {
    addDate.value = res.data
    if (res.data.pieChartVOS && res.data.pieChartVOS.length > 0) {
      leftEchartsData.value.series[0].data = res.data.pieChartVOS.map(item => ({
        value: item.count,
        name: item.label,
        ratio: item.ratio,
      }))
    } else {
      leftEchartsData.value.series[0].data = []
    }
  })
}

// handleChangeReduceTime()
// handleChangeAddTime()
</script>

<style lang="scss" scoped>
.month-box {
  display: grid;
  grid-template-columns: repeat(2, minmax(750px, 1fr));
  gap: 0 10px;
  margin-top: 20px;
  .left,
  .right {
    // width: 50%;
    border: 1px solid #ffffff;
    padding: 10px 20px;
    box-sizing: border-box;
    border-radius: 10px;
    box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.12);
  }
}
</style>
