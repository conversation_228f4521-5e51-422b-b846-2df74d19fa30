<template>
  <div class="detail-box">
    <RankingList />
    <div class="detail-right">
      <ModelStatusInfo />
      <MatcheInfo />
    </div>
  </div>
</template>

<script setup>
import RankingList from './rankingList.vue'
import ModelStatusInfo from './ModelStatusInfo.vue'
import MatcheInfo from './matcheInfo.vue'
</script>

<style lang="scss" scoped>
.detail-box {
  display: flex;
  margin-top: 10px;
  gap: 0 10px;
  // align-items: normal;
}
.detail-right {
  flex: 1;
}
</style>
