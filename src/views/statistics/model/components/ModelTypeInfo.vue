<template>
  <div class="main-box">
    <div class="left">
      <div class="left-head flex-between">
        <TitleContent contentTip="不同时间状态下素人创作者和亚马逊影响者的佣金档位分析">
          订单佣金分析
        </TitleContent>
        <SelectDateBtn @change="handleChangeMathTimes" isRangeDay isUpdateTime />
      </div>
      <div class="flex-between">
        <div class="left-brokerage flex-between">
          <span>订单平均佣金（＄）</span>
          <span>{{ averageOrderCommissionV1 }}</span>
        </div>
        <div class="title-time">实时更新数据：{{ lastOrderTime }}</div>
      </div>
      <div class="left-brokerage flex-between" style="margin-top: 5px">
        <span>订单合计（单）</span>
        <span>{{ orderCount }}</span>
      </div>
      <modelEchar style="margin-top: 10px" :option="optionv1" />
    </div>
    <div class="right">
      <div class="left-head flex-between">
        <div class="flex-center">
          <TitleContent contentTip="不同时间状态下不同模特等级的素人创作者和亚马逊影响者的佣金档位分析">
            模特等级佣金分析
          </TitleContent>
          <div class="title-time" style="margin-left: 10px">实时更新数据：{{ lastDepthTime }}</div>
        </div>
        <SelectModelType v-model="modelType" />
      </div>
      <div class="flex-between">
        <div class="left-brokerage flex-between">
          <span>订单平均佣金（＄）</span>
          <span>{{ averageOrderCommissionV2 }}</span>
        </div>
        <SelectDateBtn @change="handleChangeMathTimesV2" isRangeDay isUpdateTime />
      </div>
      <div class="left-brokerage flex-between" style="margin-top: 1px">
        <span>订单合计（单）</span>
        <span>{{ orderCountV2 }}</span>
      </div>

      <modelEchar style="margin-top: 10px" :option="optionv2" />
    </div>
  </div>
</template>

<script setup>
import SelectDateBtn from '@/views/statistics/components/SelectDateBtn.vue'
import TitleContent from '../../components/TitleContent.vue'
import modelEchar from '@/views/statistics/components/modelEchar.vue'
import SelectModelType from '../../components/SelectModelType.vue'

import { modelOrderCommissionAnalysis } from '@/api/statistics/model.js'

const modelType = ref('1')
const params1 = ref({
  beginTime: '',
  endTime: '',
})
const params2 = ref({
  beginTime: '',
  endTime: '',
  modelCooperation: modelType.value,
})

watch(modelType, val => {
  params2.value.modelCooperation = val
  initQuery2()
})
const handleChangeMathTimes = val => {
  params1.value.beginTime = val[0] + ' 00:00:00'
  params1.value.endTime = val[1] + ' 23:59:59'
  initQuery1()
}
const handleChangeMathTimesV2 = val => {
  params2.value.beginTime = val[0] + ' 00:00:00'
  params2.value.endTime = val[1] + ' 23:59:59'
  initQuery2()
}

const colors = ['#5470C6', '#91CC75', '#EE6666']
const optionv1 = ref({
  color: colors,
  //   graphic: {
  //     type: 'text',
  //     invisible: false,
  //     left: 'center',
  //     top: 'middle',
  //     style: {
  //       text: '暂无数据',
  //       fontSize: 16,
  //       fill: '#909399',
  //     },
  //   },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'none',
    },
    formatter: function (params) {
      //   let xAxisValue = params[0].axisValue
      let value1 = params[0].value
      let value2 = params[1].value
      let sum = (value1 * 100 + value2 * 100) / 100
      return `
        <div style="text-align: center">
            <p>总计: ${sum}</p>
            <p>${params[0].seriesName}: ${value1}</p>
            <p>${params[1].seriesName}: ${value2}</p>
        </div>
       `
    },
  },
  grid: {
    left: '6%',
    right: '14%',
    bottom: '0%',
    containLabel: true, // 关键配置
  },
  legend: {
    orient: 'vertical',
    selectedMode: false,
    right: 10,
    icon: 'circle',
    itemWidth: 8, // 控制圆点大小
    itemHeight: 8,
    itemGap: 12, // 项间距
    textStyle: {
      color: '#666', // 文字颜色
    },
    data: ['素人创作者', '亚马逊影响者'],
  },
  xAxis: [
    {
      type: 'category',
      name: '佣金档位',
      axisLabel: {
        fontSize: 12,
        interval: 0,
      },
      axisTick: {
        show: false,
        // alignWithLabel: true,
      },
      // prettier-ignore
      data: [],
    },
  ],
  yAxis: [
    {
      type: 'value',
      name: '单位：单',
      position: 'left',
      alignTicks: true,
      splitLine: {
        show: false,
      },
      axisLine: {
        show: true,
      },
    },
  ],
  series: [
    {
      name: '素人创作者',
      type: 'bar',
      data: [],
    },
    {
      name: '亚马逊影响者',
      type: 'bar',
      data: [],
    },
  ],
})
const optionv2 = ref({
  color: colors,
  //   graphic: {
  //     type: 'text',
  //     invisible: false,
  //     left: 'center',
  //     top: 'middle',
  //     style: {
  //       text: '暂无数据',
  //       fontSize: 16,
  //       fill: '#909399',
  //     },
  //   },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'none',
    },
    formatter: function (params) {
      //   let xAxisValue = params[0].axisValue
      let value1 = params[0].value
      let value2 = params[1].value
      let sum = (value1 * 100 + value2 * 100) / 100
      return `
        <div style="text-align: center">
            <p>总计: ${sum}</p>
            <p>${params[0].seriesName}: ${value1}</p>
            <p>${params[1].seriesName}: ${value2}</p>
        </div>
       `
    },
  },
  grid: {
    left: '6%',
    right: '14%',
    bottom: '0%',
    containLabel: true, // 关键配置
  },
  legend: {
    orient: 'vertical',
    selectedMode: false,
    right: 10,
    icon: 'circle',
    itemWidth: 8, // 控制圆点大小
    itemHeight: 8,
    itemGap: 12, // 项间距
    textStyle: {
      color: '#666', // 文字颜色
    },
    data: ['素人创作者', '亚马逊影响者'],
  },
  xAxis: [
    {
      type: 'category',
      name: '佣金档位',
      axisLabel: {
        fontSize: 12,
        interval: 0,
      },
      axisTick: {
        show: false,
        // alignWithLabel: true,
      },
      // prettier-ignore
      data: [],
    },
  ],
  yAxis: [
    {
      type: 'value',
      name: '单位：单',
      position: 'left',
      alignTicks: true,
      splitLine: {
        show: false,
      },
      axisLine: {
        show: true,
      },
    },
  ],
  series: [
    {
      name: '素人创作者',
      type: 'bar',
      data: [],
    },
    {
      name: '亚马逊影响者',
      type: 'bar',
      data: [],
    },
  ],
})

const averageOrderCommissionV1 = ref(0)
const averageOrderCommissionV2 = ref(0)
const lastOrderTime = ref('')
const lastDepthTime = ref('')
const orderCount = ref(0)
const orderCountV2 = ref(0)
const initQuery1 = () => {
  modelOrderCommissionAnalysis(params1.value).then(res => {
    optionv1.value.xAxis[0].data = res.data.orderCommissionSectionArray || []
    optionv1.value.series[0].data = res.data.averagePeopleOrderCountArray || []
    optionv1.value.series[1].data = res.data.amazonInfluencerOrderCountArray || []
    averageOrderCommissionV1.value = res.data.averageOrderCommission || 0
    lastOrderTime.value = res.data.dateTime || ''
    orderCount.value = res.data.orderCount || 0
  })
}
const initQuery2 = () => {
  modelOrderCommissionAnalysis(params2.value).then(res => {
    optionv2.value.xAxis[0].data = res.data.orderCommissionSectionArray || []
    optionv2.value.series[0].data = res.data.averagePeopleOrderCountArray || []
    optionv2.value.series[1].data = res.data.amazonInfluencerOrderCountArray || []
    averageOrderCommissionV2.value = res.data.averageOrderCommission || 0
    lastDepthTime.value = res.data.dateTime || ''
    orderCountV2.value = res.data.orderCount || 0
  })
}

// initQuery1()
// initQuery2()
</script>

<style lang="scss" scoped>
.main-box {
  display: grid;
  grid-template-columns: repeat(2, minmax(750px, 1fr));
  //   grid-template-columns: repeat(2, 1fr);
  gap: 0 10px;
  margin-top: 10px;
  .left,
  .right {
    // width: 50%;
    border: 1px solid #ffffff;
    padding: 10px 20px 15px 20px;
    box-sizing: border-box;
    border-radius: 10px;
    box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.12);
  }
}
.left-head {
  display: flex;
}
.left-brokerage {
  font-size: 12px;
  color: #626262;
  background: #f2f2f2;
  width: 200px;
  padding: 4px 16px;
  border-radius: 4px;
  align-items: baseline;
}
.title-time {
  font-size: 12px;
  color: #848484;
}
</style>
