<template>
  <div class="pie-box">
    <div class="flex-between">
      <!-- <TitleContent :contentTip="options.tip">
        {{ options.title }}
      </TitleContent> -->
      <div class="flex-center">
        <TitleContent :contentTip="options.tip">{{ options.title }}</TitleContent>
        <div class="title-time">最后更新时间：{{ data.writeTimeEnd }}</div>
      </div>
      <el-date-picker
        v-model="time"
        type="month"
        placeholder="请选择月份"
        format="YYYY-MM"
        value-format="YYYY-MM"
        :disabled-date="disabledDate"
        @change="handleChangeTime"
      />
    </div>
    <div class="pie-info">
      <div class="info-left">
        <div class="info-left-date">
          <div>该月{{ type === 'add' ? '新增' : '淘汰' }}模特（人）</div>
          <div class="date-num">{{ data.modelNumber || 0 }}</div>
        </div>
        <div class="info-left-date" style="margin-top: 10px">
          <div>{{ type === 'add' ? '新增' : '淘汰' }}模特平均佣金（＄）</div>
          <div class="date-num">{{ data.modelAverageCommission || 0 }}</div>
        </div>
      </div>
      <div class="info-right">
        <modelEchar :option="echartsData" height="180px" />
      </div>
    </div>
  </div>
</template>

<script setup>
import modelEchar from '@/views/statistics/components/modelEchar.vue'
import TitleContent from '../../components/TitleContent.vue'
import { getNowMonth } from '@/utils/index.js'
const props = defineProps({
  options: {
    type: Object,
    default: () => ({}),
  },
  echartsData: {
    type: Object,
    default: () => ({}),
  },
  data: {
    type: Object,
    default: () => ({}),
  },
  type: {
    type: String,
    default: 'add',
  },
})
const emit = defineEmits(['changeTime'])
const time = ref(getNowMonth(1))

function handleChangeTime() {
  emit('changeTime', time.value)
}
const disabledDate = time => {
  const now = new Date()
  const currentMonthStart = new Date(now.getFullYear(), now.getMonth(), 1)
  return time >= currentMonthStart
}
function init() {
  emit('changeTime', time.value)
}

init()
</script>

<style lang="scss" scoped>
.pie-info {
  display: flex;
  align-items: center;
}

.info-left {
  width: 30%;
  margin-right: 20px;
  flex-shrink: 0;
  &-date {
    font-size: 14px;
    color: #626262;
    background: #f2f2f2;
    border-radius: 4px;
    padding: 10px;
  }
  .date-num {
    margin-top: 10px;
  }
}
.info-right {
  width: 70%;
}
.title-time {
  font-size: 12px;
  color: #848484;
  margin-left: 10px;
}
</style>
