<template>
  <div class="main-box">
    <div class="left-head flex-between">
      <div class="flex-center">
        <TitleContent contentTip="不同时间状态下淘汰和新增模特数量的变化趋势">模特数量趋势分析</TitleContent>
        <div class="title-time" style="margin-left: 10px">最后更新时间：{{ writeTimeEnd }}</div>
      </div>
      <SelectDateBtn
        :showDate="false"
        :defaultTime="'2'"
        :isShowWeek="false"
        @change="handleChangeMathTimes"
      />
    </div>
    <modelEchar style="margin-top: 10px" :option="option" />
  </div>
</template>

<script setup>
import SelectDateBtn from '@/views/statistics/components/SelectDateBtn.vue'
import TitleContent from '../../components/TitleContent.vue'
import modelEchar from '@/views/statistics/components/modelEchar.vue'
import { modelNumberTrendAnalysis } from '@/api/statistics/model'

const params = ref({
  beginTime: '',
  endTime: '',
})
const curType = ref('2')
const handleChangeMathTimes = (val, type) => {
  curType.value = type
  params.value.beginTime = val[0] + ' 00:00:00'
  params.value.endTime = val[1] + ' 23:59:59'
  init()
}

const colors = ['#D7D7D7', '#169BD5']
const option = ref({
  color: colors,
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'none',
    },
    formatter: function (params) {
      let xAxisValue = params[0].axisValue
      let value1 = params[0].value
      let value2 = params[1].value
      //   let sum = (value1 * 100 + value2 * 100) / 100
      return `
          <div style="text-align: center">
              <p>${xAxisValue}</p>
              <p>${params[0].seriesName}: ${value1}</p>
              <p>${params[1].seriesName}: ${value2}</p>
          </div>
         `
    },
  },
  grid: {
    left: '6%',
    right: '10%',
    bottom: '3%',
    containLabel: true,
  },
  legend: {
    orient: 'horizontal',
    selectedMode: false,
    top: 0,
    right: 200,
    icon: 'circle',
    itemWidth: 8,
    itemHeight: 8,
    itemGap: 12,
    textStyle: {
      color: '#333',
    },
    data: ['淘汰数', '新增数'],
  },
  xAxis: [
    {
      type: 'category',
      name: '单位：日',
      axisLabel: {
        fontSize: 12,
        interval: 0,
      },
      nameGap: 10,
      nameTextStyle: {
        padding: [7, 0, 0, 0],
        verticalAlign: 'top',
      },
      axisTick: {
        show: false,
      },
      // prettier-ignore
      data: [],
    },
  ],
  yAxis: [
    {
      type: 'value',
      name: '单位：个',
      position: 'left',
      nameGap: 25,
      axisLabel: {
        margin: 30,
      },
      nameTextStyle: {
        padding: [0, 0, 0, -105],
      },
      alignTicks: true,
      splitLine: {
        show: false,
      },
      axisLine: {
        show: true,
      },
    },
  ],
  series: [
    {
      name: '淘汰数',
      type: 'line',
      data: [],
    },
    {
      name: '新增数',
      type: 'line',
      data: [],
    },
  ],
})
const writeTimeEnd = ref('')
const init = () => {
  modelNumberTrendAnalysis(params.value).then(res => {
    option.value.xAxis[0].data = res.data.dateArray || []
    option.value.series[0].data = res.data.eliminatedNumberArray || []
    option.value.series[1].data = res.data.newAdditionsNumberArray || []
    option.value.xAxis[0].name = curType.value == '2' ? '单位：日' : '单位：月'
    writeTimeEnd.value = res.data.writeTimeEnd
  })
}

// init()
</script>

<style lang="scss" scoped>
.main-box {
  margin-top: 10px;
  border: 1px solid #ffffff;
  padding: 10px 20px;
  box-sizing: border-box;
  border-radius: 10px;
  box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.12);
}
.left-head {
  display: flex;
}
.left-brokerage {
  font-size: 12px;
  color: #626262;
  background: #f2f2f2;
  width: 200px;
  padding: 4px 16px;
  border-radius: 4px;
  align-items: baseline;
}
.title-time {
  font-size: 12px;
  color: #848484;
  margin-left: 10px;
}
</style>
