<template>
  <div class="container">
    <TitleHead title="模特基础信息" :time="statisticsTimes" />
    <ModelStatistics @change="handleStatisticsTimes" />

    <TitleHead style="margin-top: 1%" title="模特订单佣金分析" />
    <ModelTypeInfo />

    <TitleHead style="margin-top: 10px" title="模特数量分析" />
    <ModelCountInfo />

    <ModelMonthInfo />

    <ModelData />

    <ModelDetailBox />
  </div>
</template>

<script setup>
import TitleHead from '@/views/statistics/components/TitleHead.vue'
import ModelStatistics from '@/views/statistics/model/components/ModelStatistics.vue'
import ModelTypeInfo from '@/views/statistics/model/components/ModelTypeInfo.vue'
import ModelCountInfo from '@/views/statistics/model/components/ModelCountInfo.vue'
import ModelMonthInfo from './components/ModelMonthInfo.vue'
import ModelDetailBox from './components/ModelDetailBox.vue'
import ModelData from './components/ModelData.vue'

const statisticsTimes = ref('')
const handleStatisticsTimes = val => {
  statisticsTimes.value = val
}
</script>

<style lang="scss" scoped>
.container {
  padding: 10px 20px;
}
</style>
