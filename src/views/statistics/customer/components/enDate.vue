<template>
  <div class="en-box">
    <div>英文部客服数据</div>
    <div class="flex-grid">
      <div>
        <TitleContent :showTip="false">基础数据</TitleContent>
      </div>
      <div class="flex-between">
        <TitleContent :showTip="false">服务数据</TitleContent>
        <SelectDateBtn showDay defaultTime="4" @change="handleChangeTime" />
      </div>
    </div>
    <div class="table-box" ref="tableBoxRef">
      <!-- 中间竖线 -->
      <div class="divider-line" v-if="tableData && tableData.length > 0"></div>
      <el-table
        v-loading="loading"
        :data="tableData"
        :header-cell-style="{ 'background-color': '#ffffff !important' }"
        stripe
        ref="tableRef"
        style="height: 550px"
      >
        >
        <el-table-column label="序号" type="index" width="50"></el-table-column>
        <el-table-column label="客服姓名" min-width="180" prop="customerServiceName" align="center">
          <template #default="{ row }">
            <div>
              {{ row.customerServiceName }}
              <!-- <el-tag
                hit
                v-if="row.customerServiceStatus === 0"
                effect="light"
                type="success"
                size="small"
                round
              >
                正常
              </el-tag>
              <el-tag hit v-else effect="light" type="error" size="small" round>停用</el-tag> -->
            </div>
          </template>
        </el-table-column>
        <el-table-column label="关联模特（个）" width="180" prop="modelCount">
          <template #default="{ row }">
            <div>
              <div>{{ row.modelCount }}</div>
              <div class="tip">上月新增：{{ row.modelNewCount }} 上月淘汰：{{ row.modelOustCount }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="合作中模特（个）" width="180" prop="modelCooperationCount">
          <template #default="{ row }">
            <div>
              <div>{{ row.modelCooperationCount }}</div>
              <div class="tip">正常模特：{{ row.modelNormalCount }} 行程中：{{ row.modelTravelCount }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="服务中订单数（单）" width="180" prop="orderCount">
          <template #default="{ row }">
            <div>
              <div>{{ row.orderCount }}</div>
              <div class="tip">昨日新增：{{ row.orderNewCount }} 昨日完成：{{ row.orderFinishCount }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="合计服务订单数（单）" width="180" prop="orderTotalCount"></el-table-column>
        <el-table-column label="拖单率" prop="dragOrderRate" width="100">
          <template #default="{ row }">
            <div>{{ row.dragOrderRate }}%</div>
            <div class="tip">拖单数：{{ row.dragOrderCount }}</div>
          </template>
        </el-table-column>
        <el-table-column label="售后率" prop="afterSaleRate" width="100">
          <template #default="{ row }">
            <div>{{ row.afterSaleRate }}%</div>
            <div class="tip">售后数：{{ row.afterSaleCount }}</div>
          </template>
        </el-table-column>
        <el-table-column
          label="排单数（单）"
          :width="tableWidth * 0.5 * 0.2"
          prop="orderScheduledCount"
          fixed="right"
        >
          <template #default="{ row }">
            <div>
              <div>{{ row.orderScheduledCount }}</div>
              <div class="tip">
                素人：{{ row.orderScheduledSoleCount }} 影响者：{{ row.orderScheduledInfluencerCount }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="商家驳回数（单）"
          prop="rejectOrderCount"
          fixed="right"
          :width="tableWidth * 0.5 * 0.2"
        ></el-table-column>
        <el-table-column
          label="平均佣金（美元）"
          :width="tableWidth * 0.5 * 0.2"
          prop="averageCommission"
          fixed="right"
        >
          <template #default="{ row }">
            <div>
              <div>{{ row.averageCommission }}</div>
              <div class="tip">
                素人：{{ row.averageCommissionSole }} 影响者：{{ row.averageCommissionInfluencer }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="排单优质模特占比"
          :width="tableWidth * 0.5 * 0.2"
          prop="proportionOfQualityModel"
          fixed="right"
        >
          <template #default="{ row }">
            <div>
              <div>{{ row.proportionOfQualityModel }}%</div>
              <div class="tip">
                <!-- 中度：{{ row.proportionOfMidModel }}% -->
                一般：{{ row.proportionOfGeneralModel }}%
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="拖单数"
          :width="tableWidth * 0.5 * 0.1"
          prop="rightDragOrderCount"
          fixed="right"
        ></el-table-column>
        <el-table-column
          label="售后数"
          :width="tableWidth * 0.5 * 0.1"
          prop="rightAfterSaleCount"
          fixed="right"
        ></el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup>
import TitleContent from '@/views/statistics/components/TitleContent.vue'
import SelectDateBtn from '@/views/statistics/components/SelectDateBtn.vue'
import { getEnglishCustomerServiceData } from '@/api/statistics/customer'

const tableData = ref([])
const params = ref({
  beginTime: '',
  endTime: '',
})

const tableRef = ref(null)
const dividerRef = ref(null)
const tableBoxRef = ref(null)

const tableWidth = ref(0)
const updateTableWidth = () => {
  tableWidth.value = document.querySelector('.table-box')?.offsetWidth
}

function handleChangeTime(val) {
  params.value.beginTime = val[0] + ' 00:00:00'
  params.value.endTime = val[1] + ' 23:59:59'
  init()
}
const loading = ref(false)
function init() {
  loading.value = true
  getEnglishCustomerServiceData(params.value)
    .then(res => {
      tableData.value = res.data
    })
    .finally(() => {
      loading.value = false
    })
}

onMounted(() => {
  updateTableWidth()
  window.addEventListener('resize', updateTableWidth)
})
</script>

<style lang="scss" scoped>
.en-box {
  margin-top: 15px;
  border: 1px solid #ffffff;
  padding: 20px 30px;
  box-sizing: border-box;
  border-radius: 4px;
  box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.12);
}
.flex-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  // grid-template-columns: 3fr 2fr;
}

.table-box {
  position: relative;
}

.divider-line {
  z-index: 99 !important;
  position: absolute;
  left: 50.2%;
  transform: translateX(-1px);
  width: 2px;
  height: 100%;
  background-color: #ebeef5;
  z-index: 1;
}
.tip {
  color: #7f7f7f;
  font-size: 12px;
}
</style>
