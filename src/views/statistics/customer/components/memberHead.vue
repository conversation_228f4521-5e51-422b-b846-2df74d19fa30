<template>
  <div class="member-head">
    <div class="member-head__item member-head__box">
      <div class="head-title">服务中订单数</div>
      <div class="head-value">
        {{ data.serviceOrderCount }}
        <span class="head-value-ic">单</span>
      </div>
      <el-divider class="head-divider" />
      <div class="head-text">
        <span>昨日新增 {{ data.orderNewCount }}</span>
        <span class="head-text-second">昨日完成 {{ data.orderFinishCount }}</span>
      </div>
    </div>
    <div class="member-head__item member-head__box">
      <div class="head-title flex-start">
        昨日排单数
        <el-tooltip
          popper-class="public-white-tooltips"
          content="包含被驳回的单数"
          :hide-after="0"
          placement="top"
        >
          <template #content>
            <div class="tip-box">包含被驳回的单数</div>
          </template>
          <el-icon class="icon" size="18" color="#c5c8ce"><QuestionFilled /></el-icon>
        </el-tooltip>
      </div>
      <div class="head-value">
        {{ data.orderScheduledCount }}
        <span class="head-value-ic">单</span>
      </div>
      <el-divider class="head-divider" />
      <div class="head-text">
        <span>素人 {{ data.orderScheduledSoleCount }}</span>
        <span class="head-text-second">影响者 {{ data.orderScheduledInfluencerCount }}</span>
      </div>
    </div>
    <div class="member-head__item member-head__box">
      <div class="head-title">昨日被驳回模特次数</div>
      <div class="head-value">
        {{ data.modelRejectCount }}
        <span class="head-value-ic">次</span>
      </div>
      <el-divider class="head-divider" />
      <div class="head-text">
        <span>素人 {{ data.modelRejectSoleCount }}</span>
        <span class="head-text-second">影响者 {{ data.modelRejectInfluencerCount }}</span>
      </div>
    </div>
    <div class="member-head__item member-head__box">
      <div class="head-title">昨日平均佣金</div>
      <div class="head-value">
        {{ data.averageCommission }}
        <span class="head-value-ic">美元</span>
      </div>
      <el-divider class="head-divider" />
      <div class="head-text">
        <span>素人 {{ data.averageCommissionSole }}</span>
        <span class="head-text-second">影响者 {{ data.averageCommissionInfluencer }}</span>
      </div>
    </div>
    <!-- <div class="member-head__item member-head__time">最后更新时间： {{ getBeforeDate(1) + ' 23:59:59' }}</div> -->
  </div>
</template>

<script setup>
import { getCustomerServiceBaseBoard } from '@/api/statistics/customer'
import { getBeforeDate } from '@/utils/index'

const data = ref({})
function init() {
  getCustomerServiceBaseBoard().then(res => {
    data.value = res.data
  })
}

init()
</script>

<style lang="scss" scoped>
.member-head {
  display: flex;
  justify-content: flex-start;
  gap: 0 20px;
  &__item {
    flex: 1;
    // flex: 1 1 300px;
    // min-width: 280px; // 设置最小宽度
  }
  &__box {
    border: 1px solid #ffffff;
    padding: 20px 30px;
    box-sizing: border-box;
    border-radius: 4px;
    box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.12);
  }
  &__time {
    display: flex;
    align-items: end;
    font-size: 13px;
    color: #333;
    padding: 0 30px;
    box-sizing: border-box;
  }
  .head-title {
    color: #0000006d;
    font-size: 14px;
  }
  .head-value {
    margin-top: 5px;
    font-size: 30px;
    color: #000000d8;
    &-ic {
      font-size: 14px;
      margin-left: 5px;
    }
  }
  .head-divider {
    margin: 15px 0 10px 0;
    border-top-color: #0000006d;
  }
  .head-text {
    font-size: 13px;
    color: #000000a5;
    &-second {
      margin-left: 15px;
    }
  }
}
</style>
