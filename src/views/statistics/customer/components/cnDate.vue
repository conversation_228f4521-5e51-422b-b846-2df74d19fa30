<template>
  <div class="cn-box">
    <div>中文部客服数据</div>
    <div class="flex-grid">
      <div>
        <TitleContent :showTip="false">基础数据</TitleContent>
      </div>
      <div class="flex-between">
        <TitleContent :showTip="false">服务数据</TitleContent>
        <SelectDateBtn showDay defaultTime="" isClear @change="handleChangeTime" />
      </div>
    </div>
    <div class="table-box">
      <!-- 中间竖线 -->
      <div class="divider-line" v-if="tableData && tableData.length > 0"></div>
      <el-table
        v-loading="loading"
        :data="tableData"
        :header-cell-style="{ 'background-color': '#ffffff !important' }"
        stripe
        style="width: 100%; height: 350px"
      >
        <el-table-column label="序号" type="index" width="50"></el-table-column>
        <el-table-column label="客服姓名" prop="customerServiceName" align="center">
          <template #default="{ row }">
            <div>
              {{ row.customerServiceName }}
              <!-- <el-tag
                hit
                v-if="row.customerServiceStatus === 0"
                effect="light"
                type="success"
                size="small"
                round
              >
                正常
              </el-tag>
              <el-tag hit v-else effect="light" type="error" size="small" round>停用</el-tag> -->
            </div>
          </template>
        </el-table-column>
        <el-table-column label="服务客户数（个）" prop="customerCount" width="130"></el-table-column>
        <el-table-column label="服务中订单数（单）" prop="orderCount" width="180">
          <template #default="{ row }">
            <div>
              <div>{{ row.orderCount }}</div>
              <div class="tip">昨日新增：{{ row.orderNewCount }} 昨日完成：{{ row.orderFinishCount }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="合计服务订单数（单）" prop="orderTotalCount" width="160"></el-table-column>
        <el-table-column label="售后率" prop="afterSaleRate" width="100">
          <template #default="{ row }">
            <div>{{ row.afterSaleRate }}%</div>
            <div class="tip">售后数：{{ row.afterSaleCount }}</div>
          </template>
        </el-table-column>
        <el-table-column
          label="任务单（个）"
          :width="tableWidth * 0.25"
          prop="taskCount"
          class-name="task-count"
          fixed="right"
        >
          <template #default="{ row }">
            <div>
              <div v-if="params.beginTime">
                新增：{{ row.taskNewCountInTime }} 完成：{{ row.taskFinishCountInTime }}
              </div>

              <template v-else>
                <div>{{ row.taskCount }}</div>
                <div class="tip">昨日新增：{{ row.taskNewCount }} 昨日完成：{{ row.taskFinishCount }}</div>
              </template>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="售后数"
          :width="tableWidth * 0.25"
          prop="rightAfterSaleCount"
          fixed="right"
        ></el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup>
import TitleContent from '@/views/statistics/components/TitleContent.vue'
import SelectDateBtn from '@/views/statistics/components/SelectDateBtn.vue'
import { useDebounceFn } from '@vueuse/core'
import { getChineseCustomerServiceData } from '@/api/statistics/customer.js'

const tableData = ref([])
const tableWidth = ref(0)
const params = ref({
  beginTime: '',
  endTime: '',
})

// 创建防抖函数（300ms延迟）
// const updateTableWidth = useDebounceFn(() => {
//   tableWidth.value = document.querySelector('.table-box').offsetWidth
// }, 300)
const updateTableWidth = () => {
  tableWidth.value = document.querySelector('.table-box')?.offsetWidth
}

function handleChangeTime(val) {
  if (val && val.length && val.length > 0) {
    params.value.beginTime = val[0] + ' 00:00:00'
    params.value.endTime = val[1] + ' 23:59:59'
  } else {
    params.value.beginTime = ''
    params.value.endTime = ''
  }

  init()
}
const loading = ref(false)
function init() {
  loading.value = true
  getChineseCustomerServiceData(params.value)
    .then(res => {
      tableData.value = res.data
    })
    .finally(() => {
      loading.value = false
    })
}

onMounted(() => {
  updateTableWidth()
  window.addEventListener('resize', updateTableWidth)
})
</script>

<style lang="scss" scoped>
.cn-box {
  margin-top: 15px;
  border: 1px solid #ffffff;
  padding: 20px 30px;
  box-sizing: border-box;
  border-radius: 4px;
  box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.12);
}
.flex-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
}

.table-box {
  position: relative;
  :deep(.el-table) {
    .task-count {
      padding-left: 20px;
    }
  }
  // max-height: 280px;
  // overflow-y: auto;
}
.divider-line {
  z-index: 99 !important;
  position: absolute;
  left: 50%;
  transform: translateX(-1px);
  width: 2px;
  height: 100%;
  background-color: #ebeef5;
  z-index: 1;
}
.tip {
  color: #7f7f7f;
  font-size: 12px;
}
</style>
