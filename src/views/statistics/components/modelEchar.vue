<template>
  <div ref="container" :style="{ width: props.width, height: props.height }"></div>
</template>

<script setup>
import * as echarts from 'echarts'
import { useDebounceFn } from '@vueuse/core'

// props
const props = defineProps({
  option: {
    type: [Object, null],
    required: true,
  }, //配置项
  width: {
    type: String,
    default: '100%',
  },
  //可屏幕适配 echarts文字大小需动态计算
  height: {
    type: String,
    default: '250px',
  },
  loading: {
    type: Boolean,
    default: false,
  },
})

const container = ref(null) // 容器元素
const instance = shallowRef(null) // 实例
const resizeObserver = shallowRef(null) // 元素尺寸侦听器
const debounceTimer = ref(null) //防抖计时器id

function init() {
  instance.value = echarts.getInstanceByDom(container.value)
  if (!instance.value) {
    instance.value = echarts.init(container.value)
  }
  draw()
}

function draw() {
  if (!props.option || !instance.value) return
  instance.value.setOption(props.option, {
    notMerge: true,
  })
}

// 图表自适应
// 重置图表尺寸
function resize() {
  clearTimeout(debounceTimer.value)
  debounceTimer.value = setTimeout(() => {
    instance.value?.resize({
      animation: {
        duration: 300,
      },
    })
    debounceTimer.value = null
  }, 300)
}
resizeObserver.value = new ResizeObserver(resize)

// 重绘图表
watch(props, () => {
  nextTick(() => {
    draw()
  })
})

// watchEffect(() => {
//   props.loading ? instance.value?.showLoading() : instance.value?.hideLoading()
// })

onMounted(() => {
  nextTick(() => {
    init()
    resizeObserver.value.observe(container.value)
  })
})

onUnmounted(() => {
  instance.value?.dispose()
  resizeObserver.value?.disconnect()
  resizeObserver.value = null
  clearTimeout(debounceTimer.value)
  debounceTimer.value = null
})

defineExpose({ getInstance: () => instance.value, draw, resize })
</script>

<style lang="scss" scoped></style>
