<template>
  <div class="title">
    <div class="mark"></div>
    <slot></slot>
    <el-tooltip
      v-if="showTip"
      popper-class="public-white-tooltips"
      :content="contentTip"
      :hide-after="0"
      placement="top"
    >
      <template #content>
        <div class="tip-box">
          {{ contentTip }}
        </div>
      </template>
      <el-icon class="icon" size="18" color="#c5c8ce"><QuestionFilled /></el-icon>
    </el-tooltip>
  </div>
</template>

<script setup>
const props = defineProps({
  contentTip: {
    type: String,
    default: '',
  },
  showTip: {
    type: Boolean,
    default: true,
  },
})
</script>

<style lang="scss">
.public-white-tooltips {
  background: rgb(48 49 51 / 70%) !important;
  border: 1px solid transparent !important;
  .el-popper__arrow {
    &::before {
      display: block;
      border: 4px solid rgb(48 49 51 / 70%) !important;
      width: 0;
      height: 0;
      border-left-color: transparent !important;
      border-top-color: transparent !important;
      background: transparent !important;
      transform: translate(-2px, 2px) rotateZ(45deg) !important;
    }
  }
}
</style>

<style scoped lang="scss">
.title {
  position: relative;
  color: #4f4f4f;
  font-size: 14px;
  display: flex;
  align-items: center;
  margin: 15px 0;

  .mark {
    display: inline-block;
    width: 8px;
    height: 8px;
    //   background: var(--el-color-primary);
    background: #c5c8ce;
    margin-right: 8px;
  }

  .icon {
    margin-left: 8px;
  }
}
.tip-box {
  width: 200px;
  white-space: pre-wrap;
  max-height: 45vh;
  overflow-y: auto;
  text-align: center;
}
</style>
