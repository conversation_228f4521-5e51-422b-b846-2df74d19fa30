<template>
  <div>
    <modelEchar :option="echartsData" height="180px" />
  </div>
</template>

<script setup>
import modelEchar from '@/views/statistics/components/modelEchar.vue'

const echartsData = ref({
  tooltip: {
    trigger: 'item',
  },
  legend: {
    formatter: name => {
      const total = leftEchartsData.value.series[0].data.reduce((sum, item) => sum + item.value, 0)
      const dataItem = leftEchartsData.value.series[0].data.find(item => item.name === name)
      const percent = ((dataItem.value / total) * 100).toFixed(2)
      return `{name|${name}}{value|${dataItem.value}个}{percent|${percent}%}`
    },
    orient: 'vertical',
    right: '10%',
    top: 'center',
    icon: 'circle',
    itemWidth: 12,
    itemHeight: 12,
    itemGap: 18,
    textStyle: {
      rich: {
        name: {
          width: 80,
          padding: [0, 10, 0, 0],
        },
        value: {
          width: 70,
          align: 'left',
          padding: [0, 10, 0, 0],
        },
        percent: {
          width: 60,
          align: 'left',
        },
      },
    },
  },
  series: [
    {
      type: 'pie',
      radius: ['40%', '70%'],
      center: ['25%', '50%'],
      avoidLabelOverlap: false,
      label: {
        show: false,
        position: 'center',
      },
      labelLine: {
        show: false,
      },
      data: [
        { value: 1048, name: '一般模特' },
        { value: 735, name: '中度模特' },
        { value: 580, name: '优质模特' },
      ],
    },
  ],
})
</script>

<style lang="scss" scoped></style>
