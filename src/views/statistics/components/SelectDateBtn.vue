<template>
  <div class="flex-center">
    <el-radio-group v-if="showBtn" v-model="times" @change="handleChangeMathTimes">
      <el-radio-button v-for="item in filteredTimeOptions" :value="item.value">
        {{ item.label }}
      </el-radio-button>
    </el-radio-group>
    <slot name="picker"></slot>
    <div style="width: 250px; margin-left: 10px; display: flex" v-if="showDate">
      <el-date-picker
        popper-class="picker-range"
        style="width: 250px"
        v-model="datePicker"
        type="datetimerange"
        range-separator="-"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        format="YYYY-MM-DD"
        value-format="YYYY-MM-DD"
        :shortcuts="shortcuts"
        @change="handleDatePickerChange"
        @clear="handleDatePickerClear"
      />
    </div>
  </div>
</template>

<script setup>
import { getBeforeDate } from '@/utils/index'
import { dayjs } from 'element-plus'

const props = defineProps({
  showDate: {
    type: Boolean,
    default: true,
  },
  showBtn: {
    type: Boolean,
    default: true,
  },
  isShowWeek: {
    type: Boolean,
    default: true,
  },
  defaultTime: {
    type: String,
    default: '1',
  },
  showDay: {
    type: Boolean,
    default: false,
  },
  isClear: {
    type: Boolean,
    default: false,
  },
  isRangeDay: {
    type: Boolean,
    default: false,
  },
  isUpdateTime: {
    type: Boolean,
    default: false,
  },
})
const shortcuts = [
  {
    text: '今天',
    value: () => {
      const start = new Date()
      start.setHours(0, 0, 0, 0)
      const end = new Date()
      end.setHours(23, 59, 59, 59)
      return [start, end]
    },
  },
]
const times = ref(props.defaultTime)
const datePicker = ref([])
const curDateRage = ref([])
const timeOptions = ref([
  {
    label: '近一周',
    value: '1',
    isShow: props.isShowWeek,
  },
  {
    label: '近一月',
    value: '2',
  },
  {
    label: '近一年',
    value: '3',
  },
])

const timeOptionsV1 = ref([
  {
    label: '昨日',
    value: '4',
  },
  {
    label: '上周',
    value: '5',
  },
  {
    label: '上月',
    value: '6',
  },
])

// 添加计算属性过滤选项
const filteredTimeOptions = computed(() => {
  if (!props.showDay) {
    return timeOptions.value.filter(item => item.isShow !== false)
  } else {
    return timeOptionsV1.value
  }
})

const emit = defineEmits(['change'])

const getLastWeekRange = () => {
  // dayjs.weekStart = 1
  // dayjs.locale('de')
  const today = dayjs()
  const lastMonday = today.subtract(7, 'days').startOf('week').add(1, 'day')
  const lastSunday = lastMonday.add(6, 'days')
  return [lastMonday.format('YYYY-MM-DD'), lastSunday.format('YYYY-MM-DD')]
  // return [
  //   today.subtract(1, 'week').startOf('week').format('YYYY-MM-DD'),
  //   today.subtract(1, 'week').endOf('week').format('YYYY-MM-DD'),
  // ]
}
function getLastMonthRange() {
  const baseDate = dayjs()
  return [
    baseDate.subtract(1, 'month').startOf('month').format('YYYY-MM-DD'),
    baseDate.subtract(1, 'month').endOf('month').format('YYYY-MM-DD'),
  ]
}
function getLastYearMonths() {
  const currentMonth = dayjs()
  const startMonth = currentMonth.clone().subtract(1, 'year').add(1, 'month').startOf('month')

  const endMonth = currentMonth.clone().endOf('month')

  return [startMonth.format('YYYY-MM-DD'), endMonth.format('YYYY-MM-DD')]
}
const handleChangeMathTimes = value => {
  if (!props.showBtn) return
  if (value && value != '') {
    if (value == 1) {
      curDateRage.value = props.isUpdateTime
        ? [getBeforeDate(6), getBeforeDate(0)]
        : [getBeforeDate(7), getBeforeDate(1)]
    } else if (value == 2) {
      curDateRage.value = props.isUpdateTime
        ? [getBeforeDate(29), getBeforeDate(0)]
        : [getBeforeDate(30), getBeforeDate(1)]
    } else if (value == 3) {
      props.isRangeDay
        ? (curDateRage.value = [getBeforeDate(365), getBeforeDate(0)])
        : (curDateRage.value = getLastYearMonths())
    } else if (value == 4) {
      curDateRage.value = [getBeforeDate(1), getBeforeDate(1)]
    } else if (value == 5) {
      curDateRage.value = getLastWeekRange()
    } else if (value == 6) {
      curDateRage.value = getLastMonthRange()
    }
    datePicker.value = curDateRage.value
  }
  emit('change', curDateRage.value, value)
}

const handleDatePickerChange = val => {
  if (!val || val.length === 0) {
    if (props.isClear) {
      times.value = ''
      emit('change', val)
    } else if (props.showBtn) {
      handleChangeMathTimes(times.value)
    }
  } else {
    emit('change', val)
  }
}

const handleDatePickerClear = () => {
  emit('change', [])
}

handleChangeMathTimes(props.defaultTime)
</script>

<style lang="scss" scoped>
:global(.picker-range .el-date-range-picker__time-header) {
  display: none;
}
</style>
