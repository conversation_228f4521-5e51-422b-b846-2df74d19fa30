<template>
  <div class="flex-center">
    <el-radio-group :model-value="props.modelValue" @change="handleModelChange">
      <el-radio-button v-for="item in options" :value="item.value">
        {{ item.label }}
      </el-radio-button>
    </el-radio-group>
  </div>
</template>

<script setup>
const props = defineProps({
  modelValue: {
    type: String,
    default: '1',
  },
})
const emits = defineEmits(['update:modelValue'])

const options = ref([
  {
    label: '优质模特',
    value: '1',
  },
  // {
  //   label: '中度模特',
  //   value: '2',
  // },
  {
    label: '一般模特',
    value: '0',
  },
])

const handleModelChange = val => {
  emits('update:modelValue', val)
}
</script>

<style lang="scss" scoped></style>
