<template>
  <div class="flex-between">
    <div class="flex-center" style="align-items: end">
      <Title style="margin: 0">{{ title }}</Title>
      <div class="title-time" v-if="time">
        {{ isLastTimes ? '最后更新时间：' : '实时更新数据：' }}{{ time }}
      </div>
    </div>
    <el-date-picker
      v-if="showDatePicker"
      v-model="dataValue"
      type="date"
      placeholder="请选择日期"
      @change="emit('changeDate', dataValue)"
      format="YYYY-MM-DD"
      value-format="YYYY-MM-DD"
      clearable
    />
  </div>
</template>

<script setup>
import Title from '@/components/Public/title'

const props = defineProps({
  title: {
    type: String,
    default: '标题',
  },
  time: {
    type: String,
    default: '',
  },
  showDatePicker: {
    type: Boolean,
    default: false,
  },
  isLastTimes: {
    type: Boolean,
    default: false,
  },
})

const dataValue = ref('')
const emit = defineEmits(['changeDate'])
</script>

<style lang="scss" scoped>
.title-time {
  margin-left: 10px;
  font-size: 14px;
  color: #848484;
}
</style>
