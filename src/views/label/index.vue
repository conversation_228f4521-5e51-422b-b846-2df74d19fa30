<template>
  <div>
    <!-- 模特标签 -->
    <el-tabs v-model="activeName" class="demo-tabs" @tab-change="handleClick">
      <el-tab-pane label="模特标签" name="modelLabel" v-if="checkPermi(['tag:model:list'])">
        <div class="main">
          <div>
            <elLabelTable labelType="1009" ref="labelTable" v-if="activeName === 'modelLabel'" />
          </div>
        </div>
      </el-tab-pane>
      <!-- 类目标签 -->
      <el-tab-pane label="类目标签" name="second" v-if="checkPermi(['tag:category:list'])">
        <template #label>
          <span class="custom-tabs-label">类目标签</span>
        </template>
        <elLabelTable labelType="1008" v-if="activeName === 'second'" />
      </el-tab-pane>

      <el-tab-pane label="拍摄标签" name="three" v-if="checkPermi(['tag:shoot:list'])">
        <template #label>
          <span class="custom-tabs-label">拍摄标签</span>
        </template>
        <elLabelTable labelType="1010" v-if="activeName === 'three'" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script setup>
import elLabelTable from './components/elLabelTable.vue'
import { checkPermi } from '@/utils/permission'
const labelTable = ref(null)
// tabs页
const activeName = ref('modelLabel')

function handleClick(tabName) {
  // let id = null
  // tabName =='second' ? id = 1008 : id = 1009
  // labelTable.value?.sortTableDestory()
  // labelTable.value?.getmodelLabelSelect()
}
onMounted(() => {
  if (!checkPermi(['tag:model:list'])) {
    activeName.value = 'second'
  }
  if (!checkPermi(['tag:model:list']) && !checkPermi(['tag:category:list'])) {
    activeName.value = 'three'
  }
})
</script>

<style lang="scss" scoped>
.el-tabs {
  margin: 10px 20px;
  .main {
    display: flex;
    flex-direction: column;
    font-size: 16px;
    color: #000;
    font-weight: normal;
    .main-top {
      display: flex;
      justify-content: flex-start;
      margin-bottom: 20px;
      .main-text {
        margin-right: 20px;
      }
      .main-space {
        margin-right: 5%;
      }
    }
    .margin-spaces {
      margin-bottom: 20px;
    }
  }
}
.demo-tabs > .el-tabs__content {
  color: #6b778c;
  font-size: 32px;
  font-weight: 600;
}

.demo-pagination-block {
  float: right;
  margin-right: 3%;
}
.el-tabs .el-tabs__nav {
  justify-content: flex-start;
}
</style>
