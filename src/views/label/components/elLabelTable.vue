<template>
  <!-- 类目标签 -->
  <div class="main">
    <div class="main-top">
      <div class="main-space">
        <span class="main-text">标签名称</span>
        <el-input
          v-model="input"
          style="width: 166px"
          placeholder="请输入"
          @keyup.enter="getmodelLabelSelect"
        />
      </div>
      <div class="main-space">
        <span class="main-text">状态</span>
        <el-select v-model="select" placeholder="请选择" style="width: 115px" clearable>
          <el-option label="启用状态" value="0" />
          <el-option label="禁用状态" value="1" />
        </el-select>
      </div>

      <div class="main-space">
        <el-button v-btn type="primary" @click="getmodelLabelSelect">搜索</el-button>
        <el-button v-btn type="primary" @click="resetLabel" plain>重置</el-button>
      </div>
    </div>
    <div class="margin-spaces" v-if="labelType != '1010'">
      <el-button
        v-btn
        type="primary"
        v-hasPermi="[labelType == '1009' ? 'tag:model:add' : 'tag:category:add']"
        icon="Plus"
        @click="addLabel"
      >
        新增一级标签
      </el-button>
    </div>
    <div>
      <!-- <SortTable /> -->
      <el-table
        v-loading="tableLoading"
        :data="paginatedData"
        ref="tableContainer"
        row-key="key"
        :expand-row-keys="treeTableExpandKeys"
        @expand-change="treeTableExpandChange"
        :header-cell-style="{ 'text-align': 'center' }"
        :cell-style="{ 'text-align': 'center' }"
      >
        <el-table-column label="序号" width="100" type="">
          <template #default="scope">
            <span v-if="scope.row.parentId == '0'">{{ paginatedData.indexOf(scope.row) + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column label="一级标签" width="200">
          <template #default="scope">
            <span>{{ scope.row.name }}</span>
          </template>
        </el-table-column>

        <el-table-column label="标签名称" width="110" show-overflow-tooltip>
          <template #default="scope">
            <div>
              <el-tag
                style="width: 90px; display: block; line-height: 24px; text-align: center"
                class="one-ell"
              >
                {{ scope.row.name }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="备注" show-overflow-tooltip v-if="labelType != '1010'">
          <template #default="scope">
            {{ scope.row.remark }}
          </template>
        </el-table-column>
        <el-table-column label="英文名称" show-overflow-tooltip width="160" v-if="labelType == '1010'">
          <template #default="scope">
            <div style="display: flex; justify-content: center">
              <el-tag
                style="width: 90px; display: block; line-height: 24px; text-align: center"
                class="one-ell"
              >
                {{ scope.row.name }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="创建信息" min-width="200">
          <template #default="scope">
            <div>创建人：{{ scope.row.createUser.name }}</div>
            <div>创建时间：{{ scope.row.createTime }}</div>
          </template>
        </el-table-column>
        <el-table-column label="状态">
          <template #default="scope">
            <el-switch v-model="scope.row.status" @change="handleSwitchChange(scope.row)" />
          </template>
        </el-table-column>
        <el-table-column
          label="排序"
          v-hasPermi="[
            labelType == '1009'
              ? 'tag:model:sort'
              : labelType == '1008'
              ? 'tag:category:sort'
              : 'tag:shoot:sort',
          ]"
        >
          <template #default="{ row, $index }">
            <div
              class="drag-icon"
              :class="[`drag-icon-${labelType}`]"
              @dbClick.prevent
              @mousedown="rowDrop()"
            >
              <div>—</div>
              <div>—</div>
              <div>—</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-space size="large" style="cursor: pointer">
              <template v-if="labelType == '1010'">
                <el-button
                  v-btn
                  v-hasPermi="['tag:shoot:sub']"
                  type="primary"
                  size="small"
                  link
                  v-if="scope.row.level == 1 || scope.row.level == 2 ? false : true"
                  @click="addShootLabels(scope.row.id, scope.row.level)"
                >
                  新增子标签
                </el-button>
                <el-button
                  v-btn
                  type="primary"
                  v-hasPermi="['tag:shoot:edit']"
                  size="small"
                  link
                  @click="amendLabel(scope.row.id, scope.row)"
                >
                  修改
                </el-button>
              </template>
              <template v-else>
                <el-button
                  v-btn
                  v-hasPermi="[labelType == '1009' ? 'tag:model:sub' : 'tag:category:sub']"
                  type="primary"
                  size="small"
                  link
                  v-if="scope.row.level == 2 ? false : true"
                  @click="addLabels(scope.row.id, scope.row.level)"
                >
                  新增子标签
                </el-button>
                <el-button
                  v-btn
                  type="primary"
                  v-hasPermi="[labelType == '1009' ? 'tag:model:edit' : 'tag:category:edit']"
                  size="small"
                  link
                  @click="amendLabel(scope.row.id, scope.row)"
                >
                  修改
                </el-button>
              </template>
              <el-button
                v-btn
                type="primary"
                v-hasPermi="[
                  labelType == '1009'
                    ? 'tag:model:delete'
                    : labelType == '1008'
                    ? 'tag:category:delete'
                    : 'tag:shoot:delete',
                ]"
                size="small"
                link
                @click="delLabels(scope.row.id)"
              >
                删除
              </el-button>
            </el-space>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
  <!-- <div class="flex-end" style="margin-top: 10px;">
    <el-pagination
      background
      :current-page="currentPage"
      :page-size="pageSize"
      :page-sizes="[5, 10, 15, 20]"
      :total="numPage"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div> -->

  <!-- 弹出层 -->
  <!-- 一级标签 -->
  <el-dialog
    v-model="dialogVisible"
    title="添加一级标签"
    width="500"
    align-center
    :before-close="handleClose"
  >
    <div>
      <el-form :model="ruleForm" label-width="auto" style="max-width: 600px" :rules="rules" ref="addRef">
        <el-form-item label="一级标签名称" prop="name">
          <el-input v-model="ruleForm.name" maxlength="50" show-word-limit />
        </el-form-item>
        <el-form-item label="状态" prop="resource">
          <el-radio-group v-model="ruleForm.resource">
            <el-radio :value="0">启用</el-radio>
            <el-radio :value="1">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="ruleForm.desc" maxlength="32" type="textarea" show-word-limit />
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button v-btn type="primary" plain @click="cancelOneLabel">取消</el-button>
        <el-button v-btn type="primary" @click="submitForm">保存</el-button>
      </div>
    </template>
  </el-dialog>
  <!-- 子类标签 -->
  <el-dialog
    v-model="dialogVisibles"
    title="添加子类标签"
    width="500"
    align-center
    :before-close="handleChildLabel"
  >
    <div>
      <el-form :model="ruleForms" label-width="auto" style="max-width: 600px" :rules="rules" ref="formRef">
        <el-form-item label="子标签名称" prop="name">
          <el-input v-model="ruleForms.name" maxlength="50" show-word-limit />
        </el-form-item>
        <el-form-item label="所属标签">
          <el-tree-select
            v-model="beautySubtag"
            :data="Firstlabel"
            check-strictly
            node-key="value"
            :render-after-expand="false"
            style="width: 240px"
            :props="{
              children: 'children',
              label: 'label',
              value: 'value',
            }"
          />
        </el-form-item>
        <el-form-item label="状态" prop="resource">
          <el-radio-group v-model="ruleForms.resource">
            <el-radio :value="0">启用</el-radio>
            <el-radio :value="1">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="ruleForms.desc" maxlength="32" type="textarea" show-word-limit />
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button v-btn type="primary" plain @click="cancelForms">取消</el-button>
        <el-button v-btn type="primary" @click="submitForms">保存</el-button>
      </div>
    </template>
  </el-dialog>
  <!-- 删除标签 -->
  <el-dialog
    class="dialogtoo"
    v-model="deldialog"
    width="500"
    style="text-align: center"
    title="确认删除吗？"
    align-center
  >
    <template #footer>
      <el-button v-btn type="primary" plain @click="deldialog = false">取消</el-button>
      <el-button v-btn type="primary" @click="delLabelclick">确定</el-button>
    </template>
  </el-dialog>

  <!-- 修改 -->
  <el-dialog v-model="amendialogVisible" title="修改" width="500" align-center :before-close="handleAmend">
    <div>
      <el-form
        :model="amendrulForm"
        label-width="auto"
        style="max-width: 600px"
        :rules="rules"
        ref="editForm"
      >
        <el-form-item label="标签名称" prop="name">
          <el-input v-model="amendrulForm.name" maxlength="50" show-word-limit />
        </el-form-item>
        <el-form-item label="状态" prop="resource">
          <el-radio-group v-model="amendrulForm.resource">
            <el-radio :value="0">启用</el-radio>
            <el-radio :value="1">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="amendrulForm.desc" maxlength="32" type="textarea" show-word-limit />
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button v-btn type="primary" plain @click="cancelForm">取消</el-button>
        <el-button v-btn type="primary" @click="amendsubmitForm">保存</el-button>
      </div>
    </template>
  </el-dialog>

  <el-dialog
    align-center
    append-to-body
    title="添加二级标签"
    v-model="showShootDialog"
    width="600px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="closeShootDialog"
  >
    <el-form label-width="120px" :model="shootForm" :rules="shootFormRules" ref="shootFormRef">
      <el-form-item label="二级标签名称" prop="cnName">
        <el-input maxlength="10" v-model="shootForm.cnName" placeholder="填写字段名称" clearable />
      </el-form-item>
      <el-form-item label="字段英文名" prop="enName">
        <el-input
          maxlength="50"
          v-model="shootForm.enName"
          placeholder="英文名将会展示给模特，请确认翻译无误后填写"
          clearable
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="closeShootDialog">取消</el-button>
      <el-button type="primary" @click="handleShootForm">保存</el-button>
    </template>
  </el-dialog>
</template>
<script setup name="subcomponent">
import { ElMessage } from 'element-plus'
import Sortable from 'sortablejs'
import { checkPermi } from '@/utils/permission'
import {
  getModelTagList,
  getCategoryTagList,
  deleteCategoryTag,
  deleteModelTag,
  addCategoryTag,
  addModelTag,
  editCategoryTag,
  editModelTag,
  getParentLabel,
  modelTagSort,
  categoryTagSort,
} from '@/api/label/label'

const { proxy } = getCurrentInstance()
const tableLoading = ref(false)
const loadingStatus = ref(false)

defineExpose({ sortTableDestory, getmodelLabelSelect })

const props = defineProps({
  labelType: {
    type: [Number, String],
  },
})

//状态
function handleSwitchChange(val) {
  if (!checkPermi([props.labelType == '1009' ? 'tag:model:status' : 'tag:category:status'])) {
    if (val.level == '0') {
      const index = paginatedData.value.findIndex(item => item.id == val.id)
      paginatedData.value[index].status = !val.status
    }
    if (val.level == '1') {
      const pIndex = paginatedData.value.findIndex(item => item.id == val.parentId)
      const cIndex = paginatedData.value[pIndex].children.findIndex(item => item.id == val.id)
      paginatedData.value[pIndex].children[cIndex].status = !val.status
    }
    if (val.level == '2') {
      const tempIndex = allList.value.findIndex(item => item.id == val.parentId)
      const pIndex = paginatedData.value.findIndex(item => item.id == allList.value[tempIndex].parentId)
      const cIndex = paginatedData.value[pIndex].children.findIndex(
        item => item.id == allList.value[tempIndex].id
      )
      const sIndex = paginatedData.value[pIndex].children[cIndex].children.findIndex(
        item => item.id == val.id
      )
      paginatedData.value[pIndex].children[cIndex].children[sIndex].status = !val.status
    }
    return ElMessage.warning('暂无权限')
  }
  if (props.labelType == '1009') {
    editModelTag({
      categoryId: props.labelType,
      parentId: val.parentId,
      id: val.id,
      name: val.name,
      remark: val.desc,
      status: val.status ? 0 : 1,
      sort: 0,
    })
      .then(r => {
        if (r.code == 200) {
          ElMessage({
            message: '更改标签状态成功',
            type: 'success',
          })
          getmodelLabelSelect()
        }
      })
      .catch(err => {
        if (val.level == '0') {
          const index = paginatedData.value.findIndex(item => item.id == val.id)
          paginatedData.value[index].status = !val.status
        }
        if (val.level == '1') {
          const pIndex = paginatedData.value.findIndex(item => item.id == val.parentId)
          const cIndex = paginatedData.value[pIndex].children.findIndex(item => item.id == val.id)
          paginatedData.value[pIndex].children[cIndex].status = !val.status
        }
        if (val.level == '2') {
          const tempIndex = allList.value.findIndex(item => item.id == val.parentId)
          const pIndex = paginatedData.value.findIndex(item => item.id == allList.value[tempIndex].parentId)
          const cIndex = paginatedData.value[pIndex].children.findIndex(
            item => item.id == allList.value[tempIndex].id
          )
          const sIndex = paginatedData.value[pIndex].children[cIndex].children.findIndex(
            item => item.id == val.id
          )
          paginatedData.value[pIndex].children[cIndex].children[sIndex].status = !val.status
        }
      })
  } else {
    editCategoryTag({
      categoryId: props.labelType,
      parentId: val.parentId,
      id: val.id,
      name: val.name,
      remark: val.desc,
      status: val.status ? 0 : 1,
      sort: 0,
    })
      .then(r => {
        if (r.code == 200) {
          ElMessage({
            message: '更改标签状态成功',
            type: 'success',
          })
          getmodelLabelSelect()
        }
      })
      .catch(err => {
        if (val.level == '0') {
          const index = paginatedData.value.findIndex(item => item.id == val.id)
          paginatedData.value[index].status = !val.status
        }
        if (val.level == '1') {
          const pIndex = paginatedData.value.findIndex(item => item.id == val.parentId)
          const cIndex = paginatedData.value[pIndex].children.findIndex(item => item.id == val.id)
          paginatedData.value[pIndex].children[cIndex].status = !val.status
        }
        if (val.level == '2') {
          const tempIndex = allList.value.findIndex(item => item.id == val.parentId)
          const pIndex = paginatedData.value.findIndex(item => item.id == allList.value[tempIndex].parentId)
          const cIndex = paginatedData.value[pIndex].children.findIndex(
            item => item.id == allList.value[tempIndex].id
          )
          const sIndex = paginatedData.value[pIndex].children[cIndex].children.findIndex(
            item => item.id == val.id
          )
          paginatedData.value[pIndex].children[cIndex].children[sIndex].status = !val.status
        }
      })
  }
  // editLabel({
  //   categoryId: props.labelType,
  //   parentId: val.parentId,
  //   id: val.id,
  //   name: val.name,
  //   remark: val.desc,
  //   status: val.status ? 0 : 1,
  //   sort: 0,
  // }).then(r => {
  //   if (r.code == 200) {
  //     ElMessage({
  //       message: '更改标签状态成功',
  //       type: 'success',
  //     })
  //     getmodelLabelSelect()
  //   }
  // })
}

// 获取列表信息
// 状态管理
const alltabData = ref([])
const tableData = ref([])
const modelLabelList = ref([])

// 分页器条数
const numPage = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)

// 重置信息
function resetLabel() {
  input.value = ''
  select.value = ''
  getmodelLabelSelect()
}

const paginatedData = ref([])

function getmodelLabelSelect() {
  loadingStatus.value = true
  if (props.labelType == '1009') {
    getModelTagList({
      categoryId: props.labelType,
      name: input.value,
      status: select.value,
    })
      .then(res => {
        if (res.code == 200) {
          const transformStatus = (items, level = 0) => {
            return items.map((item, index) => {
              item.id = item.id + ''
              item.status = item.status == 0
              item.level = level
              item.key = item.id + '' + index
              if (item.children && Array.isArray(item.children)) {
                item.children = transformStatus(item.children, level + 1)
              }
              return item
            })
          }
          alltabData.value = transformStatus(res.data)
          paginatedData.value = alltabData.value
          numPage.value = res.data.length
          loadingStatus.value = false
          if (checkPermi(['tag:model:sort'])) {
            rowDrop()
          }
        }
      })
      .catch(() => {
        loadingStatus.value = false
      })
  } else if (props.labelType == '1008') {
    getCategoryTagList({
      categoryId: props.labelType,
      name: input.value,
      status: select.value,
    })
      .then(res => {
        if (res.code == 200) {
          const transformStatus = (items, level = 0) => {
            return items.map((item, index) => {
              item.id = item.id + ''
              item.status = item.status == 0
              item.level = level
              item.key = item.id + '' + index
              if (item.children && Array.isArray(item.children)) {
                item.children = transformStatus(item.children, level + 1)
              }
              return item
            })
          }
          alltabData.value = transformStatus(res.data)
          paginatedData.value = alltabData.value
          numPage.value = res.data.length
          loadingStatus.value = false
          if (checkPermi(['tag:category:sort'])) {
            rowDrop()
          }
        }
      })
      .catch(() => {
        loadingStatus.value = false
      })
  } else if (props.labelType == '1010') {
    getCategoryTagList({
      categoryId: 1009,
      name: input.value,
      status: select.value,
    })
      .then(res => {
        if (res.code == 200) {
          const transformStatus = (items, level = 0) => {
            return items.map((item, index) => {
              item.id = item.id + ''
              item.status = item.status == 0
              item.level = level
              item.key = item.id + '' + index
              if (item.children && Array.isArray(item.children)) {
                item.children = transformStatus(item.children, level + 1)
              }
              return item
            })
          }
          alltabData.value = transformStatus(res.data)
          paginatedData.value = alltabData.value
          numPage.value = res.data.length
          loadingStatus.value = false
          if (checkPermi(['tag:category:sort'])) {
            rowDrop()
          }
        }
      })
      .catch(() => {
        loadingStatus.value = false
      })
  }

  // getLabelList({
  //   categoryId: props.labelType,
  //   name: input.value,
  //   status: select.value,
  // })
  //   .then(res => {
  //     if (res.code == 200) {
  //       const transformStatus = (items, level = 0) => {
  //         return items.map(item => {
  //           item.id = item.id + ''
  //           item.status = item.status == 0
  //           item.level = level
  //           if (item.children && Array.isArray(item.children)) {
  //             item.children = transformStatus(item.children, level + 1)
  //           }
  //           return item
  //         })
  //       }
  //       alltabData.value = transformStatus(res.data)
  //       paginatedData.value = alltabData.value
  //       numPage.value = res.data.length
  //       loadingStatus.value = false
  //       if (checkPermi([props.labelType == '1009' ? 'tag:model:sort' : 'tag:category:sort'])) {
  //         rowDrop()
  //       }
  //     }
  //   })
  //   .catch(() => {
  //     loadingStatus.value = false
  //   })
}

function handleSizeChange(value) {
  pageSize.value = value // 更新每页显示条数
}

function handleCurrentChange(value) {
  currentPage.value = value // 更新当前页码
}

//修改标签
const amendialogVisible = ref(false)
const amendrulForm = ref({
  name: '',
  resource: 0,
  desc: '',
  parentId: '',
})

// 点击修改按钮
const amendid = ref()
const amendall = ref()

function amendLabel(id, all) {
  amendid.value = id
  amendrulForm.value.name = all.name
  amendrulForm.value.resource = all.status ? 0 : 1
  amendrulForm.value.desc = all.remark
  amendall.value = all.parentId
  rules.value.name[0].message = `请输入${all.level == '0' ? '一' : all.level == 1 ? '二' : '三'}级标签名称`
  amendialogVisible.value = true
}

function amendsubmitForm() {
  proxy.$refs['editForm'].validate(valid => {
    if (valid) {
      if (props.labelType == '1009') {
        editModelTag({
          categoryId: props.labelType,
          id: amendid.value,
          parentId: amendall.value,
          name: amendrulForm.value.name,
          remark: amendrulForm.value.desc,
          status: amendrulForm.value.resource,
          sort: 0,
        }).then(res => {
          if (res.code == 200) {
            ElMessage({
              message: '成功',
              type: 'success',
            })
            amendrulForm.value = {
              name: '',
              resource: 0,
              desc: '',
              parentId: '',
            }
            amendialogVisible.value = false
            getmodelLabelSelect()
          }
        })
      } else {
        editCategoryTag({
          categoryId: props.labelType,
          id: amendid.value,
          parentId: amendall.value,
          name: amendrulForm.value.name,
          remark: amendrulForm.value.desc,
          status: amendrulForm.value.resource,
          sort: 0,
        }).then(res => {
          if (res.code == 200) {
            ElMessage({
              message: '成功',
              type: 'success',
            })
            amendrulForm.value = {
              name: '',
              resource: 0,
              desc: '',
              parentId: '',
            }
            amendialogVisible.value = false
            getmodelLabelSelect()
          }
        })
      }
      // editLabel({
      //   categoryId: props.labelType,
      //   id: amendid.value,
      //   parentId: amendall.value,
      //   name: amendrulForm.value.name,
      //   remark: amendrulForm.value.desc,
      //   status: amendrulForm.value.resource,
      //   sort: 0,
      // }).then(res => {
      //   if (res.code == 200) {
      //     ElMessage({
      //       message: '成功',
      //       type: 'success',
      //     })
      //     amendrulForm.value = {
      //       name: '',
      //       resource: 0,
      //       desc: '',
      //       parentId: '',
      //     }
      //     amendialogVisible.value = false
      //     getmodelLabelSelect()
      //   }
      // })
    }
  })
}

const cancelForm = () => {
  amendrulForm.value = {
    name: '',
    resource: 0,
    desc: '',
    parentId: '',
  }
  proxy.$refs['editForm'].resetFields()
  amendialogVisible.value = false
}

function handleAmend() {
  amendrulForm.value = {
    name: '',
    resource: 0,
    desc: '',
    parentId: '',
  }
  proxy.$refs['editForm'].resetFields()
  amendialogVisible.value = false
}

// 弹出层
const dialogVisible = ref(false)

function addLabel() {
  rules.value.name[0].message = `请输入一级标签名称`
  dialogVisible.value = true
}

const ruleForm = ref({
  name: '',
  resource: 0,
  desc: '',
})
const rules = ref({
  name: [
    {
      required: true,
      message: '请输入一级标签名称',
      trigger: 'blur',
    },
    { min: 1, max: 50, message: '请输入50个字以内', trigger: 'blur' },
  ],
  resource: [
    {
      required: true,
      message: '状态不能为空',
      trigger: 'change',
    },
  ],
})

// 添加提交表单的方法
//添加一级标签
const submitForm = () => {
  proxy.$refs['addRef'].validate(valid => {
    if (valid) {
      if (props.labelType == '1009') {
        addModelTag({
          categoryId: props.labelType,
          name: ruleForm.value.name,
          remark: ruleForm.value.desc,
          status: ruleForm.value.resource,
        }).then(res => {
          if (res.code == 200) {
            ElMessage({
              message: '成功',
              type: 'success',
            })
            ruleForm.value = {
              name: '',
              resource: 0,
              desc: '',
            }
            getmodelLabelSelect()
            dialogVisible.value = false
          }
        })
      } else {
        addCategoryTag({
          categoryId: props.labelType,
          name: ruleForm.value.name,
          remark: ruleForm.value.desc,
          status: ruleForm.value.resource,
        }).then(res => {
          if (res.code == 200) {
            ElMessage({
              message: '成功',
              type: 'success',
            })
            ruleForm.value = {
              name: '',
              resource: 0,
              desc: '',
            }
            getmodelLabelSelect()
            dialogVisible.value = false
          }
        })
      }
      // addtoLabel({
      //   categoryId: props.labelType,
      //   name: ruleForm.value.name,
      //   remark: ruleForm.value.desc,
      //   status: ruleForm.value.resource,
      // }).then(res => {
      //   if (res.code == 200) {
      //     ElMessage({
      //       message: '成功',
      //       type: 'success',
      //     })
      //     ruleForm.value = {
      //       name: '',
      //       resource: 0,
      //       desc: '',
      //     }
      //     getmodelLabelSelect()
      //     dialogVisible.value = false
      //   }
      // })
    }
  })
}

const cancelOneLabel = () => {
  ruleForm.value = {
    name: '',
    resource: 0,
    desc: '',
  }
  proxy.$refs['addRef'].resetFields()
  dialogVisible.value = false
}

function handleClose() {
  ruleForm.value = {
    name: '',
    resource: 0,
    desc: '',
  }
  proxy.$refs['addRef'].resetFields()
  dialogVisible.value = false
}

// 删除标签
const delid = ref()
const deldialog = ref(false)

function delLabels(id) {
  proxy
    .$confirm('确认删除该标签吗？')
    .then(_ => {
      delid.value = id
      delLabelclick()
    })
    .catch(_ => {})
}

function delLabelclick() {
  if (props.labelType == '1009') {
    deleteModelTag({ id: delid.value })
      .then(res => {
        if (res.code == 200) {
          ElMessage({
            message: '删除成功',
            type: 'success',
          })
          deldialog.value = false
          getmodelLabelSelect()
        }
      })
      .catch(err => {
        deldialog.value = false
        Cannotbedeleted.value = true
      })
  } else {
    deleteCategoryTag({ id: delid.value })
      .then(res => {
        if (res.code == 200) {
          ElMessage({
            message: '删除成功',
            type: 'success',
          })
          deldialog.value = false
          getmodelLabelSelect()
        }
      })
      .catch(err => {
        deldialog.value = false
        Cannotbedeleted.value = true
      })
  }
  // delLabel(delid.value)
  //   .then(res => {
  //     if (res.code == 200) {
  //       ElMessage({
  //         message: '删除成功',
  //         type: 'success',
  //       })
  //       deldialog.value = false
  //       getmodelLabelSelect()
  //     }
  //   })
  //   .catch(err => {
  //     deldialog.value = false
  //     Cannotbedeleted.value = true
  //   })
}

// 添加子类标签
const beautySubtag = ref('')
const Firstlabel = ref([])
const dialogVisibles = ref(false)
const ruleForms = ref({
  name: '',
  resource: 0,
  desc: '',
  region: '',
})

// 添加子类标签上的父标签
function addLabels(id, level) {
  getParentLabel(props.labelType).then(res => {
    if (res.code == 200) {
      Firstlabel.value = res.data.map(md => {
        return {
          value: md.id,
          label: md.name,
          children: md.children.map(cc => {
            return {
              value: cc.id,
              label: cc.name,
            }
          }),
        }
      })
    }
  })
  rules.value.name[0].message = `请输入${level == '0' ? '二' : '三'}级标签名称`
  ruleForms.value.name = ''
  beautySubtag.value = id * 1
  ruleForms.value.resource = 0
  dialogVisibles.value = true
  ruleForms.region = Firstlabel.value.find(item => item.id === beautySubtag.value)
}

const showShootDialog = ref(false)
const shootFormRef = ref()
const shootForm = ref({
  cnName: '',
  enName: '',
})
const shootFormRules = {
  cnName: [{ required: true, message: '请填写字段名称', trigger: 'blur' }],
  enName: [{ required: true, message: '请填写字段英文名', trigger: 'blur' }],
}
function addShootLabels(id, level) {
  showShootDialog.value = true
}
function closeShootDialog() {
  shootForm.value.cnName = ''
  shootForm.value.enName = ''
  shootFormRef.value.resetFields()
  showShootDialog.value = false
}
function handleShootForm() {
  shootFormRef.value.validate(valid => {
    if (valid) {
      console.log('shootForm', shootForm.value)
    }
  })
}

function submitForms() {
  proxy.$refs['formRef'].validate(valid => {
    if (valid) {
      if (props.labelType == '1009') {
        addModelTag({
          categoryId: props.labelType,
          name: ruleForms.value.name,
          parentId: beautySubtag.value,
          remark: ruleForms.value.desc,
          status: ruleForms.value.resource,
        }).then(res => {
          if (res.code == 200) {
            ElMessage({
              message: '成功',
              type: 'success',
            })
            ruleForms.value = {
              name: '',
              resource: 0,
              desc: '',
              region: '',
            }
            dialogVisibles.value = false

            getmodelLabelSelect()
          }
        })
      } else {
        addCategoryTag({
          categoryId: props.labelType,
          name: ruleForms.value.name,
          parentId: beautySubtag.value,
          remark: ruleForms.value.desc,
          status: ruleForms.value.resource,
        }).then(res => {
          if (res.code == 200) {
            ElMessage({
              message: '成功',
              type: 'success',
            })
            ruleForms.value = {
              name: '',
              resource: 0,
              desc: '',
              region: '',
            }
            dialogVisibles.value = false

            getmodelLabelSelect()
          }
        })
      }
      // addtoLabel({
      //   categoryId: props.labelType,
      //   name: ruleForms.value.name,
      //   parentId: beautySubtag.value,
      //   remark: ruleForms.value.desc,
      //   status: ruleForms.value.resource,
      // }).then(res => {
      //   if (res.code == 200) {
      //     ElMessage({
      //       message: '成功',
      //       type: 'success',
      //     })
      //     ruleForms.value = {
      //       name: '',
      //       resource: 0,
      //       desc: '',
      //       region: '',
      //     }
      //     dialogVisibles.value = false

      //     getmodelLabelSelect()
      //   }
      // })
    }
  })
}

const cancelForms = () => {
  ruleForms.value = {
    name: '',
    resource: 0,
    desc: '',
    region: '',
  }
  formRef.value.resetFields()
  dialogVisibles.value = false
}

function handleChildLabel() {
  ruleForms.value = {
    name: '',
    resource: 0,
    desc: '',
    region: '',
  }
  formRef.value.resetFields()
  dialogVisibles.value = false
}

//搜索按钮
//标签名称
const input = ref('')

const select = ref('')
const formRef = ref(null)

getmodelLabelSelect()

function flattenTree(tree) {
  let result = []

  function recurse(node) {
    // 添加当前节点到结果数组
    result.push(node)

    // 如果当前节点有子节点，则递归处理每个子节点
    if (node.children && node.children.length > 0) {
      node.children.forEach(child => recurse(child))
    }
  }

  tree.forEach(node => recurse(node))
  return result
}

const allList = computed(() => {
  return flattenTree(paginatedData.value)
})
//默认展开的数据
const treeTableExpandKeys = ref([])
function treeTableExpandChange(row, expanded) {
  if (expanded) {
    treeTableExpandKeys.value.push(row.key)
  } else {
    const idx = treeTableExpandKeys.value.indexOf(row.key)
    treeTableExpandKeys.value.splice(idx, 1)
  }
}
const sortTable = ref(null)
function sortTableDestory() {
  if (sortTable.value) {
    sortable.value.destroy()
  }
  treeTableExpandKeys.value = []
}
const tableContainer = ref(null)

function rowDrop() {
  const el = document.querySelector('.el-table__body-wrapper tbody')
  let skipLevel = false
  sortTable.value = Sortable.create(el, {
    animation: 200, // 定义排序动画的时间
    onStart: () => {
      skipLevel = false
    },
    onMove: ({ dragged, related }) => {
      const oldRow = allList.value[dragged.rowIndex] // 移动的那个元素
      const newRow = allList.value[related.rowIndex] // 新的元素
      // 父节点不相同的情况不允许跨级拖动
      if (oldRow.parentId !== newRow.parentId) {
        skipLevel = true
        return false
      }
    },
    onEnd: e => {
      const newRow = allList.value[e.newIndex]
      const oldRow = allList.value[e.oldIndex]
      if (oldRow.id == newRow.id) {
        if (skipLevel) ElMessage.warning('父节点不相同的情况不允许跨级拖动')
        return false
      }

      const idx1 = treeTableExpandKeys.value.indexOf(oldRow.key)
      const idx2 = treeTableExpandKeys.value.indexOf(newRow.key)

      // 处理渲染key
      let okey = oldRow.key.slice(oldRow.id.toString().length)
      let nkey = newRow.key.slice(newRow.id.toString().length)
      oldRow.key = oldRow.id + nkey
      newRow.key = newRow.id + okey

      //处理第一级标签的拖动
      if (oldRow.parentId == '0') {
        const oldIndex = paginatedData.value.findIndex(item => item.id == oldRow.id)
        const newIndex = paginatedData.value.findIndex(item => item.id == newRow.id)

        handleSaveTagSort(oldRow.id, newRow.id, () => {
          if (newIndex >= 0 && oldIndex >= 0) {
            paginatedData.value.splice(newIndex, 0, paginatedData.value.splice(oldIndex, 1)[0])
            // 处理展开状态
            if (idx1 >= 0) treeTableExpandKeys.value.splice(idx1, 1, oldRow.key)
            if (idx2 >= 0) treeTableExpandKeys.value.splice(idx2, 1, newRow.key)
          }
        })
      } else {
        //处理子标签的拖动
        if (newRow.level == 1) {
          const pIndex = paginatedData.value.findIndex(item => item.id == newRow.parentId)
          const oldIndex = paginatedData.value[pIndex].children.findIndex(item => item.id == oldRow.id)
          const newIndex = paginatedData.value[pIndex].children.findIndex(item => item.id == newRow.id)

          handleSaveTagSort(oldRow.id, newRow.id, () => {
            if (newIndex >= 0 && oldIndex >= 0) {
              paginatedData.value[pIndex].children.splice(
                newIndex,
                0,
                paginatedData.value[pIndex].children.splice(oldIndex, 1)[0]
              )
              // 处理展开状态
              if (idx1 >= 0) treeTableExpandKeys.value.splice(idx1, 1, oldRow.key)
              if (idx2 >= 0) treeTableExpandKeys.value.splice(idx2, 1, newRow.key)
            }
          })
        } else {
          //三级标签直接拿
          handleSaveTagSort(oldRow.id, newRow.id, getmodelLabelSelect)
        }
      }
    },
  })
}

// function setKey(data, index) {
//   if (data && Array.isArray(data)) {
//     data.forEach((item, i) => {
//       item.key = item.id + '' + i
//       if (item.children && Array.isArray(item.children)) {
//         setKey(item.children, i)
//       }
//     })
//   } else if (data && typeof data === 'object') {
//     data.key = data.id + '' + index
//     if (data.children && Array.isArray(data.children)) {
//       setKey(data.children, index)
//     }
//   }
// }

function handleSaveTagSort(oid, nid, cb) {
  tableLoading.value = true
  if (props.labelType == '1009') {
    modelTagSort({
      originTagId: oid,
      resultTagId: nid,
    })
      .then(res => {
        if (res.code == 200 && cb) cb()
      })
      .finally(() => (tableLoading.value = false))
  } else {
    categoryTagSort({
      originTagId: oid,
      resultTagId: nid,
    })
      .then(res => {
        if (res.code == 200 && cb) cb()
      })
      .finally(() => (tableLoading.value = false))
  }
  // tagSort({
  //   originTagId: oid,
  //   resultTagId: nid,
  // })
  //   .then(res => {
  //     if (res.code == 200 && cb) cb()
  //   })
  //   .finally(() => (tableLoading.value = false))
}
</script>

<style lang="scss" scoped>
.el-tabs {
  margin-left: 20px;

  .main {
    display: flex;
    flex-direction: column;
    font-size: 16px;
    color: #000;
    font-weight: normal;

    .main-top {
      display: flex;
      justify-content: flex-start;
      margin-bottom: 20px;

      .main-text {
        margin-right: 20px;
      }

      .main-space {
        margin-right: 5%;
      }
    }

    .margin-spaces {
      margin-bottom: 20px;
    }
  }
}

.demo-tabs > .el-tabs__content {
  color: #6b778c;
  font-size: 32px;
  font-weight: 600;
}

.drag-icon {
  font-size: 16px;
  line-height: 3px;
  color: #999;
  cursor: grab;

  &:active {
    cursor: grabbing;
  }
}
</style>
