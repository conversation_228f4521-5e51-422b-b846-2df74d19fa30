<template>
  <div style="line-height: 1.5">
    <el-avatar class="model-avatar" icon="UserFilled" :src="$picUrl + data.modelPic + '!1x1compress'" />
    <div class="flex-center">
      {{ data.name }}
      <model-score
        v-if="data.cooperationScore || data.cooperationScore === 0"
        :score="data.cooperationScore"
        style="margin-left: 5px"
      />
    </div>
    <div v-if="showAccount">{{ data.account ? `(ID:${data.account})` : '-' }}</div>
    <biz-model-type-new :value="data.type" />
    <!-- <biz-model-type :value="row.intentionModel.type" /> -->
  </div>
</template>

<script setup>
defineProps({
  data: {
    type: Object,
    required: true,
  },
  showAccount: {
    type: Boolean,
    default: true,
  },
})
</script>

<style scoped lang="scss"></style>
