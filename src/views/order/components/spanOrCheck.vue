<template>
  <div class="editable-span" ref="editableSpanRef">
    <span @click="showCheckbox = true" v-if="!showCheckbox">{{ handleModelType() || '-' }}</span>
    <el-checkbox-group :model-value="props.modelValue" v-else @change="changeFormValue($event)">
      <el-checkbox-button v-for="item in list" :key="item.value * 1" :value="item.value * 1">
        {{ item.label }}
      </el-checkbox-button>
    </el-checkbox-group>
  </div>
</template>

<script setup>
import { ElMessage } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: Array,
    default: [],
  },
  filedName: {
    type: String,
    default: '',
  },
  list: {
    type: Array,
    default: () => [],
  },
})

const emit = defineEmits(['update:modelValue', 'changeValue'])

const showCheckbox = ref(false)
const editableSpanRef = ref()

function changeFormValue(value) {
  emit('update:modelValue', value)
  emit('changeValue', value)
}
function handleModelType() {
  let name = ''
  if (props.modelValue && props.modelValue.length > 0) {
    if (props.modelValue.length == 1) {
      name = props.list.find(item => item.value == props.modelValue[0])?.label
    } else {
      name = '素人创作者、亚马逊影响者'
    }
  }
  return name
}
const handleClickOutside = event => {
  if (editableSpanRef.value && !editableSpanRef.value.contains(event.target)) {
    if (!props.modelValue || props.modelValue.length == 0) {
      emit('update:modelValue', [1])
    }
    showCheckbox.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside, true)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside, true)
})

const close = () => {
  showInput.value = false
}

defineExpose({ close })
</script>

<style lang="scss" scoped>
@import '@/assets/styles/customForm.scss';
@import '@/assets/styles/form-disabled.scss';
.editable-span {
  display: flex;
  align-items: center;
}
</style>
