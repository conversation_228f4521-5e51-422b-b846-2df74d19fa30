<template>
  <el-dialog
    v-model="dialogVisible"
    title="添加预选模特"
    width="900"
    align-center
    append-to-body
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="68px" @submit.prevent>
      <el-form-item label="搜索">
        <el-input v-model="queryParams.val" clearable style="max-width: 300px" placeholder="请输入对应的值">
          <template #prepend>
            <el-select v-model="queryParams.select" placeholder="请选择" clearable style="width: 100px">
              <el-option
                v-for="item in selectOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item label="年龄层" prop="ageGroup">
        <el-select
          v-model="queryParams.ageGroup"
          placeholder="请选择"
          multiple
          collapse-tags
          collapse-tags-tooltip
          clearable
          style="width: 180px"
        >
          <el-option
            v-for="item in biz_model_ageGroup"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="模特标签" prop="modelTag">
        <el-tree-select
          v-model="queryParams.modelTag"
          :data="modelTagsList"
          multiple
          :render-after-expand="false"
          default-expand-all
          show-checkbox
          check-strictly
          check-on-click-node
          style="width: 150px"
          filterable
          :filter-node-method="filterTreeNode"
          node-key="id"
          :props="{
            children: 'children',
            label: 'name',
            value: 'id',
          }"
        />
        <!-- <el-select
          v-model="queryParams.modelTag"
          multiple
          collapse-tags
          collapse-tags-tooltip
          placeholder="请选择"
          style="width: 100%"
        >
          <el-option
            v-for="item in modelTagsList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select> -->
      </el-form-item>
      <el-form-item label="擅长品类" prop="specialtyCategory">
        <el-select
          v-model="queryParams.specialtyCategory"
          multiple
          collapse-tags
          collapse-tags-tooltip
          placeholder="请选择"
          style="width: 150px"
        >
          <el-option v-for="item in modelCategoryList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button v-btn type="primary" icon="Search" native-type="submit" @click="handleQuery">
          搜索
        </el-button>
        <el-button v-btn icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="flex-between">
      <div class="flex-start gap-10">
        <h3>模特列表</h3>
        <SortButton v-model="queryParams.waitsSort" @change="handleQuery">待拍数</SortButton>
      </div>
      <el-button v-btn type="primary" round :disabled="!checkList.length" @click="handleConfirm">
        确认添加
      </el-button>
    </div>
    <el-divider style="margin-top: 0" />

    <div v-loading="loading" style="max-height: 500px; overflow-y: auto">
      <el-empty v-if="!tableData.length" description="暂无数据" :image-size="80"></el-empty>

      <el-checkbox-group v-model="checkList">
        <template v-for="(data, i) in tableData" :key="i">
          <div class="flex-between model-item-box">
            <div class="flex-start gap-10 model-item">
              <PercentageImg style="flex-" width="90px" :src="$picUrl + data.modelPic" />
              <label :for="`add-p-m-checkbox-${i}`" class="info-box">
                <div class="flex-start">
                  <span>{{ data.name }}&emsp;</span>
                  <!-- 平台 -->
                  <biz-model-platform :value="data.platform" />
                  <!-- 模特类型 -->
                  <biz-model-type :value="data.type" />
                  <!-- 模特等级 -->
                  <biz-model-cooperation :value="data.cooperation" />
                </div>
                <div>{{ `(ID:${data.account})` }}</div>
                <div class="flex-start gap-10">
                  <!-- 年龄层 -->
                  <template v-for="item in biz_model_ageGroup" :key="item.value">
                    <span v-if="item.value == data.ageGroup">{{ item.label }}</span>
                  </template>
                  <span>{{ data.sex == '1' ? '男' : '女' }}</span>
                  <!-- 国家 -->
                  <template v-for="item in biz_nation" :key="item.value">
                    <span v-if="item.value == data.nation">{{ item.label }}</span>
                  </template>
                </div>
                <div class="one-ell">
                  模特标签：{{ data.tags ? data.tags.map(item => item.name).join('、') : '' }}
                </div>
                <div style="max-width: 90%" class="one-ell">
                  擅长品类：
                  <el-popover
                    placement="top-start"
                    title=""
                    :width="600"
                    trigger="hover"
                    :content="
                      data.specialtyCategory ? data.specialtyCategory.map(item => item.name).join('、') : ''
                    "
                  >
                    <template #reference>
                      {{
                        data.specialtyCategory ? data.specialtyCategory.map(item => item.name).join('、') : ''
                      }}
                    </template>
                  </el-popover>

                  <!-- {{ data.specialtyCategory ? data.specialtyCategory.map(item => item.name).join('、') : '' }} -->
                </div>
                <div>剩余待拍数量：{{ data.waits }}</div>
                <div>剩余可携带订单数量：{{ data.carryCount || 0 }}单</div>
                <div>模特佣金：{{ data.commission }}{{ handleCommissionUnit(data.commissionUnit) }}</div>
              </label>
              <div class="checkbox-item">
                <el-checkbox :id="`add-p-m-checkbox-${i}`" :value="data.id" :disabled="data.id == modelId">
                  {{ '' }}
                </el-checkbox>
              </div>
            </div>
          </div>

          <el-divider />
        </template>
      </el-checkbox-group>
    </div>
    <div class="flex-end pt-10">
      <el-pagination
        background
        @size-change="pageChange({ pageNum: 1, pageSize: $event })"
        @current-change="pageChange({ pageNum: $event, pageSize })"
        :current-page="pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        layout="total, prev, pager, next, jumper"
        :total="total"
      />
    </div>
  </el-dialog>
</template>

<script setup>
import PercentageImg from '@/components/ImagePreview/percentageImg.vue'
import SortButton from '@/components/Button/SortButton.vue'
import { addPreselectModelList, modelCategorySelect, modelCategorySelectRank } from '@/api/model/model'
import { addPreselectModel } from '@/api/order/order'
import { ElLoading, ElMessage } from 'element-plus'
import { bizCommissionUnit } from '@/utils/dict'

const { proxy } = getCurrentInstance()
const { biz_nation, biz_model_ageGroup } = proxy.useDict('biz_nation', 'biz_model_ageGroup')

defineExpose({
  open,
  close,
})

const emits = defineEmits(['success'])

const dialogVisible = ref(false)
const selectOptions = [
  { label: '模特名称', value: 'name' },
  { label: '模特ID', value: 'account' },
  // { label: '模特标签', value: 'modelTag' },
  // { label: '擅长品类', value: 'specialtyCategory' },
]
const videoId = ref('')
const modelId = ref('')
const queryParams = ref({
  val: '',
  select: 'name',
  waitsSort: '',
  ageGroup: [],
  modelTag: [],
  specialtyCategory: [],
})
const modelTagsList = ref([])
const modelCategoryList = ref([])
const loading = ref(false)
const tableData = ref([])
const checkList = ref([])
const pageNum = ref(1)
const pageSize = ref(10)
const total = ref(0)
const modelParams = ref({})

function open(id, mId, params) {
  videoId.value = id
  modelId.value = mId || ''
  modelParams.value = params
  handleQuery()
  dialogVisible.value = true
}
function close() {
  dialogVisible.value = false
}

function handleQuery() {
  let { val, select, ...params } = queryParams.value
  if (select) {
    params[select] = val
  }
  loading.value = true
  addPreselectModelList({
    ...params,
    ...modelParams.value,
    pageNum: pageNum.value,
    pageSize: pageSize.value,
  })
    .then(res => {
      tableData.value = res.data.rows
      total.value = res.data.total
    })
    .finally(() => {
      loading.value = false
    })
}

function resetQuery() {
  handleClose()
  handleQuery()
}
function handleClose() {
  queryParams.value = {
    val: '',
    select: 'name',
    waitsSort: '',
    ageGroup: [],
  }
  checkList.value = []
  pageNum.value = 1
}

// 分页跳转
function pageChange(page) {
  pageNum.value = page.pageNum
  pageSize.value = page.pageSize
  handleQuery()
}

// 模特擅长品类下拉
function getModelCategorySelect() {
  modelCategorySelectRank({ rank: 1, categoryId: 1008 }).then(res => {
    if (res.code == 200) {
      modelCategoryList.value = res.data
    }
  })
}
// 模特标签下拉
function getModelTagsSelect() {
  modelCategorySelect({ status: 0, categoryId: 1009 }).then(res => {
    if (res.code == 200) {
      modelTagsList.value = res.data
    }
  })
}

function handleCommissionUnit(val) {
  let b = bizCommissionUnit.find(item => item.value == val)
  return b ? b.label : ''
}
function filterTreeNode(value, data) {
  if (!value) return true
  return data.name.includes(value)
}

function handleConfirm() {
  const el_loading = ElLoading.service({
    lock: true,
    text: '正在添加中，请稍后',
    background: 'rgba(0, 0, 0, 0.7)',
  })
  addPreselectModel({
    modelId: checkList.value.join(','),
    videoId: videoId.value,
  })
    .then(res => {
      ElMessage.success('添加成功')
      emits('success', res)
      close()
    })
    .finally(() => el_loading.close())
}

getModelTagsSelect()
getModelCategorySelect()
</script>

<style scoped lang="scss">
.model-item-box {
  .model-item {
    width: 100%;
    align-items: flex-start;
    position: relative;

    .info-box {
      width: 86%;
      font-weight: 500;

      div {
        margin: 0 0 5px 0;
        font-size: 14px;
        line-height: 1.4;
      }
    }

    .checkbox-item {
      position: absolute;
      top: calc(50% - 10px);
      right: 10px;
    }
  }
}
</style>
