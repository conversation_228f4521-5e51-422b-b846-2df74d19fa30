<template>
  <el-dialog
    v-model="dialogVisible"
    title="预选模特列表"
    width="900"
    align-center
    append-to-body
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <template #header>
      <div class="flex-start">
        预选模特列表
        <el-button
          v-btn
          plain
          round
          v-hasPermi="['order:manage:preselection']"
          style="margin-left: 10px"
          :disabled="disabled"
          @click="handleAdd"
        >
          添加预选
        </el-button>
      </div>
    </template>
    <ElTablePage
      :columns="columns"
      :data="tableData"
      :loading="tableLoading"
      :currentPage="pageNum"
      :pageSize="pageSize"
      :total="total"
      height="400px"
      @page-change="pageChange"
    >
      <template #model="{ row }">
        <div class="flex-column">
          <div>
            <span>{{ row.model?.name }}</span>
            <template v-if="row.flag == 1">
              <el-tag type="danger" size="small" round>商家拒绝</el-tag>
            </template>
          </div>
          <div>{{ row.model?.id ? `(ID:${row.model.account})` : '' }}</div>
        </div>
      </template>
      <template #action="{ row }">
        <el-button
          v-btn
          v-if="row.status === 2"
          link
          type="primary"
          :disabled="disabled"
          v-hasPermi="['order:manage:preselection']"
          @click="handleAction('取消选定', row)"
        >
          取消选定
        </el-button>
        <el-button
          v-btn
          v-if="row.status === 0 || row.status === 1"
          link
          type="primary"
          :disabled="disabled"
          v-hasPermi="['order:manage:preselection']"
          @click="handleAction('选定', row)"
        >
          选定
        </el-button>
        <el-button
          v-btn
          v-if="row.status === 0 || row.status === 1 || row.status === 2"
          link
          type="primary"
          :disabled="disabled"
          v-hasPermi="['order:manage:preselection']"
          @click="handleAction('淘汰', row)"
        >
          淘汰
        </el-button>
        <el-button
          v-btn
          v-if="row.status === 3"
          link
          type="primary"
          :disabled="disabled"
          v-hasPermi="['order:manage:preselection']"
          @click="handleAction('恢复选定', row)"
        >
          恢复选定
        </el-button>
      </template>
    </ElTablePage>
  </el-dialog>

  <AddPreselectModelList ref="AddPreselectModelListRef" @success="handleAddSuccess" />
</template>

<script setup>
import ElTablePage from '@/components/Table/ElTablePage'
import AddPreselectModelList from '@/views/order/components/dialog/addPreselectModelList.vue'
import { preselectStatusList } from '../../list/data'
import { preselectModelList, editPreselectModel } from '@/api/order/order'
import { ElLoading, ElMessage, ElMessageBox } from 'element-plus'
import permi from '@/plugins/auth'

defineExpose({
  open,
  close,
})

const emits = defineEmits(['change'])

const AddPreselectModelListRef = ref()

const dialogVisible = ref(false)
const videoId = ref('')
const columns = [
  {
    prop: 'addUser',
    label: '添加人',
    minWidth: '200',
    handle: (data, row) => {
      let str = ''
      if (row.addType == 1) {
        str += '意向模特'
      } else if (row.addType == 2) {
        str += '模特自选'
      } else if (row.addType == 3) {
        str += row.addUser.name
      }
      if (row.addTime) {
        str += '\n' + row.addTime
      }
      return str
    },
  },
  { slot: 'model', prop: 'model', label: '模特姓名', minWidth: '200' },
  {
    prop: 'status',
    label: '状态',
    width: '120',
    handle: data => {
      let item = preselectStatusList.find(item => item.value == data)
      if (item) {
        return item.label
      }
      return '-'
    },
  },
  { slot: 'action', prop: 'action', label: '操作', width: '150' },
  { prop: 'remark', label: '备注', minWidth: '150', ellipsis: true },
]
const tableData = ref([])
const pageNum = ref(1)
const pageSize = ref(20)
const total = ref(0)
const tableLoading = ref(false)
const modelParams = ref({})
const disabled = ref(false)
const isUpdate = ref(false)

function open(id, params, isDisabled, isIn) {
  videoId.value = id
  modelParams.value = params
  disabled.value = isDisabled ? true : false
  isUpdate.value = false
  handleQuery(isIn)
  dialogVisible.value = true
}
function close() {
  dialogVisible.value = false
}
function handleClose() {
  if(isUpdate.value) {
    emits('change')
  }
}
function handleQuery(isIn = false) {
  tableLoading.value = true
  preselectModelList(videoId.value)
    .then(res => {
      tableData.value = res.data.rows
      total.value = res.data.total
      if(isIn && permi.hasPermi('order:manage:preselection')) {
        nextTick(() => {
          handleAdd()
        })
      }
    })
    .finally(() => (tableLoading.value = false))
}
// 分页跳转
function pageChange(page) {
  pageNum.value = page.currentPage
  pageSize.value = page.pageSize
  handleQuery()
}

function handleAdd() {
  let modelId = tableData.value.find(item => item.status == 2)?.modelId
  AddPreselectModelListRef.value.open(videoId.value, modelId, modelParams.value)
}
function handleAction(btn, row) {
  isUpdate.value = true
  let status
  if (btn === '选定') {
    status = 2
  }
  if (btn === '取消选定') {
    status = 1
  }
  if (btn === '恢复选定') {
    status = 2
  }
  if (btn === '淘汰') {
    status = 3
    ElMessageBox.prompt('淘汰原因：', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputPlaceholder: '请输入淘汰原因',
      inputValidator: val => {
        return true
      },
      inputPattern: /^.{0,32}$/,
      inputErrorMessage: '请输入淘汰原因(32字以内)',
    }).then(({ value }) => {
      // console.log(value);
      handleEditPreselectModel({
        id: row.id,
        status,
        remark: value,
        videoId: videoId.value,
      })
    })
    return
  }
  handleEditPreselectModel({
    id: row.id,
    status,
    videoId: videoId.value,
  })
}

function handleAddSuccess() {
  isUpdate.value = true
  handleQuery(false)
}

function handleEditPreselectModel(data) {
  const el_loading = ElLoading.service({
    lock: true,
    text: '正在执行中，请稍后',
    background: 'rgba(0, 0, 0, 0.7)',
  })
  editPreselectModel(data)
    .then(res => {
      ElMessage.success('操作成功')
      handleQuery()
    })
    .catch(() => handleQuery())
    .finally(() => el_loading.close())
}
</script>

<style scoped lang="scss">

</style>
