<template>
  <el-dialog
    v-model="dialogVisible"
    title="暂停匹配"
    width="500"
    align-center
    append-to-body
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <div class="pause-match-content">
      <p>
        <div>确认暂停该订单匹配吗？</div>
        <span>
          若当前有沟通中或已选定模特，暂停后将会取消所有匹配<br/>英文部客服需重新选择预选模特
        </span>
      </p>
      <el-form-item ref="ReasonRef" label="暂停原因" label-position="top" required>
        <el-input
          v-model="form.reason"
          type="textarea"
          maxlength="300"
          show-word-limit
          clearable
          :rows="4"
          placeholder="请输入暂停原因"
          @input="handleChange"
        />
      </el-form-item>
    </div>
    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="handleConfirm">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { pauseMatch } from '@/api/order/preselection'

const { proxy } = getCurrentInstance()

const emits = defineEmits(['success'])

const dialogVisible = ref(false)
const form = reactive({
  reason: ''
})
const ReasonRef = ref(null)
const videoId = ref('')

function open(id) {
  videoId.value = id
  dialogVisible.value = true
}

function close() {
  dialogVisible.value = false
}

function handleClose() {
  videoId.value = ''
  form.reason = ''
  ReasonRef.value.validateState = 'success'
  dialogVisible.value = false
}

function handleChange(val) {
  if (val) {
    ReasonRef.value.validateState = 'success'
  }
}
function handleConfirm() {
  if (!form.reason.trim()) {
    ReasonRef.value.validateMessage = '请输入暂停原因'
    ReasonRef.value.validateState = 'error'
    return
  }
  proxy.$modal.loading('执行中...')
  pauseMatch({ videoId: videoId.value, pauseReason: form.reason.trim() })
    .then(res => {
      proxy.$modal.msgSuccess('已暂停匹配')
      emits('success')
      close()
    })
    .finally(() => {
      proxy.$modal.closeLoading()
    })
}

defineExpose({
  open,
  close
})
</script>

<style scoped lang="scss">
.pause-match-content {

  p {
    padding: 10px 20px;
    border-radius: 10px;
    background-color: var(--el-color-warning-light-9);
    color: var(--el-color-warning);
    font-size: 13px;

    div {
      color: #000;
      font-size: 16px;
      margin-bottom: 10px;
    }
  }
}
</style>