<template>
  <div>
    <el-dialog
      v-model="dialogVisible"
      title="更换模特"
      align-center
      width="1200px"
      append-to-body
      destroy-on-close
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="close"
    >
      <div class="model-select-list-box">
        <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="88px" @submit.prevent>
          <el-form-item label="平台">
            <el-select v-model="props.platform" :disabled="true" suffix-icon="" style="width: 120px">
              <el-option
                v-for="item in biz_model_platform"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="拍摄国家">
            <el-select v-model="props.nation" :disabled="true" suffix-icon="" style="width: 120px">
              <el-option
                v-for="item in biz_nation"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="模特类型" v-if="props.modelType != '3'">
            <el-select
              v-model="props.modelType"
              placeholder="请选择模特类型"
              :disabled="true"
              suffix-icon=""
              style="width: 120px"
            >
              <el-option
                v-for="item in biz_model_type"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="模特类型" v-else>
            <el-select
              placeholder="请选择模特类型"
              v-model="queryParams.type"
              suffix-icon=""
              style="width: 130px"
              clearable
            >
              <el-option
                v-for="item in biz_model_type"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="模特类型">
            <el-select v-model="props.modelType" :disabled="true" suffix-icon="" style="width: 120px">
              <el-option
                v-for="item in biz_model_type"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            
          </el-form-item> -->
          <el-form-item label="模特名称" prop="name">
            <el-input
              v-model="queryParams.name"
              clearable
              style="width: 150px"
              placeholder="请输入模特名称"
            ></el-input>
          </el-form-item>
          <el-form-item label="模特ID" prop="account">
            <el-input
              v-model="queryParams.account"
              clearable
              style="width: 120px"
              placeholder="请输入模特ID"
            ></el-input>
          </el-form-item>
          <el-form-item label="模特性别" prop="sex">
            <el-select v-model="queryParams.sex" multiple clearable placeholder="请选择" style="width: 120px">
              <el-option label="男性" :value="1" />
              <el-option label="女性" :value="0" />
            </el-select>
          </el-form-item>
          <el-form-item label="模特标签" prop="tags">
            <el-select
              v-model="queryParams.tags"
              multiple
              collapse-tags
              collapse-tags-tooltip
              clearable
              placeholder="请选择"
              style="width: 130px"
            >
              <el-option v-for="item in modelTagsList" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="擅长品类" prop="specialtyCategory">
            <el-select
              v-model="queryParams.specialtyCategory"
              multiple
              collapse-tags
              collapse-tags-tooltip
              clearable
              placeholder="请选择"
              style="width: 130px"
            >
              <el-option
                v-for="item in modelCategoryList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="模特年龄层" prop="ageGroup">
            <el-select
              v-model="queryParams.ageGroup"
              multiple
              collapse-tags
              collapse-tags-tooltip
              clearable
              placeholder="请选择"
              style="width: 130px"
            >
              <el-option
                v-for="item in biz_model_ageGroup"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" native-type="submit" @click="onQuery">搜索</el-button>
            <el-button @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-tabs :model-value="curTab" class="tabs">
          <el-tab-pane label="模特列表" :name="1">
            <div v-loading="loading" class="list-box">
              <div v-show="!modelList.length">
                <el-empty description="暂无数据" :image-size="80" v-once></el-empty>
              </div>
              <ModelListItem
                v-for="item in modelList"
                :key="item.id"
                ref="ModelListRef"
                :data="item"
                @selectModelInfo="handleSelectModelInfo"
                @openVideo="handleOpenVideo"
                @openPhoto="handleOpenPhoto"
                @openDetails="handleOpenDetails"
              />
            </div>
            <div class="pagination-box">
              <el-pagination
                background
                @size-change="pageChange({ pageNum: 1, pageSize: $event })"
                @current-change="pageChange({ pageNum: $event, pageSize })"
                :current-page="pageNum"
                :page-size="pageSize"
                layout="total, prev, pager, next, jumper"
                :total="total"
              />
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-dialog>
    <ModelVideoList ref="ModelVideoListRef" />
    <ModelLifePhoto ref="ModelLifePhotoRef" />
    <DialogVideo ref="dialogVideoRef" :videoSrc="videoSrc" @close="videoSrc = ''" />
  </div>
</template>

<script setup>
import { computed, getCurrentInstance, ref } from 'vue'
import {
  modelCategorySelectRank,
  modelCategorySelect,
  getModelList,
  getChangeModelList,
  modelCategoryTagSelect,
} from '@/api/model/model'
import ModelListItem from '@/views/order/components/dialog/modelListItem.vue'
import ModelVideoList from '@/views/model/modelManage/components/modelVideoList'
import ModelLifePhoto from '@/views/model/modelManage/components/modelLifePhoto'
import DialogVideo from '@/components/Dialog/video'
const { proxy } = getCurrentInstance()
const { biz_model_type, biz_nation, biz_model_platform, biz_model_cooperation, biz_model_ageGroup } =
  proxy.useDict(
    'biz_model_type',
    'biz_nation',
    'biz_model_platform',
    'biz_model_cooperation',
    'biz_model_ageGroup'
  )
const props = defineProps({
  nation: {
    type: String,
    required: true,
  },
  platform: {
    type: String,
    required: true,
  },
  modelType: {
    type: String,
    required: true,
  },
})
defineExpose({ open, close })

const queryRef = ref(null)
const ModelListRef = ref(null)
const ModelVideoListRef = ref(null)
const ModelLifePhotoRef = ref(null)
const dialogVisible = ref(false)
const dialogVideoRef = ref(null)
const videoSrc = ref('')
const modelTagsList = ref([])
const modelCategoryList = ref([])
const queryParams = ref({
  account: undefined,
  name: undefined,
  sex: [],
  ageGroup: [],
  specialtyCategory: [],
  tags: [],
  type: '',
})
const extraParams = ref({})
const loading = ref(false)
const pageNum = ref(1)
const pageSize = ref(10)
const pageSizes = [10, 20, 50, 100]
const total = ref(0)
const curTab = ref(1)
const modelList = ref([])

const emits = defineEmits(['selectModelInfo'])
function resetFrom() {
  pageNum.value = 1
  pageSize.value = 20
  queryParams.value = {
    account: undefined,
    name: undefined,
    sex: [],
    ageGroup: [],
    specialtyCategory: [],
    tags: [],
    type: '',
  }
}
function open(param = {}) {
  getModelCategorySelect()
  getModelTagsSelect()
  pageNum.value = 1
  extraParams.value = param
  let params = {
    ...queryParams.value,
    ...extraParams.value,
  }
  getModelMerchantList(params)
  dialogVisible.value = true
}
function close() {
  resetFrom()
  dialogVisible.value = false
}
// 模特擅长品类下拉
function getModelCategorySelect() {
  modelCategorySelectRank({ rank: 1, categoryId: 1008 }).then(res => {
    if (res.code == 200) {
      modelCategoryList.value = res.data
    }
  })
}
// 模特标签下拉
function getModelTagsSelect() {
  modelCategoryTagSelect({ categoryId: '1009' }).then(res => {
    if (res.code == 200) {
      modelTagsList.value = res.data
    }
  })
  // modelCategorySelect({ status: 0, categoryId: 1009 }).then(res => {
  //   if (res.code == 200) {
  //     modelTagsList.value = res.data
  //   }
  // })
}
// 获取模特列表
function getModelMerchantList(params) {
  params.pageNum = pageNum.value
  params.pageSize = pageSize.value
  if (props.nation) {
    params.nation = props.nation
  }
  if (props.platform) {
    params.platform = props.platform
  }
  if (props.modelType) {
    params.type = props.modelType == '3' ? '' : props.modelType
  }
  if (queryParams.value.type) {
    params.type = queryParams.value.type
  }
  loading.value = true
  getChangeModelList(params)
    .then(res => {
      modelList.value = res.data.rows
      total.value = res.data.total
    })
    .finally(() => (loading.value = false))
}
// 分页跳转
function pageChange(page) {
  pageNum.value = page.pageNum
  pageSize.value = page.pageSize
  const listBox = document.querySelector('.list-box')
  // listBox.scrollTo({top: 0, behavior: 'smooth'})
  listBox ? listBox.scrollTop = 0 : ''
  handleQuery(true)
}
//搜索
function onQuery() {
  pageNum.value = 1
  handleQuery()
}

function handleQuery() {
  // pageNum.value = 1
  let params = {
    ...queryParams.value,
    ...extraParams.value,
  }
  getModelMerchantList(params)
}
function handleOpenVideo(src) {
  videoSrc.value = src
  dialogVideoRef.value?.open()
}
function handleOpenPhoto(photos) {
  ModelLifePhotoRef.value?.open(photos)
}
function handleOpenDetails(row) {
  ModelVideoListRef.value?.open(row.id)
}
function handleSelectModelInfo(data) {
  emits('selectModelInfo', data)
  close()
}
//重置
function resetQuery() {
  pageNum.value = 1
  pageSize.value = 20
  queryParams.value = {
    account: undefined,
    name: undefined,
    sex: [],
    ageGroup: [],
    specialtyCategory: [],
    tags: [],
    type: '',
  }
  handleQuery()
}
</script>

<style lang="scss" scoped>
.model-select-list-box {
  position: relative;
  padding-bottom: 80px;
  max-height: 750px;
  // max-height: 80vh;
  .el-form-item {
    margin-bottom: 12px;
  }
}
.list-box {
  max-height: 510px;
  // max-height: 55vh;
  overflow-y: auto;
  min-height: 210px;
  padding: 0 10px 10px 6px;
}
.pagination-box {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}
</style>
