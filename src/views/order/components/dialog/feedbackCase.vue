<template>
  <el-dialog
    v-model="dialogVisible"
    width="700px"
    title="匹配情况反馈"
    align-center
    destroy-on-close
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <div class="form-box" v-if="!isDetailDialog">
      <el-input
        v-model="feedbackCaseContent"
        style="width: 100%"
        :rows="4"
        type="textarea"
        maxlength="300"
        show-word-limit
        placeholder="请输入反馈内容"
        :disabled="disabled"
        clearable
      />
      <div class="flex-between" style="align-items: flex-start;margin: 10px 0;">
        <span style="color: red;">注意：若商家还未回复，请不要重复发起反馈；</span>
        <el-button v-btn type="primary" round :loading="disabled" @click="submit">发起反馈</el-button>
      </div>
      <h2>
        历史匹配情况进度
      </h2>
    </div>
    <div class="case-box" v-loading="loading">
      <el-steps
        direction="vertical"
        process-status="wait"
        finish-status="success"
      >
        <el-step
          v-for="(item) in steps"
          :key="item.id"
          :title="item.sendTime"
          :status="item.replyContent == '0' ? 'process' : 'wait'"
        >
          <template #icon>
            <div class="icon-box" :class="{'icon-active': item.replyContent == '0'}"></div>
          </template>
          <template #description>
            <div class="description" v-if="item.replyContent == '0'">
              <div class="flex-start curColor">
                {{ item.sendUser.name || '蜗牛运营' }}：
                {{ item.sendContent }}
              </div>
              <div class="reply">
                <span v-if="item?.replyUser?.name">{{ item.replyUser.name}}：</span>
                <span v-else>商家：</span>
                <span class="wait">等待商家确认中</span>
              </div>
            </div>
            <div class="description" v-else>
              <el-button v-if="item.replyContent === 1 && item.operateEdit == 0 && !isDetailDialog" class="edit-btn" round type="primary" @click="editOrder(item)">修改订单</el-button>
              <div>
                {{ item.sendUser.name || '蜗牛运营' }}：{{ item.sendContent }}
              </div>
              <div class="flex-start reply">
                <span v-if="item?.replyUser?.name">{{ item.replyUser.name}}：</span>
                <span v-else>商家：</span>
                <div>
                  <div
                    class="reply-content"
                    :class="{ success: item.replyContent === 1 }"
                    v-if="item.replyContent !== 0"
                  >
                    {{ item.replyContent === 1 ? '同意' : '不同意' }}
                  </div>
                  <div>{{ item.reason }}</div>
                </div>
              </div>
            </div>
          </template>
        </el-step>
      </el-steps>
      <div v-if="!steps.length" class="flex-center no-data">
        暂无反馈信息
      </div>
    </div>
  </el-dialog>
  <EditDemands ref="EditDemandsRef" @success="getList()" />
</template>

<script setup>
import EditDemands from '@/views/order/components/dialog/editDemands.vue'
import { orderfeedback, sendOrderFeedbackCase } from '@/api/order/order'
import { ElMessageBox, ElMessage } from 'element-plus'

const dialogVisible = ref(false)
const videoId = ref('')
// const active = ref(2)
const steps = ref([])
const loading = ref(false)
const disabled = ref(false)
const feedbackCaseContent = ref('')

const EditDemandsRef = ref()

defineExpose({
  open,
  close
})

// const emits = defineEmits(['success'])
const isDetailDialog = ref(false)

function open(id,isDetail = false) {
  isDetailDialog.value = isDetail
  videoId.value = id
  dialogVisible.value = true
  getList()
}
function close() {
  dialogVisible.value = false
}
function handleClose() {
  feedbackCaseContent.value = ''
  steps.value = []
  disabled.value = false
}

function getList() {
  loading.value = true
  orderfeedback(videoId.value).then(res => {
    steps.value = res.data
  }).finally(() => loading.value = false)
}

function submit() {
  if(!feedbackCaseContent.value) {
    ElMessage.error('请输入反馈内容！')
    return
  }
  ElMessageBox.confirm('确认将匹配情况发送给商家吗？', '温馨提示', {
    autofocus: false,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
  }).then(() => {
    disabled.value = true
    sendOrderFeedbackCase({
      sendContent: feedbackCaseContent.value,
      videoId: videoId.value
    }).then(res => {
      ElMessage.success('操作成功')
      feedbackCaseContent.value = ''
      getList()
    }).finally(() => disabled.value = false)
  }).catch(() => {}) 
}

function editOrder(row) {
  EditDemandsRef.value.open(videoId.value, row.id)
}
</script>

<style scoped lang="scss">
.form-box {

  h2 {
    color: #000;
    font-weight: 600;
  }
}
.case-box {
  min-height: 400px;
  max-height: 600px;
  overflow-y: auto;

  .icon-box {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #d7d7d7;
  }
  .icon-active {
    background: var(--el-color-primary-light-2);
  }
  .curColor {
    color: #000;
  }
  .description {
    position: relative;
    font-size: 15px;

    .edit-btn {
      position: absolute;
      top: -26px;
      right: 0;
    }
  }
  .reply {
    margin-top: 10px;
    align-items: baseline;

    span {
      flex-shrink: 0;
    }

    .reply-content {
      padding: 1px 10px 1px 0px;
      font-size: 15px;
      color: var(--el-color-danger);
      // border-radius: var(--el-border-radius-round);
      // background-color: var(--el-color-info);

      &.success {
        color: var(--el-color-success);
      }
    }
    .wait {
      color: var(--el-color-primary-light-2);
    }
  }

  .no-data {
    width: 100%;
    height: 250px;
    font-size: 16px;
    color: #7f7f7f;
  }

  :deep(.el-step__main) {
    margin-bottom: 15px;
  }
  :deep(.el-step__description) {
    padding: 0;
  }
}
</style>