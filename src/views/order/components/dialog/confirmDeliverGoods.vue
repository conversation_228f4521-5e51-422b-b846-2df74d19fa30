<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isFirst ? '订单发货' : '填写补发物流单号'"
    width="550px"
    align-center
    append-to-body
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <div class="flex-center confirm-box">
      <el-form ref="formRef" :model="form" label-width="auto" @submit.native.prevent>
        <div class="form-box">
          <el-form-item label="发货方式" v-if="isFirst">
            <el-radio-group v-model="form.logisticFlag">
              <el-radio :value="0">物流发货</el-radio>
              <el-radio :disabled="flag" :value="1">标记发货</el-radio>
            </el-radio-group>
            <div class="tip" style="width: 100%; line-height: 13px">
              请注意：一经确认发货，订单将流转至待完成，请确保物流单号的准确性。
            </div>
          </el-form-item>
          <template v-for="(item, index) in form.Logistics" :key="index">
            <el-form-item
              v-if="form.logisticFlag != 1"
              label="物流单号"
              :prop="'Logistics.' + index + '.orderNum'"
              :rules="orderNumRules"
            >
              <div class="form-item">
                <el-input
                  v-model.trim="item.orderNum"
                  placeholder="请输入正确的物流单号"
                  maxlength="100"
                  clearable
                  @input="val => (item.orderNum = val.replace(/\s+/g, ''))"
                />
                <!-- <el-icon
                  v-if="form.Logistics && form.Logistics.length - 1 === index"
                  :size="20"
                  @click="handleAddInput"
                >
                  <CirclePlusFilled />
                </el-icon>
                <el-icon v-if="form.Logistics.length > 1" :size="20" @click="handleDelInput(index)">
                  <Delete />
                </el-icon> -->
              </div>
            </el-form-item>
            <el-form-item label="备注" v-if="isFirst">
              <el-input
                v-model="form.logisticFlagRemark"
                placeholder="请输入备注"
                maxlength="150"
                show-word-limit
                type="textarea"
                :rows="4"
                resize="none"
                clearable
              />
            </el-form-item>
            <el-form-item
              v-if="!isFirst"
              label="补发原因"
              :prop="'Logistics.' + index + '.reissueCause'"
              :rules="remarkRules"
            >
              <div class="form-item">
                <el-input
                  v-model="item.reissueCause"
                  placeholder="请输入补发原因"
                  maxlength="300"
                  show-word-limit
                  type="textarea"
                  :rows="4"
                  clearable
                />
              </div>
            </el-form-item>
            <el-form-item
              v-if="!isFirst && finishTaskList?.length"
              label="完结任务"
              :prop="'Logistics.' + index + '.taskDetailIds'"
              :rules="taskDetailIdsRules"
            >
              <div v-loading="taskLoading" class="task-box">
                <div>
                  <span>请点选需要同步完结的任务单</span>
                  <span style="color: #7f7f7f">（支持多个处理中的任务同步完结）</span>
                </div>
                <TaskButton v-model="item.taskDetailIds" :checkList="finishTaskList" :width="410" />
              </div>
            </el-form-item>
          </template>
        </div>
      </el-form>

      <!-- <div class="flex-center btn">
        <el-button v-btn type="primary" round @click="onConfirm()">
          {{ isFirst ? '确认发货' : '确认补发' }}
        </el-button>
      </div> -->
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="close()">取消</el-button>
        <el-button type="primary" @click="onConfirm()">{{ isFirst ? '确认' : '确认补发' }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { orderShipping, getShippingInfo, shippingFlag } from '@/api/order/order'
import { english_d_reg } from '@/utils/RegExp'
import TaskButton from '@/components/Button/TaskButton.vue'
import { getReturnPendingTask } from '@/api/task/workOrder'
import { checkLogisticRepetition } from '@/api/task/logistics.js'
import { ElMessageBox, ElIcon } from 'element-plus'
import { Warning } from '@element-plus/icons-vue'

const { proxy } = getCurrentInstance()

const dialogVisible = ref(false)
const formRef = ref()
const videoId = ref('')
const form = ref({
  logisticFlag: 0,
  logisticFlagRemark: '',
  Logistics: [
    {
      orderNum: '',
      reissueCause: '',
      taskDetailIds: [],
    },
  ],
})
const isFirst = ref(true)

const finishTaskList = ref([])
const taskLoading = ref(false)

const orderNumRules = [
  { required: true, message: '*物流单号不能为空', trigger: 'blur' },
  { pattern: english_d_reg, message: '请输入字母或数字', trigger: 'change' },
]
const remarkRules = [{ required: true, message: '*补发原因不能为空', trigger: ['blur', 'change'] }]
const taskDetailIdsRules = [{ required: true, message: '请选择完结任务', trigger: 'change' }]

defineExpose({
  open,
})

const emits = defineEmits(['success'])
const flag = ref(false)
function open(id, first = false, logisticFlag = false) {
  isFirst.value = first
  videoId.value = id
  dialogVisible.value = true
  if (!first) getTaskList()
  if (first) getLogisticFlag()
}

function close() {
  dialogVisible.value = false
}

function handleClose() {
  form.value = {
    logisticFlag: 0,
    logisticFlagRemark: '',
    Logistics: [
      {
        orderNum: '',
        reissueCause: '',
        taskDetailIds: [],
      },
    ],
  }
  flag.value = false
  finishTaskList.value.length = 0
  formRef.value.resetFields()
}

function handleAddInput() {
  form.value.Logistics.push({
    orderNum: '',
  })
}
function handleDelInput(i) {
  form.value.Logistics.splice(i, 1)
}

function getTaskList() {
  taskLoading.value = true
  getReturnPendingTask({
    videoId: videoId.value,
  })
    .then(res => {
      if (res.code === 200 && res.data?.length) {
        finishTaskList.value = res.data
      }
    })
    .catch(() => {
      finishTaskList.value.length = 0
    })
    .finally(() => {
      taskLoading.value = false
    })
}

//查询是否标记发货
function getLogisticFlag() {
  getShippingInfo(videoId.value).then(res => {
    if (res.data && res.data.logisticFlag && res.data.logisticFlag == 1) {
      flag.value = true
    }
  })
}

function onConfirm() {
  formRef.value.validate(valid => {
    if (valid) {
      proxy.$modal.loading('正在提交中，请稍后')
      if (form.value.logisticFlag == 1) {
        shippingFlag({
          videoId: videoId.value,
          remark: form.value.logisticFlagRemark,
        })
          .then(res => {
            proxy.$modal.msgSuccess('操作成功')
            emits('success')
            close()
          })
          .finally(() => proxy.$modal.closeLoading())
      } else {
        checkRepetition()
      }
    }
  })
}

//查询是否重复
function checkRepetition() {
  checkLogisticRepetition(form.value.Logistics[0].orderNum)
    .then(res => {
      if (res.code == 200) {
        if (res.data) {
          openTip()
        } else {
          proxy.$modal.confirm('确认保存？', '提示', {}).then(() => {
            handleOrderShipping()
          })
        }
      }
    })
    .finally(() => {
      proxy.$modal.closeLoading()
    })
}
// 重复提醒
function openTip() {
  ElMessageBox({
    title: '重复提醒',
    showCancelButton: true,
    showConfirmButton: true,
    message: () =>
      h(
        'div',
        {
          class: 'flex-column gap-10',
          style: {
            width: '396px',
          },
        },
        [
          h(
            ElIcon,
            {
              color: 'var(--el-color-warning)',
              size: 70,
            },
            () => h(Warning)
          ),
          h(
            'div',
            {
              style: {
                padding: '10px 15px',
                'text-align': 'center',
              },
            },
            `物流单号重复，是否确认${isFirst.value ? '提交' : '补发'}物流?`
          ),
        ]
      ),
  })
    .then(() => {
      handleOrderShipping()
    })
    .catch(() => {
      proxy.$modal.closeLoading()
    })
}
// 补发物流单号
function handleOrderShipping()  {
  proxy.$modal.loading('正在提交中，请稍后')
  orderShipping({
    videoId: videoId.value,
    reissue: 1,
    // number: form.value.Logistics.map(item => item.orderNum).filter(Boolean),
    number: form.value.Logistics[0].orderNum,
    reissueCause: form.value.Logistics[0].reissueCause,
    taskDetailIds:
      !isFirst.value &&
      form.value.Logistics[0].taskDetailIds?.length &&
      form.value.Logistics[0].taskDetailIds[0] != -1
        ? form.value.Logistics[0].taskDetailIds
        : undefined,
  })
    .then(res => {
      proxy.$modal.msgSuccess('操作成功')
      emits('success')
      close()
    })
    .finally(() => proxy.$modal.closeLoading())
}
</script>

<style scoped lang="scss">
:deep(.el-form-item__error) {
  // left: 20px;
}
.confirm-box {
  flex-direction: column;
  min-height: 230px;

  .form-box {
    width: 488px;
    max-height: 500px;
    overflow-y: auto;

    .form-item {
      width: 100%;
      gap: 6px;
      color: var(--el-color-primary);
      .el-icon {
        cursor: pointer;
      }
    }

    .task-box {
      width: 100%;
      max-height: 300px;
      overflow: hidden;
      overflow-y: auto;
    }
  }

  .tip {
    text-align: center;
    font-size: 12px;
    color: var(--el-color-danger);
  }
  .btn {
    margin: 24px 0 20px;
  }
}
</style>
