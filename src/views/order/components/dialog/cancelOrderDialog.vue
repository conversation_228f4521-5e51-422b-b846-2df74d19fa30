<template>
  <div>
    <el-dialog
      title="取消订单"
      v-model="dialogVisible"
      width="600px"
      align-center
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="close"
    >
      <div class="cancel-box">
        <div style="font-weight: 600">
          取消后合并中的订单将会被同步取消，是否确认取消所有合
          <br />
          并支付中的订单？
        </div>
        <div class="table-box">
          <el-table :data="orderList">
            <el-table-column prop="orderNum" label="订单号" align="center" width="180" />
            <el-table-column prop="payAmountDollar" label="美金" align="center">
              <template v-slot="{ row }">
                <div>${{ row.payAmountDollar }}</div>
              </template>
            </el-table-column>
            <el-table-column prop="currentExchangeRate" label="百度汇率" align="center" />
            <el-table-column prop="payAmount" label="人民币" align="center">
              <template v-slot="{ row }">
                <div>￥{{ row.payAmount }}</div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer flex-center">
          <el-button @click="close" round class="footer-btn">取 消</el-button>
          <el-button type="primary" @click="confirm" round class="footer-btn">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { cancelOrder } from '@/api/order/order'
import { ElMessage } from 'element-plus'
import useOrderApi from '@/hooks/useOrderApi'
const { handleCancelOrder } = useOrderApi()

const dialogVisible = ref(false)

const emits = defineEmits(['success'])
const orderList = ref([])
const orderNum = ref('')
const open = (list = [], orderNumber) => {
  orderList.value = list
  orderNum.value = orderNumber
  dialogVisible.value = true
}

const close = () => {
  orderList.value = []
  orderNum.value = ''
  dialogVisible.value = false
}

const confirm = () => {
  handleCancelOrder({ orderNum: orderNum.value }, () => {
    ElMessage.success('取消成功')
    emits('success')
    close()
  })
}

defineExpose({
  open,
  close,
})
</script>

<style lang="scss" scoped>
.cancel-box {
  font-size: 14px;
  text-align: center;
  // ;
}
.table-box {
  margin-top: 10px;
  max-height: 600px;
  overflow: auto;
}
.footer-btn {
  padding: 9px 30px;
}
</style>
