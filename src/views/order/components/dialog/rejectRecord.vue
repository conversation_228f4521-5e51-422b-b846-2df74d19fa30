<template>
  <el-dialog
    v-model="dialogVisible"
    title="订单驳回记录"
    width="500"
    align-center
    append-to-body
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
    style="font-weight: 600"
  >
    <div class="list-box" v-loading="loading">
      <el-empty
        v-if="!historyModels.length"
        description="暂无记录"
        :image-size="80"
        style="margin-top: 80px"
      ></el-empty>
      <div class="flex-column gap-10 model-box" v-for="(row, index) in historyModels" :key="row.id">
        <div class="tit">第{{ historyModels.length - index }}次驳回</div>
        <div class="flex-between gap-5" style="width: 100%">
          <div class="one-ell model-list-info">驳回时间：{{ row.oustTime }}</div>
          <div
            class="one-ell model-list-info"
            v-ellipsis-tooltips="row.oustOperatorName + ' / ' + row.oustOperatorNickName"
          >
            操作运营：{{ row.oustOperatorName || '-' }} / {{ row.oustOperatorNickName || '-' }}
          </div>
        </div>
        <div class="flex-between gap-5 divider" style="width: 100%">
          <div
            v-if="row.intentionModelName"
            class="one-ell model-list-info"
            v-ellipsis-tooltips="row.intentionModelName + ' / ' + row.intentionModelAccount"
          >
            新意向模特：{{ row.intentionModelName }} / {{ row.intentionModelAccount || '-' }}
          </div>
          <div v-else class="one-ell model-list-info">新意向模特：无</div>
          <div
            class="one-ell model-list-info"
            v-ellipsis-tooltips="row.rejectModelName + ' / ' + row.rejectModelAccount"
          >
            驳回模特：{{ row.rejectModelName || '-' }} / {{ row.rejectModelAccount || '-' }}
          </div>
        </div>
        <div class="model-list-info">驳回原因：{{ row.remark }}</div>
      </div>
    </div>

    <template #footer>
      <div class="flex-center">
        <el-button plain style="width: 100px" @click="close">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { getOrderVideoRejectRecord } from '@/api/order/preselection'

const dialogVisible = ref(false)
// const { proxy } = getCurrentInstance()

defineProps({
  disabled: Boolean,
})

defineExpose({
  open,
  close,
})

const emits = defineEmits(['success'])

const historyModels = ref([])
const loading = ref(false)
const videoId = ref('')

function open(id) {
  if (!id) return
  videoId.value = id
  getList()
  dialogVisible.value = true
}

function close() {
  dialogVisible.value = false
}

function handleClose() {
  historyModels.value.length = 0
  dialogVisible.value = false
}

function getList() {
  loading.value = true
  getOrderVideoRejectRecord({ videoId: videoId.value })
    .then(res => {
      if (res.code === 200) {
        historyModels.value = res.data
      }
    })
    .finally(() => {
      loading.value = false
    })
}
</script>

<style scoped lang="scss">
.list-box {
  // padding-right: 10px;
  min-height: 350px;
  max-height: 550px;
  overflow-y: auto;
  font-weight: 500;

  .model-box {
    padding: 20px;
    align-items: flex-start;
    background: #f7f7f7;
    border-radius: 8px;
    box-sizing: border-box;

    & + .model-box {
      margin-top: 20px;
    }

    .divider {
      padding-bottom: 15px;
      border-bottom: 1px solid #ebebeb;
      margin-bottom: 5px;
    }

    .tit {
      font-weight: 700;
      font-size: 15px;
      color: #000;
    }

    .model-list-info {
      width: 100%;
    }

    .flex-between .model-list-info {
      width: 50%;
      flex-shrink: 0;
    }
  }
}
</style>
