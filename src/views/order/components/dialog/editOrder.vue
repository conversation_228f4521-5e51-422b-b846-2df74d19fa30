<template>
  <div>
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'edit' ? '修改订单信息' : '审核订单'"
      width="1200"
      align-center
      append-to-body
      destroy-on-close
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="close"
    >
      <div v-loading="loading">
        <div class="form-box">
          <el-form label-width="110px" :model="form" ref="formRef" :rules="rules">
            <el-row :gutter="20">
              <el-col :span="12">
                <div class="form-back" style="background: #fafcf6">
                  <el-form-item label="视频编码">{{ form.videoCode }}</el-form-item>
                  <el-form-item label="平台" prop="platform">
                    <el-radio-group v-model="form.platform" @change="changeFormValue($event, 'platform')">
                      <el-radio-button
                        v-for="item in biz_model_platform"
                        :key="item.value"
                        :value="item.value"
                        :disabled="item.value == '3' && form.picCount != null"
                      >
                        {{ item.label }}
                      </el-radio-button>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item label="视频格式" prop="videoFormat">
                    <el-radio-group v-model="form.videoFormat">
                      <el-radio-button
                        v-for="item in videoFormatList"
                        :key="item.value"
                        :value="item.value"
                        :disabled="platformEmun.Tiktok == form.platform"
                      >
                        {{ item.label }}
                      </el-radio-button>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item label="拍摄国家" prop="shootingCountry">
                    <el-radio-group
                      v-model="form.shootingCountry"
                      @change="changeFormValue($event, 'shootingCountry')"
                    >
                      <el-radio-button v-for="item in biz_nation" :key="item.value" :value="item.value">
                        {{ item.label }}
                      </el-radio-button>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item label="模特类型" prop="modelTypeList">
                    <template #label>
                      <div>
                        <div>模特类型</div>
                        <div
                          style="font-size: 12px; margin-top: -15px; text-align: center"
                          v-if="isMultiSelModel"
                        >
                          (可多选)
                        </div>
                      </div>
                    </template>
                    <el-checkbox-group
                      v-model="form.modelTypeList"
                      @change="changeModelValue($event)"
                      v-if="isMultiSelModel"
                    >
                      <el-checkbox-button
                        v-for="item in biz_model_type"
                        :key="item.value * 1"
                        :value="item.value * 1"
                        :label="item.value * 1"
                      >
                        {{ item.label }}
                      </el-checkbox-button>
                    </el-checkbox-group>
                    <el-radio-group
                      v-model="form.modelType"
                      @change="changeFormValue($event, 'modelType')"
                      v-else
                    >
                      <el-radio-button
                        v-for="item in biz_model_type"
                        :key="item.value * 1"
                        :value="item.value * 1"
                        :disabled="modelTypeDisabled"
                      >
                        {{ item.label }}
                      </el-radio-button>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item label="产品中文名" prop="productChinese">
                    <el-input
                      placeholder="例如：女童压力裤"
                      v-model="form.productChinese"
                      maxlength="30"
                      show-word-limit
                      clearable
                    />
                  </el-form-item>
                  <el-form-item label="产品英文名" prop="productEnglish">
                    <el-input
                      v-model="form.productEnglish"
                      placeholder="例如：Girls compression shorts"
                      maxlength="50"
                      show-word-limit
                      clearable
                    />
                  </el-form-item>
                  <el-form-item label="产品链接" prop="isLink">
                    <div :class="{ 'flex-end': form.isLink == 1, 'form-link': form.isLink == 1 }">
                      <div style="margin-right: 10px">
                        <el-radio-group v-model="form.isLink" @change="doChangeIsLink">
                          <el-radio-button
                            v-for="item in [
                              { label: '有链接', value: 1 },
                              { label: '没链接', value: 0 },
                            ]"
                            :key="item.value"
                            :value="item.value"
                            :disabled="isSelectLinkDisabled"
                          >
                            {{ item.label }}
                          </el-radio-button>
                        </el-radio-group>
                        <el-input
                          v-if="form.isLink"
                          v-model="form.productLink"
                          placeholder="请输入产品链接"
                          clearable
                        />
                        <!-- @input="getLink" -->
                      </div>
                      <template v-if="form.isLink == 1">
                        <div style="width: 75px; height: 75px">
                          <el-image
                            :src="$picUrl + productLinkImg + '!squarecompress'"
                            class="flex-center"
                            style="width: 75px; height: 75px"
                          >
                            <template #error>
                              <img
                                :src="$picUrl + 'static/assets/no-img.png'"
                                alt=""
                                style="width: 100%; height: 100%"
                              />
                            </template>
                          </el-image>
                        </div>
                        <el-button
                          v-btn
                          link
                          type="primary"
                          :icon="Switch"
                          @click="handleDragUploadDialogOpen('product')"
                        >
                          更换图片
                        </el-button>
                      </template>
                    </div>
                  </el-form-item>
                  <el-form-item label="产品图" v-if="form.isLink == 0" prop="productPicInfo">
                    <!-- <div class="image-list" v-for="(item, i) in form.filePicList" :key="item.id">
                    <div class="image-modal flex-around">
                      <el-icon size="20" color="#ffffffc4">
                        <Delete style="cursor: pointer" @click="doDeleteProUrl(i)" />
                      </el-icon>
                      <el-icon size="20" color="#ffffffc4">
                        <View style="cursor: pointer" @click="doShowProUrl(i)" />
                      </el-icon>
                    </div>
                    <el-image
                      ref="imgViewRef"
                      preview-teleported
                      :src="$picUrl + item.picUrl + '!squarecompress'"
                      fit="fill"
                      class="image-item"
                      :preview-src-list="item.picUrl ? [item.picUrl] : []"
                    ></el-image>
                  </div>
                  <div
                    class="image-upload"
                    @click="DragUploadDialogRef1.open()"
                    v-if="form.filePicList && form.filePicList.length < 1"
                  >
                    <el-icon size="28" color="#909399"><Plus /></el-icon>
                  </div> -->
                    <DragImageUpload
                      v-model="form.filePicList"
                      @openUploadDialog="handleDragUploadDialogOpen('product')"
                    />
                    <!-- <CropperUpload
                    v-model="form.filePicList"
                    preview
                    :fixed-number-arr="[[4, 4]]"
                    :limit="1"
                    :bucketType="'order'"
                  /> -->
                  </el-form-item>
                  <el-form-item label="参考视频">
                    <el-input
                      v-model="form.referenceVideoLink"
                      placeholder="请输入参考视频的链接"
                      clearable
                    />
                  </el-form-item>
                  <el-form-item label="意向模特">
                    <div class="flex-column" v-if="form.selModelInfo?.account">
                      <div class="model-box">
                        <el-avatar :size="38" :src="$picUrl + form.selModelInfo.modelPic + '!1x1compress'" />
                        <div class="play-modal flex-around" @click="removeSelModel">
                          <el-icon size="20" color="#fff"><Delete /></el-icon>
                        </div>
                      </div>
                      <span style="margin-top: -15px">{{ form.selModelInfo.name }}</span>
                    </div>
                    <el-button link type="primary" v-btn :icon="Switch" @click="changeSelModel">
                      <span v-if="form.selModelInfo?.account">更换模特</span>
                      <span v-else>选择模特</span>
                    </el-button>
                  </el-form-item>
                  <el-form-item label="照片选配">
                    <!-- :disabled="disabled_edit" -->
                    <el-radio-group v-model="form.picCount" :disabled="true" v-if="form.picCount">
                      <el-radio-button
                        v-for="item in picCountOptionsList"
                        :key="item.value"
                        :value="item.value"
                        @click="picCountClick(item.value)"
                      >
                        {{ item.label }}
                      </el-radio-button>
                    </el-radio-group>
                    <span v-else>无</span>
                  </el-form-item>
                  <el-form-item label="参考图片" v-if="form.picCount">
                    <div class="image-list" v-for="(item, i) in form.referencePic" :key="item.id">
                      <div class="image-modal flex-around">
                        <el-icon size="20" color="#ffffffc4">
                          <Delete style="cursor: pointer" @click="doDeletePicUrl(i)" />
                        </el-icon>
                        <el-icon size="20" color="#ffffffc4">
                          <View style="cursor: pointer" @click="doShowPicUrl(i)" />
                        </el-icon>
                      </div>
                      <el-image
                        ref="imgViewRef"
                        preview-teleported
                        :src="$picUrl + item.picUrl + '!squarecompress'"
                        fit="fill"
                        class="image-item"
                        :preview-src-list="item.picUrl ? [item.picUrl] : []"
                      ></el-image>
                    </div>
                    <div
                      class="image-upload"
                      @click="form.picCount ? DragUploadDialogRef.open() : ''"
                      v-if="!disabled_edit && form.referencePic && form.referencePic.length < addPicNumber"
                    >
                      <el-icon size="28" color="#909399"><Plus /></el-icon>
                    </div>
                  </el-form-item>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="form-back" style="background: #fffaf5">
                  <div class="form-shoot">
                    <div class="form-head">
                      <div
                        class="form-head__label"
                        style="font-size: 14px; color: #606266; font-weight: 700; width: 110px"
                      >
                        <span v-if="isShowEditShootRequired" style="color: var(--el-color-danger)">*</span>
                        拍摄建议
                      </div>
                      <el-button round size="small" type="primary" @click="doEditShootRequired">
                        修改
                      </el-button>
                    </div>
                    <div class="form-translate">
                      <div class="form-translate__btn">
                        <el-button round size="small" @click="doTranslate">翻译</el-button>
                      </div>
                      <div style="width: 80%">
                        <div
                          class="form-translate__content"
                          :class="{ 'translate-top': isShowTranslateContent }"
                        >
                          <template v-for="(item, index) in form.shootRequired" :key="item.id">
                            {{ item.content }}
                            <br />
                          </template>
                          <!-- {{ content }} -->
                        </div>
                        <el-divider v-if="isShowTranslateContent" />
                        <div
                          class="form-translate__content translate-box"
                          v-if="isShowTranslateContent"
                          v-loading="translateLoading"
                        >
                          <template v-for="(item, index) in transformContentList" :key="index">
                            {{ item }}
                            <br />
                          </template>
                          <!-- {{ transformContent }} -->
                          <!-- <div class="translate-box__btn" @click="isShowTranslateContent = false">收起翻译</div> -->
                        </div>
                      </div>
                    </div>
                    <!-- <div class="form-translate" style="margin-top: 10px" v-if="isShowEditShootRequired">
                    <div class="form-translate__btn">
                      <el-button round size="small">改</el-button>
                    </div>
                    <div style="width: 80%">
                      <InputText :rows="4" ref="demandsRef" placeholder="1.请用英文输入产品卖点" v-model="form.isShowEditShootRequired"/>
                    </div>
                  </div> -->
                  </div>
                  <el-form-item
                    label=""
                    label-width="0"
                    prop="demands"
                    style="display: flex; flex-direction: column"
                    v-if="isShowEditShootRequired"
                  >
                    <div class="flex-center" style="width: 100%; margin-top: 10px">
                      <div style="width: 110px; text-align: center">
                        <el-button round size="small">改</el-button>
                      </div>
                      <el-input
                        style="width: 80%"
                        placeholder="1.请用英文输入产品卖点"
                        v-model="form.demands"
                        type="textarea"
                        :autosize="{ minRows: 4 }"
                        maxlength="5000"
                      />
                      <!-- <InputText
                      style="width: 80%"
                      :rows="4"
                      placeholder="1.请用英文输入产品卖点"
                      v-model="form.demands"
                      ref="demandsRef"
                      :length="5000"
                      :isShowLimit="false"
                    /> -->
                    </div>
                    <template #error>
                      <span class="hint-tip">*请用英文，简短描述重要的拍摄建议</span>
                    </template>
                  </el-form-item>
                  <el-form-item
                    label="匹配模特注意事项"
                    label-width="165px"
                    style="display: flex; flex-direction: column"
                    prop="cautionsTemp"
                  >
                    <el-input
                      style="width: 80%; margin-left: 110px"
                      placeholder="1.请输入注意事项内容"
                      v-model="form.cautionsTemp"
                      type="textarea"
                      :autosize="{ minRows: 4 }"
                      maxlength="800"
                    />
                    <!-- <InputText
                    ref="cautionsTempRef"
                    :rows="4"
                    style="margin-left: 110px"
                    placeholder="1.请输入注意事项内容"
                    v-model="form.cautionsTemp"
                  /> -->
                    <!-- <el-input
                    style="margin-left: 110px"
                    :rows="4"
                    type="textarea"
                    resize="none"
                    placeholder="1.请输入注意事项内容"
                  /> -->
                  </el-form-item>
                  <el-form-item>
                    <!-- <div class="image-list" v-for="(item, i) in imageList" :key="item.id">
                    <div @click.stop="doDeletePicUrl(i)" class="image-modal flex-around">
                    </div>
                    <el-image
                      ref="imgViewRef"
                      preview-teleported
                      :src="$picUrl + item.picUrl + '!square'"
                      fit="fill"
                      class="image-item"
                      :preview-src-list="item.picUrl ? [$picUrl + item.picUrl] : []"
                    />
                  </div>
                  <div
                    class="image-upload"
                    @click="doShowSelectImage"
                    v-if="imageList && imageList.length < 1"
                  >
                    <el-icon size="28" color="#909399"><Plus /></el-icon>
                  </div> -->
                    <DragImageUpload
                      v-model="imageList"
                      @openUploadDialog="handleDragUploadDialogOpen('cautionsImg')"
                      :limit="10"
                    />
                    <div class="prompt-text" style="width: 100%">
                      <div style="line-height: 20px; font-size: 12px">
                        1.请上传图片大小不超过5M，格式为 png/jpg、jpeg 的文件;
                        <br />
                        2.最多可以传10张
                      </div>
                    </div>
                  </el-form-item>
                  <el-form-item label="剪辑要求" prop="clipsRequiredTemp">
                    <el-input
                      style="width: 100%"
                      placeholder="1.请输入剪辑要求"
                      v-model="form.clipsRequiredTemp"
                      type="textarea"
                      :autosize="{ minRows: 4 }"
                      maxlength="800"
                    />
                    <!-- <InputText
                    :rows="4"
                    ref="clipsRequiredTempRef"
                    placeholder="1.请输入剪辑要求"
                    v-model="form.clipsRequiredTemp"
                  /> -->
                    <!-- <span v-if="isShowClipsErrow" class="hint-tip" style="margin-left: 0">*请用英文，简短描述剪辑要求</span> -->
                  </el-form-item>
                  <el-form-item label="视频时长" prop="videoDuration">
                    <el-radio-group v-model="form.videoDuration">
                      <el-radio-button :value="120">120S</el-radio-button>
                      <el-radio-button :value="90">90S</el-radio-button>
                      <el-radio-button :value="1">自定义</el-radio-button>
                    </el-radio-group>
                    <el-input-number
                      title=""
                      v-if="form.videoDuration === 1"
                      v-model="form.videoDurationCustom"
                      :min="1"
                      :max="99999"
                      :precision="0"
                      :step="1"
                      controls-position="right"
                      style="margin: -10px 0 0 10px"
                      placeholder="请输入视频需要的时长(单位:秒)"
                      clearable
                    />
                  </el-form-item>
                  <el-form-item label="产品品类" prop="productCategory">
                    <el-select
                      v-model="form.productCategory"
                      multiple
                      collapse-tags
                      clearable
                      placeholder="请选择"
                    >
                      <el-option
                        v-for="item in productCategoryList"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                  <div class="flex-center">
                    <el-button
                      v-if="dialogType === 'audit'"
                      size="large"
                      type="default"
                      v-btn
                      round
                      @click="saveCacheParams"
                      :disabled="isShowAudit"
                    >
                      保存本页
                    </el-button>
                    <el-button
                      v-if="dialogType === 'audit'"
                      size="large"
                      type="primary"
                      v-btn
                      round
                      @click="doSubmit"
                      :disabled="isShowAudit"
                    >
                      审核通过
                    </el-button>
                    <el-button
                      v-if="dialogType === 'edit'"
                      size="large"
                      type="primary"
                      v-btn
                      round
                      @click="saveEdit"
                    >
                      确认修改
                    </el-button>
                  </div>
                  <div v-if="dialogType === 'audit'" class="flex-center audit-tip">
                    确认审核通过后，内容将同步至商家端
                  </div>
                </div>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </div>

      <template #footer>
        <div class="flex-between" v-if="dialogType === 'audit'">
          <el-button
            style="font-size: 16px; padding: 10px"
            v-btn
            link
            :loading="loading"
            v-if="prevVideoId"
            icon="Back"
            @click="getDetails(prevVideoId, 'next')"
          >
            上一单
          </el-button>
          <div></div>
          <el-button
            style="font-size: 16px; padding: 10px"
            v-btn
            link
            :loading="loading"
            v-if="nextVideoId"
            @click="getDetails(nextVideoId, 'next')"
          >
            下一单
            <el-icon style="margin-left: 5px"><Right /></el-icon>
          </el-button>
        </div>
      </template>
    </el-dialog>
    <DragUploadDialog
      ref="DragUploadDialogRef"
      title="上传参考图片"
      :limit="addPicNumber1"
      @success="upSuccess"
      bucketType="order"
    />
    <DragUploadDialog
      ref="DragUploadDialogRef1"
      :title="DragUploadDialogTitle"
      :limit="dragUploadLimit"
      @success="changePicSuccess"
      bucketType="order"
    />
    <CropperDialog
      ref="CropperDialogRef"
      :img="imgFile"
      :fixed-number-arr="[[2, 2]]"
      :previews-width="180"
      :is-show-action="false"
      @confirm="onConfirmCropper"
    />
    <SelectSingleModel
      ref="SelectSingleModelRef"
      :nation="form.shootingCountry + ''"
      :modelType="form.modelType + ''"
      :platform="form.platform + ''"
      @selectModelInfo="handleSelectModelInfo"
    />
  </div>
</template>

<script setup>
import DragImageUpload from '@/components/ImageUpload/dragImageUpload.vue'
// import ShootRequired from '@/views/order/components/shootRequired.vue'
// import LimitingCondition from '@/views/order/components/limitingCondition.vue'
import { modelCategorySelectRank } from '@/api/model/model'
import { getVideoOrderDetail, getTranslate, editVideoOrder } from '@/api/order/order'
import { editMatchVideoOrder } from '@/api/order/preselection'
import { chinese_reg, englishCharacter_d_reg } from '@/utils/RegExp'
import { ElMessage, ElMessageBox } from 'element-plus'
import { uploadCloudFile } from '@/api/index'
import CropperDialog from '@/components/Cropper/cropperDialog'
import SelectSingleModel from '@/views/order/components/dialog/selectSingleModel.vue'
import { debounce } from '@/utils/public'

import InputText from '@/components/InputText/index.vue'
// import CropperUpload from '@/components/FileUpload/cropperUpload'
import { videoFormatOptions, picCountOptions, Platform } from '@/views/order/list/data.js'
import { Switch, Right } from '@element-plus/icons-vue'
import { useViewer } from '@/hooks/useViewer'

defineExpose({
  open,
  close,
})

const emits = defineEmits(['success'])
const { showViewer } = useViewer()
const { proxy } = getCurrentInstance()
const { biz_model_platform, biz_nation, biz_model_type } = proxy.useDict(
  'biz_model_platform',
  'biz_nation',
  'biz_model_type'
)

const pathHead = proxy.$picUrl
const videoFormatList = ref(videoFormatOptions)
const picCountOptionsList = ref(picCountOptions)
const platformEmun = ref(Platform)
const disabled_edit = ref(false)
const addPicNumber = ref(2)
const addPicNumber1 = ref(2)
const disabled = ref(false)

const translateLoading = ref(false)
const isShowEditShootRequired = ref(false)
const isShowClipsErrow = ref(false)
const productCategoryList = ref([])
const CropperDialogRef = ref(null)
const imgFile = ref({})
const SelectSingleModelRef = ref(null)
const DragUploadDialogRef1 = ref(null)
const DragUploadDialogTitle = ref('')
const dragUploadLimit = ref(1)

const dialogVisible = ref(false)
const orderId = ref('')
const dialogType = ref('audit')
const createUserId = ref('')
const loading = ref(false)
const formRef = ref()
const imageList = ref([])
const form = ref({
  videoCode: '',
  platform: '0',
  videoFormat: '1',
  shootingCountry: '7',
  modelType: '1',
  modelTypeList: [1],
  productChinese: '',
  productEnglish: '',
  isLink: 1,
  productLink: '',
  referenceVideoLink: '',
  picCount: 1,
  productPic: [],
  referencePic: [],
  productPicInfo: {},
  filePicList: [],
  videoDuration: 120,
  videoDurationCustom: 120,
  // customVideoDuration: 0,
  productCategory: '',
  selModelInfo: {},
  demands: '',
  shootRequired: [],
  clipsRequired: [],
  clipsRequiredTemp: '',
  cautionsTemp: '',
  cautionsPics: [],
  cautions: [],
})

const DragUploadDialogRef = ref(null)
const rules = ref({
  platform: [{ required: true, message: '请选择平台', trigger: 'blur' }],
  videoFormat: [{ required: true, message: '请选择平台视频格式', trigger: 'blur' }],
  shootingCountry: [{ required: true, message: '请选择拍摄国家', trigger: 'blur' }],
  modelTypeList: [{ required: true, message: '请选择模特类型', trigger: 'change' }],
  productChinese: [
    { required: true, message: '请输入产品中文名', trigger: 'blur' },
    // { pattern: chineseCharacter_d_reg, message: '请输入产品中文名', trigger: 'change' },
  ],
  productEnglish: [
    { required: true, message: '请输入产品英文名', trigger: 'blur' },
    { pattern: englishCharacter_d_reg, message: '请输入产品英文名', trigger: 'change' },
  ],
  isLink: [{ required: true, validator: checkIsLink, trigger: 'change' }],
  productPicInfo: [{ required: true, validator: selectProductPic, trigger: 'blur' }],
  // clipsRequiredTemp: [{ required: false, validator: checkClipsRequired, trigger: 'change' }],
  demands: [{ required: true, validator: checkDemands, trigger: 'change' }],
  productCategory: [{ required: true, message: '请选择产品品类', trigger: 'change' }],
  videoDuration: [{ required: true, message: '请选择视频时长', trigger: 'change' }],
})

function checkClipsRequired(rule, value, callback) {
  if (value) {
    // let index = value.findIndex((v: { value: string }) => v.value && !english_d_reg.test(v.value))
    let index = chinese_reg.test(value)
    if (index) {
      return (isShowClipsErrow.value = true)
      // return callback(new Error('*请用英文，简短描述剪辑要求'))
    } else {
      isShowClipsErrow.value = false
    }
  } else {
    isShowClipsErrow.value = true
  }
  return callback()
}
function checkDemands(rule, value, callback) {
  if (value) {
    // let index = value.findIndex((v: { value: string }) => v.value && !english_d_reg.test(v.value))
    if (chinese_reg.test(value)) {
      return callback(new Error('*请用英文，简短描述重要的拍摄建议'))
    }
  } else {
    return callback(new Error('*请用英文，简短描述重要的拍摄建议'))
  }
  return callback()
}
//根据已添加的图片计算剩余可添加图片数量
watch(
  () => form.value.referencePic?.length,
  (newVal, oldVal) => {
    addPicNumber1.value = addPicNumber.value - (newVal || 0)
  }
)
watch(
  () => addPicNumber.value,
  (newVal, oldVal) => {
    addPicNumber1.value = addPicNumber.value - (form.value.referencePic?.length || 0)
  }
)
let pcVal = false
// 处理照片数量单选框取消选中 picCountClick->picCountChange
function picCountClick(val) {
  // if (disabled.value || disabled_edit.value) return
  // pcVal = false
  // if (form.value.picCount === val) {
  //   pcVal = true
  //   form.value.picCount = undefined
  // }
}

//判断模特类型是否可以多选
const isMultiSelModel = computed(() => {
  if (form.value.shootingCountry == '7' && form.value.platform == Platform['Amazon'] && !form.value.picCount)
    return true
  else {
    formRef.value?.clearValidate('modelTypeList')
    return false
  }
})

const oldModelType = ref([])
watch(
  () => form.value.modelTypeList,
  (newVal, oldVal) => {
    if (oldVal && oldVal.length > 0) {
      oldModelType.value = oldVal
    }
  }
)

function handleSelectModelInfo(data) {
  const tempInfo = { ...data }
  tempInfo.pic = tempInfo.picUrl
  form.value.selModelInfo = tempInfo
}

function picCountChange() {
  // 点击取消选中且清空参考图
  if (form.value.picCount == 1) {
    addPicNumber.value = 2
    addPicNumber1.value = 2
    if (form.value.referencePic && form.value.referencePic.length > 2) {
      form.value.referencePic = form.value.referencePic.slice(0, 2)
    }
  } else {
    addPicNumber.value = 5
    addPicNumber1.value = 5
  }

  if (pcVal) {
    form.value.picCount = undefined
    form.value.referencePic = []
  }
}

function checkIsLink(rule, value, callback) {
  if (form.value.isLink == 0) return callback()
  if ((value && !form.value.productLink) || !form.value.productLink.startsWith('https://')) {
    return callback(new Error('请输入真实的产品链接'))
  }
  return callback()
}

function selectProductPic(rule, value, callback) {
  if (!form.value.filePicList || (form.value.filePicList && form.value.filePicList.length === 0)) {
    return callback(new Error('请上传产品图'))
  }
  return callback()
}

function open(id, type, cId) {
  orderId.value = id
  dialogType.value = type
  createUserId.value = cId
  dialogVisible.value = true
  getCategoryList()
  getDetails(id, type)
}
function close() {
  resetFrom()
  dialogVisible.value = false
}
const cautionsTempRef = ref(null)
const clipsRequiredTempRef = ref(null)
const demandsRef = ref(null)
//初始化操作
function resetFrom() {
  formRef.value.resetFields()
  isShowTranslateContent.value = false
  isShowEditShootRequired.value = false
  cautionsTempRef.value?.resetValue()
  clipsRequiredTempRef.value?.resetValue()
  demandsRef.value?.resetValue()
}

const productLinkImg = ref('')
const prevVideoId = ref('')
const nextVideoId = ref('')
const isShowAudit = ref(false)

function getDetails(id, type = '') {
  if (type == 'next') {
    resetFrom()
  }
  loading.value = true
  getVideoOrderDetail(id)
    .then(res => {
      if (typeof res.data == 'string') {
        // if(res.data == '订单状态有变动，请刷新后重试') {
        //   return isShowAudit.value = true
        // } else {
        return ElMessage.error(res.data)
        // }
      }
      isShowAudit.value = false
      orderId.value = id
      prevVideoId.value = res.data.prevVideoId
      nextVideoId.value = res.data.nextVideoId

      // 获取缓存参数
      if (dialogType.value === 'audit' && getCacheParams()) return

      let filePicList = []
      if (res.data.productPic && res.data.productPic.length > 0) {
        const tempData = {
          // url: res.data.productPic ? pathHead + res.data.productPic : '',
          // name: res.data.productPic ? pathHead + res.data.productPic : '',
          // id: undefined,
          // picUrl: res.data.productPic ? pathHead + res.data.productPic : '',
          url: res.data.productPic ? res.data.productPic : '',
          name: res.data.productPic ? res.data.productPic : '',
          id: undefined,
          picUrl: res.data.productPic ? res.data.productPic : '',
        }
        if (tempData.url && tempData.url != null) {
          filePicList = [tempData]
        }
      }
      let referencePic = []
      if (res.data.referencePic && res.data.referencePic.length) {
        referencePic = res.data.referencePic.map(item => ({
          url: item,
          picUrl: item,
        }))
      }
      let modelTypeList = []
      if (res.data.modelType == '3') {
        modelTypeList = [0, 1]
        temporaryChangeData.value.modelTypeList = [0, 1]
      } else if (res.data.platform == Platform['Amazon'] && res.data.shootingCountry == '7') {
        modelTypeList[0] = res.data.modelType
        temporaryChangeData.value.modelTypeList[0] = res.data.modelType
      } else {
        modelTypeList = [res.data.modelType]
      }
      if (
        res.data.orderVideoCautionsVO &&
        res.data.orderVideoCautionsVO.cautions &&
        res.data.orderVideoCautionsVO.cautions.length > 0
      ) {
        let cautionsText = res.data.orderVideoCautionsVO.cautions.map((item, i) => item.content).join('\n')
        form.value.cautionsTemp = cautionsText
      }
      if (
        res.data.orderVideoCautionsVO &&
        res.data.orderVideoCautionsVO.cautionsPics &&
        res.data.orderVideoCautionsVO.cautionsPics.length > 0
      ) {
        imageList.value = res.data.orderVideoCautionsVO.cautionsPics.map(item => ({
          picUrl: item,
          url: item,
        }))
      } else {
        imageList.value = []
      }
      form.value = {
        videoCode: res.data.videoCode,
        platform: res.data.platform + '',
        videoFormat: res.data.videoFormat,
        shootingCountry: res.data.shootingCountry + '',
        modelType: res.data.modelType,
        modelTypeList,
        productChinese: res.data.productChinese,
        isLink: res.data.productLink ? 1 : 0,
        productEnglish: res.data.productEnglish,
        productLink: res.data.productLink,
        referenceVideoLink: res.data.referenceVideoLink,
        picCount: res.data.picCount,
        selModelInfo: res.data.intentionModel ? res.data.intentionModel : {},
        shootRequired: res.data.shootRequired || [],
        productPic: res.data.productPic,
        referencePic,
        filePicList,
        demands: '',
        videoDuration: res.data.platform == '0' ? 120 : res.data.platform == '1' ? 90 : 1,
        videoDurationCustom: 120,
        clipsRequiredTemp: '',
        cautionsTemp: form.value.cautionsTemp,
        cautionsPics: [],
        orderVideoCautionsVO: res.data.orderVideoCautionsVO,
        // customVideoDuration: 0,
        productCategory: res.data.productCategory ? res.data.productCategory : '',
      }
      productLinkImg.value = res.data.productLink ? res.data.productPic : null
      addPicNumber.value = form.value.picCount == 2 ? 5 : 2
      temporaryChangeData.value.modelType = form.value.modelType
      temporaryChangeData.value.platform = form.value.platform
      temporaryChangeData.value.shootingCountry = form.value.shootingCountry
      // // 原拍摄要求
      // form.value.merchantShootRequired = res.data.merchantShootRequired
      // // 原限制条件
      // form.value.merchantLimitingCondition = res.data.merchantLimitingCondition
      // 产品品类
      if (res.data.productCategory?.length) {
        form.value.productCategory = res.data.productCategory.map(item => item.id)
      }
      // 视频时长
      if (type == 'edit') {
        if (res.data.videoDuration === 120) {
          form.value.videoDuration = 120
        } else if (res.data.videoDuration === 90) {
          form.value.videoDuration = 90
        } else {
          form.value.videoDuration = 1
          form.value.videoDurationCustom = res.data.videoDuration || 120
        }
      }
      // let cautionsText = ''
      // // 匹配模特注意事项
      // if (res.data.cautions?.length) {
      //   cautionsText = res.data.cautions.map((item, i) => i + 1 + '.' + item.content).join('\n')
      //   form.value.cautions = res.data.cautions
      // }
      let clipsRequiredText = ''
      // 剪辑要求
      if (res.data.clipsRequired?.length) {
        clipsRequiredText = res.data.clipsRequired.map((item, i) => item.content).join('\n')
        form.value.clipsRequiredTemp = clipsRequiredText
        form.value.clipsRequired = res.data.clipsRequired
      }
      nextTick(() => {
        // cautionsTempRef.value?.resetValue(cautionsText)
        // clipsRequiredTempRef.value?.resetValue(clipsRequiredText)
        // console.log('匹配模特注意事项', form.value.cautionsTemp, cautionsText)
        // console.log('剪辑要求', form.value.clipsRequiredTemp, clipsRequiredText)
      })
    })
    .catch(e => {
      console.log(e)

      close()
    })
    .finally(() => (loading.value = false))
}

const isSelModel = computed(() => {
  if (form.value.shootingCountry == '7' && form.value.platform == Platform['Amazon'] && form.value.picCount)
    return true
  else {
    formRef.value?.clearValidate('modelTypeList')
    return true
  }
})
// 表单联动 模特类型禁用
const modelTypeDisabled = computed(() => {
  if (form.value.platform != '0' || form.value.picCount) {
    // 平台不是亚马逊
    return true
  } else if (form.value.shootingCountry != '7') {
    // 平台亚马逊且国家不是美国
    return true
  }
  if (form.value.isLink == 0) {
    // 产品无链接
    return true
  }
  return false
})

//表单联动 产品链接
const isSelectLinkDisabled = computed(() => {
  if ((form.value.platform == '0' && form.value.modelType == '0') || form.value.modelType == '3') {
    return true
  } else {
    return false
  }
})
//更换模特
function changeSelModel() {
  if (isMultiSelModel.value && form.value.modelTypeList && form.value.modelTypeList.length == 0) {
    ElMessage.warning('请选择模特类型')
    return
  }
  SelectSingleModelRef.value.open({
    filterBlackListBizUserId: createUserId.value,
  })
}

//删除模特
function removeSelModel() {
  ElMessageBox.confirm('确定删除该意向模特吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      form.value.selModelInfo = {}
    })
    .catch(() => {})
}
//翻译
const isShowTranslateContent = ref(false)
const transformContentList = ref([])
function doTranslate() {
  if (isShowTranslateContent.value) {
    isShowTranslateContent.value = false
    return
  }
  if (form.value.shootRequired && form.value.shootRequired.length > 0) {
    translateLoading.value = true
    isShowTranslateContent.value = true

    let data = []
    form.value.shootRequired.forEach(item => {
      data.push(item.content)
    })
    // data = content.value.split('\n')
    getTranslate({ language: 1, wordList: data })
      .then(res => {
        if (res.data && res.data.length > 0) {
          transformContentList.value = res.data
          translateLoading.value = false
        }
      })
      .finally(() => (translateLoading.value = false))
  } else {
    ElMessage.warning('暂无翻译内容')
  }
}
//参考图片上传回调
function upSuccess(data) {
  form.value.referencePic?.push(...data)
  // addPicNumber.value = addPicNumber.value - (form.value.referencePic?.length || 0)
}

const imgLimit = computed(() => {
  let temp = 10
  if (imageList.value && imageList.value.length > 0) {
    temp = 10 - imageList.value.length
  }
  console.log(temp, imageList.value.length)
  return temp
})

function changePicSuccess(data) {
  if (DragUploadDialogTitle.value == '') {
    dragUploadLimit.value = imgLimit.value
    imageList.value.push(...data)
  } else {
    dragUploadLimit.value = 1
    if (form.value.isLink == 0) {
      form.value.filePicList.push(...data)
    } else {
      form.value.productPic = data.length > 0 ? data[0].picUrl : ''
      productLinkImg.value = data.length > 0 ? data[0].picUrl : ''
    }
  }
}

//有链接无链接切换
function doChangeIsLink() {
  form.value.productPic = productLinkImg.value ? productLinkImg.value : null
}

//平台切换时临时数据
const temporaryChangeData = ref({
  platform: '0',
  shootingCountry: '7',
  modelType: '1',
  modelTypeList: ['1'],
})

function changeModelValue(value) {
  if (value && value.length > 0) {
    value.indexOf(0) != -1 ? (form.value.isLink = 1) : ''
  }

  if (value.length == 1 || value.length == 0) {
    form.value.modelType = value[0]
    if (
      form.value.selModelInfo &&
      form.value.selModelInfo.id &&
      form.value.selModelInfo.type != form.value.modelType
    ) {
      ElMessageBox.confirm('', {
        customStyle: {
          'border-radius': '10px',
        },
        message: h('div', null, [
          h('div', { style: 'text-align: center;' }, `变更模特类型会导致已选模特无法满足拍摄需求`),
          h('div', { style: 'text-align: center;' }, `需重新选择模特`),
        ]),
        confirmButtonText: '好的',
        cancelButtonText: '取消',
        center: true,
        roundButton: true,
      })
        .then(() => {
          form.value.selModelInfo = {}
        })
        .catch(() => {
          form.value.modelTypeList = oldModelType.value
          if (form.value.modelTypeList && form.value.modelTypeList.length > 0) {
            form.value.modelTypeList.length == 2
              ? (form.value.modelType = '3')
              : (form.value.modelType = form.value.modelTypeList[0])
          }
        })
    }
  }
  if (value.length == 2) {
    form.value.modelType = '3'
  }
  // if (value.length == 0 && form.value.selModel && form.value.selModel.length > 0) {
  //   hintDialogTitle.value = '变更模特类型会导致已选模特无法满足拍摄需求'
  //   hintDialogContent.value = '需重新选择模特'
  //   isShowHint.value = true
  // }
}

//切换的一些联动
let content = ''
function changeFormValue(value, type) {
  let isConfirm = false
  if (!isMultiSelModel.value) {
    form.value.modelTypeList = [1]
  }
  if (type == 'platform') {
    content = '平台'
    isConfirm = handleModelPlatform(value)
  } else if (type == 'modelType') {
    content = '模特类型'
    isConfirm = true
  } else {
    content = '拍摄国家'
    isConfirm = true
  }
  if (form.value.selModelInfo && form.value.selModelInfo.id && isConfirm) {
    ElMessageBox.confirm('', {
      customStyle: {
        'border-radius': '10px',
      },
      message: h('div', null, [
        h('div', { style: 'text-align: center;' }, `变更${content}会导致已选模特无法满足拍摄需求`),
        h('div', { style: 'text-align: center;' }, `需重新选择模特`),
      ]),
      confirmButtonText: '好的',
      cancelButtonText: '取消',
      center: true,
      roundButton: true,
    })
      .then(() => {
        form.value.selModelInfo = {}
        if (type == 'platform') {
          selectPlatformChange(value)
          temporaryChangeData.value.platform = value + ''
        } else if (type == 'shootingCountry') {
          selectNationChange(value)
          temporaryChangeData.value.shootingCountry = value + ''
        } else {
          temporaryChangeData.value.modelType = value + ''
          selectModelTypeChange(value)
        }
      })
      .catch(() => {
        if (type == 'platform') {
          form.value.platform = temporaryChangeData.value.platform
        } else if (type == 'shootingCountry') {
          console.log(666)
          form.value.shootingCountry = temporaryChangeData.value.shootingCountry
        } else {
          form.value.modelType = temporaryChangeData.value.modelType
        }
        form.value.modelTypeList = oldModelType.value
        if (form.value.modelTypeList && form.value.modelTypeList.length > 0) {
          form.value.modelTypeList.length == 2
            ? (form.value.modelType = '3')
            : (form.value.modelType = form.value.modelTypeList[0])
        }
      })
  } else {
    if (type == 'platform') {
      selectPlatformChange(value)
      temporaryChangeData.value.platform = value + ''
    } else if (type == 'shootingCountry') {
      selectNationChange(value)
      temporaryChangeData.value.shootingCountry = value + ''
    } else {
      temporaryChangeData.value.modelType = value + ''
      selectModelTypeChange(value)
    }
  }
}

//切换时判断是否有符合条件的模特
function handleModelPlatform(value) {
  let temp = ''
  if (value === Platform['Amazon']) {
    temp = Platform['Amazon']
  }
  if (value === Platform['Tiktok']) {
    temp = Platform['Tiktok']
  }
  if (value === Platform['App']) {
    temp = Platform['App']
  }
  if (value === Platform['Other']) {
    temp = Platform['Other']
  }
  if (form.value.selModelInfo && form.value.selModelInfo.platform) {
    let tempList = form.value.selModelInfo.platform.split(',')
    temp = tempList.find(item => {
      return item == value
    })
  }
  return temp == value ? false : true
}

// 表单联动 平台
function selectPlatformChange(value) {
  if (value === platformEmun.value.Amazon) {
    form.value.videoFormat = 1
    form.value.videoDuration = 120
    // form.value.picCount = undefined
    disabled_edit.value = false
    // props.type === 'edit' ? (disabled_edit.value = true) : (disabled_edit.value = false)
    // if (form.value.selModel && form.value.selModel.length > 0) {
    //   form.value.selModel = []
    // }
    // rules.value.referenceVideoLink[0].required = false
    return
  }
  form.value.videoDuration = 90
  if (value === platformEmun.value.Tiktok) {
    disabled_edit.value = false
    form.value.videoFormat = 2
    form.value.modelType = 1
    // if (props.type === 'edit') {
    //   disabled_edit.value = true
    // }

    // if (form.value.selModel && form.value.selModel.length > 0) {
    //   form.value.selModel = []
    // form.value.selModel.filter(item => {
    //   return item.platform.indexOf(Platform['Tiktok']) != -1
    // })
    // }
    // rules.value.referenceVideoLink[0].required = true
    return
  }
  if (value === platformEmun.value.App) {
    disabled_edit.value = true
    form.value.videoFormat = 1
    form.value.picCount = undefined
    form.value.modelType = 1
    form.value.referencePic = []
    // if (form.value.selModel && form.value.selModel.length > 0) {
    //   form.value.selModel = []
    // }
    // rules.value.referenceVideoLink[0].required = false
    return
  }
  form.value.videoFormat = 1
  form.value.modelType = 1

  if (value === platformEmun.value.Other) {
    disabled_edit.value = false
    // if (props.type === 'edit') {
    //   disabled_edit.value = true
    // }
    // if (form.value.selModel && form.value.selModel.length > 0) {
    //   form.value.selModel = []
    // }
    // rules.value.referenceVideoLink[0].required = false
    return
  }
  // rules.value.referenceVideoLink[0].required = false
}
// 表单联动 拍摄国家
function selectNationChange(value) {
  if (value != '7' && form.value.platform == '0') {
    form.value.modelType = 1
    disabled_edit.value = false
  }
  // if (props.type === 'edit') {
  //   disabled_edit.value = true
  // }

  // if (form.value.selModel && form.value.selModel.length > 0) {
  //   form.value.selModel = form.value.selModel.filter(item => {
  //     return item.platform.nation == value
  //   })
  // }
}
// 表单联动 模特类型
function selectModelTypeChange(value) {
  if (form.value.modelType == '1' && form.value.platform == Platform['Amazon']) {
    disabled_edit.value = false
  } else {
    disabled_edit.value = true
    form.value.picCount = undefined
  }

  // if (form.value.selModel && form.value.selModel.length > 0) {
  //   form.value.selModel = form.value.selModel.filter(item => {
  //     if (item.platform) {
  //       return item.platform.type == value
  //     } else {
  //       return item.type == value
  //     }
  //   })
  // }
  // if (props.type === 'edit') {
  //   disabled_edit.value = true
  // }
  // if(form.value.selModel?.type != value) {
  //   form.value.selModel = {}
  // }
}
//展示参考图片
function doShowPicUrl(index) {
  const list = form.value.referencePic?.map(item => item.picUrl) || []
  showViewer(list, { index })
}
//展示参考图片
function doShowProUrl(index) {
  const list = form.value.filePicList?.map(item => item.picUrl) || []
  showViewer(list, { index })
}
//删除产品图片
function doDeleteProUrl(index) {
  form.value.filePicList?.splice(index, 1)
}
//删除参考图片
function doDeletePicUrl(index) {
  form.value.referencePic?.splice(index, 1)
}
//修改拍摄要求
function doEditShootRequired() {
  if (form.value.shootRequired && form.value.shootRequired.length > 0 && form.value.demands == '') {
    form.value.demands = form.value.shootRequired.map(item => item.content).join('\n')
  }
  isShowEditShootRequired.value = !isShowEditShootRequired.value
}
// 获取链接图片
function getLink(value) {
  debounce(getLinkApi(value))
}
const productLinkLoading = ref(false)
const productPicUrlError = ref(false)
function getLinkApi(url) {
  let curRandomCode = ''
  if (url) {
    curRandomCode = Math.random().toString(36).substring(2)
    let RandomCode = curRandomCode
    productLinkLoading.value = true
    // form.value.productPicInfo.id = 0
    // form.value.productPicInfo.name = ''
    // form.value.productPicInfo.picUrl = ''
    // form.value.productPicInfo.videoUrl = ''
    uploadAmazonUrl({ url })
      .then(res => {
        if (RandomCode === curRandomCode && res.code == 200) {
          if (res.msg) {
            productPicUrlError.value = true
          } else {
            productPicUrlError.value = false
            // form.value.productPicInfo.id = res.data.id
            // form.value.productPicInfo.name = res.data.name
            // form.value.productPicInfo.picUrl = res.data.picUrl
            productLinkImg.value = res.data.picUrl
          }
        }
      })
      .catch(() => {
        if (RandomCode === curRandomCode) {
          productPicUrlError.value = true
        }
      })
      .finally(() => {
        if (RandomCode === curRandomCode) {
          productLinkLoading.value = false
        }
      })
  }
}
//修改限制条件

//处理inputText的输入框内容转数组
function handleInputText(val) {
  if (val && val.length) {
    let countList = []
    countList = val.split('\n')
    return countList.map((item, index) => {
      // const startIndex = index < 9 ? 2 : 3
      return { content: item, sort: index, type: 1 }
      // return { content: item.slice(startIndex), sort: index, type: 1 }
    })
  } else {
    return []
  }
}

function handleSubmitParams() {
  const { clipsRequiredTemp, cautionsTemp, demands, selModelInfo, ...params } = form.value
  let referencePic = params.referencePic
  let videoDuration = params.videoDuration
  if (demands && demands.length && isShowEditShootRequired.value) {
    const shootRequired = handleInputText(demands)
    const temp = params.shootRequired
    params.shootRequired = shootRequired
    // temp?.forEach((item, i) => {
    //   if (item?.id) {
    //     params.shootRequired[i].id = item.id
    //   }
    // })
  }
  let c1 = handleInputText(clipsRequiredTemp)
  let c2 = handleInputText(cautionsTemp)
  if (imageList.value && imageList.value.length > 0) {
    imageList.value.forEach(item => {
      params.cautionsPics.push(item.picUrl)
    })
  }
  if (params.clipsRequired?.length) {
    c1 = c1?.map((item, i) => {
      if (params.clipsRequired[i]?.id) {
        return {
          ...params.clipsRequired[i],
          content: item.content,
        }
      }
      return item
    })
  }
  if (params.orderVideoCautionsVO.cautions?.length) {
    c2 = c2?.map((item, i) => {
      if (params.orderVideoCautionsVO.cautions[i]?.id) {
        return {
          ...params.orderVideoCautionsVO.cautions[i],
          content: item.content,
        }
      }
      return item
    })
  }
  params.clipsRequired = c1
  params.cautions = c2
  let intentionModelId
  if (selModelInfo.id) {
    intentionModelId = selModelInfo.id
  }
  if (params.picCount && referencePic && referencePic.length) {
    params.referencePic = referencePic.map(item => {
      if (item.response && item.response.code == 200) {
        return item.response.data.picUrl
      }
      return item.picUrl
    })
  } else {
    params.referencePic = null
  }
  // if (params.picCount && referencePic && referencePic.length) {
  //   params.referencePic = referencePic.map(item => {
  //     if (item.response && item.response.code == 200) {
  //       return {
  //         id: item.response.data.id,
  //         name: item.response.data.name,
  //         picUrl: item.response.data.picUrl,
  //         videoUrl: '',
  //       }
  //     }
  //     return {
  //       id: item.id,
  //       name: item.name,
  //       picUrl: item.picUrl,
  //       videoUrl: '',
  //     }
  //   })
  // }
  if (typeof params.productLink === 'string') {
    params.productLink = params.productLink.trim()
  }
  if (!params.isLink) {
    params.productLink = null
    if (params.filePicList && params.filePicList.length > 0) {
      params.productPic = params.filePicList[0].picUrl.startsWith(pathHead)
        ? params.filePicList[0].picUrl.slice(pathHead.length)
        : params.filePicList[0].picUrl
    }
  } else {
    // params.productPic = null
    params.productPic = productLinkImg.value ? productLinkImg.value : null
    params.productPicinfo = {}
  }
  if (params.videoDuration === 1) {
    videoDuration = params.videoDurationCustom
  } else {
    videoDuration = params.videoDuration
  }
  // if(params.picCount) {
  //   params.referencePic
  // }
  params.orderVideoCautionsDTO = {
    cautions: params.cautions,
    cautionsPics: params.cautionsPics,
  }
  return { ...params, intentionModelId, videoDuration }
}

// 缓存表单
const AUDIT_ORDER_PARAMS = 'auditOrderParams'
function saveCacheParams() {
  let cacheParams = localStorage.getItem(AUDIT_ORDER_PARAMS)
  if (cacheParams) {
    cacheParams = JSON.parse(cacheParams)
    cacheParams[orderId.value] = {
      form: form.value,
      imageList: imageList.value,
    }
  } else {
    cacheParams = { [orderId.value]: {
      form: form.value,
      imageList: imageList.value,
    }}
  }
  localStorage.setItem(AUDIT_ORDER_PARAMS, JSON.stringify(cacheParams))
  ElMessage.success('保存成功')
}
// 获取缓存表单
function getCacheParams() {
  let cacheParams = localStorage.getItem(AUDIT_ORDER_PARAMS)
  if (cacheParams) {
    cacheParams = JSON.parse(cacheParams)
    if (cacheParams[orderId.value]) {
      if (cacheParams[orderId.value].form) {
        form.value = cacheParams[orderId.value].form
        productLinkImg.value = cacheParams[orderId.value].form.productPic
        if (form.value.demands) {
          isShowEditShootRequired.value = true
        }
      }
      if (cacheParams[orderId.value].imageList) {
        imageList.value = cacheParams[orderId.value].imageList
      }
      return true
    }
  }
  return false
}
// 清除缓存表单
function clearCacheParams() {
  let cacheParams = localStorage.getItem(AUDIT_ORDER_PARAMS)
  if (cacheParams) {
    cacheParams = JSON.parse(cacheParams)
    if (cacheParams[orderId.value]) {
      delete cacheParams[orderId.value]
    }
    localStorage.setItem(AUDIT_ORDER_PARAMS, JSON.stringify(cacheParams))
  }
}

//审核通过
function doSubmit() {
  formRef.value.validate(valid => {
    if (valid) {
      loading.value = true
      let params = handleSubmitParams()
      editVideoOrder({ ...params, id: orderId.value, auditType: 9 })
        .then(res => {
          if (typeof res.data == 'string') {
            return ElMessage.error(res.data)
          }
          if (res.data && res.data.cannotModel && res.data.cannotModel.length > 0) {
            return ElMessage.warning('当前意向模特已无法满足拍摄需求')
          }
          ElMessage.success('审核成功')
          clearCacheParams()
          isShowAudit.value = true
          imageList.value = []
          emits('success')
          nextVideoId.value ? getDetails(nextVideoId.value, 'next') : close()
          // getDetails(orderId.value)
        })
        .catch(e => {})
        .finally(() => {
          loading.value = false
        })
    }
  })
}

// 编辑保存
function saveEdit() {
  formRef.value.validate(valid => {
    if (valid) {
      loading.value = true
      let params = handleSubmitParams()
      editMatchVideoOrder({ ...params, id: orderId.value })
        .then(res => {
          if (typeof res.data == 'string') {
            return ElMessage.error(res.data)
          }
          if (res.data && res.data.cannotModel && res.data.cannotModel.length > 0) {
            return ElMessage.warning('当前意向模特已无法满足拍摄需求')
          }
          ElMessage.success('保存成功')
          emits('success')
          close()
        })
        .catch(e => {})
        .finally(() => {
          loading.value = false
        })
    }
  })
}

//获取品类
function getCategoryList() {
  modelCategorySelectRank({ rank: 1, categoryId: 1008 }).then(res => {
    productCategoryList.value = res.data
  })
}
//更换图片
function changePic() {
  DragUploadDialogRef1.value.open()
}
// 上传按钮事件
let uploadRowData = {}
function fileInputChange(file) {
  imgFile.value = file
  // uploadRowData = row
  CropperDialogRef.value.open()
  // emits('uploadChange', file, btnName, rowData)
}
// 上传裁剪产品图
async function onConfirmCropper(img) {
  // console.log(img, uploadRowData);
  proxy.$modal.loading('正在上传中...')
  try {
    const formData = new FormData()
    formData.append('file', img.raw)
    let upres = await uploadCloudFile(formData, 'order')
    // form.value.productLink = pathHead + upres.data.picUrl
    form.value.productPic = upres.data.picUrl
    productLinkImg.value = upres.data.picUrl
    // let res = await uploadProductImg({
    //   resourceId: upres.data.id,
    //   videoId: uploadRowData.id,
    // })
    proxy.$modal.closeLoading()
    proxy.$modal.msgSuccess('上传成功！')
    handleQuery()
  } catch (e) {
    proxy.$modal.closeLoading()
  }
}
//上传匹配模特注意事项下面的图片
function doShowSelectImage() {
  console.log(6666)
}

//更换产品图与上传图片
function handleDragUploadDialogOpen(type) {
  if (type == 'cautionsImg') {
    DragUploadDialogTitle.value = ''
    dragUploadLimit.value = imgLimit.value
  } else {
    dragUploadLimit.value = 1
    DragUploadDialogTitle.value = '更换产品图'
  }
  DragUploadDialogRef1.value.open()
}
// getDetails()
</script>

<style scoped lang="scss">
@import '@/assets/styles/customForm.scss';
@import '@/assets/styles/form-disabled.scss';

// :deep(.el-checkbox-group) {
//   .el-checkbox-button.is-active {
//     .el-checkbox-button__inner {
//       border-left: none;
//       border-left-color: transparent;
//     }
//   }
//   .el-checkbox-button.is-checked:first-child {
//     .el-checkbox-button__inner {
//       border-left: none;
//     }
//   }
// }

.form-box {
  // :deep(.el-loading-mask){
  //   position: fixed;
  //   top: 0;
  //   left: 0;
  //   width: 100%;
  //   height: 100%;
  //   z-index: 9999; /* 遮罩层级要比进度条高 */
  //   background-color: rgba(0, 0, 0, 0.5);
  // }
  .hint-tip {
    margin-left: 110px;
    color: #f56c6c;
    font-size: 12px;
    line-height: 20px;
  }
  min-height: 500px;
  max-height: 700px;
  overflow-x: hidden;

  .form-back {
    height: 100%;
    border-radius: 15px;
    padding: 10px 10px 0 0;
  }
  .form-link {
    align-items: end;
  }
  .form-shoot {
    .form-head {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      &__label {
        font-size: 14px;
        color: #606266;
        font-weight: 700;
        width: 110px;
        text-align: end;
        padding-right: 12px;
      }
    }
    .form-translate {
      width: 100%;
      display: flex;
      align-items: center;
      &__content {
        line-height: 20px;
        padding: 5px 11px;
        white-space: pre-wrap;
        overflow-x: hidden;
        word-break: break-all;
        min-height: 94px;
        border: 1px solid var(--el-border-color);
        background: #fff;
        border-radius: var(--el-border-radius-base);
      }
      &__btn {
        width: 110px;
        text-align: center;
      }
      &__ipt {
        width: 80%;
      }
    }
    :deep(.el-divider) {
      width: 90%;
      margin: 0;
      margin-left: 21px;
      z-index: 6;
    }
    .translate-top {
      border-bottom: none;
      border-radius: 4px 4px 0 0;
    }
    .translate-box {
      color: #aaa;
      margin-top: -1px;
      border-top: none;
      border-radius: 0 0 4px 4px;
      position: relative;
      &__btn {
        color: #409eff;
        font-size: 12px;
        // position: absolute;
        position: -webkit-sticky;
        position: sticky;
        text-align: end;
        bottom: 2px;
        right: 2px;
        cursor: pointer;
      }
    }
  }
  .audit-tip {
    margin-top: 10px;
    color: #aaa;
    font-size: 16px;
  }
}
:deep(.el-upload-list__item) {
  width: 75px;
  height: 75px;

  img {
    object-fit: fill;
  }
}

:deep(.el-upload--picture-card) {
  width: 75px;
  height: 75px;
}
//上传样式
.image-upload {
  box-sizing: border-box;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px dashed var(--el-border-color-darker);
  width: 70px;
  height: 70px;
  border-radius: 6px;
  background-color: var(--el-fill-color-lighter);
  cursor: pointer;
  margin-bottom: 10px;
  // margin-top: -10px;
  &:hover {
    border-color: #409eff;
  }
}
.image-list {
  height: 70px;
  position: relative;
  margin-right: 10px;
  margin-bottom: 10px;
  .image-modal {
    position: absolute;
    top: 0;
    left: 0;
    width: 70px;
    height: 100%;
    border-radius: 6px;
    background-color: #2c2b2b66;
    z-index: 9;
    opacity: 0;
    &:hover {
      opacity: 1;
    }
  }
}
.image-item {
  border-radius: 6px;
  box-sizing: border-box;
  width: 70px;
  height: 70px;
  // margin-right: 10px;
}
.model-box {
  position: relative;
  &:hover {
    .play-modal {
      opacity: 1;
    }
  }
}
.play-modal {
  cursor: pointer;
  width: 38px;
  height: 38px;
  position: absolute;
  top: 0;
  left: 0;
  border-radius: 50%;
  background-color: #2c2b2b66;
  z-index: 9;
  opacity: 0;
}
</style>
