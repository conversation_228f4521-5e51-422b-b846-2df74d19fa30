<template>
  <el-dialog
    v-model="dialogVisible"
    width="500px"
    title="备注"
    align-center
    destroy-on-close
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <div class="form-box">
      <el-input
        v-model="remark"
        style="width: 100%"
        :rows="4"
        type="textarea"
        maxlength="300"
        show-word-limit
        placeholder="请输入备注内容"
        :disabled="disabled"
        clearable
      />
      <div class="flex-end" style="align-items: flex-start; margin: 10px 0">
        <el-button v-btn type="primary" round :loading="disabled" @click="submit">备注</el-button>
      </div>
      <h2>历史备注记录：</h2>
    </div>
    <div class="case-box" v-loading="loading">
      <el-steps direction="vertical">
        <el-step v-for="item in steps" :key="item.id" :title="item.createBy" status="process">
          <template #icon>
            <div class="icon-box"></div>
          </template>
          <template #description>
            <div class="description">
              <div class="waitColor">{{ item.createTime }}</div>
              <div>{{ item.commentContent }}</div>
            </div>
          </template>
        </el-step>
      </el-steps>
      <div v-if="!steps.length" class="flex-center no-data">暂无记录</div>
    </div>
  </el-dialog>
</template>

<script setup>
import { addOrderRemark, orderRemarkRecords } from '@/api/order/order'
import { ElMessageBox, ElMessage } from 'element-plus'

const emits = defineEmits(['success'])

const dialogVisible = ref(false)
const videoId = ref('')
// const active = ref(2)
const steps = ref([])
const loading = ref(false)
const disabled = ref(false)
const remark = ref('')

defineExpose({
  open,
  close,
})

// const emits = defineEmits(['success'])

function open(id) {
  videoId.value = id
  dialogVisible.value = true
  getList()
}
function close() {
  dialogVisible.value = false
  remark.value = ''
  steps.value = []
  disabled.value = false
}
function handleClose() {
  remark.value = ''
  steps.value = []
  disabled.value = false
}

function getList() {
  loading.value = true
  orderRemarkRecords({ videoId: videoId.value })
    .then(res => {
      steps.value = res.data
    })
    .finally(() => (loading.value = false))
}

function submit() {
  if (!remark.value) {
    ElMessage.error('请输入反馈内容！')
    return
  }
  ElMessageBox.confirm('确认备注吗？', '温馨提示', {
    autofocus: false,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
  })
    .then(() => {
      disabled.value = true
      addOrderRemark({
        commentContent: remark.value,
        videoId: videoId.value,
      })
        .then(res => {
          ElMessage.success('备注成功')
          emits('success')
          remark.value = ''
          getList()
        })
        .finally(() => (disabled.value = false))
    })
    .catch(() => {})
}
</script>

<style scoped lang="scss">
.case-box {
  max-height: 400px;
  overflow-y: auto;

  .icon-box {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #d7d7d7;
  }
  .icon-active {
    background: var(--el-color-primary-light-2);
  }
  .waitColor {
    color: #777;
  }
  .description {
    font-size: 16px;
  }

  .no-data {
    width: 100%;
    height: 150px;
    font-size: 16px;
    color: #7f7f7f;
  }

  :deep(.el-step__main) {
    margin-bottom: 15px;
  }
}
</style>
