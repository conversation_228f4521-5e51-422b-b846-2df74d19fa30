<template>
  <el-dialog
    v-model="helpUploadDialogVisible"
    title="上传视频"
    align-center
    width="700px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    custom-close
  >
    <div class="help-box">
      <el-form
        :model="form"
        :rules="rules"
        ref="formRef"
        label-width="auto"
        @close="() => formRef.resetFields()"
      >
        <el-form-item label="视频地址" prop="uploadLink" style="flex-direction: column;">
          <el-input
            placeholder="请输入视频地址"
            :rows="3"
            resize="none"
            type="textarea"
            v-model="form.uploadLink"
          ></el-input>
          <div class="tips" v-if="isShowLinkHint">请填入视频上传最终地址，确保商家查阅正常</div>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div class="flex-center btn">
        <slot name="button">
          <el-button v-btn plain @click="helpClose" style="padding: 0 30px">取消</el-button>
          <el-button v-btn type="primary" @click="onConfirm">确认提交</el-button>
        </slot>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { uploadLink } from '@/api/order/order'
import { ElMessageBox, ElMessage, ElLoading } from 'element-plus'
const helpUploadDialogVisible = ref(false)
const formRef = ref(null)

const form = ref({
  uploadLink: '',
})
const rules = {
  uploadLink: [{ required: true, validator: validateLink, trigger: 'change' }],
}

defineExpose({ open })
const emits = defineEmits(['success'])

const videoId = ref('')
const isShowLinkHint = ref(true)

function open(id, link) {
  helpUploadDialogVisible.value = true
  videoId.value = id
  form.value.needUploadLink = link
}

// function helpClose() {
//     helpUploadDialogVisible.value = false
// }

//视频链接校验规则
function validateLink(rule, value, callback) {
  const regLink = 'https://www.amazon.com'
  if (value && value.startsWith(regLink)) {
    isShowLinkHint.value = true
    return callback()
  } else {
    isShowLinkHint.value = false
    return callback(new Error('视频链接有格式错误'))
  }
}

function helpClose() {
  isShowLinkHint.value = true
  formRef.value.resetFields()
  helpUploadDialogVisible.value = false
}

//确认提交
function onConfirm() {
  formRef.value.validate(valid => {
    if (valid) {
      uploadLink({
        id: videoId.value,
        uploadLink: form.value.uploadLink,
      })
        .then(res => {
          ElMessage.success('上传成功')
          helpClose()
          emits('success')
          // if (callback) callback(res)
        })
        .catch(e => {
          if (error) error(e)
        })
        .finally(() => {})
    }
  })
}
</script>

<style scoped lang="scss"></style>
