<template>
  <el-dialog
    v-model="dialogVisible"
    width="600px"
    title="反馈素材给商家"
    align-center
    destroy-on-close
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <div class="form-box">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        :disabled="disabled"
        :validate-on-rule-change="false"
        label-width="120px"
        @submit.native.prevent
      >
        <el-form-item label="反馈类型" prop="type">
          <el-radio-group v-if="!picCuont && !isEdit" v-model="form.type" @change="handleTypeChange">
            <el-radio-button value="1">视频</el-radio-button>
          </el-radio-group>
          <el-radio-group v-else v-model="form.type" @change="handleTypeChange">
            <el-radio-button
              v-for="item in feedbackType"
              :key="item.value"
              :value="item.value"
              :disabled="!picCuont || isEdit"
            >
              {{ item.label }}
            </el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="视频地址" prop="videoUrl" v-if="form.type != '3'">
          <el-input v-model="form.videoUrl" clearable placeholder="请输入商家可以查看到视频正确的地址" />
        </el-form-item>
        <el-form-item label="照片地址" prop="picUrl" v-if="form.type != '1'">
          <el-input v-model="form.picUrl" clearable placeholder="请输入商家可以查看到照片正确的地址" />
        </el-form-item>
        <el-form-item label="修改理由" prop="modifyReason" v-if="isEdit">
          <el-input
            v-model="form.modifyReason"
            clearable
            placeholder="请输入修改理由"
            show-word-limit
            type="textarea"
            maxlength="300"
            :rows="4"
            resize="none"
          />
        </el-form-item>
        <!-- @change="() => formRef.validate('videoScore')" -->
        <el-form-item label="视频评分" prop="videoScore" v-if="form.type != '3' && isShowScore">
          <el-rate
            v-model="form.videoScore"
            :max="10"
            allow-half
          />
          已选{{ form.videoScore }}星
        </el-form-item>
        <el-form-item label="评价内容" prop="videoScoreContent" v-if="form.type != '3' && isShowScore">
          <el-input
            v-model="form.videoScoreContent"
            style="width: 100%"
            :rows="4"
            type="textarea"
            maxlength="300"
            show-word-limit
            placeholder="请对本次视频做出详细的评价"
            clearable
          />
        </el-form-item>
        <el-form-item label="完结任务" prop="taskDetailIds" v-if="finishTaskList?.length">
          <div v-loading="taskLoading" class="task-box">
            <div>
              <span>请点选需要同步完结的任务单</span>
              <span style="color: #7f7f7f">（支持多个处理中的任务同步完结）</span>
            </div>
            <TaskButton v-model="form.taskDetailIds" :checkList="finishTaskList" :width="448" />
          </div>
        </el-form-item>
      </el-form>

      <div class="flex-center" style="align-items: flex-start; margin: 10px 0">
        <el-button v-btn type="primary" round :loading="disabled" @click="submit">确认提交</el-button>
        <!-- <el-button v-btn round plain @click="close">取消</el-button>
        <el-button v-btn type="primary" round :loading="disabled" @click="submit">提交</el-button> -->
      </div>
    </div>
    <el-divider>历史记录</el-divider>
    <div class="case-box" v-loading="loading">
      <el-steps direction="vertical" process-status="wait" finish-status="success">
        <el-step v-for="item in steps" :key="item.id" :title="item.createTime" status="wait">
          <template #title>
            <div>
              {{ item.createTime }}
              <el-tag v-if="item.rollbackId" type="warning" round>订单回退</el-tag>
            </div>
          </template>
          <template #icon>
            <div class="icon-box icon-active"></div>
          </template>
          <template #description>
            <div class="description curColor">
              <div>
                {{ item.createUser?.name || '蜗牛运营' }}：反馈{{
                  item.type == '1' ? '视频' : item.type == '2' ? '视频和照片' : '照片'
                }}
                <el-button
                  v-if="item.canModify"
                  round
                  type="primary"
                  @click="handleEditRecord(item)"
                  size="small"
                >
                  修改
                </el-button>
              </div>
              <div v-if="item.videoUrl" style="word-break: break-all">视频地址:{{ item.videoUrl }}</div>
              <div v-if="item.picUrl" style="word-break: break-all">照片地址:{{ item.picUrl }}</div>
              <div v-if="item.modifyReason" class="text-warp">
                <div class="fs-0">修改理由:</div>
                <div>{{ item.modifyReason }}</div>
              </div>
            </div>
          </template>
        </el-step>
      </el-steps>
      <div v-if="!steps.length" class="flex-center no-data">暂无记录</div>
    </div>
  </el-dialog>
</template>

<script setup>
import { getFeedbackList, addFeedbackLink } from '@/api/order/order'
import { addFeedBackClip, checkCanScore } from '@/api/clip'
import { getFeedbackMaterialPendingTask } from '@/api/task/workOrder'
import { ElMessageBox, ElMessage, ElLoading } from 'element-plus'
import TaskButton from '@/components/Button/TaskButton.vue'

const dialogVisible = ref(false)
const videoId = ref('')
const steps = ref([])
const loading = ref(false)
const disabled = ref(false)
const formRef = ref()
const form = ref({
  type: '1',
  picUrl: '',
  videoUrl: '',
  videoScore: null,
  videoScoreContent: '',
  taskDetailIds: [],
  modifyReason: null,
})
const isEdit = ref(false)

const finishTaskList = ref([])
const taskLoading = ref(false)

const feedbackType = ref([
  {
    label: '视频',
    value: '1',
  },
  {
    label: '视频和照片',
    value: '2',
  },
  {
    label: '照片',
    value: '3',
  },
])

const rules = {
  type: [{ required: true, message: '请选择反馈类型', trigger: 'blur' }],
  videoUrl: [{ required: true, message: '请输入商家可以查看到视频正确的地址', trigger: 'blur' }],
  picUrl: [{ required: true, message: '请输入商家可以查看到照片正确的地址', trigger: 'blur' }],
  videoScore: [
    { required: true, message: '请选择视频评分', trigger: 'blur' },
    { validator: checkMark, trigger: 'change' },
  ],
  videoScoreContent: [{ required: false, message: '请对本次视频做出详细的评价', trigger: 'blur' }],
  taskDetailIds: [{ required: true, message: '请选择完结任务', trigger: 'change' }],
  modifyReason: [{ required: true, message: '请输入修改理由', trigger: 'blur' }],
}
function checkMark(rule, value, callback) {
  if (value > 0) {
    if(value < 1) {
      return callback(new Error('请选择视频评分最少1分'))
    }
    return callback()
  }
  return callback(new Error('请选择视频评分'))
}

defineExpose({
  open,
  close,
})

const emits = defineEmits(['success'])

const picCuont = ref(null)
const taskTypeValue = ref(null)
const isClipShow = ref(false)
const clipDetailId = ref(null)
const isCloseDialog = ref(false)
function open(
  id,
  count = null,
  taskType = null,
  status = null,
  detailId = null,
  isClip = false,
  link = null,
  isClose = false
) {
  picCuont.value = count ? true : false
  isClipShow.value = isClip
  isCloseDialog.value = isClose
  if (count) {
    form.value.type = '2'
  }
  if (taskType) {
    taskType == 1 ? (form.value.type = '1') : (form.value.type = '3')
    taskTypeValue.value = taskType
  }
  // if (status) {
  //   form.value.type = '1'
  //   picCuont.value = false
  // }
  if (detailId) {
    clipDetailId.value = detailId
  }
  if (link) {
    form.value.videoUrl = link
  }
  videoId.value = id
  dialogVisible.value = true
  handleCheckCanScore(id)
  getTaskList()
  getList()
}
function close() {
  dialogVisible.value = false
}
function handleClose() {
  form.value = {
    type: '1',
    picUrl: '',
    videoUrl: '',
    videoScore: null,
    videoScoreContent: '',
    modifyReason: null,
  }
  isShowScore.value = false
  isClipShow.value = false
  clipDetailId.value = null
  steps.value = []
  isEdit.value = false
  finishTaskList.value = []
  disabled.value = false
  formRef.value.clearValidate()
}

function getList() {
  loading.value = true
  getFeedbackList({ videoId: videoId.value })
    .then(res => {
      steps.value = res.data
    })
    .finally(() => (loading.value = false))
}

function getTaskList() {
  let params = {
    videoId: videoId.value,
  }
  if (form.value.type) {
    params.afterSaleClass = form.value.type == '2' ? '1,2' : form.value.type == '3' ? 2 : 1
  }
  if (taskTypeValue.value) {
    params.afterSaleClass = taskTypeValue.value
  }
  taskLoading.value = true
  finishTaskList.value = []
  getFeedbackMaterialPendingTask(params)
    .then(res => {
      if (res.code === 200 && res.data?.length) {
        finishTaskList.value = res.data
      }
    })
    .catch(() => {
      finishTaskList.value.length = 0
    })
    .finally(() => {
      taskLoading.value = false
    })
}

function handleParams() {
  const params = form.value
  if(params.type == 3) params.videoScore = null
  for (const key in params) {
    if (params[key] === null || params[key] === '' || params[key] === undefined) {
      delete params[key]
    }
  }
  return params
}

function handleTypeChange(val) {
  getTaskList()
}

function submit() {
  formRef.value.validate(valid => {
    if (valid) {
      ElMessageBox.confirm('确认提交反馈素材给商家？', '温馨提示', {
        autofocus: false,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
      })
        .then(() => {
          disabled.value = true
          const el_loading = ElLoading.service({
            lock: true,
            text: '正在提交中，请稍后',
            background: 'rgba(0, 0, 0, 0.7)',
          })
          const params = handleParams()
          if (!isClipShow.value) {
            addFeedbackLink({
              ...params,
              taskDetailIds:
                form.value.taskDetailIds?.length && form.value.taskDetailIds[0] != -1
                  ? form.value.taskDetailIds
                  : undefined,
              videoId: videoId.value,
            })
              .then(res => {
                ElMessage.success('操作成功')
                emits('success')
                // close()
                handleClose()
                getList()
              })
              .finally(() => {
                disabled.value = false
                el_loading.close()
              })
          } else {
            addFeedBackClip({
              ...params,
              taskDetailIds:
                form.value.taskDetailIds?.length && form.value.taskDetailIds[0] != -1
                  ? form.value.taskDetailIds
                  : undefined,
              videoId: videoId.value,
              materialInfoId: clipDetailId.value,
            })
              .then(res => {
                ElMessage.success('操作成功')
                emits('success')
                handleClose()
                if (isCloseDialog.value) close()
                getList()
              })
              .finally(() => {
                disabled.value = false
                el_loading.close()
              })
          }
        })
        .catch(() => {})
    }
  })
}
const isShowScore = ref(false)
function handleCheckCanScore(videoId) {
  checkCanScore({ videoId }).then(res => {
    isShowScore.value = res.data
  })
}

//修改历史记录
function handleEditRecord(data) {
  isEdit.value = true
  form.value.modifyReason = null
  form.value.picUrl = data.picUrl
  form.value.videoUrl = data.videoUrl
  // form.value.videoScore = data.videoScore
  // form.value.videoScoreContent = data.videoScoreContent
  form.value.type = data.type + ''
  form.value.modifyId = data.id
}
</script>

<style scoped lang="scss">
@import '@/assets/styles/customForm.scss';
@import '@/assets/styles/form-disabled.scss';
.case-box {
  max-height: 300px;
  overflow-y: auto;

  .icon-box {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #d7d7d7;
  }
  .icon-active {
    background: var(--el-color-primary-light-2);
  }
  .curColor {
    color: #000;
    // color:#a8abb2
  }
  .description {
    font-size: 16px;
  }

  .no-data {
    width: 100%;
    height: 150px;
    font-size: 16px;
    color: #7f7f7f;
  }

  :deep(.el-step__main) {
    margin-bottom: 15px;
  }
}
.task-box {
  width: 100%;
  max-height: 300px;
  overflow: hidden;
  overflow-y: auto;
}
.text-warp {
  display: flex;
  align-items: baseline;
  word-break: break-all;
  line-break: anywhere;
  white-space: break-spaces;
  display: flex;
  align-items: baseline;
}
</style>
