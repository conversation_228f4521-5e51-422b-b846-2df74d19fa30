<template>
  <el-dialog
    v-model="dialogVisible"
    title="确认驳回模特素材"
    width="750"
    align-center
    append-to-body
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <div class="form-box">
      <div style="color: red; margin-bottom: 10px">
        确认驳回后，模特将会收到售后信息，请完整描述订单问题；
      </div>
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        :disabled="disabled"
        :validate-on-rule-change="false"
        label-width="120px"
      >
        <el-form-item label="驳回标题" prop="title">
          <el-input
            v-model="form.title"
            placeholder="请输入驳回标题"
            maxlength="64"
            show-word-limit
            clearable
          />
        </el-form-item>
        <el-form-item label="驳回原因" prop="remark">
          <!-- <Editor ref="EditorRef" v-model="form.remark" :min-height="200" /> -->
          <WangEditor v-model="form.remark" />
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div class="flex-center">
        <el-button v-btn @click="close">取消</el-button>
        <el-button v-btn type="primary" :disabled="disabled" @click="onConfirm">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { rejectModelFeedback } from '@/api/order/order'
import { ElLoading, ElMessage } from 'element-plus'
import WangEditor from '@/components/WangEditor'

defineExpose({
  open,
  close,
})

const emits = defineEmits(['success'])

const EditorRef = ref()

const dialogVisible = ref(false)
const materialId = ref('')
const disabled = ref(false)
const formRef = ref()
const form = ref({
  title: '',
  remark: '',
})

const rules = {
  title: [{ required: true, message: '请输入驳回标题', trigger: 'blur' }],
  remark: [{ required: true, validator: checkRemark, trigger: 'blur' }],
}

async function checkRemark(rule, value, callback) {
  const textContent = value?.replace(/<[^<>]+>/g, '').replace(/&nbsp;/gi, '') || '' // 去掉HTML标签
  const isEmpty = textContent.trim() === '' // 检查是否为空
  if (isEmpty) {
    callback(new Error('请输入驳回原因'))
  } else {
    callback()
  }
}

// async function checkRemark(rule, value, callback) {
//   let text = await EditorRef.value.getText().trim()
//   if(!text){
//     return callback(new Error('请输入驳回原因'));
//   }
//   return callback()
// }

function open(id) {
  materialId.value = id
  dialogVisible.value = true
}
function close() {
  dialogVisible.value = false
}

function handleClose() {
  form.value = {
    title: '',
    remark: undefined,
  }
  formRef.value.clearValidate()
}

function onConfirm() {
  formRef.value.validate(valid => {
    if (valid) {
      // console.log(form.value);
      disabled.value = true
      const el_loading = ElLoading.service({
        lock: true,
        text: '正在提交中，请稍后',
        background: 'rgba(0, 0, 0, 0.7)',
      })
      rejectModelFeedback({
        ...form.value,
        materialId: materialId.value,
      })
        .then(res => {
          ElMessage.success('驳回成功')
          emits('success')
          close()
        })
        .finally(() => {
          disabled.value = false
          el_loading.close()
        })
    }
  })
}
</script>

<style scoped lang="scss"></style>
