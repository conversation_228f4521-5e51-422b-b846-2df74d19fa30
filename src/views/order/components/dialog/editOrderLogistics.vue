<template>
  <el-dialog
    v-model="dialogVisible"
    title="修改"
    width="550px"
    align-center
    append-to-body
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <div class="flex-center">
      <el-form ref="formRef" :model="form" label-width="100px" :rules="rules" @submit.native.prevent>
        <div class="form-box">
          <el-form-item label="类型" prop="type">
            <el-radio-group v-model="form.type">
              <el-radio :value="1">修改单号</el-radio>
              <el-radio :value="2" v-if="isShowCancellation">作废</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="物流单号" prop="number" v-if="form.type == 1">
            <el-input
              v-model.trim="form.number"
              placeholder="请输入正确的物流单号"
              maxlength="100"
              clearable
              @input="val => (form.number = val.replace(/\s+/g, ''))"
            />
          </el-form-item>
          <el-form-item v-if="form.type == 1" label="修改原因" prop="remark">
            <el-input
              v-model="form.remark"
              placeholder="请输入修改原因"
              maxlength="300"
              show-word-limit
              type="textarea"
              :rows="4"
              resize="none"
              style="width: 400px"
              clearable
            />
          </el-form-item>
          <el-form-item v-else label="作废原因" prop="remark1">
            <el-input
              v-model="form.remark1"
              placeholder="请输入作废原因"
              maxlength="300"
              show-word-limit
              type="textarea"
              :rows="4"
              resize="none"
              style="width: 400px"
              clearable
            />
          </el-form-item>
        </div>
      </el-form>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="close()">取消</el-button>
        <el-button type="primary" @click="onConfirm()">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { checkLogisticRepetition, operationVideoLogistic } from '@/api/task/logistics.js'
import { ElMessageBox, ElIcon } from 'element-plus'
import { Warning } from '@element-plus/icons-vue'

const { proxy } = getCurrentInstance()

const dialogVisible = ref(false)
const formRef = ref()
const logisticsInfo = ref({})
const form = ref({
  type: 1,
  number: '',
  remark: '',
  remark1: '',
})

const rules = {
  type: [{ required: true, message: '请选择类型', trigger: 'change' }],
  number: [{ required: true, message: '请输入物流单号', trigger: 'change' }],
  remark: [{ required: true, message: '请输入修改原因', trigger: 'change' }],
  remark1: [{ required: true, message: '请输入作废原因', trigger: 'change' }],
}

const isShowCancellation = ref(false)

defineExpose({
  open,
})

const emits = defineEmits(['success'])

function open(row, length) {
  if (row) {
    logisticsInfo.value = row
    form.value.number = row.number
    if (length != 1) {
      isShowCancellation.value = true
    } else {
      isShowCancellation.value = false
    }
    dialogVisible.value = true
  }
}

function close() {
  dialogVisible.value = false
}

function handleClose() {
  form.value = {
    type: 1,
    number: '',
    remark: '',
    remark1: '',
  }
  formRef.value.resetFields()
  logisticsInfo.value = {}
}

//查询是否重复
function checkRepetition() {
  proxy.$modal.loading('正在保存...')
  checkLogisticRepetition(form.value.number)
    .then(res => {
      if (res.code == 200) {
        if (res.data) {
          openTip()
        } else {
          proxy.$modal.confirm('确认保存？', '提示', {}).then(() => {
            save()
          })
        }
      }
    })
    .finally(() => {
      proxy.$modal.closeLoading()
    })
}

function openTip() {
  ElMessageBox({
    title: '重复提醒',
    showCancelButton: true,
    showConfirmButton: true,
    message: () =>
      h(
        'div',
        {
          class: 'flex-column gap-10',
          style: {
            width: '396px',
          },
        },
        [
          h(
            ElIcon,
            {
              color: 'var(--el-color-warning)',
              size: 70,
            },
            () => h(Warning)
          ),
          h(
            'div',
            {
              style: {
                padding: '10px 15px',
                'text-align': 'center',
              },
            },
            '物流单号重复，是否确认修改物流单号?'
          ),
        ]
      ),
  })
    .then(() => {
      save()
    })
    .catch(() => {
      proxy.$modal.closeLoading()
    })
}

function save() {
  proxy.$modal.loading('正在保存...')
  operationVideoLogistic({
    id: logisticsInfo.value.id,
    type: form.value.type,
    number: form.value.number,
    remark: form.value.type == 1 ? form.value.remark : form.value.remark1,
  })
    .then(res => {
      if (res.code == 200) {
        emits('success')
        close()
      }
    })
    .finally(() => {
      proxy.$modal.closeLoading()
    })
}

function onConfirm() {
  formRef.value.validate(valid => {
    if (valid) {
      if (form.value.type == 2) {
        proxy.$modal.confirm('确认保存？', '提示', {}).then(() => {
          save()
        })
      } else {
        checkRepetition()
      }
    }
  })
}
</script>

<style scoped lang="scss"></style>
