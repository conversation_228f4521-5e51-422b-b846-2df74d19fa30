<template>
  <el-dialog
    v-model="dialogVisible"
    width="690px"
    title="修改订单"
    align-center
    destroy-on-close
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <div class="form-box" v-loading="loading">
      <el-form ref="formRef" :model="form" :validate-on-rule-change="false" label-width="0" :rules="rules">
        <el-form-item label="拍摄国家" prop="shootingCountry" label-width="110px">
          <el-radio-group v-model="form.shootingCountry">
            <el-radio-button v-for="item in biz_nation" :key="item.value" :value="item.value">
              {{ item.label }}
            </el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="模特类型" prop="modelType" label-width="110px">
          <el-radio-group v-model="form.modelType">
            <!-- <el-radio-button value="3" label="3" :disabled="modelTypeDisabled">都可以</el-radio-button> -->
            <el-radio-button
              v-for="item in biz_model_type"
              :key="item.value"
              :value="item.value"
              :disabled="modelTypeDisabled"
            >
              {{ item.label }}
            </el-radio-button>
          </el-radio-group>
        </el-form-item>

        <!-- 拍摄要求 -->
        <div class="shoot-required">
          <div class="flex-start title">
            <div>拍摄建议:</div>
            <el-button v-btn round type="primary" size="small" @click="editShowEditShootRequired">
              修改
            </el-button>
          </div>
          <div class="required-content">
            <div class="flex-start" style="align-items: baseline; width: 100%">
              <div class="content-icon">原</div>
              <div style="flex: 1">
                <div class="auto-scroll">
                  <template v-for="(item, index) in form.merchantShootRequired" :key="item.id">
                    {{ item.content }}
                    <br />
                  </template>
                </div>
                <div style="display: flex; justify-content: end; margin: 5px 10px 0 0">
                  <el-button v-btn plain round size="small" @click="doTranslate">翻译</el-button>
                </div>
              </div>
            </div>
            <el-divider
              border-style="dashed"
              style="margin: 10px 0; width: 98%"
              v-if="isShowTranslateContent"
            />
            <div
              class="flex-start"
              style="align-items: baseline; width: 100%"
              v-if="isShowTranslateContent"
              v-loading="translateLoading"
            >
              <div class="content-icon">译</div>
              <div style="flex: 1">
                <div class="auto-scroll">
                  <template v-for="(item, index) in transformContentList" :key="item">
                    {{ item }}
                    <br />
                  </template>
                </div>
                <div style="display: flex; justify-content: end; margin: 5px 10px 0 0">
                  <el-button v-btn plain round size="small" @click="isShowTranslateContent = false">
                    收起翻译
                  </el-button>
                </div>
              </div>
            </div>
            <el-form-item
              v-if="isShowEditShootRequired"
              label=""
              label-width="0"
              prop="demands"
              style="display: flex; flex-direction: column"
            >
              <div class="flex-center" style="width: 100%; margin-top: 10px; align-items: baseline">
                <!-- <div style=" text-align: center"> -->
                <div class="content-icon" style="line-height: 18px">改</div>
                <!-- </div> -->
                <el-input
                  style="width: 100%"
                  placeholder="1.请用英文输入产品卖点"
                  v-model="form.demands"
                  type="textarea"
                  :autosize="{ minRows: 4 }"
                  maxlength="5000"
                />
                <!-- <InputText
                  style="width: 100%"
                  :rows="4"
                  placeholder="1.请用英文输入产品卖点"
                  v-model="form.demands"
                  ref="demandsRef"
                  :length="5000"
                  :isShowLimit="false"
                /> -->
              </div>
              <span v-if="isShowDemandsErrow" class="hint-tip">*请用英文，简短描述重要的拍摄建议</span>
            </el-form-item>
          </div>
        </div>
        <!-- 匹配模特注意事项 -->
        <div class="shoot-required">
          <div class="flex-start title" style="align-items: baseline">
            <div>
              匹配模特
              <br />
              注意事项
            </div>
            <el-button v-btn round type="primary" size="small" @click="editShowEditCautions">修改</el-button>
          </div>
          <div class="required-content">
            <div class="flex-start" style="align-items: baseline; width: 100%">
              <div class="content-icon">原</div>
              <div class="flex-between" style="flex: 1">
                <div class="auto-scroll">
                  <template v-for="(item, index) in form.merchantCautions" :key="item.id">
                    {{ item.content }}
                    <br />
                  </template>
                </div>
              </div>
            </div>
            <el-form-item
              v-if="isShowEditCautions"
              label=""
              label-width="0"
              prop="cautionsTemp"
              style="display: flex; flex-direction: column"
            >
              <div class="flex-center" style="width: 98%; margin-top: 10px; align-items: baseline">
                <!-- <div style=" text-align: center"> -->
                <div class="content-icon" style="line-height: 18px">改</div>
                <!-- </div> -->
                <el-input
                  style="width: 100%"
                  placeholder="1.请输入注意事项"
                  v-model="form.cautionsTemp"
                  type="textarea"
                  :autosize="{ minRows: 4 }"
                  maxlength="800"
                />
                <!-- <InputText
                  style="width: 100%"
                  :rows="4"
                  placeholder="1.请输入注意事项"
                  v-model="form.cautionsTemp"
                  ref="cautionsTempRef"
                /> -->
              </div>
            </el-form-item>
          </div>
        </div>

        <div class="shoot-required" style="margin-top: 20px">
          <div class="required-content">
            <el-form-item style="margin-bottom: 0">
              <DragImageUpload
                v-model="imageList"
                @openUploadDialog="handleDragUploadDialogOpen"
                :limit="10"
              />
            </el-form-item>
          </div>
        </div>

        <!-- <ShootRequired
          v-model:shootRequired="form.merchantShootRequired"
          v-model:translateList="demandsTranslateList"
          v-model:newDemands="form.newDemands"
        />
        <Cautions
          v-model:cautions="form.merchantCautions"
          v-model:translateList="demandsTranslateList"
          v-model:newCautions="form.newCautions"
        /> -->

        <!-- <LimitingCondition
          v-model:limitingCondition="form.merchantLimitingCondition"
          v-model:translateList="conditionsTranslateList"
          v-model:newConditions="form.newConditions"
          v-model:newModelConditions="form.newModelConditions"
        /> -->
      </el-form>
    </div>
    <div class="flex-center btn" style="margin-top: 10px">
      <el-button v-btn class="btn-big" plain round @click="dialogVisible = false">取消</el-button>
      <el-button v-btn class="btn-big" type="primary" round @click="onConfirm">确认修改</el-button>
    </div>
  </el-dialog>
  <DragUploadDialog
    ref="DragUploadDialogRef"
    title=""
    :limit="imgLimit"
    @success="changePicSuccess"
    bucketType="order"
  />
</template>

<script setup>
// import ShootRequired from '@/views/order/components/shootRequired.vue'
// import Cautions from '@/views/order/components/cautions.vue'
// import LimitingCondition from '@/views/order/components/limitingCondition.vue'
import InputText from '@/components/InputText/index.vue'
import DragImageUpload from '@/components/ImageUpload/dragImageUpload.vue'
import DragUploadDialog from '@/components/FileUpload/dragUploadDialog.vue'
import {
  updateOrderDetails,
  getVideoOrderDetail,
  orderDetails,
  getTranslate,
  videoOrderDetails,
} from '@/api/order/order'
import { english_d_reg, chineseCharacter_d_reg, englishCharacter_d_reg, chinese_reg } from '@/utils/RegExp'

const { proxy } = getCurrentInstance()

const { biz_nation, biz_model_type } = proxy.useDict('biz_nation', 'biz_model_type')
const isShowEditShootRequired = ref(false)
const isShowDemandsErrow = ref(false)
const cautionsTempRef = ref(null)
const demandsRef = ref(null)
const dialogVisible = ref(false)
const orderId = ref('')
const loading = ref(false)
const imageList = ref([])
const DragUploadDialogRef = ref(null)
const form = ref({
  shootingCountry: '',
  modelType: '',
  cautionsTemp: '',
  demands: '',
  merchantShootRequired: [], // 原拍摄要求
  newDemands: [{ content: '' }], // 新增拍摄要求
  newCautions: [{ content: '' }], // 新增注意事项
  merchantCautions: [], // 原注意事项

  // merchantLimitingCondition: [], // 原限制条件
  // newConditions: [{ content: '' }], // 新增限制条件-内部展示
  // newModelConditions: [{ content: '' }], // 新增限制条件-模特展示
})

const demandsTranslateList = ref([])

const conditionsTranslateList = ref([])

// 模特类型禁用
const modelTypeDisabled = computed(() => {
  if (form.value.shootingCountry == '7' && form.value.platform == '0') {
    return false
  }
  form.value.modelType = '1'
  return true
})

const demandsRules = [{ required: false, validator: checkDemands, trigger: 'blur' }]
const conditionsRules = [{ required: false, validator: checkConditions, trigger: 'change' }]
const rules = {
  demands: [{ required: true, validator: checkDemands, trigger: 'change' }],
}

function checkDemands(rule, value, callback) {
  if (value) {
    let index = chinese_reg.test(value)
    if (index) {
      return (isShowDemandsErrow.value = true)
    } else {
      isShowDemandsErrow.value = false
    }
  } else {
    isShowDemandsErrow.value = true
  }
  return callback()
  // if (value && !english_d_reg.test(value)) {
  //   return callback(new Error('*请用英文，简短描述重要的拍摄要求'))
  // }
  // return callback()
}
function checkConditions(rule, value, callback) {
  if (value && !chineseCharacter_d_reg.test(value)) {
    return callback(new Error('*请用中文，清晰简短描述限制条件'))
  }
  return callback()
}

//处理inputText的输入框内容转数组
function handleInputText(val) {
  if (val && val.length) {
    let countList = []
    countList = val.split('\n')
    return countList.map((item, index) => {
      // const startIndex = index < 9 ? 2 : 3
      // return { content: item.slice(startIndex), sort: index, type: 1 }
      return { content: item, sort: index, type: 1 }
    })
  } else {
    return null
  }
}

function handleClose() {
  form.value = {
    shootingCountry: '',
    modelType: '',
    platform: '',
    cautionsTemp: '',
    demands: '',
    merchantShootRequired: [], // 原拍摄要求
    newDemands: [{ content: '' }], // 新增拍摄要求
    newCautions: [{ content: '' }], // 新增注意事项
    merchantCautions: [], // 原注意事项

    // merchantLimitingCondition: [], // 原限制条件
    // newConditions: [{ content: '' }], // 新增限制条件-内部展示
    // newModelConditions: [{ content: '' }], // 新增限制条件-模特展示
  }
  // 翻译数据
  imageList.value = []
  demandsTranslateList.value = []
  conditionsTranslateList.value = []
}
const caseid = ref(0)

defineExpose({
  open,
  close,
})

const emits = defineEmits(['success'])

function open(id, caseId) {
  orderId.value = id
  caseid.value = caseId
  dialogVisible.value = true
  getList()
}

function close() {
  cautionsTempRef.value?.resetValue()
  demandsRef.value?.resetValue()
  isShowEditShootRequired.value = false
  isShowEditCautions.value = false
  isShowDemandsErrow.value = false
  demandsTranslateList.value = []
  dialogVisible.value = false
}

function getList() {
  loading.value = true

  videoOrderDetails(orderId.value)
    .then(res => {
      form.value.shootingCountry = res.data.orderVideoSimpleVO.shootingCountry + ''
      form.value.modelType = res.data.orderVideoSimpleVO?.modelType + ''
      form.value.platform = res.data.orderVideoSimpleVO?.platform + ''
      // 拍摄要求
      form.value.merchantShootRequired = res.data.orderVideoSimpleVO?.shootRequired
      // 限制条件
      form.value.merchantCautions = res.data.orderVideoSimpleVO?.orderVideoCautionsVO?.cautions
      if (
        res.data.orderVideoSimpleVO?.orderVideoCautionsVO?.cautionsPics &&
        res.data.orderVideoSimpleVO?.orderVideoCautionsVO?.cautionsPics.length > 0
      ) {
        imageList.value = res.data.orderVideoSimpleVO.orderVideoCautionsVO.cautionsPics.map(item => ({
          picUrl: item,
          url: item,
        }))
      }
    })
    .catch(e => {
      console.error('editDemands', e)
      close()
    })
    .finally(() => (loading.value = false))
}
const imgLimit = computed(() => {
  let temp = 10
  if (imageList.value && imageList.value.length > 0) {
    temp = 10 - imageList.value.length
  }
  return temp
})
//更换产品图与上传图片
function handleDragUploadDialogOpen() {
  DragUploadDialogRef?.value.open()
}
function changePicSuccess(data) {
  imageList.value.push(...data)
}

function onConfirm() {
  // let length = form.value.newModelConditions.filter(item => item.content).length
  // // console.log(length, conditionsTranslateList.value.length);
  // if (length && conditionsTranslateList.value.length != length) {
  //   proxy.$modal.msgError('请先翻译-限制条件-模特展示')
  //   return
  // }
  proxy.$modal
    .confirm('确认修改', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '再想想',
    })
    .then(() => {
      const { demands, cautionsTemp, ...params } = form.value
      let data = {
        id: orderId.value,
        caseId: caseid.value,
        modelType: form.value.modelType,
        shootingCountry: form.value.shootingCountry,
        shootRequired: form.value.merchantShootRequired,
        cautions: form.value.merchantCautions,
        orderVideoCautionsDTO: {},
        // operationModelShootRequired: [],
        // operationLimitingCondition: [],
        // modelLimitingCondition: [],
      }
      if (demands && demands.length && isShowEditShootRequired.value) {
        const shootRequired = handleInputText(demands)
        const temp = data.shootRequired
        data.shootRequired = shootRequired

        temp?.forEach((item, i) => {
          if (data.shootRequired[i]) {
            data.shootRequired[i].id = item.id
          }
          // if (item[i]?.id) {
          //   data.shootRequired[i].id = item.id
          // }
        })
        // params.shootRequired ==
        //   shootRequired?.map((item, i) => {
        //     if (shootRequired[i]?.id) {
        //       params.shootRequired[i].id = shootRequired[i].id
        //     }
        //   })
        // data.shootRequired = handleInputText(demands)
      }
      if (isShowEditCautions.value) {
        // data.cautions = handleInputText(cautionsTemp)
        data.orderVideoCautionsDTO.cautions = handleInputText(cautionsTemp)
      } else {
        data.orderVideoCautionsDTO.cautions = data.cautions
      }

      if (data.orderVideoCautionsDTO.cautions?.length) {
        data.cautions?.map((item, i) => {
          data.orderVideoCautionsDTO.cautions[i].id = data.cautions[i].id
        })
      }
      if (imageList.value && imageList.value.length > 0) {
        data.orderVideoCautionsDTO.cautionsPics = imageList.value.map(item => item.picUrl)
      } else {
        data.orderVideoCautionsDTO.cautionsPics = []
      }
      // try {
      //   // 拍摄要求
      //   if (form.value.newDemands.length) {
      //     form.value.newDemands.forEach((item, i) => {
      //       if (item.content) {
      //         data.shootRequired.push({
      //           target: 1,
      //           content: item.content,
      //           sort: i,
      //           type: 1,
      //         })
      //       }
      //     })
      //   }
      //   //注意事项
      //   if (form.value.newCautions.length) {
      //     form.value.newCautions.forEach((item, i) => {
      //       if (item.content) {
      //         data.cautions.push({
      //           target: 2,
      //           content: item.content,
      //           sort: i,
      //           type: 1,
      //         })
      //       }
      //     })
      //   }
      // 限制条件-内部
      // if (form.value.newConditions.length) {
      //   form.value.newConditions.forEach((item, i) => {
      //     if (item.content) {
      //       data.operationLimitingCondition.push({
      //         target: 2,
      //         content: item.content,
      //         sort: i,
      //         type: 2,
      //       })
      //     }
      //   })
      // }
      // 限制条件-模特
      // if (conditionsTranslateList.value.length) {
      //   conditionsTranslateList.value.forEach((item, i) => {
      //     data.modelLimitingCondition.push({
      //       target: 2,
      //       content: item,
      //       sort: i,
      //       type: 2,
      //     })
      //   })
      // }
      // } catch (e) {
      //   console.error(e)
      // }
      proxy.$modal.loading('正在提交中，请稍后')
      updateOrderDetails(data)
        .then(res => {
          proxy.$modal.msgSuccess('修改成功')
          emits('success')
          close()
        })
        .finally(() => proxy.$modal.closeLoading())
    })
    .catch(() => {})
}

//翻译
const isShowTranslateContent = ref(false)
const transformContentList = ref([])
const translateLoading = ref(false)
function doTranslate() {
  if (form.value.merchantShootRequired && form.value.merchantShootRequired.length > 0) {
    translateLoading.value = true
    isShowTranslateContent.value = true

    let data = []
    form.value.merchantShootRequired.forEach(item => {
      data.push(item.content)
    })
    // data = content.value.split('\n')
    getTranslate({ language: 1, wordList: data })
      .then(res => {
        if (res.data && res.data.length > 0) {
          transformContentList.value = res.data
          translateLoading.value = false
        }
      })
      .finally(() => (translateLoading.value = false))
  } else {
    proxy.$modal.msgWarning('暂无翻译内容')
    // ElMessage.warning('暂无翻译内容')
  }
}
//修改拍摄内容
function editShowEditShootRequired() {
  if (form.value.merchantShootRequired && form.value.merchantShootRequired.length > 0) {
    form.value.demands = form.value.merchantShootRequired.map(item => item.content).join('\n')
  }
  isShowEditShootRequired.value = !isShowEditShootRequired.value
}
const isShowEditCautions = ref(false)
function editShowEditCautions() {
  if (form.value.merchantCautions && form.value.merchantCautions.length > 0) {
    form.value.cautionsTemp = form.value.merchantCautions.map(item => item.content).join('\n')
  }
  isShowEditCautions.value = !isShowEditCautions.value
}
</script>

<style scoped lang="scss">
@import '@/assets/styles/form-disabled.scss';
.form-box {
  min-height: 500px;
  max-height: 700px;
  overflow-y: auto;

  :deep(.el-form-item__label) {
    font-size: 16px;
  }

  .title {
    gap: 10px;
    width: 100%;
    font-size: 16px;
    margin: 10px 0;

    div {
      width: 100px;
      text-align: right;
    }

    .add-icon {
      color: var(--el-color-primary);
      cursor: pointer;
    }
  }
  .item {
    gap: 10px;
    width: 100%;
    font-size: 16px;

    span {
      width: 100px;
      flex-shrink: 0;
      text-align: right;
      color: var(--el-color-primary);
    }
    .red {
      color: var(--el-color-danger);
    }
    .el-input {
      // flex-grow: 1;
      width: 70%;
    }
    .el-icon {
      color: var(--el-color-primary);
      cursor: pointer;
    }
  }

  .btn {
    margin: 24px 0 20px;
  }
}

.shoot-required {
  .required-content {
    background: #f2f2f2;
    margin: 0 30px;
    padding: 10px 0 10px 20px;
    // padding: 10px 20px;
    border-radius: 10px;
    .content-icon {
      margin-right: 10px;
      font-size: 12px;
      border-radius: 50%;
      border: 1px solid #606266;
      width: 20px;
      height: 20px;
      text-align: center;
    }
  }
  .title {
    gap: 10px;
    width: 100%;
    font-size: 16px;
    margin: 10px 0;

    div {
      width: 100px;
      text-align: right;
    }

    .add-icon {
      color: var(--el-color-primary);
      cursor: pointer;
    }
  }
}
.auto-scroll {
  // max-height: 100px;
  width: 100%;
  overflow-y: auto;
  word-break: break-all;
}
.hint-tip {
  margin-left: 30px;
  color: #f56c6c;
  font-size: 12px;
  line-height: 20px;
}
</style>
