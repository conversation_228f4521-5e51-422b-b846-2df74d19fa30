<template>
  <el-dialog
    v-model="dialogVisible"
    title="申请退款"
    width="600"
    align-center
    append-to-body
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <div class="form-box" v-loading="loading">
      <div class="info-box">
        <div class="title">订单信息</div>
        <div class="info-item">
          <span class="label">订单原价</span>
          <span>￥{{ originAmount }}</span>
        </div>
        <div class="info-item">
          <span class="label">参与活动</span>
          <!-- <span v-if="videoPromotionAmount">5单满减:-￥20</span> -->
          <span
            v-if="
              handleShowDiscount(form.orderDiscountDetailVOS, '1') ||
              handleShowDiscount(form.orderDiscountDetailVOS, '4')
            "
          >
            <template v-if="handleShowDiscount(form.orderDiscountDetailVOS, '1')">
              限时满减优惠:-￥{{ handleDiscount(form.orderDiscountDetailVOS, '1') }}
            </template>
            <span
              v-if="
                handleShowDiscount(form.orderDiscountDetailVOS, '1') &&
                handleShowDiscount(form.orderDiscountDetailVOS, '4')
              "
            >
              /
            </span>
            <template v-if="handleShowDiscount(form.orderDiscountDetailVOS, '4')">
              每月首单立减:-￥{{ handleDiscount(form.orderDiscountDetailVOS, '4') }}
            </template>
          </span>
          <span v-else>-</span>
        </div>
        <div class="info-item">
          <span class="label">订单实付</span>
          <span>￥{{ orderAmount }}</span>
        </div>
        <div class="info-item">
          <span class="label">已退金额</span>
          <span>￥{{ refundTotal }}</span>
        </div>
      </div>
      <div style="padding: 0 0 8px 20px; font-weight: 600; font-size: 15px">申请信息</div>
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        :disabled="disabled"
        :validate-on-rule-change="false"
        label-width="100px"
      >
        <el-form-item label="退款类型" prop="refundType">
          <el-radio-group v-model="form.refundType" @change="handleChange">
            <el-radio-button :value="1">补偿订单</el-radio-button>
            <el-radio-button v-if="!isTask" :value="3" :disabled="hasPicCount">取消选配</el-radio-button>
            <el-radio-button v-if="!isTask" :value="2">取消订单</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="form.refundType === 2" label="可退金额">
          <div>￥{{ orderRefundAmount }}</div>
        </el-form-item>
        <template v-if="form.refundType === 3">
          <el-form-item label="选配规格">
            <div>{{ picCountText }}</div>
          </el-form-item>
          <el-form-item label="剩余可退金额">
            <div>￥{{ form.refundAmount }}</div>
          </el-form-item>
          <el-form-item
            label="退款方案"
            prop="refundPicCount"
            v-if="form.surplusPicCount && !isShowRefundPic"
          >
            <div class="flex-center">
              <el-radio-group size="small" v-model="form.refundPicCount">
                <el-radio-button :value="1" :disabled="form.surplusPicCount < 1">1张</el-radio-button>
                <el-radio-button :value="2" :disabled="form.surplusPicCount < 2">2张</el-radio-button>
                <el-radio-button :value="3" :disabled="form.surplusPicCount < 3">3张</el-radio-button>
                <el-radio-button :value="4" :disabled="form.surplusPicCount < 4">4张</el-radio-button>
                <el-radio-button :value="5" :disabled="form.surplusPicCount < 5">5张</el-radio-button>
              </el-radio-group>
            </div>
          </el-form-item>
          <el-form-item label="退款金额" v-if="form.surplusPicCount && !isShowRefundPic">
            <div>￥{{ refundRealyAmount }}</div>
          </el-form-item>
          <el-form-item
            :label="refundCauseLabel"
            prop="refundCause"
            v-if="form.surplusPicCount && !isShowRefundPic"
          >
            <el-input
              v-model="form.refundCause"
              type="textarea"
              :autosize="{ minRows: 2, maxRows: 4 }"
              :placeholder="'请输入' + refundCauseLabel"
              maxlength="500"
              show-word-limit
              clearable
            />
          </el-form-item>
        </template>

        <el-form-item
          v-if="form.refundType === 1"
          :label="refundAmountLabel"
          prop="refundAmount"
          :rules="refundAmountRules"
        >
          <el-input-number
            title=""
            v-model="form.refundAmount"
            :min="0"
            :max="999999"
            :precision="2"
            controls-position="right"
            style="width: 200px"
            clearable
          />
        </el-form-item>
        <!-- <el-form-item label="取消订单" prop="isCancelOrder" v-if="form.refundType == 1">
          <el-radio-group v-model="form.isCancelOrder">
            <el-radio-button :value="0">不取消订单</el-radio-button>
            <el-radio-button :value="1">取消订单</el-radio-button>
          </el-radio-group>
        </el-form-item> -->
        <el-form-item :label="refundCauseLabel" prop="refundCause" v-if="form.refundType != 3">
          <el-input
            v-model="form.refundCause"
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 4 }"
            :placeholder="'请输入' + refundCauseLabel"
            maxlength="500"
            show-word-limit
            clearable
          />
        </el-form-item>
        <el-form-item
          label="完结任务"
          prop="taskDetailIds"
          v-if="form.refundType === 1 && finishTaskList?.length"
        >
          <div v-loading="taskLoading" class="task-box">
            <div>
              <span>请点选需要同步完结的任务单</span>
              <span style="color: #7f7f7f">（支持多个处理中的任务同步完结）</span>
            </div>
            <TaskButton v-model="form.taskDetailIds" :checkList="finishTaskList" :width="408" />
          </div>
        </el-form-item>
      </el-form>
      <div v-if="isCancelOpention" style="color: #d9001b; text-align: center">
        {{ isCancelOpention == 1 ? '照片选配费用已退款，无法重复发起' : '' }}
      </div>
      <!-- v-if="refundTotal >= orderAmount && form.refundType == 3 && !isCancelOpention" -->
      <div style="color: #d9001b; text-align: center" v-if="isShowRefundPic">可退款金额为0，无法发起退款</div>
      <div style="color: #d9001b; text-align: center" v-if="isShowError && refundType">
        <span v-if="refundTotal >= orderAmount && form.refundType == 3"></span>
        <span v-else>
          {{ refundTypeOptions.find(item => item.value == refundType).label }}审核中，无法重复发起
        </span>
      </div>
    </div>
    <template #footer>
      <div class="flex-center">
        <el-button v-btn @click="close">取消</el-button>
        <el-button v-btn type="primary" @click="onConfirm" :disabled="isShowError">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import TaskButton from '@/components/Button/TaskButton.vue'
import { getReturnPendingTask } from '@/api/task/workOrder'
import { getRefundInfo } from '@/api/order/order'
import useOrderApi from '@/hooks/useOrderApi'
import { picCountOptions, refundTypeOptions } from '../../list/data'
import * as math from 'mathjs'
const props = defineProps({
  isTask: {
    type: Boolean,
    default: false,
  },
})

defineExpose({
  open,
  close,
})

const emits = defineEmits(['success'])

const { handleApplyRefund } = useOrderApi()

const dialogVisible = ref(false)
const videoId = ref('')
const picCountText = ref('')
const loading = ref(false)
const disabled = ref(false)
const formRef = ref()
const form = ref({
  refundType: 1,
  isCancelOrder: 0,
  refundAmount: 0,
  refundCause: '',
  taskDetailIds: [],
  refundPicCount: null,
})
const hasPicCount = ref(false)
const orderRefundAmount = ref(0)
const canRefund = ref('1')
const refundTotal = ref(0)

const finishTaskList = ref([])
const taskLoading = ref(false)

const rules = {
  refundType: [{ required: true, message: '请选择退款类型', trigger: 'blur' }],
  refundCause: [{ required: true, message: '请输入理由', trigger: 'blur' }],
  taskDetailIds: [{ required: true, message: '请选择完结任务', trigger: 'change' }],
  isCancelOrder: [{ required: true, message: '请选择', trigger: 'change' }],
  refundPicCount: [{ required: true, message: '请选择退款方案', trigger: 'change' }],
}
const refundAmountRules = [{ required: true, message: '请输入补偿金额', trigger: 'blur' }]

const isShowError = computed(() => {
  return canRefund.value == '0' || (refundTotal.value >= orderAmount.value && form.value.refundType == 3)
})

const refundRealyAmount = computed(() => {
  let s = 0
  if (form.value.refundPicCount) {
    if (form.value.refundPicCount == form.value.surplusPicCount) {
      s = form.value.refundAmount
    } else {
      s = math.format(
        math.divide(math.bignumber(form.value.refundAmount), math.bignumber(form.value.surplusPicCount))
      )
      s = math.multiply(math.bignumber(s), math.bignumber(100))
      s = math.floor(s)
      s = math.multiply(math.bignumber(s), math.bignumber(form.value.refundPicCount))
      s = math.divide(math.bignumber(s), math.bignumber(100))
    }
  }
  return s
})

const isShowRefundPic = computed(() => {
  if (refundTotal.value >= orderAmount.value && form.value.refundType == 3 && !isCancelOpention.value) {
    return true
  } else {
    return false
  }
})

const refundAmountLabel = computed(() => {
  if (form.value.refundType === 1) {
    return '补偿金额'
  }
  // if (form.value.refundType === 3) {
  //   return '选配金额'
  // }
  return '退款金额'
})
const refundCauseLabel = computed(() => {
  if (form.value.refundType === 1) {
    return '补偿理由'
  }
  return '退款理由'
})

const orderStatus = ref('')
function open(id, picCount, status) {
  orderStatus.value = status
  videoId.value = id
  hasPicCount.value = picCount === null
  getInfo({
    refundType: 1,
    videoId: id,
  })
  dialogVisible.value = true
  getTaskList()
}
function close() {
  orderStatus.value = ''
  dialogVisible.value = false
}

function handleClose() {
  form.value = {
    refundType: 1,
    isCancelOrder: 0,
    refundAmount: 0,
    refundCause: '',
    taskDetailIds: [],
    isFullRefundPic: 0,
    refundPicCount: null,
  }
  picCountText.value = ''
  canRefund.value = '1'
  isCancelOpention.value = ''
  refundType.value = ''
  disabled.value = false
  formRef.value.clearValidate()
}

function handleChange(val) {
  getInfo({
    refundType: val,
    videoId: videoId.value,
  })
}

const isCancelOpention = ref('')
const refundType = ref('')
const orderAmount = ref(0)
const originAmount = ref(0)
const videoPromotionAmount = ref(0)

function getInfo(data) {
  disabled.value = true
  getRefundInfo(data)
    .then(res => {
      orderRefundAmount.value = res.data.refundAmount
      refundTotal.value = res.data.refundTotal || 0
      canRefund.value = res.data.canRefund
      isCancelOpention.value = res.data.isCancelOpention || ''
      refundType.value = res.data.refundType || ''
      orderAmount.value = res.data.amount
      originAmount.value = res.data.originAmount
      videoPromotionAmount.value = res.data.videoPromotionAmount

      form.value.refundAmount = res.data.refundAmount
      form.value.refundTotal = res.data.refundTotal
      form.value.surplusPicCount = res.data.surplusPicCount
      form.value.isCancelOrder = 0
      form.value.orderDiscountDetailVOS = res.data.orderDiscountDetailVOS || []
      // picCountText.value = res.data.picPrice || ''
      if (res.data.picCount) {
        res.data.picCount == 1
          ? (picCountText.value = '2张/$' + res.data.picPrice)
          : (picCountText.value = '5张/$' + res.data.picPrice)
      } else {
        picCountText.value = '-'
      }
      // let c = picCountOptions.find(item => item.value == res.data.picCount)
      // if (c) {
      //   picCountText.value = '2张/$' + picCountText.value
      // } else {
      //   picCountText.value = '-'
      // }
      disabled.value = false
    })
    .catch(() => close())
}

function getTaskList() {
  taskLoading.value = true
  getReturnPendingTask({
    videoId: videoId.value,
  })
    .then(res => {
      if (res.code === 200 && res.data?.length) {
        finishTaskList.value = res.data
      }
    })
    .catch(() => {
      finishTaskList.value.length = 0
    })
    .finally(() => {
      taskLoading.value = false
    })
}

function onConfirm() {
  formRef.value.validate(valid => {
    if (valid) {
      // ElMessageBox.confirm('确认取消退款', {
      //   confirmButtonText: '确定',
      //   cancelButtonText: '取消',
      //   center: true,
      // })
      //   .then(res => {
      handleApplyRefund(
        {
          ...form.value,
          refundAmount:
            form.value.refundType === 3 ? JSON.parse(refundRealyAmount.value) : form.value.refundAmount,
          taskDetailIds:
            form.value.refundType === 1 &&
            form.value.taskDetailIds?.length &&
            form.value.taskDetailIds[0] != -1
              ? form.value.taskDetailIds
              : undefined,
          videoId: videoId.value,
          isCancelOrder: form.value.refundType === 1 ? form.value.isCancelOrder : undefined,
        },
        () => {
          emits('success')
          close()
        }
      )
      // })
      // .catch(() => {})
    }
  })
}
const handleDiscount = (list, type) => {
  return list.find(item => item.type == type).discountAmount
}
const handleShowDiscount = (list, type) => {
  return list && list.length > 0 ? list.some(item => item.type == type) : false
}
</script>

<style scoped lang="scss">
:deep(.el-radio-button) {
  margin: 0 10px 10px 0;

  &.is-active {
    box-shadow: none;
  }

  .el-radio-button__inner {
    border-left: var(--el-border);
    border-radius: var(--el-border-radius-base);
  }
}
:deep(.el-radio-group) {
  .el-radio-button.is-active {
    .el-radio-button__inner {
      box-shadow: none;
    }
  }
}
.form-box {
  .info-box {
    padding: 10px 20px;
    margin: 0 0 10px 10px;
    border: 1px solid #d7d7d7;
    border-radius: 8px;
    background-color: #f2f2f280;

    .title {
      font-size: 16px;
      margin-bottom: 10px;
    }
    .info-item {
      .label {
        margin-right: 15px;
      }
    }
  }
}
.task-box {
  width: 100%;
  max-height: 300px;
  overflow: hidden;
  overflow-y: auto;
}
</style>
