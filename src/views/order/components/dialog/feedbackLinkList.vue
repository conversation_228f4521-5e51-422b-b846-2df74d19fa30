<template>
  <el-dialog
    v-model="dialogVisible"
    title="模特反馈素材"
    width="700"
    align-center
    append-to-body
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <template #header>
      <div class="flex-start">
        模特反馈素材
        <el-button
          v-btn
          v-if="tableData.length"
          plain
          round
          style="margin-left: 10px"
          @click="handleOpenUpload"
        >
          帮模特上传
        </el-button>
      </div>
    </template>
    <div class="list" v-loading="tableLoading">
      <template v-if="!tableData.length">
        <div class="no-data">
          <h3 style="text-align: center">暂无模特上传素材信息</h3>
          <div class="flex-center">
            <el-button v-btn plain round @click="handleOpenUpload">帮模特上传</el-button>
          </div>
        </div>
      </template>
      <div class="list-item" v-for="row in tableData" :key="row.id">
        <div class="flex-between head">
          <div style="display: flex; align-items: center">
            <h3 v-if="row.status === 1">已驳回</h3>
            <h3 v-else>待确认</h3>
            <el-tag v-if="row.rollbackId" style="margin-left: 10px" type="warning" round>订单回退</el-tag>
          </div>
          <div>
            <el-button v-btn link type="primary" @click="markDownloadStatus(row.id)">
              {{ row.downloadStatus == 1 ? '已下载' : '未下载' }}
            </el-button>
            <el-button v-btn v-if="row.status === 1" link type="primary" @click="handleView(row)">
              驳回原因
            </el-button>
            <el-button
              v-btn
              v-if="row.status === 0"
              link
              type="primary"
              @click="handleReject(row)"
              v-hasPermi="['order:manage:feedbackMaterial-reject']"
            >
              驳回
            </el-button>
          </div>
        </div>
        <div class="content-box" v-for="item in row.materialInfos" :key="item.id">
          <div class="flex-start">
            <div class="label">上传时间：</div>
            <div class="text">{{ item.uploadTime }}</div>
          </div>
          <div class="flex-start">
            <div class="label">上传对象：</div>
            <div class="text">{{ handleName(item) }}</div>
          </div>
          <div class="flex-start">
            <div class="label">链接：</div>
            <div class="flex-start gap-5 text-n-all" style="margin-bottom: 0">
              <el-link type="primary" target="_blank" :underline="false" :href="item.link">
                {{ item.link }}
              </el-link>
              <CopyButton type="primary" size="small" round :copy-content="item.link" />
            </div>
          </div>
          <div class="flex-start">
            <div class="label">备注：</div>
            <div class="text">{{ item.note || '无' }}</div>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
  <el-dialog
    v-model="uploadDialogVisible"
    title="帮模特上传反馈素材"
    width="700"
    align-center
    append-to-body
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="uploadDialogClose"
  >
    <div class="form-box">
      <el-form
        ref="formRef"
        style="width: 100%"
        :model="form"
        :rules="rules"
        label-width="auto"
        class="demo-ruleForm"
      >
        <el-form-item label="地址" prop="link">
          <el-input v-model="form.link" clearable placeholder="请输入正确的素材下载地址" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" clearable placeholder="请输入备注内容" />
        </el-form-item>
        <!-- <el-form-item label="完结任务" prop="taskDetailIds" v-if="finishTaskList?.length">
          <div v-loading="taskLoading" class="task-box">
            <div>
              <span>请点选需要同步完结的任务单</span>
              <span style="color: #7f7f7f">（支持多个处理中的任务同步完结）</span>
            </div>
            <TaskButton v-model="form.taskDetailIds" :checkList="finishTaskList" :width="590" />
          </div>
        </el-form-item> -->
        <el-form-item>
          <div class="flex-center" style="width: 100%; margin-top: 20px">
            <el-button class="btn" round @click="uploadDialogVisible = false">取消</el-button>
            <el-button class="btn" type="primary" round @click="submitForm()">提交</el-button>
          </div>
        </el-form-item>
      </el-form>
    </div>
  </el-dialog>

  <RejectFeedbackLink ref="RejectFeedbackLinkRef" @success="handleSuccess" />
</template>

<script setup>
import CopyButton from '@/components/Button/CopyButton'
import TaskButton from '@/components/Button/TaskButton.vue'
import RejectFeedbackLink from '@/views/order/components/dialog/rejectFeedbackLink.vue'
import { feedbackModelList, backHelpUpload, markDownload } from '@/api/order/order'
import { getBackHelpModelUploadMaterialPendingTask } from '@/api/task/workOrder'
import useOrderApi from '@/hooks/useOrderApi'
import { ElLoading, ElMessage } from 'element-plus'

defineExpose({
  open,
  close,
})

const emits = defineEmits(['success'])

const { openRejectRemark } = useOrderApi()

const RejectFeedbackLinkRef = ref()

const dialogVisible = ref(false)
const videoId = ref('')

const tableData = ref([])
// const pageNum = ref(1)
// const pageSize = ref(20)
// const total = ref(0)
const tableLoading = ref(false)

const uploadDialogVisible = ref(false)
const formRef = ref()
const form = ref({
  link: '',
  remark: '',
  taskDetailIds: [],
})
const finishTaskList = ref([])
const taskLoading = ref(false)
const rules = {
  link: [
    { required: true, message: '请输入素材下载地址', trigger: 'blur' },
    { type: 'url', message: '请输入正确的素材下载地址', trigger: 'blur' },
  ],
  taskDetailIds: [{ required: true, message: '请选择完结任务', trigger: 'change' }],
}

const isUpdate = ref(false)
const taskDetailId = ref()

function open(id, taskId = '') {
  videoId.value = id
  taskDetailId.value = taskId
  getTaskList()
  handleQuery()
  dialogVisible.value = true
}
function close() {
  taskDetailId.value = ''
  dialogVisible.value = false
}
function handleClose() {
  if (isUpdate.value) {
    emits('success')
  }
  finishTaskList.value.length = 0
  isUpdate.value = false
}
function handleQuery() {
  tableLoading.value = true
  feedbackModelList({ videoId: videoId.value })
    .then(res => {
      tableData.value = res.data
      // total.value = res.data.total
    })
    .finally(() => (tableLoading.value = false))
}
// 分页跳转
// function pageChange(page) {
//   pageNum.value = page.currentPage
//   pageSize.value = page.pageSize
//   handleQuery()
// }

function handleSuccess() {
  isUpdate.value = true
  handleQuery()
}

function getTaskList() {
  taskLoading.value = true
  finishTaskList.value = []
  getBackHelpModelUploadMaterialPendingTask({
    videoId: videoId.value,
  })
    .then(res => {
      if (res.code === 200 && res.data?.length) {
        finishTaskList.value = res.data
      }
    })
    .catch(() => {
      finishTaskList.value.length = 0
    })
    .finally(() => {
      taskLoading.value = false
    })
}

//标记下载状态
function markDownloadStatus(id) {
  markDownload(id)
    .then(() => {
      // ElMessage.success('下载成功')
      handleSuccess()
    })
    .catch(() => {
      // ElMessage.warning('下载失败')
    })
}

function handleOpenUpload() {
  uploadDialogVisible.value = true
  getTaskList()
}
function submitForm() {
  formRef.value.validate(valid => {
    if (valid) {
      const params = {
        videoId: videoId.value,
        link: form.value.link,
        note: form.value.remark,
        taskDetailId: taskDetailId.value ? taskDetailId.value : '',
        taskDetailIds:
          form.value.taskDetailIds?.length && form.value.taskDetailIds[0] != -1
            ? form.value.taskDetailIds
            : undefined,
      }
      const el_loading = ElLoading.service({
        lock: true,
        text: '正在提交中，请稍后',
        background: 'rgba(0, 0, 0, 0.7)',
      })
      backHelpUpload(params)
        .then(() => {
          ElMessage.success('上传成功')
          handleSuccess()
          uploadDialogVisible.value = false
        })
        .finally(() => el_loading.close())
    }
  })
}
function uploadDialogClose() {
  form.value.link = ''
  form.value.remark = ''
  form.value.taskDetailIds = []
  formRef.value?.resetFields()
}

function handleName(item) {
  if (item.object == 2 && item.back) {
    return `内部运营(${item.back.name})`
  } else if (item.object == 3 && item.model) {
    return `模特上传(${item.model.name})`
  }
  return ''
}

function handleReject(row) {
  RejectFeedbackLinkRef.value.open(row.id)
}
function handleView(row) {
  openRejectRemark(row.title, row.remark)
}
</script>

<style scoped lang="scss">
.list {
  min-height: 300px;
  max-height: 750px;
  overflow: auto;

  .no-data {
    width: 100%;
    padding-top: 60px;

    h3 {
      font-weight: 600;
    }
  }

  .list-item {
    color: #000;

    .head {
      padding: 0 10px;
      background-color: var(--el-color-primary-light-9);
    }

    .content-box {
      width: 100%;
      padding: 10px 0;
      border-bottom: 1px solid #ccc;

      &:last-child {
        border: none;
      }

      .flex-start {
        align-items: baseline;
        margin-bottom: 8px;
      }

      .label {
        width: 75px;
        text-align: right;
        flex-shrink: 0;
      }
      .text {
        width: 88%;
        // white-space: normal;
        word-wrap: break-word;
      }
    }
  }
}
.form-box {
  min-height: 300px;

  .btn {
    width: 100px;
  }

  .task-box {
    width: 100%;
    max-height: 300px;
    overflow: hidden;
    overflow-y: auto;
  }
}
</style>
