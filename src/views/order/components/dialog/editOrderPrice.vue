<template>
  <el-dialog
    v-model="dialogVisible"
    title="修改订单费用"
    width="500"
    align-center
    append-to-body
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <!-- <div class="flex-column gap-10">
      <div>
        <el-input-number
          title=""
          v-model="amount"
          :min="0"
          :max="99999"
          :precision="2"
          style="width: 200px"
          @keydown="channelInputLimit"
        />
      </div>
      <div>原视频佣金：$ {{ videoPrice }}</div>
    </div> -->
    <el-form :model="form" ref="formRef" label-width="140px" :rules="rules">
      <el-form-item prop="videoPrice" label="视频佣金">
        <el-input-number
          title=""
          v-model="form.videoPrice"
          :min="0"
          :max="999.99"
          :precision="2"
          style="width: 300px"
          controls-position="right"
          @keydown="channelInputLimit"
        />
        &emsp;$
      </el-form-item>
      <el-form-item prop="picPrice" label="照片佣金">
        <el-input-number
          :disabled="!isPicCount"
          title=""
          v-model="form.picPrice"
          :min="0"
          :max="999.99"
          :precision="2"
          style="width: 300px"
          controls-position="right"
          @keydown="channelInputLimit"
        />
        &emsp;$
      </el-form-item>
      <el-form-item prop="commissionPaysTaxes" label="佣金代缴税费">
        <el-input-number
          title=""
          v-model="form.commissionPaysTaxes"
          :min="0"
          :max="999.99"
          :precision="2"
          style="width: 300px"
          controls-position="right"
          @keydown="channelInputLimit"
        />
        &emsp;$
      </el-form-item>
      <el-form-item prop="exchangePrice" label="PayPal代付手续费">
        <el-input-number
          title=""
          v-model="form.exchangePrice"
          :min="0"
          :max="999.99"
          :precision="2"
          style="width: 300px"
          controls-position="right"
          @keydown="channelInputLimit"
        />
        &emsp;$
      </el-form-item>
      <el-form-item prop="servicePrice" label="蜗牛服务费">
        <el-input-number
          title=""
          v-model="form.servicePrice"
          :min="0"
          :max="999.99"
          :precision="2"
          style="width: 300px"
          controls-position="right"
          @keydown="channelInputLimit"
        />
        &emsp;$
      </el-form-item>
      <el-form-item prop="hj" label="合计">${{ hj.toFixed(2) }}</el-form-item>
      <el-form-item prop="remark" label="备注">
        <el-input type="textarea" v-model="form.remark" maxlength="800" :autosize="{ minRows: 4, maxRows: 8 }" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="flex-center">
        <el-button v-btn @click="close">取消</el-button>
        <el-button v-btn type="primary" :loading="disabled" @click="onConfirm">确认修改</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { updateOrderVideoPrice } from '@/api/order/order'
import { ElLoading, ElMessage } from 'element-plus'
import { bizCommissionUnit } from '@/utils/dict'

defineExpose({
  open,
  close,
})

const emits = defineEmits(['success'])

const dialogVisible = ref(false)
const videoId = ref('')
const disabled = ref(false)
const amount = ref(0)
const videoPrice = ref(0)
const formRef = ref(null)
const form = ref({
  videoPrice: null,
  picPrice: null,
  exchangePrice: null,
  commissionPaysTaxes: null,
  servicePrice: null,
  remark: null,
  videoId: '',
})
const isPicCount = ref(false)

const hj = computed(() => {
  return (
    (form.value.videoPrice || 0) +
    (form.value.picPrice || 0) +
    (form.value.commissionPaysTaxes || 0) +
    (form.value.servicePrice || 0) +
    (form.value.exchangePrice || 0)
  )
})

const rules = {
  videoPrice: [{ required: true, message: '请输入视频佣金', trigger: 'change' }],
  picPrice: [{ required: true, message: '请输入照片佣金', trigger: 'change' }],
  commissionPaysTaxes: [{ required: true, message: '请输入佣金代缴税费', trigger: 'change' }],
  exchangePrice: [{ required: true, message: '请输入PayPal代付手续费', trigger: 'change' }],
  servicePrice: [{ required: true, message: '请输入蜗牛服务费', trigger: 'change' }],
  remark: [{ required: true, message: '请输入备注', trigger: 'blur' }],
}

function open(id, data) {
  form.value.videoId = id
  // amount.value = price || 0
  data.picCount ? (isPicCount.value = true) : (isPicCount.value = false)
  form.value.videoPrice = data.videoPrice || 0
  form.value.picPrice = data.picPrice || 0
  form.value.servicePrice = data.servicePrice || 0
  form.value.exchangePrice = data.exchangePrice || 0
  form.value.commissionPaysTaxes = data.commissionPaysTaxes || 0
  // oldCommission.value = commission || 0
  // oldCommissionUnit.value = bizCommissionUnit.find(item => item.value === commissionUnit)?.label
  dialogVisible.value = true
}
function close() {
  form.value = {
    videoPrice: null,
    picPrice: null,
    exchangePrice: null,
    commissionPaysTaxes: null,
    servicePrice: null,
    remark: null,
    videoId: '',
  }
  dialogVisible.value = false
}

function handleClose() {
  form.value = {
    videoPrice: null,
    picPrice: null,
    exchangePrice: null,
    commissionPaysTaxes: null,
    servicePrice: null,
    remark: null,
    videoId: '',
  }
  amount.value = 0
  disabled.value = false
}

function onConfirm() {
  formRef.value.validate(valid => {
    if (valid) {
      const el_loading = ElLoading.service({
        lock: true,
        text: '正在提交中，请稍后',
        background: 'rgba(0, 0, 0, 0.7)',
      })
      disabled.value = true
      updateOrderVideoPrice(form.value)
        .then(res => {
          ElMessage.success('修改成功')
          emits('success')
          close()
        })
        .finally(() => {
          disabled.value = false
          el_loading.close()
        })
    }
  })
}
const channelInputLimit = e => {
  const key = e.key
  if (key === 'Enter') e.preventDefault()
  const notAllowList = ['e', '+', '-']
  if (notAllowList.includes(key)) {
    e.returnValue = false
    return false
  }
  return true
}
</script>

<style scoped lang="scss"></style>
