<template>
  <el-dialog
    v-model="dialogVisible"
    title="创建任务单"
    width="800"
    align-center
    append-to-body
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      :disabled="disabled"
      :validate-on-rule-change="false"
      label-width="120px"
    >
      <el-form-item label="视频编码">{{ videoCode }}</el-form-item>
      <el-form-item label="任务单类型" prop="taskType">
        <el-radio-group v-model="form.taskType" @change="taskTypeChange">
          <el-radio-button label="售后" :value="1" />
          <el-radio-button label="工单" :value="2" />
        </el-radio-group>
      </el-form-item>
      <el-form-item label="售后分类" v-if="form.taskType == '1'" prop="afterSaleClass">
        <el-radio-group v-model="form.afterSaleClass" @change="afterSaleClassChange">
          <el-radio-button label="视频" :value="1" />
          <el-radio-button :disabled="!picCount" label="照片" :value="2" />
        </el-radio-group>
      </el-form-item>
      <div v-if="form.taskType == '1'">
        <el-form-item v-if="form.afterSaleClass == '1'" label="售后类型" prop="afterSaleVideoType">
          <el-radio-group v-model="form.afterSaleVideoType">
            <el-radio-button v-for="item in afterSaleVideoTypeList" :key="item.value" :value="item.value">
              {{ item.label }}
            </el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-else label="售后类型" prop="afterSalePicType">
          <el-radio-group v-model="form.afterSalePicType">
            <el-radio-button v-for="item in afterSalePicTypeList" :key="item.value" :value="item.value">
              {{ item.label }}
            </el-radio-button>
          </el-radio-group>
        </el-form-item>
      </div>
      <el-form-item v-else label="工单类型" prop="workOrderType">
        <el-radio-group v-model="form.workOrderType" @change="workOrderTypeChange">
          <el-radio-button v-for="item in workOrderTypeList" :key="item.value" :value="item.value">
            {{ item.label }}
          </el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="问题描述" prop="content">
        <el-input
          v-model="form.content"
          style="width: 94%"
          :rows="4"
          type="textarea"
          maxlength="1000"
          show-word-limit
          placeholder="请输入对问题的具体描述"
          clearable
        />
      </el-form-item>
      <el-form-item
        label="补充剪辑要求"
        v-if="
          (form.afterSaleVideoType == '1' ||
            form.afterSaleVideoType == '2' ||
            form.afterSalePicType == '1' ||
            form.afterSalePicType == '3') &&
          form.taskType == 1
        "
      >
        <el-input
          v-model="form.clipRecord"
          style="width: 94%"
          maxlength="1000"
          show-word-limit
          placeholder="请输入补充剪辑要求"
          clearable
        />
      </el-form-item>
      <!-- <el-form-item label="补充剪辑要求" prop="content">
        <el-input
          v-model="form.content"
          style="width: 94%"
          :rows="4"
          type="textarea"
          maxlength="1000"
          show-word-limit
          placeholder="请输入补充剪辑要求"
          clearable
        />
      </el-form-item> -->
      <el-form-item label="问题图片">
        <PasteUpload
          v-if="dialogVisible"
          v-model="form.issuePic"
          :disabled="form.issuePic.length >= 5"
          style="margin-bottom: 10px; width: 94%"
          :limit="5"
          :alwaysShow="true"
          :bucketType="'afterSale'"
          :size="5"
        />
        <div style="width: 100%; margin-bottom: 10px">
          请上传大小不超过
          <span style="color: #d9001b">5M</span>
          ，格式为
          <span style="color: #d9001b">png/jpg/jpeg</span>
          的图片,最多支持上传5张
        </div>
        <ViewerImageList urlName="picUrl" :data="form.issuePic" is-preview-all @delete="deleteImg" />
      </el-form-item>
      <el-form-item label="优先级" prop="priority">
        <el-radio-group v-model="form.priority">
          <el-radio-button label="一般" :value="2" />
          <el-radio-button label="紧急" :value="1" />
        </el-radio-group>
      </el-form-item>
      <el-form-item label="处理人" prop="assigneeId">
        <SelectLoad
          v-model="form.assigneeId"
          :request="listUser"
          :requestCallback="res => res.data"
          :totalCallback="res => res.data.length"
          keyValue="userId"
          keyLabel="userName"
          keyWord="userName"
          placeholder="请选择处理人"
          style="width: 280px"
        />
        <!-- <el-select
          v-model="form.assigneeId"
          collapse-tags
          collapse-tags-tooltip
          clearable
          style="width: 220px"
        >
          <el-option v-for="item in assigneeInfo" :key="item.id" :label="item.name" :value="item.id" />
        </el-select> -->
      </el-form-item>
      <!-- <el-form-item label="任务单类型" prop="type">
        <template #label>
          <div>
            工单类型
            <el-tooltip
              content="
                <div>
                  <h3>类型说明</h3>
                  <div style='padding: 0 0 10px 10px;color: #666;'>
                    <div>1. 要原视频：需要模特提供原视频素材</div>
                    <div>2. 修改视频：需要模特根据要求修改视频</div>
                    <div>3. 重新上传：需要模特重新上传至亚马逊店铺中</div>
                    <div>4. 重拍视频：需要模特重新拍摄视频</div>
                    <div>5. 没拍照片：订单中涉及的选配照片未提供</div>
                    <div>6. 没拍视频：订单中涉及的视频未提供</div>
                    <div>7. 补拍视频：需要模特根据要求补拍视频</div>
                    <div>8. 链接有误：模特上传链接有问题，需要重新提供正确的</div>
                  </div>
                </div>
              "
              raw-content
              placement="bottom"
              effect="light"
            >
              <el-icon><QuestionFilled /></el-icon>
            </el-tooltip>
          </div>
        </template>
        <el-radio-group v-model="form.type">
          <el-radio v-for="item in typeList" :key="item.value" :value="item.value">{{ item.label }}</el-radio>
        </el-radio-group>
      </el-form-item> -->
    </el-form>
    <template #footer>
      <el-button v-btn @click="close">取消</el-button>
      <el-button v-btn type="primary" :disabled="disabled" @click="onSubmit">提交</el-button>
    </template>
    <el-dialog
      width="500"
      v-model="hintDialog"
      align-center
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <div style="text-align: center; font-size: 16px">
        <div v-if="form.taskType == '1'">
          <template v-if="form.afterSaleVideoType != 1 && form.afterSalePicType != 1">
            <div>已存在一笔同为</div>
            <div>
              售后 - {{ form.afterSaleClass == 1 ? '视频' : '照片' }}
              <span v-if="form.afterSaleClass == 1">
                -
                {{
                  afterSaleVideoTypeList.find(item => item.value == form.afterSaleVideoType)?.label
                }}的任务单
              </span>
              <span v-else>
                - {{ afterSalePicTypeList.find(item => item.value == form.afterSalePicType)?.label }}的任务单
              </span>
            </div>
            <div style="color: #d9001b; margin-top: 20px">确定再次提交？</div>
          </template>
          <template v-else>
            <div>已存在一笔同为{{ form.afterSaleClass == 1 ? '重拍视频' : '重拍照片' }}的任务单</div>
            <div style="color: #d9001b; margin-top: 20px">不支持重复发起</div>
          </template>
        </div>
        <div v-else>
          <div>已存在一笔同为"{{ workOrderTypeMap[form.workOrderType] }}"的工单</div>
          <div v-if="form.workOrderType != 1" style="color: #d9001b; margin-top: 20px">确定再次提交？</div>
          <div v-else style="color: #d9001b; margin-top: 20px">不支持重复发起</div>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer flex-center">
          <template
            v-if="form.workOrderType == 1 || form.afterSaleVideoType == 1 || form.afterSalePicType == 1"
          >
            <el-button type="primary" @click="hintDialog = false">好的</el-button>
          </template>
          <template v-else>
            <el-button @click="hintDialog = false">不了</el-button>
            <el-button type="primary" @click="handleHintConfirm">是的</el-button>
          </template>
        </div>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<script setup>
import ViewerImageList from '@/components/ImagePreview/viewerImageList.vue'
import PasteUpload from '@/components/FileUpload/pasteUpload.vue'
import SelectLoad from '@/components/Select/SelectLoad'
import { listUser } from '@/api/system/user'
import { createTask, createTaskOrder, checkTaskExist, historyClipRecord } from '@/api/order/order'
import { orderVideoList } from '@/api/order/select'
import { userList } from '@/api/system/user'
import { ElLoading, ElMessage, ElMessageBox } from 'element-plus'
import { workOrderTypeMap, workOrderTypeList } from '@/views/task/data.js'

defineExpose({
  open,
  close,
})

const emits = defineEmits(['success'])

const hintDialog = ref(false)

const afterSaleVideoTypeList = [
  { label: '重拍视频', value: 1 },
  { label: '补拍视频', value: 2 },
  { label: '要高清视频', value: 3 },
  { label: '原素材', value: 4 },
  { label: '重新上传', value: 5 },
  { label: '重新剪辑', value: 6 },
]
const afterSalePicTypeList = [
  { label: '重拍照片', value: 1 },
  { label: '要高清照片', value: 2 },
  { label: '补拍照片', value: 3 },
  { label: '原素材', value: 4 },
]

const dialogVisible = ref(false)
const formRef = ref()
const disabled = ref(false)
const editVideoId = ref(false)
const videoCode = ref('')
const form = ref({
  taskType: 1,
  afterSaleClass: 1,
  afterSaleVideoType: '',
  afterSalePicType: '',
  workOrderType: '',
  issuePic: [],
  assigneeId: '',
  content: '',
  priority: 2,
  videoId: '',
  clipRecord: '',
})
const rules = {
  taskType: [{ required: true, message: '请选择工单类型', trigger: 'blur' }],
  afterSaleClass: [{ required: true, message: '请选择售后分类', trigger: 'blur' }],
  afterSaleVideoType: [{ required: true, message: '请选择售后类型', trigger: 'blur' }],
  afterSalePicType: [{ required: true, message: '请选择售后类型', trigger: 'blur' }],
  workOrderType: [{ required: true, message: '请选择工单类型', trigger: 'blur' }],
  content: [{ required: true, message: '请输入对问题的具体描述', trigger: 'blur' }],
  priority: [{ required: true, message: '请选择优先级', trigger: 'blur' }],
  // videoId: [{ required: true, message: '请选择关联视频', trigger: 'blur' }],
  assigneeId: [{ required: true, message: '请选择处理人', trigger: 'blur' }],
}
const videoListRow = ref({})
const assigneeInfo = ref([])
const hintDialogData = ref({})
const picCount = ref('')
const assigneeId = ref('')
function open(row, videoId) {
  console.log(row,55);
  
  videoListRow.value = row
  videoCode.value = row.videoCode || ''
  picCount.value = row.picCount || null
  form.value.videoId = videoId ? videoId : row.id
  getHistoryClipRecord(form.value.videoId)
  if (form.value.videoId) {
    console.log(row, 123)
    editVideoId.value = true
    if (row.issue && row.issue.id) {
      assigneeInfo.value.push(row.issue)
      assigneeId.value = row.issue.id
      form.value.assigneeId = row.issue.id
    }
  } else {
    editVideoId.value = false
  }
  dialogVisible.value = true
}
function close() {
  dialogVisible.value = false
}

function getHistoryClipRecord(id) {
  historyClipRecord({ videoId: id }).then(res => {
    if (res.data.historyClipRecordListVOS && res.data.historyClipRecordListVOS.length > 0) {
      let history = res.data.historyClipRecordListVOS.find(item => item.isInitial == 1)?.content || ''
      form.value.clipRecord = history
    }
  })
}

function afterSaleClassChange() {
  form.value.afterSaleVideoType = ''
  form.value.afterSalePicType = ''
}

function taskTypeChange(val) {
  form.value.afterSaleVideoType = ''
  form.value.afterSaleClass = 1
  form.value.afterSalePicType = ''
  form.value.workOrderType = ''
  if (val == 2) {
    form.value.assigneeId = ''
  }
  if (val == 1) {
    form.value.assigneeId = assigneeId.value
  }
}

function workOrderTypeChange(val) {
  if (val == workOrderTypeMap['模特没收到货'] || val == workOrderTypeMap['上传异常']) {
    form.value.assigneeId = videoListRow.value.contact.id
  } else if (val == workOrderTypeMap['下架视频'] || val == workOrderTypeMap['催素材'] || val == workOrderTypeMap['素材链接问题']) {
    form.value.assigneeId = videoListRow.value.issue.id
  } else {
    form.value.assigneeId = ''
  }
}

function handleClose() {
  form.value = {
    taskType: 1,
    afterSaleClass: 1,
    afterSaleVideoType: '',
    afterSalePicType: '',
    workOrderType: '',
    issuePic: [],
    assigneeId: '',
    content: '',
    priority: 2,
    videoId: '',
    clipRecord: '',
  }
  assigneeId.value = ''
  hintDialogData.value = {}
  disabled.value = false
  editVideoId.value = false
  assigneeInfo.value = []
  picCount.value = ''
  formRef.value.clearValidate()
}

function getUserInfo(_val, row) {
  form.value.assigneeId = row.userVO.id
  assigneeInfo.value[0] = {
    id: row.userVO.id,
    name: row.userVO.name,
  }
}

function deleteImg(data, i) {
  form.value.issuePic.splice(i, 1)
}
function handleHintConfirm() {
  hintDialog.value = false
  disabled.value = true
  const el_loading = ElLoading.service({
    lock: true,
    text: '正在提交中，请稍后',
    background: 'rgba(0, 0, 0, 0.7)',
  })
  createTaskOrder(hintDialogData.value)
    .then(res => {
      ElMessage.success('创建成功')
      emits('success')
    })
    .finally(() => {
      disabled.value = false
      dialogVisible.value = false
      el_loading.close()
    })
}
function onSubmit() {
  formRef.value.validate(valid => {
    if (valid) {
      ElMessageBox.confirm('确认提交？', '温馨提示', {
        autofocus: false,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
      })
        .then(() => {
          disabled.value = true
          const el_loading = ElLoading.service({
            lock: true,
            text: '正在提交中，请稍后',
            background: 'rgba(0, 0, 0, 0.7)',
          })
          let { issuePic, ...params } = form.value
          params.issuePic = issuePic.map(item => item.picUrl)
          if (params.taskType == 1) {
            delete params.workOrderType
          } else {
            delete params.afterSaleVideoType
            delete params.afterSaleClass
          }
          if (
            params.afterSaleVideoType != 1 &&
            params.afterSaleVideoType != 2 &&
            params.afterSalePicType != 1 &&
            params.afterSalePicType != 3
          ) {
            delete params.clipRecord
          }
          params.afterSaleClass == 1 ? delete params.afterSalePicType : delete params.afterSaleVideoType
          checkTaskExist(params).then(res => {
            if (res.data) {
              hintDialogData.value = params
              hintDialog.value = true
              el_loading.close()
              disabled.value = false
            } else {
              createTaskOrder(params)
                .then(res => {
                  ElMessage.success('创建成功')
                  emits('success')
                  dialogVisible.value = false
                })
                .finally(() => {
                  disabled.value = false
                  el_loading.close()
                })
            }
          })
        })
        .catch(() => {})
    }
  })
}
</script>

<style scoped lang="scss">
@import '@/assets/styles/customForm.scss';
@import '@/assets/styles/form-disabled.scss';
</style>
