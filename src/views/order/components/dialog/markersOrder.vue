<template>
  <el-dialog
    v-model="dialogVisible"
    title="标记订单"
    width="700"
    align-center
    append-to-body
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <div class="form-box" v-loading="loading">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        :disabled="disabled"
        :validate-on-rule-change="false"
        label-width="120px"
      >
        <el-form-item label="排单类型" prop="scheduleType">
          <el-radio-group v-model="form.scheduleType">
            <el-radio-button :value="1">排单</el-radio-button>
            <el-radio-button :value="2">携带排单</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="form.scheduleType === 1" label="" prop="commission" :rules="commissionRules">
          <el-input
            v-model="form.commission"
            clearable
            style="max-width: 400px"
            placeholder="请输入调整后的佣金（0则表示免佣金）"
          >
            <template #prepend>
              <el-select
                v-model="form.commissionUnit"
                placeholder="请选择单位"
                clearable
                style="width: 100px"
              >
                <el-option
                  v-for="item in bizCommissionUnit"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </template>
          </el-input>
          <span style="margin-left: 10px; color: red">
            原佣金：{{ oldCommission }}{{ oldCommissionUnit }}
          </span>
        </el-form-item>
        <template v-if="form.scheduleType === 2">
          <el-form-item label="携带类型" prop="carryType">
            <el-radio-group v-model="form.carryType">
              <el-radio-button :value="1">主携带</el-radio-button>
              <el-radio-button :value="2">被携带</el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item v-if="form.carryType === 1" label="" prop="mainCarryCount">
            <el-input-number
              title=""
              v-model="form.mainCarryCount"
              :min="0"
              controls-position="right"
              style="width: 200px"
              placeholder="请输入携带数量"
              clearable
            />
          </el-form-item>
          <el-form-item v-if="form.carryType === 2" label="" prop="mainCarryVideoId">
            <el-select v-model="form.mainCarryVideoId" placeholder="请选择主携带订单" style="width: 240px">
              <el-option
                v-for="item in mainCarryVideoList"
                :key="item.id"
                :label="item.videoCode"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </template>
        <el-form-item label="发货备注" prop="shippingRemark">
          <el-input
            v-model="form.shippingRemark"
            type="textarea"
            :rows="4"
            placeholder="请输入发货备注"
            style="width: 80%"
            maxlength="800"
            show-word-limit
            clearable
          />
        </el-form-item>
        <el-form-item label="发货图片">
          <el-upload
            :file-list="fileList"
            action=""
            list-type="picture-card"
            :on-preview="handlePictureCardPreview"
            :http-request="uploadPhotoFile"
            :before-upload="handleBeforeUpload"
            :on-remove="removeUpload"
            :class="{
              disabled: disabled || fileList.length >= 3,
            }"
          >
            <!-- :class="{
              disabled,
            }" -->
            <el-icon><Plus /></el-icon>
          </el-upload>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div class="flex-center">
        <el-button v-btn @click="close">取消</el-button>
        <el-button v-btn type="primary" :disabled="disabled" @click="onConfirm">
          {{ confirmText }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { selectListMainCarry } from '@/api/order/select'
import { markOrder, getMarkOrderInfo } from '@/api/order/order'
import { uploadCloudFile } from '@/api/index'
import { bizCommissionUnit } from '@/utils/dict'
import { beforeUpload } from '@/utils/public'
import { ElLoading, ElMessage } from 'element-plus'

const { proxy } = getCurrentInstance()
const picHead = proxy.$picUrl

defineExpose({
  open,
  close,
})

const emits = defineEmits(['success', 'previews'])

const dialogVisible = ref(false)
const videoId = ref('')
const loading = ref(false)
const disabled = ref(false)
const formRef = ref()
const form = ref({
  scheduleType: 1,
  commission: '',
  commissionUnit: bizCommissionUnit[0].value,
  carryType: 1,
  mainCarryCount: 0,
  mainCarryVideoId: '',
  shippingRemark: '',
})
const oldCommission = ref(0)
const oldCommissionUnit = ref('')
const fileList = ref([])
const mainCarryVideoList = ref([])
const flag = ref(0)
const confirmText = ref('确认')

const rules = {
  scheduleType: [{ required: true, message: '请选择排单类型', trigger: 'blur' }],
  shippingRemark: [{ required: true, message: '请输入发货备注', trigger: 'blur' }],
  carryType: [{ required: true, message: '请选择携带类型', trigger: 'blur' }],
  mainCarryCount: [{ required: true, message: '请输入携带数量', trigger: 'blur' }],
  mainCarryVideoId: [{ required: true, message: '请选择主携带订单', trigger: 'blur' }],
}
const commissionRules = [{ required: true, validator: checkCommission, trigger: 'blur' }]

function checkCommission(rule, value, callback) {
  if (!value) {
    return callback(new Error('请输入佣金'))
  }
  if (isNaN(value)) {
    return callback(new Error('请输入数字'))
  }
  const numberReg = /^\d+(\.\d{1,2})?$/
  if (!numberReg.test(value)) {
    return callback(new Error('只能输入二位小数'))
  }
  if (!form.value.commissionUnit) {
    return callback(new Error('请选择佣金单位'))
  }
  return callback()
}

function open(id, orderFlag) {
  videoId.value = id
  // oldCommission.value = commission || 0
  // oldCommissionUnit.value = bizCommissionUnit.find(item => item.value === commissionUnit)?.label
  if (orderFlag) {
    disabled.value = true
    flag.value = orderFlag
    confirmText.value = '已标记'
  }
  getFlagInfo()
}
function close() {
  dialogVisible.value = false
}

function handleClose() {
  form.value = {
    scheduleType: 1,
    commission: '',
    commissionUnit: bizCommissionUnit[0].value,
    carryType: 1,
    mainCarryCount: 0,
    mainCarryVideoId: '',
    shippingRemark: '',
  }
  fileList.value = []
  disabled.value = false
  confirmText.value = '确认'
  formRef.value.clearValidate()
}

function getSelectList() {
  selectListMainCarry(videoId.value).then(res => {
    mainCarryVideoList.value = res.data
  })
}
// 获取标记信息详情
function getFlagInfo() {
  getMarkOrderInfo(videoId.value)
    .then(res => {
      if (res.data) {
        form.value = res.data
        if (!form.value.scheduleType) {
          form.value.scheduleType = 1
        }

        if (res.data.originCommission || res.data.originCommission == 0) {
          // form.value.commission = res.data.commission ? res.data.commission : res.data.originCommission
          oldCommission.value = res.data.originCommission
        }

        if (res.data.originCommissionUnit) {
          form.value.commissionUnit = res.data.originCommissionUnit
          oldCommissionUnit.value = bizCommissionUnit.find(
            item => item.value === res.data.originCommissionUnit
          )?.label
        }

        if (res.data.shippingPics && res.data.shippingPics.length) {
          fileList.value = res.data.shippingPics.map(item => ({
            // ...item,
            // url: item.picUrl,
            url: picHead + item,
            picUrl: item,
          }))
        }
        dialogVisible.value = true
        getSelectList()
      }
    })
    .catch(() => {
      close()
    })
}

function uploadPhotoFile(options) {
  // console.log(options);
  const formData = new FormData()
  formData.append('file', options.file)
  uploadCloudFile(formData).then(res => {
    if (res.code == 200) {
      fileList.value.push({
        ...res.data,
        url: res.data.picUrl,
        uid: options.file.uid,
      })
    }
  })
}
function handleBeforeUpload(raw) {
  return beforeUpload([raw])
}
function removeUpload(file) {
  let i = fileList.value.findIndex(item => item.uid == file.uid || item.id == file.id)
  if (i > -1) {
    fileList.value.splice(i, 1)
  }
}
function handlePictureCardPreview(uploadFile) {
  let index = fileList.value.findIndex(item => item.uid == uploadFile.uid)
  emits('previews', fileList.value, index)
}

function onConfirm() {
  formRef.value.validate(valid => {
    if (valid) {
      disabled.value = true
      let params = {
        id: videoId.value,
        scheduleType: form.value.scheduleType,
        shippingRemark: form.value.shippingRemark,
      }
      // 排单类型
      if (params.scheduleType === 1) {
        params.commission = form.value.commission
        params.commissionUnit = form.value.commissionUnit
      } else {
        // 携带类型
        params.carryType = form.value.carryType
        if (params.carryType === 1) {
          params.mainCarryCount = form.value.mainCarryCount
        } else {
          params.mainCarryVideoId = form.value.mainCarryVideoId
        }
      }
      if (fileList.value.length) {
        params.shippingPic = fileList.value.map(item => {
          return item.picUrl
          // id: item.id,
          // picUrl: item.picUrl,
          // videoUrl: '',
        })
      }
      const el_loading = ElLoading.service({
        lock: true,
        text: '正在提交中，请稍后',
        background: 'rgba(0, 0, 0, 0.7)',
      })
      markOrder(params)
        .then(res => {
          ElMessage.success('标记成功')
          emits('success')
          close()
        })
        .finally(() => {
          disabled.value = false
          el_loading.close()
        })
    }
  })
}
</script>

<style scoped lang="scss">
:deep(.el-upload-list__item) {
  width: 80px;
  height: 80px;

  img {
    object-fit: cover;
  }
}
:deep(.disabled) {
  .el-upload--picture-card {
    display: none;
  }
}
:deep(.el-upload--picture-card) {
  width: 80px;
  height: 80px;
}
:deep(.el-radio-button) {
  margin: 0 10px 10px 0;

  &.is-active {
    box-shadow: none;
  }

  .el-radio-button__inner {
    border-left: var(--el-border);
    border-radius: var(--el-border-radius-base);
  }
}
:deep(.el-radio-group) {
  .el-radio-button.is-active {
    .el-radio-button__inner {
      box-shadow: none;
    }
  }
}
</style>
