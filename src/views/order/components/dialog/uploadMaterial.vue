<template>
  <div>
    <el-dialog
      v-model="dialogVisible"
      title="上传素材信息"
      align-center
      width="550px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="close"
    >
      <div class="info" v-loading="loading">
        <div class="empty" v-if="materialInfo == null">
          <div class="empty-tip">暂无商家上传素材信息</div>
          <el-button v-btn round plain @click="helpUploadinfo">帮商家上传信息</el-button>
        </div>
        <div v-else>
          <div class="flex-start col-24">
            <div class="label">提交对象：</div>
            <div class="content more-ell" style="--l: 5; width: calc(100% - 95px); word-break: break-all">
              {{ materialInfo.object == 1 ? '商家' : '运营' }} {{ materialInfo.back?.name }}({{
                materialInfo.time
              }})
            </div>
          </div>
          <div class="flex-start col-24">
            <div class="label">上传的链接：</div>
            <div
              class="content more-ell"
              style="--l: 5; color: #409eff; width: calc(100% - 95px); cursor: pointer; word-break: break-all"
              @click="openUrl"
            >
              {{ materialInfo.needUploadLink }}
              <!-- <el-button link type="primary" v-btn @click="openUrl">
                {{ materialInfo.needUploadLink }}
              </el-button> -->
            </div>
          </div>
          <div class="flex-start col-24">
            <div class="label">视频标题：</div>
            <div class="content more-ell" style="--l: 5; width: calc(100% - 95px); word-break: break-all">
              {{ materialInfo.videoTitle }}
            </div>
          </div>
          <div class="flex-start col-24" style="align-items: flex-start">
            <div class="label">视频封面图：</div>
            <div
              v-if="materialInfo.videoCover"
              class="content more-ell"
              style="--l: 5; width: calc(100% - 95px); word-break: break-all; display: flex; align-items: end"
            >
              <el-image
                style="height: 80px; width: 80px"
                :src="$picUrl + materialInfo.videoCover + '!squarecompress'"
                @click="showViewer([materialInfo.videoCover])"
              />
              <el-button
                v-btn
                link
                type="primary"
                style="margin-left: 10px"
                @click="downloadImage($picUrl + materialInfo.videoCover)"
              >
                下载图片
              </el-button>
              <el-button
                v-btn
                link
                type="primary"
                style="margin-left: 10px"
                @click="showImageOnNewerWindow($picUrl + materialInfo.videoCover)"
              >
                新窗口中打开原图(临时)
              </el-button>
            </div>
          </div>
          <div class="flex-start col-24" v-if="materialInfo.object != 1">
            <div class="label">客服备注：</div>
            <div class="content more-ell" style="--l: 5; width: calc(100% - 95px); word-break: break-all">
              {{ materialInfo.remark }}
            </div>
          </div>
        </div>
      </div>
      <template #footer v-if="materialInfo != null">
        <div class="flex-end btn">
          <slot name="button">
            <el-button type="primary" v-btn @click="handleEdit" v-if="materialInfo?.status == 1">
              修改
            </el-button>
            <CopyButton link style="background: #fff; color: #409eff" :copy-content="handleCopy()">
              <template #default>全部复制</template>
            </CopyButton>
            <!-- <el-link
              :underline="false"
              v-copyText="copyValue"
              type="primary"
              v-copyText:callback="copyTextSuccess"
            >
              复制内容
            </el-link> -->
          </slot>
        </div>
      </template>
    </el-dialog>
    <el-dialog
      v-model="helpUploadDialogVisible"
      title="上传素材（帮商家上传）"
      align-center
      width="700px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      custom-close
      @close="helpClose"
    >
      <div class="help-box">
        <el-form
          :model="form"
          :rules="rules"
          ref="formRef"
          label-width="auto"
          @close="() => formRef.resetFields()"
        >
          <el-form-item label="上传的链接" prop="needUploadLink">
            <el-input
              placeholder="请输入上传的链接"
              :rows="3"
              resize="none"
              type="textarea"
              v-model="form.needUploadLink"
            ></el-input>
            <div class="tips" v-if="isShowLinkHint">*若有新的产品链接，直接更换即可</div>
          </el-form-item>
          <el-form-item label="视频标题" prop="videoTitle">
            <el-input
              placeholder="请输入视频标题"
              v-model="form.videoTitle"
              type="text"
              maxlength="60"
              minlength="10"
            ></el-input>
            <div class="tips" v-if="isShowTitleHint">
              请勿输入: 表情符号、"$"、非英文字符、产品 ASIN、全大写或全数字标题
            </div>
          </el-form-item>
          <el-form-item label="视频封面图">
            <ViewerImageList
              v-if="filePicList && filePicList.length > 0"
              :data="filePicList"
              :urlName="'picUrl'"
              is-preview-all
              :show-delete-btn="true"
              @delete="handleFileDelete"
            />
            <PasteUpload
              v-else
              :limit="1"
              :bucketType="'order'"
              @success="handleFileChange"
              :file-type="['png', 'jpg', 'jpeg', 'bmp', 'gif']"
              :size="5"
              :resolutionRatio="true"
            />
            <div class="upload-tips">
              1. 请上传1张大小不超过
              <span class="upload-tips__hint">5M</span>
              ，格式为
              <span class="upload-tips__hint">png/jpg、jpeg/bmp/gif</span>
              的文件;
              <br />
              2.分辨率推荐上传
              <span class="upload-tips__hint">1920*1080px</span>
              （最小上传640*360px/最大上传3840*2160px）
            </div>
          </el-form-item>
          <el-form-item label="客服备注" prop="remark">
            <el-input
              type="textarea"
              :rows="6"
              resize="none"
              v-model="form.remark"
              placeholder="请输入备注内容"
              maxlength="800"
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="flex-center btn">
          <slot name="button">
            <el-button v-btn plain @click="helpClose" style="padding: 0 30px">取消</el-button>
            <el-button v-btn type="primary" @click="onConfirm">确认提交</el-button>
          </slot>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { getUploadMaterial, backHelpUploadMaterial, editUploadMaterial } from '@/api/order/order'
import CopyButton from '@/components/Button/CopyButton.vue'
import PasteUpload from '@/components/FileUpload/pasteUpload.vue'
import ViewerImageList from '@/components/ImagePreview/viewerImageList.vue'
import { english_d_reg,englishCharacter_d } from '@/utils/RegExp'
import { ElMessage } from 'element-plus'
import { getCurrentInstance } from 'vue'
const dialogVisible = ref(false)
import { downUrlImageV1 } from '@/utils/download'
import { useViewer } from '@/hooks/useViewer'
const { showViewer } = useViewer()
const { proxy } = getCurrentInstance()
const picHead = proxy.$picUrl

const materialInfo = ref({})

defineExpose({
  open,
  close,
})

const emits = defineEmits(['success'])
const loading = ref(false)
const orderId = ref(null)
const productLink = ref('')
function open(id, link) {
  if (id) {
    orderId.value = id
    getDetail(id)
  }
  productLink.value = link || ''
  dialogVisible.value = true
}
const copyValue = ref('')

function getDetail(id) {
  loading.value = true
  getUploadMaterial(id)
    .then(res => {
      if (res.data) {
        materialInfo.value = res.data
        copyValue.value =
          `
上传对象:${materialInfo.value.object == 1 ? '商家' : '运营'} ${materialInfo.value.back?.name || ''}(${
            materialInfo.value.time
          })\n` +
          `上传的链接：${materialInfo.value.needUploadLink}\n` +
          `视频标题：${materialInfo.value.videoTitle}\n` +
          `视频封面图：${materialInfo.value.videoCover ? picHead + materialInfo.value.videoCover : ''}\n` +
          `${materialInfo.value.object == 1 ? '' : `客服备注：${materialInfo.value.remark || ''}\n`}`
      } else {
        materialInfo.value = null
      }
    })
    .finally(() => {
      loading.value = false
    })
}
function handleCopy() {
  return copyValue.value
}

const isEdit = ref(false)
function handleEdit() {
  isEdit.value = true
  form.value.id = materialInfo.value.id
  form.value.object = materialInfo.value.object
  form.value.needUploadLink = materialInfo.value.needUploadLink || ''
  form.value.remark = materialInfo.value.remark
  form.value.videoId = materialInfo.value.videoId
  form.value.videoTitle = materialInfo.value.videoTitle
  if (materialInfo.value.videoCover) {
    filePicList.value = [
      {
        picUrl: materialInfo.value.videoCover,
        url: materialInfo.value.videoCover,
        id: materialInfo.value.videoCover,
      },
    ]
  }
  close()
  helpUploadDialogVisible.value = true
}

function close() {
  copyValue.value = ''
  materialInfo.value = null
  dialogVisible.value = false
}
function downloadImage(url) {
  downUrlImageV1(url, 'image')
}
function showImageOnNewerWindow(url) {
  window.open(url, '_blank')
}

// 帮助商家上传部分代码
const helpUploadDialogVisible = ref(false)
const formRef = ref(null)
const filePicList = ref([])
const isShowLinkHint = ref(true)
const isShowTitleHint = ref(true)
const form = ref({
  needUploadLink: '',
  videoTitle: '',
  videoCover: '',
  remark: '',
})
const rules = {
  needUploadLink: [{ required: true, validator: validateLink, trigger: 'change' }],
  videoTitle: [{ required: true, validator: validateTitle, trigger: 'change' }],
}

//视频链接校验规则
function validateLink(rule, value, callback) {
  const regLink = 'https://www.amazon.com'
  if (value && value.startsWith(regLink)) {
    isShowLinkHint.value = true
    return callback()
  } else {
    isShowLinkHint.value = false
    return callback(new Error('视频链接有格式错误'))
  }
}

//视频标题校验规则
// const regexAscll = /[^\x00-\x7F]|[A-Z0-9]{10}/
const regexNum = /^\d+$/
const regexAscll = /^(?!.*\\b(?=[A-Z0-9]{10}\\b)(?!(?:[A-Z]{10}|\\d{10})\\b)[A-Z0-9]{10}\\b).*$/
// const regexS = /S/
// && !regexS.test(value)
function validateTitle(rule, value, callback) {
  if (
    englishCharacter_d.test(value) &&
    value != value.toUpperCase() &&
    !regexNum.test(value) &&
    regexAscll.test(value)
  ) {
    isShowTitleHint.value = true
    let temp = value.split(' ').filter(Boolean)
    if (temp.length >= 3 && value.length >= 10) {
      isShowTitleHint.value = true
      return callback()
    } else {
      isShowTitleHint.value = false
      return callback(new Error('请最少输入3个单词,10个字符'))
    }
  } else {
    isShowTitleHint.value = false
    return callback(new Error('请勿输入: 表情符号、"$"、非英文字符、产品 ASIN、全大写或全数字标题'))
  }
}

function openUrl(event) {
  event.preventDefault()
  // 使用window.open打开新窗口
  window.open(materialInfo.value.needUploadLink, '_blank')
}

//帮商家上传信息
function helpUploadinfo() {
  close()
  form.value.needUploadLink = productLink.value
  helpUploadDialogVisible.value = true
}
function helpClose() {
  isShowTitleHint.value = true
  isShowLinkHint.value = true
  filePicList.value = []
  isEdit.value = false
  form.value = {
    needUploadLink: '',
    videoTitle: '',
    videoCover: '',
    remark: '',
  }
  formRef.value.resetFields()
  helpUploadDialogVisible.value = false
}
//确认提交
function onConfirm() {
  const { ...params } = form.value
  formRef.value.validate(valid => {
    if (valid) {
      let picUrl = ''
      if (filePicList.value && filePicList.value.length > 0) {
        picUrl = filePicList.value[0].picUrl
      }
      if (!isEdit.value) {
        backHelpUploadMaterial({
          ...params,
          videoCover: picUrl,
          videoId: orderId.value,
        }).then(res => {
          ElMessage.success('提交成功')
          emits('success')
          helpClose()
        })
      } else {
        editUploadMaterial({ ...params, videoCover: picUrl }).then(res => {
          ElMessage.success('提交成功')
          emits('success')
          helpClose()
        })
      }
    }
  })
}

function handleFileChange(data) {
  if (data && data.length > 0) {
    filePicList.value = [data[0].data || '']
  }
}
function handleFileDelete(index) {
  filePicList.value.splice(index, 1)
}
</script>

<style scoped lang="scss">
.info {
  .col-24 {
    // align-items: flex-start;
    align-items: baseline;
    margin-bottom: 10px;
    width: 100%;

    .label {
      text-align: right;
      flex-shrink: 0;
      width: 90px;
    }
    .content {
      flex-shrink: 0;
      width: calc(100% - 95px);
      word-break: break-all;
    }
    .tip {
      flex-shrink: 0;
      color: #7f7f7f;
    }
  }
}

.empty {
  height: 300px;
  text-align: center;
  padding-top: 100px;
  &-tip {
    font-size: 18px;
    font-weight: 800;
    margin-bottom: 10px;
  }
}

.help-box {
  .tips {
    color: #aaa;
    font-size: 12px;
  }
  .upload-tips {
    width: 100%;
    line-height: 15px;
    // margin: 0 0 -50px 10px;
    margin-top: 10px;
    color: #000;
    font-size: 11px;
    &__hint {
      color: #d9001b;
    }
  }
}
:deep(.el-upload-list__item) {
  width: 80px;
  height: 80px;
}
:deep(.disabled) {
  .el-upload--picture-card {
    display: none;
  }
}
:deep(.el-upload--picture-card) {
  width: 80px;
  height: 80px;
}
:deep(.disabled) {
  .el-upload--picture-card {
    display: none;
  }
}
:deep(.hidden) {
  .el-upload--picture-card {
    display: none;
  }
}
</style>
