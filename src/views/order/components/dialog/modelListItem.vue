<template>
  <div class="model-list-item">
    <!-- <div class="status-box">
        <el-text v-if="data.status == 0" class="primary" @click="handleAction('正常合作')">
          正常合作
          <el-icon><EditPen /></el-icon>
        </el-text>
        <el-text v-else-if="data.status == 1" class="warning" @click="handleAction('暂停合作')">
          暂停合作
          <el-icon><EditPen /></el-icon>
        </el-text>
        <el-text v-else-if="data.status == 3" class="danger" @click="handleAction('取消合作')">
          取消合作
          <el-icon><EditPen /></el-icon>
        </el-text>
        <el-tooltip v-else content="2024.08.XX恢复" placement="left" effect="light">
          <el-text class="success" @click="handleAction('行程中')">
            行程中
            <el-icon><EditPen /></el-icon>
          </el-text>
        </el-tooltip>
      </div> -->
    <div class="flex-start gap-10 content-box" ref="modelListBoxRef">
      <div class="percentage-img">
        <PercentageImg
          width="90px"
          :src="$picUrl + data?.modelPic"
          :moreLength="data?.lifePhoto?.length || 0"
          radius="4px"
          @more="viewLifePhoto"
        />
        <!-- <div class="hint" v-if="data.topTime != null">置顶</div> -->
      </div>
      <div class="info-box">
        <div class="name one-ell">
          <div>{{ data.name }}</div>
          <div class="acc">{{ data.account ? `${data.account}` : '' }}</div>
        </div>
        <div class="flex-start gap-10 tag-info">
          <el-text v-if="data.sex == 1">
            <el-icon color="#3b99fc"><Male /></el-icon>
            男
          </el-text>
          <el-text v-else>
            <el-icon color="#f69661"><Female /></el-icon>
            女
          </el-text>
          <biz-model-ageGroup :value="data.ageGroup" tag="text">
            <template v-slot="{ dict }">
              <el-text>
                <el-icon color="#3b99fc"><User /></el-icon>
                {{ dict.label }}
              </el-text>
            </template>
          </biz-model-ageGroup>
          <biz-nation :value="data.nation" tag="text">
            <template v-slot="{ dict }">
              <el-text>
                <el-icon color="#3b99fc"><LocationInformation /></el-icon>
                {{ dict.label }}
              </el-text>
            </template>
          </biz-nation>
        </div>
        <div class="flex-start tag-info">
          <model-score
            v-if="data.cooperationScore || data.cooperationScore === 0"
            :score="data.cooperationScore"
            style="margin-right: 5px"
          />
          <biz-model-type-new :value="data.type" />
          <!-- <div
            style="color: #606266; margin-right: 5px"
            v-if="data.cooperationScore || data.cooperationScore === 0"
          >
            模特评分：{{ data.cooperationScore }}分
          </div> -->
          <!-- <biz-model-cooperation :value="data.cooperation" type="primary" /> -->
        </div>
        <div class="flex-start tag-info">
          <!-- <biz-model-type :value="data.type" /> -->
          <!-- <biz-model-cooperation :value="data.cooperation" /> -->
          <biz-model-platform :value="data.platform" />
        </div>
        <div class="tips">
          <span>客服：{{ data.persons[0]?.name }}</span>
          &emsp;
          <span>创建时间：{{ data.createTime }}</span>
        </div>
      </div>
      <div class="tags-box">
        <div class="flex-start gap algin_b" style="margin-bottom: 10px">
          <div class="label-text" style="color: #015478">模特标签</div>
          <div class="flex-start gap tag-list">
            <template v-for="(item, index) in data.tags" :key="item.dictId">
              <el-tag class="one-ell tag" v-if="index < 5" round>{{ item.name }}</el-tag>
            </template>
            <template v-if="data.tags.length > 5">
              <el-tooltip placement="top" effect="light" trigger="click">
                <el-tag class="tag cur" round>. . .</el-tag>
                <template #content>
                  <div class="tooltip-tag" style="max-width: 900px">
                    <template v-for="item in data.tags" :key="item.dictId">
                      <el-tag class="tag" style="margin: 2px" round>{{ item.name }}</el-tag>
                    </template>
                  </div>
                </template>
              </el-tooltip>
            </template>
          </div>
        </div>
        <div class="flex-start gap algin_b">
          <div class="label-text" style="color: #7b4d12">擅长品类</div>
          <div class="flex-start gap tag-list">
            <template v-for="(item, index) in data.specialtyCategory" :key="item.dictId">
              <el-tag class="one-ell tag" v-if="index < 9" type="warning" round>{{ item.name }}</el-tag>
            </template>
            <template v-if="data.specialtyCategory.length > 9">
              <el-tooltip placement="top" effect="light" trigger="click">
                <el-tag class="tag cur" type="warning" round>. . .</el-tag>
                <template #content>
                  <div class="tooltip-tag" style="max-width: 900px">
                    <template v-for="item in data.specialtyCategory" :key="item.dictId">
                      <el-tag class="tag" type="warning" style="margin: 2px" round>{{ item.name }}</el-tag>
                    </template>
                  </div>
                </template>
              </el-tooltip>
            </template>
          </div>
        </div>
      </div>
      <div class="video-box flex-start gap-10">
        <VideoCover
          v-for="(item, i) in data.amazonVideo"
          :key="i"
          platform="0"
          class="img"
          width="100px"
          height="100px"
          suffix="!squarecompress"
          :src="item.picUri"
          fit="fill"
          @click="openVideo(item)"
        />
        <VideoCover
          v-for="(item, i) in data.tiktokVideo"
          :key="i"
          platform="1"
          class="img"
          width="100px"
          height="100px"
          suffix="!squarecompress"
          :src="item.picUri"
          fit="fill"
          @click="openVideo(item)"
        />
      </div>
      <div class="flex-column more-box">
        <div v-if="isOverflow" class="gengduo" @click="openDetails()">···</div>
      </div>
    </div>
    <el-divider />
    <div class="flex-between">
      <el-row class="statistics-box">
        <el-col :span="4">
          <el-statistic title="可拍数" :value="data.can" />
        </el-col>
        <el-col :span="4">
          <el-statistic title="待拍数" :value="data.waits" />
        </el-col>
        <el-col :span="4">
          <el-statistic title="超时率" :value="data.overtimeRate * 100">
            <template #suffix>%</template>
          </el-statistic>
        </el-col>
        <el-col :span="4">
          <el-statistic title="售后率" :value="data.afterSaleRate * 100">
            <template #suffix>%</template>
          </el-statistic>
        </el-col>
        <el-col :span="4">
          <el-statistic title="已完成" :value="data.finished" />
        </el-col>
        <el-col :span="4">
          <el-statistic title="被收藏" :value="data.collected" />
        </el-col>
      </el-row>
      <div class="flex-center">
        <el-button round v-btn type="primary" @click="selectModel">选Ta</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import PercentageImg from '@/components/ImagePreview/percentageImg.vue'
import VideoCover from '@/components/ImagePreview/videoCover.vue'
import { http_reg } from '@/utils/RegExp'
import { ElMessage, ElMessageBox } from 'element-plus'
import { computed } from 'vue'

const router = useRouter()

const props = defineProps({
  data: {
    type: Object,
    required: true,
  },
})

const emits = defineEmits(['openDetails', 'openVideo', 'openPhoto', 'action', 'selectModelInfo'])

const data = toRef(() => props.data)

const modelListBoxRef = ref()
const isOverflow = computed(() => {
  let numOne = props.data.amazonVideo.length ? props.data.amazonVideo.length : 0
  let numTwo = props.data.tiktokVideo.length ? props.data.tiktokVideo.length : 0
  return numOne + numTwo > 3 ? true : false
})

function openDetails() {
  emits('openDetails', data.value)
}

function openVideo(item) {
  if (!item?.videoUrl) {
    ElMessage.warning('无视频链接！')
    return
  }
  if (!http_reg.test(item.videoUrl)) {
    ElMessage.warning('视频链接格式有误！')
    return
  }
  emits('openVideo', item.videoUrl)
}

function viewLifePhoto() {
  emits('openPhoto', data.value.lifePhoto)
}

function handleAction(btn) {
  if (btn === '详情') {
    router.push(`/model/handleModel/read/1/${data.value.id}`)
    return
  }
  if (btn === '编辑') {
    router.push(`/model/handleModel/edit/${data.value.id}`)
    return
  }
  emits('action', btn, data.value)
}
//选择模特
function selectModel() {
  ElMessageBox.confirm('确认选择该模特？', '温馨提示', {
    autofocus: false,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
  }).then(res => {
    emits('selectModelInfo', props.data)
  })
}
</script>

<style scoped lang="scss">
.algin_b {
  align-items: baseline;
}
.model-list-item {
  position: relative;
  margin: 10px 0;
  padding: 22px 10px 15px;
  border-radius: 5px;
  border: 1px solid var(--el-color-primary-light-7);

  .status-box {
    position: absolute;
    top: 0;
    right: 0;
    font-size: 13px;
    border-bottom: 1px solid var(--el-color-primary-light-7);
    border-left: 1px solid var(--el-color-primary-light-7);
    padding: 3px 10px;
    border-radius: 0 5px;
    background: #fff;

    .el-text {
      cursor: pointer;
    }

    .primary {
      color: var(--el-color-primary);
    }
    .success {
      color: var(--el-color-success);
    }
    .warning {
      color: var(--el-color-warning);
    }
    .danger {
      color: var(--el-color-danger);
    }
  }

  .content-box {
    overflow: hidden;
  }

  .percentage-img {
    position: relative;
    flex-shrink: 0;
    margin: auto 0;
    overflow: hidden;

    .hint {
      background: var(--el-color-warning);
      color: #fff;
      font-size: 13px;
      width: 60px;
      position: absolute;
      left: -20px;
      top: -2px;
      transform: scale(0.8) rotateZ(-45deg);
      text-align: center;
      line-height: 23px;
    }
  }

  .info-box {
    font-size: 14px;
    width: 300px;
    flex-shrink: 0;

    .name {
      display: flex;
      align-items: center;
      font-size: 16px;
      font-weight: 600;
      color: #333;
      max-width: 300px;
      margin-bottom: 5px;

      .acc {
        margin-left: 5px;
        font-size: 12px;
        font-weight: 300;
        color: #7f7f7f;
      }
    }

    .tag-info {
      margin-bottom: 10px;
      flex-wrap: wrap;
      gap: 5px 0;
    }

    .tips {
      font-size: 12px;
      color: #7f7f7f;
    }
  }

  .tags-box {
    font-size: 14px;
    flex-shrink: 0;

    .tag-list {
      flex-wrap: wrap;
      width: 300px;
    }
    .tag {
      width: 80px;
      display: block;
      line-height: 24px;
      text-align: center;
      &.cur {
        cursor: pointer;
      }
    }

    .gap {
      gap: 8px;
    }
  }

  .video-box {
    position: relative;
    // margin: 0 8px -30px;
  }

  .more-box {
    position: absolute;
    top: 30px;
    right: 0;
    z-index: 9;
    width: 10px;
    height: 110px;
    padding-left: 18px;
    justify-content: center;
    gap: 10px;
    background: linear-gradient(90deg, transparent, #fff 30%);

    .gengduo {
      cursor: pointer;
      width: 24px;
      height: 25px;
      line-height: 24px;
      text-align: center;
      margin-left: -32px;
      color: #d7d7d7;
      font-size: 25px;
      background: #fff;
      border-radius: 50%;
      box-shadow: 0px 0px 4px rgb(135 135 135);

      &:hover {
        color: #80b2ec;
        box-shadow: 0px 0px 4px #80b2ec;
      }
    }
  }

  .el-divider {
    margin: 18px 0;
  }

  .statistics-box {
    width: 600px;

    .el-col {
      text-align: center;
    }
  }
}
</style>
