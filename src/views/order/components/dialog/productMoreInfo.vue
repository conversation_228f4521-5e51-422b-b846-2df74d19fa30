<template>
  <div>
    <el-dialog
      v-model="dialogVisible"
      width="600"
      :close-on-press-escape="false"
      center
      align-center
      @close="closeDialog"
    >
      <div v-loading="loading">
        <div class="dialog-row">
          <span class="dialog-label">1. 视频编码：</span>
          <span class="dialog-text">{{ orderData.videoCode || '' }}</span>
          <div class="copy-all">
            <!-- <div @click="copyContentWithImage">复制</div> -->
            <CopyButton
              style="border-radius: var(--el-border-radius-round); background: #409eff; color: #fff"
              :copy-content="handleCopy()"
            >
              <template #default>全部复制</template>
            </CopyButton>
          </div>
        </div>
        <div class="dialog-row" style="display: flex">
          <span class="dialog-label" style="min-width: 100px">2. 中文名称：</span>
          <span class="dialog-text">{{ orderData.productChinese }}</span>
        </div>
        <div class="dialog-row" style="display: flex">
          <span class="dialog-label" style="min-width: 100px">3. 英文名称：</span>
          <span class="dialog-text">{{ orderData.productEnglish }}</span>
        </div>
        <div class="dialog-row" style="display: flex; align-items: baseline">
          <div class="dialog-label" style="min-width: 100px">4. 产品链接：</div>
          <div class="product-link more-ell" style="max-width: 500px; --l: 5">
            {{ orderData.productLink ? orderData.productLink : '' }}
          </div>
        </div>
        <div class="dialog-row">
          <span class="dialog-label">5. 平台：</span>
          <span class="dialog-text">
            {{ biz_model_platform.find(item => item.value == orderData.platform)?.label }}
          </span>
        </div>
        <div class="dialog-row" v-if="orderData.videoStyle != 2">
          <span class="dialog-label">6. 视频风格：</span>
          <span class="dialog-text">
            {{ orderData.videoStyle == 1 ? 'Tiktok Style' : orderData.videoStyle == 0 ? 'Amazon Style' : '' }}
          </span>
        </div>
        <div class="dialog-row">
          <span class="dialog-label">
            {{ orderData.videoStyle == 2 ? '6. ' : '7. ' }}
            视频格式：
          </span>
          <span class="dialog-text">
            {{ orderData.videoFormat == 1 ? '横屏Horizontal recording' : '竖屏Vertical shooting' }}
          </span>
        </div>
        <div class="dialog-row" v-if="orderData.surplusPicCount">
          <span class="dialog-label">{{ orderData.videoStyle == 2 ? '7. ' : '8. ' }}照片数量：</span>
          <span class="dialog-text">
            {{ orderData.surplusPicCount }} photos
            <!-- {{ orderData.picCount == 1 ? '2 photos' : orderData.picCount == 2 ? '5 photos' : '' }} -->
          </span>
        </div>
        <div
          class="dialog-row"
          style="display: flex; align-items: baseline"
          v-if="orderData.referenceVideoLink"
        >
          <div class="dialog-label fs-0" style="min-width: 94px">
            {{ orderData.videoStyle == 2 ? (orderData.surplusPicCount ? 8 : 7) : orderData.surplusPicCount ? 9 : 8 }}.
            参考视频：
          </div>
          <div class="product-link more-ell" style="max-width: 500px; --l: 5; word-break: break-all">
            {{ orderData.referenceVideoLink || '' }}
          </div>
        </div>
        <div class="dialog-row">
          <div class="dialog-label">
            <span v-if="orderData.referenceVideoLink && orderData.surplusPicCount && orderData.videoStyle == 2">9.</span>
            <span v-else-if="orderData.referenceVideoLink && orderData.surplusPicCount">10.</span>
            <span v-else-if="(orderData.referenceVideoLink || orderData.surplusPicCount) && orderData.videoStyle != 2">9.</span>
            <span v-else-if="orderData.videoStyle == 2 && (orderData.referenceVideoLink || orderData.surplusPicCount)">8.</span>
            <span v-else-if="orderData.videoStyle == 2">7.</span>
            <span v-else>8.</span>
            模特要求：
          </div>
          <div
            class="product-require"
            v-for="item in orderData.orderVideoCautionsVO?.cautions"
            :key="item.value"
          >
            <div class="product-require__icon"></div>
            <div class="product-require__content template-pre">
              {{ item.content }}
            </div>
          </div>
          <div
            v-if="
              orderData.orderVideoCautionsVO?.cautionsPics &&
              orderData.orderVideoCautionsVO?.cautionsPics.length > 0
            "
          >
            <ViewerImageList
              :data="orderData?.orderVideoCautionsVO?.cautionsPics"
              is-preview-all
              :show-delete-btn="false"
            />
          </div>
          <!-- <div class="product-require" v-for="item in orderData.shootRequired" :key="item.value">
            <div class="product-require__icon"></div>
            <div class="product-require__content template-pre">
              {{ item.content }}
            </div>
          </div> -->
        </div>
        <div class="dialog-row">
          <div class="dialog-label">
            <span v-if="!orderData.surplusPicCount && !orderData.referenceVideoLink && orderData.videoStyle == 2">
              8.
            </span>
            <span v-else-if="!orderData.surplusPicCount && !orderData.referenceVideoLink">9.</span>
            <span v-else-if="orderData.surplusPicCount && !orderData.referenceVideoLink">10.</span>
            <span v-else-if="!orderData.surplusPicCount && orderData.referenceVideoLink && orderData.videoStyle != 2">10.</span>
            <span v-else-if="!orderData.surplusPicCount && orderData.referenceVideoLink && orderData.videoStyle == 2">9.</span>
            <span v-else>11.</span>
            商品规格要求：
          </div>
          <div class="product-require" v-if="orderData.orderSpecificationRequire">
            <div class="product-require__icon"></div>
            <div class="product-require__content template-pre">
              {{ orderData.orderSpecificationRequire }}
            </div>
          </div>
        </div>
        <div class="dialog-row">
          <div class="dialog-label">
            <span v-if="!orderData.surplusPicCount && !orderData.referenceVideoLink && orderData.videoStyle == 2">9.</span>
            <span v-else-if="!orderData.surplusPicCount && !orderData.referenceVideoLink">10.</span>
            <span v-else-if="orderData.surplusPicCount && !orderData.referenceVideoLink">11.</span>
            <span v-else-if="!orderData.surplusPicCount && orderData.referenceVideoLink && orderData.videoStyle != 2">11.</span>
            <span v-else-if="!orderData.surplusPicCount && orderData.referenceVideoLink && orderData.videoStyle == 2">10.</span>
            <span v-else>12.</span>
            特别强调：
          </div>
          <div v-if="orderData.particularEmphasis" class="product-require">
            <div class="product-require__icon"></div>
            <div class="product-require__content template-pre">
              {{ orderData.particularEmphasis }}
            </div>
          </div>
          <div v-if="orderData.particularEmphasisPic && orderData.particularEmphasisPic.length > 0">
            <ViewerImageList
              :data="orderData?.particularEmphasisPic"
              is-preview-all
              :show-delete-btn="false"
            />
          </div>
        </div>
        <div class="flex-center" style="color: #aaa">
          产品卖点及拍摄建议请在
          <el-button type="primary" link @click="routerNewWindow('/order/details/' + orderData.id)">
            订单详情
          </el-button>
          中查看
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import ViewerImageList from '@/components/ImagePreview/viewerImageList.vue'
import { orderDetails, videoOrderDetails } from '@/api/order/order.js'
import CopyButton from '@/components/Button/CopyButton.vue'

const { proxy } = getCurrentInstance()
const { biz_model_platform } = proxy.useDict('biz_model_platform')

const dialogVisible = ref(false)
const loading = ref(false)
const orderData = ref({
  productLink: '',
  shootRequired: [],
  limitingCondition: [],
})

const copyValue = ref()

defineExpose({
  open,
  close,
})

/** 复制代码成功 */
function copyTextSuccess() {
  proxy.$modal.msgSuccess('复制成功')
}

function handleCopy() {
  return copyValue.value
}

function handleCopyValue(list = []) {
  if (list && list.length > 0) {
    return list
      .map((item, index) => {
        return `   (${index + 1})${item.content}`
      })
      .join('\n')
  } else {
    return ''
  }
}
const router = useRouter()
function routerNewWindow(path) {
  const { href } = router.resolve({ path })
  window.open(href, '_blank')
}

function getOrderDetails(id) {
  loading.value = true

  videoOrderDetails(id)
    .then(res => {
      if (res.data.orderVideoSimpleVO) {
        orderData.value = res.data.orderVideoSimpleVO
        const shootRequired = handleCopyValue(orderData.value.shootRequired)
        // const cautions = handleCopyValue(orderData.value.cautions)
        copyValue.value = `${orderData.value.videoCode || ''}  ${orderData.value.productChinese}  ${
          orderData.value.productEnglish
        }
${orderData.value.productLink || ''}\n`
        if (orderData.value.videoStyle != 2) {
          copyValue.value += `
1.Video Style：${
            orderData.value.videoStyle == 1
              ? 'Tiktok Style'
              : orderData.value.videoStyle == 0
              ? 'Amazon Style'
              : ''
          }
2.Recording Orientation：${
            orderData.value.videoFormat == 1 ? '横屏Horizontal recording' : '竖屏Vertical shooting'
          }`
          if (
            orderData.value.surplusPicCount &&
            orderData.value.surplusPicCount > 0 &&
            orderData.value.referenceVideoLink
          ) {
            copyValue.value += `
3.Take photos：${orderData.value.surplusPicCount} photos
4.Reference video：${orderData.value.referenceVideoLink || ''}
5.Videoing requirement：\n${shootRequired}`
          } else if (
            orderData.value.surplusPicCount &&
            orderData.value.surplusPicCount > 0 &&
            !orderData.value.referenceVideoLink
          ) {
            copyValue.value += `
3.Take photos：${orderData.value.surplusPicCount} photos
4.Videoing requirement：\n${shootRequired}`
          } else if (!orderData.value.surplusPicCount && orderData.value.referenceVideoLink) {
            copyValue.value += `
3.Reference video：${orderData.value.referenceVideoLink || ''}
4.Videoing requirement：\n${shootRequired}`
          } else {
            copyValue.value += `
3.Videoing requirement：\n${shootRequired}`
          }
        } else {
          copyValue.value += `
1.Recording Orientation：${
            orderData.value.videoFormat == 1 ? '横屏Horizontal recording' : '竖屏Vertical shooting'
          }`
          if (
            orderData.value.surplusPicCount &&
            orderData.value.surplusPicCount > 0 &&
            orderData.value.referenceVideoLink
          ) {
            copyValue.value += `
2.Take photos：${orderData.value.surplusPicCount} photos
3.Reference video：${orderData.value.referenceVideoLink || ''}
4.Videoing requirement：\n${shootRequired}`
          } else if (
            orderData.value.surplusPicCount &&
            orderData.value.surplusPicCount > 0 &&
            !orderData.value.referenceVideoLink
          ) {
            copyValue.value += `
2.Take photos：${orderData.value.surplusPicCount} photos
3.Videoing requirement：\n${shootRequired}`
          } else if (!orderData.value.surplusPicCount && orderData.value.referenceVideoLink) {
            copyValue.value += `
2.Reference video：${orderData.value.referenceVideoLink || ''}
3.Videoing requirement：\n${shootRequired}`
          } else {
            copyValue.value += `
2.Videoing requirement：\n${shootRequired}`
          }
        }
        // 1.Video Style：${
        //           orderData.value.videoStyle == 1
        //             ? 'Tiktok Style'
        //             : orderData.value.videoStyle == 0
        //             ? 'Amazon Style'
        //             : ''
        //         }
        // 2.Recording Orientation：${
        //           orderData.value.videoFormat == 1 ? '横屏Horizontal recording' : '竖屏Vertical shooting'
        //         }`
        //         if (
        //           orderData.value.surplusPicCount &&
        //           orderData.value.surplusPicCount > 0 &&
        //           orderData.value.referenceVideoLink
        //         ) {
        //           copyValue.value += `
        // 3.Take photos：${orderData.value.surplusPicCount} photos
        // 4.Reference video：${orderData.value.referenceVideoLink || ''}
        // 5.Videoing requirement：\n${shootRequired}`
        //         } else if (
        //           orderData.value.surplusPicCount &&
        //           orderData.value.surplusPicCount > 0 &&
        //           !orderData.value.referenceVideoLink
        //         ) {
        //           copyValue.value += `
        // 3.Take photos：${orderData.value.surplusPicCount} photos
        // 4.Videoing requirement：\n${shootRequired}`
        //         } else if (!orderData.value.surplusPicCount && orderData.value.referenceVideoLink) {
        //           copyValue.value += `
        // 3.Reference video：${orderData.value.referenceVideoLink || ''}
        // 4.Videoing requirement：\n${shootRequired}`
        //         } else {
        //           copyValue.value += `
        // 3.Videoing requirement：\n${shootRequired}`
        //         }
      }
    })
    .finally(() => {
      // dialogVisible.value = true
      loading.value = false
    })
}
// async function copyContentWithImage() {
//   try {
//     const img = new Image()
//     img.src = imageUrl.value // 替换为你的本地图片路径
//     await img.decode() // 确保图片加载完成

//     const canvas = document.createElement('canvas')
//     canvas.width = img.width
//     canvas.height = img.height
//     const ctx = canvas.getContext('2d')
//     ctx.drawImage(img, 0, 0)

//     // const text = '这里是你想要复制的文本内容'
//     const text = new Blob(['Cute sleeping kitten'], {type: 'text/plain'});
//     const blob = await new Promise((resolve, reject) => {
//       canvas.toBlob(blob => {
//         if (blob) {
//           resolve(blob)
//         } else {
//           reject(new Error('Canvas to Blob conversion failed'))
//         }
//       }, 'image/png')
//     })

//     // 创建一个包含文本和图片的ClipboardItem
//     const item = new ClipboardItem({ 'text/plain': text })

//     // 将ClipboardItem放入剪贴板
//     await navigator.clipboard.write([item])

//     console.log('内容已复制到剪贴板')
//   } catch (error) {
//     console.error('复制失败:', error)
//   }
// }

function open(id) {
  dialogVisible.value = true
  getOrderDetails(id)
}
function close() {
  dialogVisible.value = false
}

function closeDialog() {
  orderData.value = {
    productLink: '',
    shootRequired: [],
    limitingCondition: [],
  }
  dialogVisible.value = false
}
const imageUrl = ref('')

onMounted(() => {
  // 假设图片位于项目的 public/images 目录下
  const imagePath = '/src/assets/logo/logo.png'
  const imageUrlValue = new URL(imagePath, import.meta.url).href
  imageUrl.value = imageUrlValue
})
</script>

<style lang="scss" scoped>
:deep(.el-dialog) {
  //   padding: 0 20px;
  border-radius: 15px;
  .el-dialog__body {
    max-height: 85vh;
    overflow: auto;
    margin-bottom: 10px;
  }
}
// :deep(.el-dialog .el-dialog__header) {
//   display: none;
// }

.dialog-row {
  position: relative;
  font-size: 16px;

  color: #6b6969;
  margin-bottom: 10px;
  // .product-link {
  // margin: 5px 0 0 15px;
  // }
  .copy-all {
    position: absolute;
    right: 0;
    top: 0px;
  }
}

.dialog-label {
  color: #000;
  font-weight: 500;
}
.dialog-header {
  position: relative;
  .product-link {
    margin-left: 15px;
  }
}
.product-require {
  display: flex;
  align-items: baseline;
  margin-left: 15px;
  &__icon {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: black;
  }
  &__content {
    line-height: 25px;
    max-width: 500px;
    margin-left: 15px;
    word-break: break-all;
    line-break: anywhere;
    white-space: pre-line;
    min-height: 25px;
  }
}

h3 {
  font-weight: 600;
  color: black;
}
</style>
