<template>
  <div>
    <el-dialog
      v-model="showRateDialog"
      title="调整汇率"
      align-center
      align="center"
      width="400px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-input-number
        title=""
        placeholder="请输入正确的汇率"
        style="width: 300px"
        :precision="4"
        v-model="rate"
        :max="8"
        :min="6.5"
        :controls="false"
      />
      <div style="color: #f59a23; margin-top: 10px">可通过百度查询当前实时汇率填入</div>
      <template #footer>
        <div class="flex-center">
          <el-button type="primary" @click="close">取消</el-button>
          <el-button type="primary" @click="doConfirmRate">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ElMessage } from 'element-plus'

const props = defineProps({
  requestApi: {
    type: Function,
    required: true,
  },
})

const showRateDialog = ref(false)
const rate = ref(null)
const changeRateOrderNum = ref(null)

defineExpose({ open, close })
const emits = defineEmits(['success'])

function open(orderNum) {
  showRateDialog.value = true
  changeRateOrderNum.value = orderNum
}

function close() {
  showRateDialog.value = false
  changeRateOrderNum.value = null
  rate.value = null
}

function doConfirmRate() {
  if (!rate.value || rate.value < 6.5) return ElMessage.warning('请输入正确的百度汇率6.5~8之间')
  props.requestApi({ baiduRate: rate.value.toFixed(4), orderNum: changeRateOrderNum.value }).then(res => {
    ElMessage.success('汇率修改成功')
    showRateDialog.value = false
    emits('success')
  })
}
</script>
