<template>
  <div>
    <el-dialog
      title="取消订单"
      v-model="dialogVisible"
      width="450px"
      align-center
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="close"
    >
      <div class="cancel-box">
        <div class="icon-box">
          <el-icon size="56" color="#e6a23c"><Warning /></el-icon>
        </div>
        <span>
          该订单合并支付中，请通知客户取消合并支付后
          <br />
          再取消订单
        </span>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
const dialogVisible = ref(false)

const close = () => {
  dialogVisible.value = false
}

const open = () => {
  dialogVisible.value = true
}

defineExpose({
  open,
  close,
})
</script>

<style lang="scss" scoped>
.cancel-box {
  font-size: 14px;
  text-align: center;
  font-weight: 600;
  padding-bottom: 30px;
}
.icon-box {
    margin-bottom: 10px;
}
</style>
