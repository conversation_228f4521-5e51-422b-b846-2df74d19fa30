<template>
  <div class="flex-start title">
    <div>限制条件:</div>
    <el-button
      v-btn
      v-show="!isEditConditions"
      round
      type="primary"
      size="small"
      @click="handleShowEdit('newConditions')"
    >
      修改
    </el-button>
  </div>
  <template v-for="(item, index) in limitingCondition" :key="index">
    <el-form-item label="">
      <div class="flex-start item">
        <div class="label">
          <span class="round" v-if="index === 0">原</span>
          {{ index + 1 + '、' }}
        </div>
        <el-input class="input-width" :value="item.content" :disabled="true" />
      </div>
    </el-form-item>
  </template>
  <template v-if="isEditConditions">
    <template v-for="(item, index) in newConditions" :key="index">
      <el-form-item label="" :prop="'newConditions.' + index + '.content'" :rules="conditionsRules">
        <div class="flex-start item">
          <div class="label">
            <span v-if="index === 0">内部展示改:</span>
            {{ index + 1 + '、' }}
          </div>
          <el-input
            class="input-width"
            v-model="item.content"
            placeholder="请输入变更后的限制条件内容-内部展示"
            maxlength="300"
            clearable
          />
          <el-icon
            v-if="index == newConditions.length - 1"
            class="add-icon"
            :size="20"
            @click="handleAddInput('newConditions')"
          >
            <CirclePlusFilled />
          </el-icon>
          <el-icon
            v-if="index >= limitingCondition.length && newConditions.length > 1"
            :size="20"
            @click="handleDelInput('newConditions', index)"
          >
            <Delete />
          </el-icon>
        </div>
      </el-form-item>
    </template>
    <template v-for="(item, index) in newModelConditions" :key="index">
      <el-form-item label="" :prop="'newModelConditions.' + index + '.content'" :rules="conditionsRules">
        <div class="flex-start item">
          <div class="label">
            <span v-if="index === 0">模特展示改:</span>
            {{ index + 1 + '、' }}
          </div>
          <el-input
            class="input-width"
            v-model="item.content"
            placeholder="请输入变更后的限制条件内容-模特展示"
            maxlength="300"
            clearable
          />
          <el-icon
            v-if="index == newModelConditions.length - 1"
            class="add-icon"
            :size="20"
            @click="handleAddInput('newModelConditions')"
          >
            <CirclePlusFilled />
          </el-icon>
          <el-icon
            v-if="index >= limitingCondition.length && newModelConditions.length > 1"
            :size="20"
            @click="handleDelInput('newModelConditions', index)"
          >
            <Delete />
          </el-icon>
          <template v-if="index === 0">
            <el-button
              v-btn
              v-show="!showConditionsTranslate"
              round
              type="primary"
              size="small"
              :loading="conditionsTranslateLoading"
              @click="handleShowEdit('conditionsTranslate')"
            >
              翻译
            </el-button>
            <el-button
              v-btn
              v-show="showConditionsTranslate"
              round
              type="primary"
              size="small"
              @click="handleShowEdit('conditionsTranslate')"
            >
              收起翻译
            </el-button>
          </template>
        </div>
      </el-form-item>
    </template>
    <template v-if="showConditionsTranslate">
      <template v-for="(item, index) in translateList" :key="index">
        <el-form-item label="">
          <div class="flex-start item">
            <div class="label">
              <span class="round" v-if="index === 0">译</span>
              {{ index + 1 + '、' }}
            </div>
            <el-input class="input-width" :value="item" :disabled="true" />
          </div>
        </el-form-item>
      </template>
    </template>
  </template>
</template>

<script setup>
import { getTranslate } from '@/api/order/order'

const props = defineProps({
  limitingCondition: {
    type: Array,
    required: true,
  },
  translateList: {
    type: Array,
    required: true,
  },
  newConditions: {
    type: Array,
    required: true,
  },
  newModelConditions: {
    type: Array,
    required: true,
  },
})

const emits = defineEmits([
  'update:limitingCondition',
  'update:translateList',
  'update:newConditions',
  'update:newModelConditions',
])

const isEditConditions = ref(false)

const showConditionsTranslate = ref(false)
const conditionsTranslateLoading = ref(false)

const conditionsRules = [{ required: false, validator: checkConditions, trigger: 'change' }]

function checkConditions(rule, value, callback) {
  // if(value && !chineseCharacter_d_reg.test(value)) {
  //   return callback(new Error('*请用中文，清晰简短描述限制条件'));
  // }
  return callback()
}

function handleShowEdit(key) {
  switch (key) {
    case 'newConditions':
      if(props.limitingCondition.length) {
        emits('update:newConditions', JSON.parse(JSON.stringify(props.limitingCondition)))
        emits('update:newModelConditions', JSON.parse(JSON.stringify(props.limitingCondition)))
      } else {
        emits('update:newConditions', [{ content: '' }])
        emits('update:newModelConditions', [{ content: '' }])
      }
      isEditConditions.value = true
      break
    case 'conditionsTranslate':
      handleConditionsTranslate()
      break
    default:
      break
  }
}

// 添加 限制条件 输入框
function handleAddInput(key) {
  props[key].push({
    content: '',
  })
}
// 删除 限制条件 输入框
function handleDelInput(key, i) {
  props[key].splice(i, 1)
}

// 原限制条件翻译
function handleConditionsTranslate() {
  // 收起翻译
  if (showConditionsTranslate.value) {
    showConditionsTranslate.value = !showConditionsTranslate.value
    return
  }
  conditionsTranslateLoading.value = true
  // let data = props.newConditions.filter(item => item.content).map(item => item.content)
  let data = props.newModelConditions.filter(item => item.content).map(item => item.content)
  getTranslate({
    language: 0,
    wordList: data,
  })
    .then(res => {
      emits('update:translateList', res.data)
      showConditionsTranslate.value = true
      conditionsTranslateLoading.value = false
    })
    .catch(() => {
      conditionsTranslateLoading.value = false
    })
}
</script>

<style scoped lang="scss">
:deep(.el-form-item__error) {
  margin-left: 142px;
}
.title {
  gap: 10px;
  width: 100%;
  font-size: 16px;
  margin: 10px 0;

  div {
    width: 100px;
    text-align: right;
  }

  .add-icon {
    color: var(--el-color-primary);
    cursor: pointer;
  }
}
.item {
  gap: 10px;
  width: 100%;
  font-size: 16px;

  .el-button+.el-button {
    margin-left: 0;
  }

  .label {
    width: 130px;
    flex-shrink: 0;
    text-align: right;

    span {
      color: var(--el-color-primary);
      font-size: 13px;
    }
    .round {
      border: 1px solid;
      border-radius: 50%;
      padding: 3px;
    }
  }
  .red {
    color: var(--el-color-danger) !important;
  }
  .el-input {
    // flex-grow: 1;
    width: 70%;
  }
  .el-icon {
    color: var(--el-color-primary);
    cursor: pointer;
  }
}
.input-width {
  width: 410px !important;
}
</style>
