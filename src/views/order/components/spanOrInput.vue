<template>
  <div class="editable-span" :class="{ 'is-disabled': disabled }">
    <span style="width: 100%;" @click="open" v-if="!showInput">{{ value || '-' }}</span>
    <el-input
      v-else
      v-model="inputValue"
      @blur="handleBlur"
      ref="inputRef"
      :maxlength="maxlength"
      style="width: 100%"
      :disabled="disabled"
    />
    <el-button
      link
      v-btn
      type="primary"
      v-if="isLink && !showInput && inputValue != '' && inputValue"
      style="margin-left: 10px"
      @click="openUrl"
    >
      跳转
    </el-button>
  </div>
</template>

<script setup>
import { ElMessage } from 'element-plus'
import { watchEffect } from 'vue'

const props = defineProps({
  value: {
    type: String,
    default: '',
  },
  maxlength: {
    type: [String, Number],
    default: null,
  },
  filedName: {
    type: String,
    default: '',
  },
  isLink: {
    type: Boolean,
    default: false,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:value'])

const showInput = ref(false)
const inputValue = ref('')
const inputRef = ref(null)

watchEffect(() => {
  inputValue.value = props.value
})

watch(showInput, newVal => {
  if (newVal) {
    nextTick(() => {
      inputRef.value.focus()
    })
  }
})

const open = () => {
  if (props.disabled) return
  showInput.value = true
}

const handleBlur = () => {
  if (
    (!inputValue.value || inputValue.value.trim() === '') &&
    props.filedName != 'referenceVideoLink' &&
    props.filedName != 'productLink'
  ) {
    inputRef.value.focus()
    inputValue.value = props.value
    return ElMessage.warning('请输入内容')
  }
  emit('update:value', inputValue.value, props.filedName)
  showInput.value = false
}

const openUrl = () => {
  window.open(inputValue.value, '_blank')
}

defineExpose({ open })
</script>

<style lang="scss" scoped>
@import '@/assets/styles/customForm.scss';
@import '@/assets/styles/form-disabled.scss';
.editable-span {
  display: flex;
  align-items: baseline;
  cursor: pointer;

  &.is-disabled {
    cursor: not-allowed;
  }
}
</style>
