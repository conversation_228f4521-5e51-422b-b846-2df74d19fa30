<template>
  <div class="editable-span">
    <div
      class="editable-span-text template-pre"
      style="flex: 1; --l: 1;cursor: pointer;"
      :class="{ 'more-ell': !showAllText }"
      @click="showInput = true"
      v-if="!showInput"
    >
      {{ handleR(value) || '-' }}
    </div>
    <el-input
      v-else
      v-model="inputValue"
      :placeholder="placeholder"
      type="textarea"
      :autosize="{ minRows: 4 }"
      @blur="handleBlur"
      ref="inputRef"
      :maxlength="maxlength"
      style="width: 100%"
    />
    <div v-if="!showInput">
      <el-button
        link
        v-btn
        type="primary"
        v-if="inputValue !== ''"
        style="margin-left: 10px"
        @click="showAllText = !showAllText"
      >
        {{ showAllText ? '收起' : '展开' }}
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ElMessage } from 'element-plus'
import { watchEffect } from 'vue'
import { chinese_reg, englishCharacter_d_reg } from '@/utils/RegExp'

const props = defineProps({
  value: {
    type: String,
    default: '',
  },
  maxlength: {
    type: [String, Number],
    default: null,
  },
  filedName: {
    type: String,
    default: '',
  },
  placeholder: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['update:value'])

const showAllText = ref(false)
const showInput = ref(false)
const inputValue = ref('')
const inputRef = ref(null)

watchEffect(() => {
  inputValue.value = props.value
})

watch(showInput, newVal => {
  if (newVal) {
    nextTick(() => {
      inputRef.value.focus()
    })
  }
})

function handleR(data) {
  return data.replace(/\r/g, '\r\n') || '-'  
}
const open = () => {
  showInput.value = true
}

const handleBlur = () => {
  if (inputValue.value.trim() === '') inputValue.value = ''
  // if (!inputValue.value || inputValue.value.trim() === '') {
  //   inputRef.value.focus()
  //   inputValue.value = props.value
  //   return ElMessage.warning('请输入内容')
  // }
  emit('update:value', inputValue.value, props.filedName)
  showInput.value = false
}

const openUrl = () => {
  window.open(inputValue.value, '_blank')
}

defineExpose({ open })
</script>

<style lang="scss" scoped>
@import '@/assets/styles/customForm.scss';
@import '@/assets/styles/form-disabled.scss';
.editable-span {
  display: flex;
  width: 100%;
  &-text {
    line-break: anywhere;
    white-space: pre-line;
  }
}
</style>
