<template>
  <div class="editable-span" ref="editableSpanRef" :id="props.filedName" :class="{ 'is-disabled': disabled }">
    <div style="width: 100%" @click="open" v-if="!showInput">
      <template v-if="!props.multiple">
        <span v-if="props.filedName == 'modelType' && props.modelValue == '3'">影/素都可以</span>
        <span v-else>{{ list.find(item => item.value == props.modelValue)?.label || '' }}</span>
      </template>
      <template v-else>
        {{
          list
            .filter(item => props.modelValue.includes(item.value))
            .map(item => item.label)
            .join(',')
        }}
      </template>
    </div>
    <el-select
      v-else
      style="width: 90%"
      :model-value="props.modelValue"
      :multiple="props.multiple"
      @change="changeFormValue"
      :append-to="'#' + props.filedName"
      :disabled="disabled"
    >
      <el-option
        v-for="item in list"
        :key="item.value"
        :label="item.label"
        :value="item.value"
        :disabled="handleDisabled(item.value)"
      />
      <el-option
        v-if="props.filedName == 'modelType'"
        key="3"
        label="影/素都可以"
        value="3"
        :disabled="handleDisabled('3')"
      />
    </el-select>
  </div>
</template>

<script setup>
const props = defineProps({
  modelValue: {
    type: [String, Number, Array],
    default: '',
  },
  filedName: {
    type: String,
    default: '',
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  isDisabled: {
    type: Boolean,
    default: false,
  },
  list: {
    type: Array,
    default: () => [],
  },
  multiple: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:modelValue', 'changeValue'])

const showInput = ref(false)
const inputValue = ref('')
const editableSpanRef = ref()
const customContainer = ref(null)

const handleDisabled = value => {
  if (props.filedName == 'platform') {
    return value == 3 && props.isDisabled
  } else if (props.filedName == 'videoFormat' || props.filedName == 'modelType') {
    return props.isDisabled
  }
}

function changeFormValue(value, type) {
  let tempValue = value
  if (props.filedName == 'modelTypeList') {
    if (tempValue.length == 0) {
      tempValue = ['1']
    }
  }
  emit('update:modelValue', tempValue)
  emit('changeValue', tempValue, props.filedName)
}

const handleClickOutside = async event => {
  const selectDom = document.querySelector('.el-select-dropdown.el-popper')
  const isClickDropdown = selectDom?.contains(event.target)
  if (editableSpanRef.value && !editableSpanRef.value.contains(event.target) && !isClickDropdown) {
    showInput.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside, true)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside, true)
})

const open = () => {
  if (props.disabled) return
  showInput.value = true
}

const close = () => {
  showInput.value = false
}

defineExpose({ close })
</script>

<style scoped lang="scss">
@import '@/assets/styles/customForm.scss';
@import '@/assets/styles/form-disabled.scss';

:deep(.el-radio-button) {
  .el-radio-button__original-radio:disabled:checked {
    + .el-radio-button__inner {
      background-color: var(--el-radio-button-checked-bg-color, var(--el-color-primary));
      border-color: var(--el-radio-button-checked-border-color, var(--el-color-primary));
      color: #fff;
    }
  }
}
:deep(.el-select-dropdown__item) {
  color: #666;
  &.is-selected {
    color: var(--el-color-primary) !important;
  }
  &.is-disabled {
    color: #c0c4cc;
  }
}
:deep(.el-select) {
  .el-tag {
    .el-icon {
      display: none;
    }
  }
}

.editable-span {
  width: 100%;
  cursor: pointer;

  &.is-disabled {
    cursor: not-allowed;
  }
}
</style>
