<template>
  <template v-if="isAll">
    <el-tag v-if="row[rejectField]" type="warning" size="small" round>商家驳回</el-tag>
    <el-tag v-if="row[rollbackField]" type="danger" size="small" round>回退订单</el-tag>
  </template>
  <el-tag v-else-if="row[rejectField]" type="warning" size="small" round>商家驳回</el-tag>
  <el-tag v-else-if="row[rollbackField]" type="danger" size="small" round>回退订单</el-tag>
</template>

<script setup>
defineProps({
  row: {
    type: Object,
    required: true,
  },
  // 是否可同时存在
  isAll: {
    type: Boolean,
    default: false,
  },
  // 商家驳回字段名
  rejectField: {
    type: String,
    default: 'hasRejectedModel',
  },
  // 回退订单字段名
  rollbackField: {
    type: String,
    default: 'rollbackId',
  },
})
</script>
