<template>
  <template v-if="list.length > 3">
    <el-image
      v-for="(item, i) in 3"
      :key="i"
      style="width: 30px; height: 30px; border-radius: 50%"
      :style="{
        marginLeft: i ? '-10px' : '0px',
      }"
      :src="$picUrl + list[i].model.modelPic + '!1x1compress'"
      fit="fill"
    >
      <template #error>
        <div class="image-error">
          <el-icon :size="30"><PictureRounded /></el-icon>
        </div>
      </template>
    </el-image>
  </template>
  <template v-else>
    <el-image
      v-for="(item, i) in list"
      :key="i"
      style="width: 30px; height: 30px; border-radius: 50%"
      :style="{
        marginLeft: i ? '-10px' : '0px',
      }"
      :src="$picUrl + item.model.modelPic + '!1x1compress'"
      fit="fill"
    >
      <template #error>
        <div class="image-error">
          <el-icon :size="30"><PictureRounded /></el-icon>
        </div>
      </template>
    </el-image>
  </template>
</template>

<script setup>
defineProps({
  list: {
    type: Array,
    default: () => [],
  },
})
</script>

<style lang="scss" scoped>
.image-error {
  color: #ccc;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
