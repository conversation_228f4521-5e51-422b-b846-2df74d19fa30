<template>
    <div class="flex-start title">
      <div>注意事项:</div>
      <el-button
        v-btn
        v-show="!isEditDemands"
        round
        type="primary"
        size="small"
        @click="handleShowEdit('newCautions')"
      >
        修改
      </el-button>
    </div>
    <template v-for="(item, index) in cautions" :key="index">
      <el-form-item label="">
        <div class="flex-start item">
          <div class="label">
            <span class="round" v-if="index === 0">原</span>
            {{ index + 1 + '、' }}
          </div>
          <el-input class="input-width" :value="item.content" :disabled="true" />
          <template v-if="index === 0">
            <el-button
              v-btn
              v-show="!showDemandsTranslate"
              round
              type="primary"
              size="small"
              :loading="demandsTranslateLoading"
              @click="handleShowEdit('demandsTranslate')"
            >
              翻译
            </el-button>
            <el-button
              v-btn
              v-show="showDemandsTranslate"
              round
              type="primary"
              size="small"
              @click="handleShowEdit('demandsTranslate')"
            >
              收起翻译
            </el-button>
          </template>
        </div>
      </el-form-item>
    </template>
    <template v-if="showDemandsTranslate">
      <template v-for="(item, index) in translateList" :key="index">
        <el-form-item label="">
          <div class="flex-start item">
            <div class="label">
              <span class="round" v-if="index === 0">译</span>
              {{ index + 1 + '、' }}
            </div>
            <el-input class="input-width" :value="item" :disabled="true" />
          </div>
        </el-form-item>
      </template>
    </template>
    <template v-if="isEditDemands">
      <template v-for="(item, index) in newCautions" :key="index">
        <el-form-item label="" :prop="'newCautions.' + index + '.content'" :rules="demandsRules">
          <div class="flex-start item">
            <div class="label">
              <span class="round" v-if="index === 0">改</span>
              {{ index + 1 + '、' }}
            </div>
            <el-input
              class="input-width"
              v-model="item.content"
              placeholder="请输入变更后的注意事项"
              maxlength="300"
              clearable
            />
            <el-icon
              v-if="index == newCautions.length - 1"
              class="add-icon"
              :size="20"
              @click="handleAddInput('newCautions')"
            >
              <CirclePlusFilled />
            </el-icon>
            <el-icon v-if="index >= cautions.length && newCautions.length > 1" :size="20" @click="handleDelInput('newCautions', index)">
              <Delete />
            </el-icon>
          </div>
        </el-form-item>
      </template>
    </template>
  </template>
  
  <script setup>
  import { getTranslate } from '@/api/order/order'
  import { english_d_reg } from '@/utils/RegExp'
  
  const props = defineProps({
    cautions: {
      type: Array,
      required: true,
    },
    translateList: {
      type: Array,
      required: true,
    },
    newCautions: {
      type: Array,
      required: true,
    },
  })
  
  const emits = defineEmits([
    'update:cautions',
    'update:translateList',
    'update:newCautions',
  ])
  
  const isEditDemands = ref(false)
  
  const showDemandsTranslate = ref(false)
  const demandsTranslateLoading = ref(false)
  
  const demandsRules = [{ required: false, validator: checkDemands, trigger: 'blur' }]
  
  function checkDemands(rule, value, callback) {
    if(!value) {
      return callback(new Error('*注意事项不能为空'));
    }
    return callback();
  }
  
  function handleShowEdit(key) {
    switch (key) {
      case 'newCautions':
        if(props.cautions.length) {
          emits('update:newCautions', JSON.parse(JSON.stringify(props.cautions)))
        } else {
          emits('update:newCautions', [{ content: '' }])
        }
        isEditDemands.value = true
        break
      case 'demandsTranslate':
        handleDemandsTranslate()
        break
      default:
        break
    }
  }
  
  // 添加 拍摄要求 输入框
  function handleAddInput(key) {
    props[key].push({
      content: ''
    })
  }
  // 删除 拍摄要求 输入框
  function handleDelInput(key, i) {
    props[key].splice(i, 1)
  }
  // 原拍摄要求翻译
  function handleDemandsTranslate() {
    // 收起翻译
    if(showDemandsTranslate.value) {
      showDemandsTranslate.value = !showDemandsTranslate.value
      return
    }
    demandsTranslateLoading.value = true
    let data = props.cautions.map(item => item.content)
    getTranslate({
      language: 1,
      wordList: data
    }).then(res => {
      emits('update:translateList', res.data)
      showDemandsTranslate.value = true
      demandsTranslateLoading.value = false
    }).catch(() => {
      demandsTranslateLoading.value = false
    })
  }
  </script>
  
  <style scoped lang="scss">
  :deep(.el-form-item__error) {
    margin-left: 142px;
  }
  .title {
    gap: 10px;
    width: 100%;
    font-size: 16px;
    margin: 10px 0;
  
    div {
      width: 100px;
      text-align: right;
    }
  
    .add-icon {
      color: var(--el-color-primary);
      cursor: pointer;
    }
  }
  .item {
    gap: 10px;
    width: 100%;
    font-size: 16px;
  
    .el-button+.el-button {
      margin-left: 0;
    }
  
    .label {
      width: 130px;
      flex-shrink: 0;
      text-align: right;
  
      span {
        color: var(--el-color-primary);
        font-size: 13px;
      }
      .round {
        border: 1px solid;
        border-radius: 50%;
        padding: 3px;
      }
    }
    .red {
      color: var(--el-color-danger) !important;
    }
    .el-input {
      // flex-grow: 1;
      width: 70%;
    }
    .el-icon {
      color: var(--el-color-primary);
      cursor: pointer;
    }
  }
  .input-width {
    width: 410px !important;
  }
  </style>
  