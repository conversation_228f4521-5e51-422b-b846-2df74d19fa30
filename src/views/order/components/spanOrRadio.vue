<template>
  <div class="editable-span" ref="editableSpanRef">
    <span style="width: 100%;" @click="showInput = true" v-if="!showInput">
      {{ list.find(item => item.value == props.modelValue)?.label || '' }}
    </span>
    <el-radio-group :model-value="props.modelValue" v-else @change="changeFormValue($event, filedName)">
      <el-radio-button
        v-for="item in list"
        :key="item.value"
        :value="item.value"
        :disabled="handleDisabled(item.value)"
      >
        {{ item.label }}
      </el-radio-button>
    </el-radio-group>
  </div>
</template>

<script setup>
const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: '',
  },
  filedName: {
    type: String,
    default: '',
  },
  isDisabled: {
    type: Boolean,
    default: false,
  },
  list: {
    type: Array,
    default: () => [],
  },
})

const emit = defineEmits(['update:modelValue', 'changeValue'])

const showInput = ref(false)
const inputValue = ref('')
const editableSpanRef = ref()

const handleDisabled = value => {
  if (props.filedName == 'platform') {
    return value == 3 && props.isDisabled
  } else if (props.filedName == 'videoFormat' || props.filedName == 'modelType') {
    return props.isDisabled
  }
}

function changeFormValue(value, type) {
  emit('update:modelValue', value)
  emit('changeValue', value, type)
}

const handleClickOutside = event => {
  if (editableSpanRef.value && !editableSpanRef.value.contains(event.target)) {
    showInput.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside, true)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside, true)
})

const close = () => {
  showInput.value = false
}

defineExpose({ close })
</script>

<style scoped lang="scss">
@import '@/assets/styles/customForm.scss';
@import '@/assets/styles/form-disabled.scss';

:deep(.el-radio-button) {
  .el-radio-button__original-radio:disabled:checked {
    + .el-radio-button__inner {
      background-color: var(--el-radio-button-checked-bg-color, var(--el-color-primary));
      border-color: var(--el-radio-button-checked-border-color, var(--el-color-primary));
      color: #fff;
    }
  }
}

.editable-span {
  display: flex;
  align-items: center;
}
</style>
