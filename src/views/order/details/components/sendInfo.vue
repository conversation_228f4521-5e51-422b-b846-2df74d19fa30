<template>
  <div>
    <template
      v-if="orderLogisticSimpleVOS && orderLogisticSimpleVOS.length > 0"
      v-for="(item, index) in orderLogisticSimpleVOS"
      :key="index"
    >
      <div class="send-info">
        <div class="info-head flex-between" @click="handleShow(item)">
          <div style="display: flex; align-items: center">
            <span v-if="!item.shippingInfoSimpleVO?.rollbackId">
              {{ index == orderLogisticSimpleVOS.length - 1 ? '首次发货' : '补发记录' }}
            </span>
            <span v-else>
              {{ item.reissue == '0' ? '补发记录' : '首次发货' }}
            </span>
            <span v-if="item.shippingInfoSimpleVO?.rollbackId">(订单回退)</span>
            <div v-if="item.number" style="color: #999; font-size: 13px; margin-left: 30px">
              物流单号：
              <span style="color: #333">{{ item.number }}</span>
            </div>
            <div
              v-if="
                orderVideoSimpleVO?.status != orderStatusMap['已完成'] &&
                orderVideoSimpleVO?.status != orderStatusMap['交易关闭'] &&
                item.number &&
                orderRollbackId == item.shippingInfoSimpleVO?.rollbackId
              "
              class="info-head-btn"
              style="margin-left: 10px; font-size: 13px"
              @click.stop="handleEditDialog(item)"
            >
              修改
            </div>
          </div>
          <div class="info-head-btn">{{ item.isUnfold ? '收起' : '展开' }}</div>
        </div>
        <div class="info-box" :style="{ display: item.isUnfold ? 'block' : 'none' }">
          <!-- {{ addressLabels }} -->
          <!-- <div v-if="!data && isObject">无需发货</div>
          <div v-else-if="status < orderStatusMap['需发货']">暂无信息</div> -->
          <!-- <template> -->
          <div style="margin-bottom: 10px" v-if="item.shippingInfoSimpleVO?.shootModel">
            收货模特：{{ item.shippingInfoSimpleVO?.shootModel?.name || '-' }}
            <span style="margin-left: 8px">
              (ID：{{ item.shippingInfoSimpleVO?.shootModel?.account || '-' }})
            </span>
          </div>
          <el-descriptions title="" :column="1">
            <el-descriptions-item label-class-name="label" label="发货信息">
              <el-button
                type="primary"
                round
                size="small"
                style="color: white"
                color="#5696c0"
                @click="copyOrderLogistic(item)"
              >
                复制发货信息
              </el-button>
              <el-row style="margin-top: 5px">
                <template v-for="(item, i) in item.delivery">
                  <el-col :span="8" v-if="index < 3">
                    <div class="label long-label">{{ item.label }}</div>
                    <div class="text">{{ item.value }}</div>
                  </el-col>
                </template>
                <!-- <el-col :span="8">
                  <div class="label">Name:</div>
                  <div class="content">{{ shippingInfoSimpleVO?.recipient }}</div>
                </el-col>
                <el-col :span="8">
                  <div class="label">Address:</div>
                  <div class="content">{{ shippingInfoSimpleVO?.detailAddress }}</div>
                </el-col>
                <el-col :span="8">
                  <div class="label">City:</div>
                  <div class="content">{{ shippingInfoSimpleVO?.city }}</div>
                </el-col> -->
              </el-row>
              <el-row>
                <template v-for="(item, i) in item.delivery">
                  <el-col :span="8" v-if="index >= 3">
                    <div class="label long-label">{{ item.label }}</div>
                    <div class="text">{{ item.value }}</div>
                  </el-col>
                </template>
                <!-- <el-col :span="8">
                  <div class="label">State:</div>
                  <div class="content">{{ shippingInfoSimpleVO?.state }}</div>
                </el-col> -->
                <!-- <el-col :span="8">
                  <div class="label">Postal code:</div>
                  <div class="content">{{ shippingInfoSimpleVO?.zipcode }}</div>
                </el-col>
                <el-col :span="8">
                  <div class="label">Country:</div>
                  <div class="content">
                  </div>
                </el-col> -->
              </el-row>
              <el-row>
                <el-col :span="8" v-if="item.shippingInfoSimpleVO?.phone">
                  <div class="label long-label" style="width: 180px">TEL</div>
                  <div class="template-pre">
                    {{ item.shippingInfoSimpleVO?.phone }}
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="label long-label" style="width: 180px">发货备注</div>
                  <div class="content">
                    {{ item.shippingInfoSimpleVO?.shippingRemark }}
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="label long-label" style="width: 180px">发货图片</div>
                  <div
                    v-if="
                      item.shippingInfoSimpleVO?.shippingPics &&
                      item.shippingInfoSimpleVO?.shippingPics.length > 0
                    "
                  >
                    <div
                      style="color: var(--el-color-primary); cursor: pointer"
                      @click="showViewer(item.shippingInfoSimpleVO?.shippingPics)"
                    >
                      查看
                    </div>
                  </div>
                  <div v-else>-</div>
                </el-col>
              </el-row>
            </el-descriptions-item>
            <!-- <el-descriptions-item label-class-name="label" label="发货备注：">
                {{ props.remark }}
              </el-descriptions-item> -->
          </el-descriptions>
          <div class="logistics-item">
            <div class="label">物流记录</div>
            <div
              class="content-box"
              style="margin-top: 20px; border-bottom: 1px solid #e8e8e8"
              v-if="item.logisticFlagTime"
            >
              <el-row>
                <el-col :span="8">
                  <div class="label" style="width: 100px">标记发货时间:</div>
                  <div class="content">{{ item.logisticFlagTime || '-' }}</div>
                </el-col>
                <el-col :span="8">
                  <div class="label" style="width: 100px">备注:</div>
                  <div class="content">{{ item.logisticFlagRemark || '-' }}</div>
                </el-col>
              </el-row>
            </div>
            <div class="content-box" style="margin-top: 15px">
              <template v-if="!orderLogisticSimpleVOS?.length">暂无信息</template>
              <template v-else>
                <el-row>
                  <el-col v-if="item.number" :span="8">
                    <div class="label" style="width: 100px">物流单号:</div>
                    <div class="content">{{ item.number || '-' }}</div>
                  </el-col>
                  <el-col v-if="item.shippingTime" :span="8">
                    <div class="label" style="width: 100px">发货时间:</div>
                    <div class="content">{{ item.shippingTime || '-' }}</div>
                  </el-col>
                  <el-col :span="8" v-if="index != orderLogisticSimpleVOS.length - 1 && item.reissueCause">
                    <div class="label" style="width: 100px">补发原因:</div>
                    <div class="content" style="width: calc(100% - 115px)">
                      {{ item.reissueCause ? item.reissueCause : '-' }}
                    </div>
                  </el-col>
                </el-row>
                <!-- <template v-for="(item, i) in data.logisticInfo" :key="i"> -->
                <!-- <div class="flex-between">
                      <div class="flex-start unfoldBtn" @click="handleUnfold(item)">
                        <span>{{ item.reissue == '1' ? '' : '补发' }}物流单号：{{ item.number }}</span>
                      </div>

                    </div> -->
                <div v-if="item.logisticInfo?.length" class="logistics-box">
                  <Logistics :dataArray="item.logisticInfo" />
                </div>
                <div v-else style="color: #999; margin: 0px 0 0 70px">暂无物流信息</div>
                <!-- </template> -->
              </template>
            </div>
          </div>
          <!-- </template> -->
        </div>
      </div>
    </template>
    <div class="send-info" v-else>
      <template v-if="orderLogisticSimpleVOS && item.shippingInfoSimpleVO">
        <div class="info-head flex-between" @click="handleShowEmpty">
          <div style="display: flex; align-items: center">首次发货</div>
          <div class="info-head-btn">{{ isUnfold ? '收起' : '展开' }}</div>
        </div>
        <div class="info-box" :style="{ display: isUnfold ? 'block' : 'none' }">
          <el-descriptions title="" :column="1">
            <el-descriptions-item label-class-name="label" label="发货信息">
              <el-row style="margin-top: 5px">
                <template v-for="(item, i) in item.delivery">
                  <el-col :span="8" v-if="i < 3">
                    <div class="label long-label">{{ item.label }}</div>
                    <div class="text">{{ item.value }}</div>
                  </el-col>
                </template>
              </el-row>
              <el-row>
                <template v-for="(item, i) in item.delivery">
                  <el-col :span="8" v-if="i >= 3">
                    <div class="label long-label">{{ item.label }}</div>
                    <div class="text">{{ item.value }}</div>
                  </el-col>
                </template>
              </el-row>
              <el-row>
                <el-col :span="8" v-if="item.shippingInfoSimpleVO?.phone">
                  <div class="label long-label" style="width: 180px">TEL</div>
                  <div class="template-pre">
                    {{ item.shippingInfoSimpleVO?.phone }}
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="label long-label" style="width: 180px">发货备注</div>
                  <div class="template-pre">
                    {{ item.shippingInfoSimpleVO?.shippingRemark }}
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="label long-label" style="width: 180px">发货图片</div>
                  <div
                    v-if="
                      item.shippingInfoSimpleVO?.shippingPics &&
                      item.shippingInfoSimpleVO?.shippingPics.length > 0
                    "
                  >
                    <div
                      style="color: var(--el-color-primary); cursor: pointer"
                      @click="showViewer(item.shippingInfoSimpleVO?.shippingPics)"
                    >
                      查看
                    </div>
                  </div>
                  <div v-else>-</div>
                </el-col>
              </el-row>
            </el-descriptions-item>
          </el-descriptions>
          <div class="logistics-item">
            <div class="label">物流记录</div>
            <div class="content">
              <div
                class="content-box"
                style="margin-top: 20px; border-bottom: 1px solid #e8e8e8"
                v-if="item.shippingInfoSimpleVO?.logisticFlagTime"
              >
                <el-row>
                  <el-col :span="8">
                    <div class="label" style="width: 100px">标记发货时间:</div>
                    <div class="content">{{ item.shippingInfoSimpleVO?.logisticFlagTime || '-' }}</div>
                  </el-col>
                  <el-col :span="8">
                    <div class="label" style="width: 100px">备注:</div>
                    <div class="content">{{ item.shippingInfoSimpleVO?.logisticFlagRemark || '-' }}</div>
                  </el-col>
                </el-row>
              </div>
              <template v-if="!orderLogisticSimpleVOS?.length">
                <div style="color: #999; margin: 20px 0 0 70px">暂无物流信息</div>
              </template>
              <template v-else>
                <el-row>
                  <el-col v-if="item.number" :span="8">
                    <div class="label" style="width: 100px">物流单号:</div>
                    <div class="content">{{ item.number || '-' }}</div>
                  </el-col>
                  <el-col v-if="item.shippingTime" :span="8">
                    <div class="label" style="width: 100px">发货时间:</div>
                    <div class="content">{{ item.shippingTime || '-' }}</div>
                  </el-col>
                </el-row>
                <!-- <template v-for="(item, i) in data.logisticInfo" :key="i"> -->
                <!-- <div class="flex-between">
                      <div class="flex-start unfoldBtn" @click="handleUnfold(item)">
                        <span>{{ item.reissue == '1' ? '' : '补发' }}物流单号：{{ item.number }}</span>
                      </div>

                    </div> -->
                <div v-if="item.logisticInfo?.length" class="logistics-box">
                  <Logistics :dataArray="item.logisticInfo" />
                </div>
                <!-- <div v-else class="logistics-box" style="max-height: 70px">无物流信息</div> -->
                <!-- </template> -->
              </template>
            </div>
          </div>
          <!-- </template> -->
        </div>
      </template>
      <template v-else><div style="text-align: center; margin: 100px 0">暂无发货记录</div></template>
    </div>

    <EditOrderLogistics ref="EditOrderLogisticsRef" @success="$emit('success')" />
  </div>
</template>

<script setup>
import EditOrderLogistics from '@/views/order/components/dialog/editOrderLogistics.vue'
import { orderStatusMap, addressInfoMap } from '@/views/order/list/data.js'
import Logistics from './logistics.vue'
import { computed, getCurrentInstance, ref } from 'vue'
const { proxy } = getCurrentInstance()
import { useViewer } from '@/hooks/useViewer'
const { showViewer } = useViewer()
const { biz_nation_e } = proxy.useDict('biz_nation_e')

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
  status: {
    type: Number,
    default: 0,
  },
  isObject: {
    type: Number,
  },
  logisticFlag: {
    type: [String, Number, Boolean],
    default: false,
  },
  remark: {
    type: String,
    default: '',
  },
})

const EditOrderLogisticsRef = ref()

const orderVideoSimpleVO = computed(() => props.data.orderVideoSimpleVO)

const orderRollbackId = ref(null)

const orderLogisticSimpleVOS = computed(() => {
  const originalData = props.data.orderLogisticSimpleVOS
  orderRollbackId.value = null
  if (originalData && originalData.length > 0) {
    originalData.forEach((item, index) => {
      if (index == 0 && item.shippingInfoSimpleVO?.rollbackId) {
        orderRollbackId.value = item.shippingInfoSimpleVO?.rollbackId
      }
      if (item.shippingInfoSimpleVO) {
        originalData[index].delivery = handleAddress(item.shippingInfoSimpleVO)
      }
    })
  }
  // 追加新属性
  if (originalData) {
    originalData.forEach(item => {
      item.isUnfold = true
    })
  }
  return originalData
})

function handleAddress(item) {
  let n = item?.nation ? item?.nation + '' : ''
  if (n) {
    return addressInfoMap[n].map(data => {
      let value = ''
      if (data.key === 'nation') {
        value = bizNationMap(n)
      } else if (item) {
        value = item[data.key] || ''
      }
      return { label: data.label, key: data.key, value, ...item }
    })
  }
  return []
}

function bizNationMap(val) {
  let n = biz_nation_e.value.find(item => item.value == val)
  return n ? n.label : '-'
}

const emits = defineEmits(['action', 'success'])

function handleAction(btn) {
  emits('action', btn)
}

function handleEditDialog(item) {
  let length = 0
  if (orderLogisticSimpleVOS.value?.length) {
    length = orderLogisticSimpleVOS.value.filter(
      item => item.shippingInfoSimpleVO?.rollbackId == orderRollbackId.value
    ).length
  }
  EditOrderLogisticsRef.value?.open(item, length)
}

// 展开
function handleUnfold(item) {
  if (!item.logisticInfo?.length) return
  item.isUnfold = !item.isUnfold
  if (item.isUnfold) {
    item.maxHeight = item.logisticInfo ? item.logisticInfo.length * 70 + 'px' : '0px'
  } else {
    item.maxHeight = '0px'
  }
}
//展开
function handleShow(item) {
  item.isUnfold = !item.isUnfold
}
//展开
const isUnfold = ref(true)
function handleShowEmpty() {
  isUnfold.value = !isUnfold.value
}

function copyOrderLogistic(item) {
  if (navigator.clipboard) {
    let copyText = item.delivery.map(delivery => `${delivery.label}: ${delivery.value}`).join('\n')
    navigator.clipboard
      .writeText(copyText)
      .then(function () {
        proxy.$modal.msgSuccess('复制成功')
      })
      .catch(function (err) {
        console.error('Failed to copy text: ', err)
      })
  }
}
</script>

<style scoped lang="scss">
.send-info {
  margin-top: 20px;
  background-color: #fff;
  //   padding: 13px;
  border-radius: 4px;
  gap: 10px;
  position: relative;
  box-shadow: var(--el-box-shadow-light);
  border: 1px solid #e9e9e9;
}
.info-head {
  padding: 13px;
  background: #f9f9f9;
  border-bottom: 1px solid #e9e9e9;
  &-btn {
    cursor: pointer;
    color: #409eff;
    font-size: 12px;
  }
}
.info-box {
  padding: 10px 10px 30px;
  background-color: #fff;
  border-radius: 4px;
  font-size: 15px;
  // box-shadow: 3px 3px 4px 0px #ddd;

  :deep(.label) {
    display: inline-block;
    width: 85px;
    margin-right: 0;
    font-size: 15px;
  }
  :deep(.el-descriptions__content) {
    font-size: 15px;
  }

  .unfoldBtn {
    gap: 4px;
    cursor: pointer;

    // span {
    //   font-weight: 500;
    // }
  }
  .arrowIcon {
    transition: 0.5s;
  }
  .up {
    transform: rotateZ(180deg);
  }

  .logistics-item {
    align-items: flex-start;

    .label {
      width: 85px;
      flex-shrink: 0;
    }
    .content-box {
      margin-top: 5px;
      flex-shrink: 0;
      width: calc(100% - 100px);
      word-break: break-all;

      .flag-btn {
        color: var(--el-color-primary);
        border-color: var(--el-color-primary);
      }
    }
    .logistics-box {
      width: 90%;
      transition: 0.5s;
      overflow: hidden;
      margin: 10px 0 0 50px;
    }
  }
}
.el-col {
  align-items: baseline;
}

.el-col-8 {
  display: flex;
  margin-bottom: 15px;
  //   align-items: center;
  font-size: 13px;
  .label {
    flex-shrink: 0;
    min-width: 100px;
    max-width: 150px;
    color: #999;
    text-align: end;
    margin-right: 20px;
  }
  .content {
    flex-shrink: 0;
    width: calc(100% - 100px);
    word-break: break-word;

    // white-space: pre;
  }
  .long-label {
    width: fit-content;
    min-width: 180px;

    & + .text {
      padding-right: 10px;
    }
  }
  .text {
    font-size: 14px;
    // flex-shrink: 0;
    width: calc(100% - 100px);
    word-break: break-all;
  }
}
.el-col-16 {
  display: flex;
  margin-bottom: 10px;
  font-size: 13px;
  .label {
    flex-shrink: 0;
    width: 100px;
    color: #999;
    text-align: end;
    margin-right: 20px;
  }
  .content {
    flex-shrink: 0;
    width: calc(100% - 100px);
    word-break: break-all;
  }
}

// :deep(.el-descriptions__cell) {
//   display: flex;
//   .el-descriptions__content {
//     flex: 1;
//   }
// }
</style>
