<template>
  <div class="info-box">
    <el-row>
      <el-col :span="8">
        <div class="label">下单运营：</div>
        <div class="content">
          {{
            orderVideoVO?.createOrderUserNickName
              ? orderVideoVO.createOrderUserNickName + `（ID:${orderVideoVO.createOrderUserAccount}）`
              : ''
          }}
        </div>
      </el-col>
      <el-col :span="8">
        <div class="label">支付方式：</div>
        <div class="content">{{ orderVO?.payType ? payTypeMap[orderVO.payType] : '' }}</div>
      </el-col>
      <el-col :span="8">
        <div class="label">照片数量：</div>
        <div class="flex-start content">
          <template v-for="item in picCountOptions" :key="item.value">
            <span v-if="item.value == orderVideoVO?.picCount">{{ item.label }}</span>
          </template>
          <el-button v-btn link type="primary" v-if="orderVideoVO?.referencePic?.length" @click="viewImg">
            参考图
          </el-button>
        </div>
      </el-col>
    </el-row>
    <!-- <el-row>
      <el-col :span="12">
        <div class="label">视频编码：</div>
        <div class="content">{{ orderVideoVO?.videoCode }}</div>
      </el-col>
      
    </el-row> -->
    <el-row>
      <el-col :span="8">
        <div class="label">使用平台：</div>
        <div class="content">
          <biz-model-platform :value="orderVideoVO?.platform" tag="text" />
        </div>
      </el-col>
      <el-col :span="8">
        <div class="label">拍摄国家：</div>
        <div class="content">
          <biz-nation :value="orderVideoVO?.shootingCountry" tag="text" />
        </div>
      </el-col>
      <el-col :span="8">
        <div class="label">模特类型：</div>
        <div class="content">
          <biz-model-type :value="orderVideoVO?.modelType" tag="text" />
        </div>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="8">
        <div class="label">意向模特：</div>
        <div class="content">
          {{
            orderVideoVO?.intentionModel
              ? orderVideoVO.intentionModel.name + `（ID:${orderVideoVO.intentionModel.account}）`
              : ''
          }}
        </div>
      </el-col>
      <el-col :span="8">
        <div class="label">拍摄模特：</div>
        <div class="content">
          {{
            orderVideoVO?.shootModel
              ? orderVideoVO.shootModel.name + `（ID:${orderVideoVO.shootModel.account}）`
              : ''
          }}
        </div>
      </el-col>
      <el-col :span="8">
        <div class="label">视频格式：</div>
        <div class="content">
          <template v-for="item in videoFormatOptions" :key="item.value">
            <span v-if="item.value == orderVideoVO?.videoFormat">{{ item.label }}</span>
          </template>
        </div>
      </el-col>
      <!-- <el-col :span="6">
        <div class="label">视频时长：</div>
        <div class="content">
          {{ orderVideoVO?.videoDuration ? orderVideoVO?.videoDuration + 's' : '' }}
        </div>
      </el-col> -->
    </el-row>
    <div class="flex-start col-24">
        <div class="label">视频时长：</div>
        <div class="content">{{ orderVideoVO?.videoDuration ? orderVideoVO?.videoDuration + 's' : '' }}</div>
      </div>
    <el-row>
      <div class="flex-start col-24">
        <div class="label">中文名称：</div>
        <div class="content">{{ orderVideoVO?.productChinese }}</div>
      </div>
    </el-row>
    <el-row>
      <div class="flex-start col-24">
        <div class="label">英文名称：</div>
        <div class="content">{{ orderVideoVO?.productEnglish }}</div>
      </div>
    </el-row>
    <el-row>
      <div class="flex-start col-24">
        <div class="label">产品链接：</div>
        <div class="content more-ell" style="--l: 5; width: calc(100% - 95px); word-break: break-all">
          {{ orderVideoVO?.productLink }}
        </div>
      </div>
    </el-row>
    <el-row>
      <div class="flex-start col-24">
        <div class="label">参考视频：</div>
        <div class="content more-ell" style="--l: 5; width: calc(100% - 95px); word-break: break-all">
          {{ orderVideoVO?.referenceVideoLink }}
        </div>
      </div>
    </el-row>
    <el-row>
      <div class="flex-start col-24">
        <div class="label">剪辑要求：</div>
        <div class="content" v-if="orderVideoVO?.clipsRequired && orderVideoVO.clipsRequired.length > 0">
          <div v-for="(item, index) in orderVideoVO.clipsRequired" :key="index">
            {{ index + 1 + '.' + item.content }}
          </div>
        </div>
      </div>
    </el-row>
    <el-row>
      <div class="flex-start col-24" style="width: 100%"  v-loading="translateLoading">
        <div class="label">
          拍摄建议：
          <el-button
            v-btn
            v-if="orderVideoVO?.shootRequired && orderVideoVO.shootRequired.length > 0"
            style="margin: 3px 0 0 6px"
            round
            plain
            size="small"
            @click="doTranslate"
          >
            翻译
          </el-button>
        </div>
        <div
          class="content"
          style="flex: 1"
          v-if="orderVideoVO?.shootRequired && orderVideoVO.shootRequired.length > 0"
        >
          <div style="line-height: 20px;" v-for="(item, index) in orderVideoVO.shootRequired" :key="index">
            {{ item.content }}
          </div>
          <template v-if="transformContentList.length">
            <el-divider style="margin: 3px 0; width: 90%" border-style="dashed" />
            <div class="tip">翻译结果</div>
            <div
              class="tip"
              v-for="(item, index) in transformContentList"
              :key="index"
             
            >
              {{ index + 1 + '.' + item }}
            </div>
          </template>
        </div>
        <!-- <div class="update-tips" v-if="demandsTips">改</div> -->
      </div>
    </el-row>
    <el-row>
      <div class="flex-start col-24">
        <div class="label">
          匹配模特
          <br />
          注意事项：
        </div>
        <div class="content" v-if="orderVideoVO?.cautions">
          <template v-for="(item, index) in orderVideoVO.cautions" :key="index">
            {{ item.content }}
          </template>
        </div>
      </div>
    </el-row>
    <!-- <el-row>
      <el-col :span="12">
        <div class="label">产品链接：</div>
        <div class="content more-ell" style="--l: 5">{{ orderVideoVO?.productLink }}</div>
      </el-col>
    </el-row> -->

    <!-- <el-row>
      <div class="flex-start col-24">
        <div class="label">限制条件：</div>
        <div class="content" v-if="orderVideoVO?.limitingCondition">
          <div v-for="(item, index) in orderVideoVO.limitingCondition" :key="index">
            {{ index + 1 + '：' + item.content }}
          </div>
        </div>
        <div class="update-tips" v-if="conditionsTips">改</div>
      </div>
    </el-row> -->
  </div>
</template>

<script setup>
import { picCountOptions, videoFormatOptions } from '@/views/order/list/data.js'
import { payTypeMap } from '@/utils/dict'
import { useViewer } from '@/hooks/useViewer'
import { getTranslate } from '@/api/order/order'

const { showViewer } = useViewer()


const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
})
const orderVO = computed(() => props.data.orderVO)
const orderVideoVO = computed(() => props.data.orderVideoVO)
const demandsTips = computed(() => {
  if (props.data?.orderVideoVO?.shootRequired) {
    return props.data.orderVideoVO.shootRequired.some(item => {
      return item.createTime !== item.updateTime
    })
  }
  return false
})
const conditionsTips = computed(() => {
  if (props.data?.orderVideoVO?.limitingCondition) {
    return props.data.orderVideoVO.limitingCondition.some(item => {
      return item.createTime !== item.updateTime
    })
  }
  return false
})

function viewImg() {
  if (orderVideoVO.value?.referencePic?.length) {
    showViewer(orderVideoVO.value?.referencePic.map(item => item))
  }
}

//翻译拍摄要求
const transformContentList = ref([])
const translateLoading = ref(false)
function doTranslate() {
  translateLoading.value = true
  let data = []
  if (orderVideoVO.value?.shootRequired) {
    orderVideoVO.value.shootRequired.forEach((item, index) => {
      data.push(item.content)
    })
  }
  getTranslate({ language: 1, wordList: data })
    .then(res => {
      transformContentList.value = res.data
      translateLoading.value = false
    })
    .finally(() => (translateLoading.value = false))
}
</script>

<style scoped lang="scss">
.info-box {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  font-size: 15px;
  box-shadow: var(--el-box-shadow-light);

  .el-col {
    align-items: baseline;
  }

  .el-col-6 {
    display: flex;
    margin-bottom: 10px;

    .label {
      flex-shrink: 0;
      width: 90px;
    }
    .content {
      flex-shrink: 0;
      width: calc(100% - 95px);
      word-break: break-all;
    }
  }

  .el-col-8 {
    display: flex;
    margin-bottom: 10px;
    align-items: center;

    .label {
      flex-shrink: 0;
      width: 90px;
    }
    .content {
      flex-shrink: 0;
      width: calc(100% - 95px);
      word-break: break-all;
    }
  }

  .el-col-12 {
    display: flex;
    margin-bottom: 10px;

    .label {
      flex-shrink: 0;
      width: 90px;
    }
    .content {
      flex-shrink: 0;
      width: calc(100% - 95px);
      word-break: break-all;
    }
  }
  .col-24 {
    align-items: flex-start;
    margin-bottom: 10px;
    width: 100%;

    .label {
      flex-shrink: 0;
      width: 90px;
    }
    .content {
      flex-shrink: 0;
      width: calc(100% - 95px);
      word-break: break-all;
    }
    .tip {
      flex-shrink: 0;
      color: #7f7f7f;
    }
  }

  .update-tips {
    width: 26px;
    height: 26px;
    text-align: center;
    line-height: 26px;
    color: #fff;
    border-radius: 50%;
    margin: auto 10px;
    font-weight: 600;
    background-color: var(--el-color-primary);
  }
}
</style>
