<template>
  <div class="exchange">
    <el-timeline
      style="max-width: 800px; margin-top: 20px"
      v-if="orderVideoOperateVOS && orderVideoOperateVOS.length > 0"
    >
      <el-timeline-item
        placement="top"
        v-for="(item, index) in orderVideoOperateVOS"
        :key="index"
        :timestamp="item.eventExecuteTime"
      >
        <!-- v-if="index == orderVideoOperateVOS?.length - 1" -->
        <div class="item">
          <div>
            {{ item.eventName }}
            <el-button
              v-if="
                item.eventName == '匹配情况反馈修改' ||
                item.eventName == '审核订单' ||
                item.eventName == '修改订单' ||
                item.eventName == '上传图片'
              "
              style="font-size: 12px"
              type="primary"
              link
              v-btn
              @click="doShowHistoryRecord"
              v-hasPermi="['order:manage:change-record']"
            >
              查看变更记录
            </el-button>
            <el-button
              v-if="
                item.eventName.indexOf('匹配') != '-1' &&
                item.eventName.indexOf('暂停匹配') == '-1' &&
                item.eventName.indexOf('继续匹配') == '-1'
              "
              style="font-size: 12px"
              type="primary"
              link
              v-btn
              @click="doMatchFeedback"
            >
              查看反馈内容
            </el-button>
          </div>
          <div class="item-text">{{ item.eventContent }}</div>
        </div>
        <!-- <div v-else>
          <div>
            {{ item.eventName }}
            <el-button
              v-if="item.eventName.indexOf('审核') != '-1'"
              style="font-size: 12px"
              type="primary"
              link
              v-btn
              @click="doShowHistoryRecord"
            >
              查看变更记录
            </el-button>
          </div>
          <div class="item-text">{{ item.eventContent }}</div>
        </div> -->
      </el-timeline-item>
    </el-timeline>
    <div class="flex-center" v-else>暂无流转记录</div>
  </div>
</template>

<script setup>
const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
})

const orderVideoOperateVOS = computed(() => props.data.orderVideoOperateVOS)

const emits = defineEmits(['showHistoryRecord', 'showFeedbackCase'])
//触发历史变更记录
function doShowHistoryRecord() {
  emits('showHistoryRecord')
}
//匹配情况反馈
function doMatchFeedback() {
  // doFeedbackCase
  emits('showFeedbackCase')
}
</script>

<style scoped lang="scss">
.exchange {
  margin-top: 20px;
  background-color: #fff;
  //   padding: 13px;
  border-radius: 4px;
  gap: 10px;
  position: relative;
  box-shadow: var(--el-box-shadow-light);
  border: 1px solid #e9e9e9;
  padding: 10px 20px;
  position: relative;
  .item:last-child::before {
    content: ' ';
    border-left: 2px solid #e9e9e9;
    position: absolute;
    left: 4px;
    height: 100%;
    top: 10px;
  }
  .item-text {
    font-size: 0.8em;
    color: #7f7f7f;
    word-break: break-all;
    line-break: anywhere;
  }
  :deep(.el-timeline-item__node) {
    background: #fff;
    border: 1px solid var(--el-timeline-node-color);
    z-index: 9;
  }
  :deep(.el-timeline-item__wrapper) {
    display: flex;
    // align-items: center;
    .el-timeline-item__timestamp {
      width: 68px;
      text-align: center;
      min-width: 68px;
    }
    .el-timeline-item__content {
      margin: 0 0 0 10px;
    }
  }
}
</style>
