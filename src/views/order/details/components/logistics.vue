<template>
  <!-- 步骤条 -->
  <div class="box-top" v-for="(item, index) in dataArray" :key="index">
    <!-- 左边 -->
    <div class="left-box-top">{{ item.curTime }}</div>
    <div class="line" :class="{ none: index == dataArray.length - 1 }">
      <!-- 中线 -->
      <div class="dot" :class="{ active: index === 0 }"></div>
      <!-- 圆点 -->
    </div>
    <div class="right-box-top" :class="{ 'content-active': index === 0 }">
      <!-- 右边 -->
      <slot name="content" :data="item">
        <div>
          <div class="title">{{ item.mainStatus }}</div>
          <div>
            {{ item.city ? '[' + item.city + '] ' : '' }} {{ item.description ? item.description : '-' }}
          </div>
        </div>
      </slot>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  dataArray: {
    type: Array,
    default: () => [],
  },
})

</script>

<style lang="scss" scoped>
$dot: #4fcc33;
.box-top {
  width: 100%;
  min-height: 60px;
  box-sizing: border-box;
  display: flex;
  flex-direction: row;

  .left-box-top {
    width: 75px;
    text-align: center;
    color: #7f7f7f;
    font-size: 13px;
  }

  .line {
    width: 2px;
    background-color: #5555558a;
    margin: 0 10px 0 10px;
    transform: translateY(13px);

    .dot {
      width: 10px;
      height: 10px;
      border: 1px solid #979797;
      background-color: #ffffff;
      border-radius: 50%;
      position: relative;
      top: -6px;
      left: -4px;
    }
  }

  .right-box-top {
    flex: 1;
    padding: 0 0 10px 5px;
    font-size: 13px;
    color: #7f7f7f;

    .title {
      font-weight: 600;
    }
  }
  .content-active {
    color: #333;
  }
}

//激活元素
.active {
  &.dot {
    border-color: $dot !important;
  }
  background: $dot !important;
}

// 隐藏元素
.none {
  background-color: rgba(0, 0, 0, 0) !important;
}
</style>
