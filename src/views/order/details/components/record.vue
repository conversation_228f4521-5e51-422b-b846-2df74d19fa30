<template>
  <div class="refund-record">
    <template v-if="recordList && recordList.length > 0">
      <div class="header">已退金额：{{ refundAmountTotal }}元</div>
      <el-table :data="recordList" style="width: 100%" border>
        <el-table-column prop="refundNum" label="退款审批号" width="180" align="center"></el-table-column>
        <el-table-column prop="refundType" label="退款类型" width="150" align="center">
          <template v-slot="{ row }">
            {{ refundTypeOptions.find(item => item.value == row.refundType)?.label }}
          </template>
        </el-table-column>
        <el-table-column prop="refundCause" label="理由" align="center">
          <template v-slot="{ row }">
            <div>
              {{ row.refundCause || '-' }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="refundAmount"
          label="退款金额（元）"
          width="150"
          align="center"
        ></el-table-column>
        <el-table-column prop="applyTime" label="发起时间" width="180" align="center"></el-table-column>
        <el-table-column prop="initiatorName" label="发起人" width="150" align="center"></el-table-column>
        <el-table-column prop="refundStatus" label="状态" width="150" align="center">
          <template v-slot="{ row }">
            {{ orderRefundStatusList.find(item => item.value == row.refundStatus)?.label }}
          </template>
        </el-table-column>
        <el-table-column prop="operateTime" label="审批时间" width="180" align="center">
          <template v-slot="{ row }">
            {{ row.operateTime || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="operateBy" label="审批人" width="150" align="center">
          <template v-slot="{ row }">
            {{ row.operateBy || '-' }}
          </template>
        </el-table-column>
      </el-table>
    </template>
    <el-empty v-else description="未发生退款"></el-empty>
  </div>
</template>

<script setup>
import { refundTypeOptions, orderRefundStatusList } from '@/views/order/list/data.js'

const props = defineProps({
  recordList: {
    type: Array,
    default: () => [],
  },
  refundAmountTotal: {
    type: Number,
    default: 0,
  }
})
</script>

<style scoped lang="scss">
.refund-record {
  margin-top: 20px;

  .header {
    margin-bottom: 10px;
    font-size: 14px;
    font-weight: 400;
    color: var(--el-text-color-regular);
  }
}
</style>
