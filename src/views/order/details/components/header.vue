<template>
  <div class="flex-start head-box">
    <div class="flex-column status-box">
      <img src="@/assets/icons/svg/money_icon.svg" />
      <span v-if="orderVideoSimpleVO?.status">{{ orderStatusMap[orderVideoSimpleVO.status] }}</span>
      <div v-if="orderVideoSimpleVO?.videoPromotionAmount" class="promotion">减</div>
    </div>
    <div class="content">
      <div style="margin-left: 16px">订单号：{{ orderSimpleVO?.orderNum }}</div>

      <div>商品总额：￥{{ orderVideoSimpleVO?.amount }}</div>

      <div
        v-if="
          orderVideoSimpleVO?.status === orderStatusMap['已完成'] ||
          orderVideoSimpleVO?.status === orderStatusMap['已取消']
        "
      >
        完成时间：{{ orderVideoSimpleVO?.statusTime || '' }}
      </div>
      <div v-else>下单时间：{{ orderSimpleVO?.orderTime }}</div>

      <div>视频编码：{{ orderVideoSimpleVO?.videoCode }}</div>
      <div>订单运营：{{ orderVideoSimpleVO?.createOrderUserName || orderVideoSimpleVO?.createOrderUserNickName }}</div>
    </div>
    <div class="button">
      <template v-if="orderVideoSimpleVO?.status === orderStatusMap['待支付']">
        <div class="flex-end">
          <!-- <el-button v-btn v-hasPermi="['order:manage:cancel']" plain round @click="handleAction('取消订单')">取消订单</el-button> -->
          <el-button
            v-btn
            v-hasPermi="['order:manage:change-order-price']"
            plain
            round
            @click="handleAction('修改订单费用')"
          >
            修改订单费用
          </el-button>
        </div>
      </template>
      <template
        v-if="
          orderVideoSimpleVO?.status === orderStatusMap['需发货'] ||
          orderVideoSimpleVO?.status === orderStatusMap['待匹配'] ||
          orderVideoSimpleVO?.status === orderStatusMap['待完成'] ||
          orderVideoSimpleVO?.status === orderStatusMap['待确认'] ||
          orderVideoSimpleVO?.status === orderStatusMap['需确认']
        "
      >
        <el-button
          :disabled="isRefund(orderVideoSimpleVO?.orderVideoRefund)"
          v-btn
          v-hasPermi="['order:manage:refund']"
          plain
          round
          @click="handleAction('申请退款')"
        >
          申请退款
        </el-button>
      </template>
      <template
        v-if="
          (orderVideoSimpleVO?.status === orderStatusMap['需发货'] ||
            orderVideoSimpleVO?.status === orderStatusMap['待完成']) &&
          orderVideoSimpleVO?.isObject === 0
        "
      >
        <el-button
          v-btn
          v-hasPermi="['order:manage:shipments']"
          :disabled="isRefund(orderVideoSimpleVO?.orderVideoRefund)"
          round
          @click="handleAction('填写物流单号')"
        >
          {{
            orderVideoSimpleVO?.status === orderStatusMap['需发货']
              ? flag
                ? '填写物流单号'
                : '去发货'
              : '补发物流'
          }}
        </el-button>
      </template>
      <template
        v-if="
          orderVideoSimpleVO?.status === orderStatusMap['需发货'] ||
          orderVideoSimpleVO?.status === orderStatusMap['待完成'] ||
          orderVideoSimpleVO?.status === orderStatusMap['需确认']
        "
      >
        <el-button v-btn v-hasPermi="['order:manage:rollback']" round @click="handleAction('订单回退')">
          订单回退
        </el-button>
      </template>
      <!-- <template v-if="orderVideoVO?.status === orderStatusMap['待完成'] && orderVideoVO.feedbackMaterialUrl">
        <el-button
          v-btn
          v-hasPermi="['order:manage:refund']"
          type="primary"
          round
          @click="handleAction('申请退款')"
        >
          申请退款
        </el-button>
      </template> -->
      <!--<template v-else-if="orderVideoVO?.status === orderStatusMap['已完成']">
        <el-button v-btn v-hasPermi="['order:manage:upload']" type="primary" round @click="handleAction('上传视频')">上传视频</el-button>
      </template> -->
    </div>
  </div>
</template>

<script setup>
import { orderStatusMap } from '@/views/order/list/data.js'
// import CountDown from '@/components/Public/countDown.vue';

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
  // flag: {
  //   type: Boolean,
  //   default: false,
  // },
})

const emits = defineEmits(['action'])

const orderSimpleVO = computed(() => props.data.orderSimpleVO)
const orderVideoSimpleVO = computed(() => props.data.orderVideoSimpleVO)
const flag = computed(() => {
  if (props.data.orderLogisticSimpleVOS && props.data.orderLogisticSimpleVOS.length > 0) {
    return props.data.orderLogisticSimpleVOS[0].logisticFlag
  } else {
    return false
  }
})

function isRefund(orderVideoRefund) {
  return (
    orderVideoRefund != null &&
    orderVideoRefund.refundType == 2 &&
    (orderVideoRefund.refundStatus == 0 ||
      orderVideoRefund.refundStatus == 1 ||
      orderVideoRefund.refundStatus == 4)
  )
  // return (
  //   orderVideoRefund != null &&
  //   (orderVideoRefund.refundStatus == 0 ||
  //     orderVideoRefund.refundStatus == 1 ||
  //     (orderVideoRefund.refundStatus == 4 && orderVideoRefund.isFullRefund == 0))
  // )
}

function handleAction(btn) {
  emits('action', btn)
}
</script>

<style scoped lang="scss">
.head-box {
  background-color: #fff;
  padding: 13px;
  border-radius: 4px;
  gap: 10px;
  position: relative;
  box-shadow: var(--el-box-shadow-light);
  // box-shadow: 3px 3px 4px 0px #ddd;

  .status-box {
    position: relative;
    padding: 20px 25px;
    border-radius: 4px;
    font-size: 16px;
    background-color: #f2f2f2;

    img {
      width: 40px;
      height: 40px;
    }

    span {
      color: #000;
    }

    .promotion {
      position: absolute;
      top: 0;
      right: 0;
      z-index: 2;
      font-size: 12px;
      padding: 0 3px;
      background: var(--el-color-warning-light-3);
      color: #fff;
      border-radius: 5px;
      line-height: 20px;
      margin-left: 7px;
    }
  }
  .content {
    font-size: 16px;
    display: grid;
    gap: 8px 0;

    &:first-child() {
      margin-bottom: 10px;
    }

    .red {
      color: var(--el-color-danger);
    }
  }
  .button {
    position: absolute;
    bottom: 50px;
    right: 13px;
  }
}
</style>
