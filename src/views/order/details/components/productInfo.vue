<template>
  <div class="info">
    <div class="product-info">
      <div class="info-head flex-between">
        <div>产品信息</div>
        <el-popover
          placement="top"
          :width="300"
          trigger="hover"
          :content="`最后变更时间：${orderVideoSimpleVO?.lastChangeTime || '暂未变更'}`"
          popper-style="text-align: center"
        >
          <!-- <template #default>
          <span v-if="orderVideoSimpleVO?.lastChangeTime">{{ orderVideoSimpleVO?.lastChangeTime }}</span>
          <span v-else>暂无变更记录</span>
        </template> -->
          <template #reference>
            <el-button
              size="small"
              v-btn
              type="warning"
              v-hasPermi="['order:manage:change-record']"
              @click="doShowHistoryRecord"
            >
              变更
            </el-button>
          </template>
        </el-popover>
      </div>
      <div class="info-content">
        <div class="info-content-left">
          <!-- <img class="icon" :src="$picUrl + orderVideoVO?.productPic" alt="" /> -->
          <el-image
            class="icon"
            :src="
              orderVideoSimpleVO?.productPic
                ? $picUrl +
                  orderVideoSimpleVO.productPic +
                  '?imageMogr2/format/webp/interlace/1/quality/100/interlace/0/thumbnail/200x'
                : ''
            "
            fit="scale-down"
            preview-teleported
            @click="showViewer([orderVideoSimpleVO?.productPic])"
          >
            <template #error>
              <img :src="$picUrl + 'static/assets/no-img.png'" alt="" style="width: 100%; height: 100%" />
            </template>
          </el-image>
        </div>
        <div class="info-content-right">
          <el-row>
            <el-col :span="8">
              <div class="label">中文名称：</div>
              <div class="content">
                {{ orderVideoSimpleVO?.productChinese }}
              </div>
            </el-col>
            <el-col :span="8">
              <div class="label">英文名称：</div>
              <div class="content">
                {{ orderVideoSimpleVO?.productEnglish }}
              </div>
            </el-col>
            <el-col :span="8">
              <div class="label">产品品类：</div>
              <div
                class="content"
                v-if="orderVideoSimpleVO?.productCategory && orderVideoSimpleVO?.productCategory.length > 0"
              >
                <template v-for="(item, index) in orderVideoSimpleVO?.productCategory">
                  {{ item.name }}{{ index != orderVideoSimpleVO.productCategory.length - 1 ? '/' : '' }}
                </template>
              </div>
            </el-col>
          </el-row>
          <el-row style="margin-top: 15px">
            <el-col :span="16">
              <div class="label" style="width: 120px">产品链接：</div>
              <div
                class="content"
                style="color: #02a7f0; cursor: pointer"
                @click="handleLink(orderVideoSimpleVO?.productLink)"
              >
                {{ orderVideoSimpleVO?.productLink }}
              </div>
            </el-col>
            <el-col :span="8" v-if="orderVideoSimpleVO?.status !== orderStatusMap['待确认']">
              <div class="label">通品：</div>
              <div class="content">
                {{
                  orderVideoSimpleVO?.isGund === 0
                    ? '否'
                    : orderVideoSimpleVO?.isGund === 1
                    ? '是'
                    : '暂未选择'
                }}
              </div>
            </el-col>
            <!-- <div class="flex-start col-24">
              <div class="label">产品链接：</div>
              <div
                class="content more-ell"
                style="--l: 5; color: #02a7f0; cursor: pointer"
                @click="handleLink(orderVideoSimpleVO?.productLink)"
              >
                {{ orderVideoSimpleVO?.productLink }}
              </div>
            </div> -->
          </el-row>
        </div>
      </div>
    </div>
    <div class="product-info">
      <div class="info-head flex-between">
        <div>拍摄信息</div>
      </div>
      <div class="video-content">
        <el-row>
          <el-col :span="8">
            <div class="label">使用平台：</div>
            <div class="content">
              <biz-model-platform :value="orderVideoSimpleVO?.platform" tag="text" />
            </div>
          </el-col>
          <el-col :span="8">
            <div class="label">拍摄国家：</div>
            <div class="content">
              <biz-nation :value="orderVideoSimpleVO?.shootingCountry" tag="text" />
            </div>
          </el-col>
          <el-col :span="8">
            <div class="label">模特类型：</div>
            <div class="content">
              <biz-model-type :value="orderVideoSimpleVO?.modelType" tag="text" />
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <div class="label">视频格式：</div>
            <div class="content">
              <template v-for="item in videoFormatOptions" :key="item.value">
                <span v-if="item.value == orderVideoSimpleVO?.videoFormat">{{ item.label }}</span>
              </template>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="label">照片数量：</div>
            <div class="content">
              <template v-for="item in picCountNumOptions" :key="item.value">
                <span v-if="item.value == orderVideoSimpleVO?.picCount">{{ item.label }}</span>
              </template>
              <el-tag style="margin: -3px 0 0 5px" type="info" v-if="orderVideoSimpleVO?.refundPicCount">
                已退款({{ orderVideoSimpleVO?.refundPicCount }}张)
              </el-tag>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="label">参考图片：</div>
            <div
              class="content"
              v-if="orderVideoSimpleVO?.referencePic && orderVideoSimpleVO?.referencePic.length > 0"
            >
              <el-button link type="primary" @click="showViewer(orderVideoSimpleVO?.referencePic)">
                查看
              </el-button>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <div class="label">视频时长：</div>
            <div class="content" v-if="orderVideoSimpleVO?.videoDuration">
              {{ orderVideoSimpleVO?.videoDuration ? orderVideoSimpleVO?.videoDuration + 's' : '' }}
            </div>
          </el-col>
          <el-col :span="16">
            <div class="label" style="width: 120px">参考视频：</div>
            <div
              class="content one-ell productLink"
              @click="handleLink(orderVideoSimpleVO?.referenceVideoLink)"
            >
              {{ orderVideoSimpleVO?.referenceVideoLink }}
            </div>
          </el-col>
        </el-row>
        <!-- <el-row>
          <div class="flex-start col-24">
            <div class="label">参考视频：</div>
            <div
              class="content more-ell"
              style="--l: 5; color: #02a7f0; cursor: pointer"
              @click="handleLink(orderVideoSimpleVO?.referenceVideoLink)"
            >
              {{ orderVideoSimpleVO?.referenceVideoLink }}
            </div>
          </div>
        </el-row> -->
        <el-row>
          <div class="flex-start col-24">
            <div class="label">剪辑要求：</div>
            <div class="content template-pre" v-if="orderVideoSimpleVO?.clipsRequired">
              <div v-for="(item, index) in orderVideoSimpleVO.clipsRequired" :key="index">
                {{ item.content }}
              </div>
            </div>
          </div>
        </el-row>
        <el-row
          v-if="orderVideoSimpleVO?.sellingPointProduct && orderVideoSimpleVO.sellingPointProduct.length > 0"
        >
          <div class="flex-start col-24" style="width: 100%" v-loading="translateLoadingV1">
            <div class="label">
              产品卖点：
              <el-button v-btn style="margin: 3px 14px 0 0" round plain size="small" @click="doTranslateV1">
                翻译
              </el-button>
            </div>
            <div class="content template-pre" style="flex: 1" v-if="orderVideoSimpleVO?.sellingPointProduct">
              <div style="line-height: 20px">
                {{ orderVideoSimpleVO.sellingPointProduct }}
              </div>
              <template v-if="shootingTransLateSuggestion">
                <el-divider style="margin: 3px 0; width: 100%" border-style="dashed" />
                <div class="tip">翻译结果</div>
                <div class="tip tip-text template-pre">
                  {{ shootingTransLateSuggestion }}
                </div>
              </template>
            </div>
          </div>
        </el-row>
        <el-row v-if="orderVideoSimpleVO?.shootRequired && orderVideoSimpleVO.shootRequired.length > 0">
          <div class="flex-start col-24" style="width: 100%" v-loading="translateLoading">
            <div class="label">
              拍摄建议：
              <el-button v-btn style="margin: 3px 14px 0 0" round plain size="small" @click="doTranslate">
                翻译
              </el-button>
            </div>
            <div
              class="content template-pre"
              style="flex: 1"
              v-if="orderVideoSimpleVO?.shootRequired && orderVideoSimpleVO.shootRequired.length > 0"
            >
              <div
                style="line-height: 20px"
                v-for="(item, index) in orderVideoSimpleVO.shootRequired"
                :key="index"
              >
                {{ handleR(item.content) }}
              </div>
              <template v-if="transformContentList.length">
                <el-divider style="margin: 3px 0; width: 100%" border-style="dashed" />
                <div class="tip">翻译结果</div>
                <div class="tip" v-for="(item, index) in transformContentList" :key="index">
                  {{ item }}
                </div>
              </template>
            </div>
          </div>
        </el-row>
        <el-row
          v-if="
            orderVideoSimpleVO?.orderVideoCautionsVO?.cautions &&
            orderVideoSimpleVO.orderVideoCautionsVO.cautions.length > 0
          "
        >
          <div class="flex-start col-24" style="width: 100%">
            <div class="label">模特要求：</div>
            <div class="content template-pre" style="flex: 1">
              <div
                style="line-height: 20px"
                v-for="(item, index) in orderVideoSimpleVO.orderVideoCautionsVO.cautions"
                :key="index"
              >
                {{ item.content }}
              </div>
            </div>
          </div>
        </el-row>
        <el-row
          v-if="
            orderVideoSimpleVO?.orderSpecificationRequire &&
            orderVideoSimpleVO.orderSpecificationRequire.length > 0
          "
        >
          <div class="flex-start col-24" style="width: 100%">
            <div class="label">商品规格要求：</div>
            <div class="content template-pre" style="flex: 1">
              <div style="line-height: 20px">
                {{ orderVideoSimpleVO?.orderSpecificationRequire }}
              </div>
            </div>
          </div>
        </el-row>
        <el-row
          v-if="orderVideoSimpleVO?.particularEmphasis && orderVideoSimpleVO.particularEmphasis.length > 0"
        >
          <div class="flex-start col-24" style="width: 100%">
            <div class="label">特别强调：</div>
            <div class="content template-pre" style="flex: 1">
              <div style="line-height: 20px">
                {{ orderVideoSimpleVO?.particularEmphasis }}
              </div>
            </div>
          </div>
        </el-row>
        <!-- <el-row>
          <div class="flex-start col-24">
            <div class="label">
              匹配模特&emsp;
              <br />
              注意事项：
            </div>
            <div class="content" v-if="orderVideoSimpleVO?.orderVideoCautionsVO?.cautions">
              <div v-for="(item, index) in orderVideoSimpleVO.orderVideoCautionsVO.cautions" :key="index">
                {{ item.content }}
              </div>
            </div>
          </div>
        </el-row> -->
        <el-row>
          <div class="flex-start col-24">
            <div
              class="label"
              v-if="
                orderVideoSimpleVO?.particularEmphasis && orderVideoSimpleVO.particularEmphasis.length > 0
              "
            ></div>
            <div class="label" v-else>特别强调：</div>
            <div class="content">
              <template
                v-if="
                  orderVideoSimpleVO?.particularEmphasisPic &&
                  orderVideoSimpleVO.particularEmphasisPic.length > 0
                "
              >
                <ViewerImageList
                  :data="orderVideoSimpleVO.particularEmphasisPic"
                  is-preview-all
                  :show-delete-btn="false"
                  :customized="true"
                />
              </template>
            </div>
          </div>
        </el-row>
      </div>
    </div>
    <div class="product-info">
      <div class="info-head flex-start">
        <div>订单信息</div>
        <!-- <div v-if="orderVideoSimpleVO?.videoPromotionAmount" class="promotion">减</div> -->
      </div>
      <div class="order-content">
        <el-row>
          <el-col :span="8">
            <div class="label">下单用户：</div>
            <div class="content">
              {{ orderSimpleVO?.orderUserName || orderSimpleVO?.orderUserNickName || '' }}
              (ID:{{ orderSimpleVO?.orderUserAccount }})
              <!-- {{
                orderSimpleVO?.orderUser
                  ? orderSimpleVO.orderUser?.nickName + `（ID:${orderSimpleVO.orderUser?.account}）`
                  : ''
              }} -->
            </div>
          </el-col>
          <el-col :span="8">
            <div class="label">下单时间：</div>
            <div class="content">
              {{ orderSimpleVO?.orderTime }}
            </div>
          </el-col>
          <el-col :span="8">
            <div class="label">支付单号：</div>
            <div class="content">
              {{ orderSimpleVO?.payNum || '-' }}
            </div>
          </el-col>
        <!-- </el-row>
        <el-row> -->
          <el-col :span="8">
            <div class="label">支付用户：</div>
            <div class="content">
              {{ orderSimpleVO?.payUserName || orderSimpleVO?.payUserNickName || '' }}
            </div>
          </el-col>
          <el-col :span="8">
            <div class="label">支付方式：</div>
            <div class="flex-start gap-5 content">
              <span>{{ orderSimpleVO?.payTime && orderSimpleVO?.payType ? payTypeMap[orderSimpleVO.payType] : '' }}</span>
              <el-tag
                v-if="
                  orderSimpleVO?.payTime &&
                  orderSimpleVO?.payType &&
                  orderSimpleVO?.payTypeDetail &&
                  (orderSimpleVO?.payType == 7 || orderSimpleVO?.payType == 17)
                "
                type="warning"
                round
                size="small"
              >
                {{ payMoneyTypeMap[orderSimpleVO.payTypeDetail] || '-' }}
              </el-tag>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="label">支付时间：</div>
            <div class="content">
              {{ orderSimpleVO?.payTime || '-' }}
            </div>
          </el-col>
          <el-col :span="8" v-if="orderSimpleVO?.payType && handleShowAuditTime(orderSimpleVO.payType)">
            <div class="label">支付审核时间：</div>
            <div class="content">
              {{ orderSimpleVO?.auditStatus != 2 && orderSimpleVO?.auditTime ? orderSimpleVO.auditTime : '-' }}
            </div>
          </el-col>
        <!-- </el-row>
        <el-row> -->
          <el-col :span="8">
            <div class="label">视频佣金：</div>
            <div class="content">${{ orderVideoSimpleVO?.videoPrice || '-' }}</div>
          </el-col>
          <el-col :span="8">
            <div class="label">PayPal代付手续费：</div>
            <div class="content">${{ orderVideoSimpleVO?.exchangePrice }}</div>
          </el-col>
          <el-col :span="8">
            <div class="label">蜗牛服务费：</div>
            <div class="content">${{ orderVideoSimpleVO?.servicePrice }}</div>
          </el-col>
        <!-- </el-row>
        <el-row> -->
          <el-col :span="8">
            <div class="label">照片佣金：</div>
            <div class="content">
              {{ orderVideoSimpleVO?.picCount ? `$${orderVideoSimpleVO.picPrice}` : '-' }}
            </div>
          </el-col>
          <el-col :span="8">
            <div class="label">实时百度汇率：</div>
            <div class="content">
              <span v-if="!orderSimpleVO?.defaultExchangeRate">{{ orderSimpleVO?.currentExchangeRate }}</span>
              <span v-else style="color: #d9001b">获取失败</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="label">佣金代缴税费：</div>
            <div class="content">${{ orderVideoSimpleVO?.commissionPaysTaxes }}</div>
          </el-col>
        <!-- </el-row>
        <el-row> -->
          <!-- <el-col :span="8">
            <div class="label">限时满减优惠：</div>
            <div class="content" style="color: #d9001b">
              -￥{{ orderVideoSimpleVO?.videoPromotionAmount || 0 }}
            </div>
          </el-col> -->
          <el-col :span="8">
            <div class="label">订单合计：</div>
            <div class="content">${{ orderVideoSimpleVO?.amountDollar }}</div>
          </el-col>
        </el-row>

        <!-- <el-row>
          <el-col :span="8">
            <div class="label">优惠券：</div>
            <div class="content">
              {{ orderVO?.payUserId }}
            </div>
          </el-col>
          <el-col :span="8">
            <div class="label">优惠金额：</div>
            <div class="content">
              {{ orderVO?.payType ? payTypeMap[orderVO.payType] : '' }}
            </div>
          </el-col>
          <el-col :span="8">
            <div class="label">百度实时汇率：</div>
            <div class="content">
              {{ orderVO?.payTime }}
            </div>
          </el-col>
        </el-row> -->
        <!-- <el-row>
          <el-col :span="8">
            <div class="label">余额支付：</div>
            <div class="content">
              {{ orderVO?.payUserId }}
            </div>
          </el-col>
          <el-col :span="8">
            <div class="label">微信支付：</div>
            <div class="content">
              {{ orderVO?.payType ? payTypeMap[orderVO.payType] : '' }}
            </div>
          </el-col>
          <el-col :span="8">
            <div class="label">订单合计：</div>
            <div class="content">
              {{ orderVO?.payTime }}
            </div>
          </el-col>
        </el-row> -->
      </div>
    </div>
    <div
      class="product-info"
      v-if="
        handleShowDiscount(orderVideoSimpleVO?.orderDiscountDetailVOS, '1') ||
        handleShowDiscount(orderVideoSimpleVO?.orderDiscountDetailVOS, '4')
      "
    >
      <div class="info-head flex-start">
        <div>优惠信息</div>
      </div>
      <div class="order-content">
        <el-row v-if="handleShowDiscount(orderVideoSimpleVO?.orderDiscountDetailVOS, '1')">
          <el-col :span="8">
            <div class="label">限时满减优惠：</div>
            <div class="content" style="color: #d9001b">
              -￥{{ handleDiscount(orderVideoSimpleVO.orderDiscountDetailVOS, '1') }}
            </div>
          </el-col>
        </el-row>
        <el-row v-if="handleShowDiscount(orderVideoSimpleVO?.orderDiscountDetailVOS, '4')">
          <el-col :span="8">
            <div class="label">每月首单立减：</div>
            <div class="content" style="color: #d9001b">
              -￥{{ handleDiscount(orderVideoSimpleVO.orderDiscountDetailVOS, '4') }}
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script setup>
import {
  orderStatusMap,
  picCountNumOptions,
  picCountOptions,
  videoFormatOptions,
} from '@/views/order/list/data.js'
import { payTypeMap } from '@/utils/dict'
import { useViewer } from '@/hooks/useViewer'
import { getTranslate, getOneTranslate } from '@/api/order/order'
import { payMoneyTypeMap } from '@/views/finance/data.js'
import ViewerImageList from '@/components/ImagePreview/viewerImageList.vue'
const { showViewer } = useViewer()

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
})

const orderSimpleVO = computed(() => props.data.orderSimpleVO)
const orderVideoSimpleVO = computed(() => props.data.orderVideoSimpleVO)

const emits = defineEmits(['showHistoryRecord', 'changeRate'])

const demandsTips = computed(() => {
  if (props.data?.orderVideoVO?.shootRequired) {
    return props.data.orderVideoVO.shootRequired.some(item => {
      return item.createTime !== item.updateTime
    })
  }
  return false
})

//触发历史变更记录
function doShowHistoryRecord() {
  emits('showHistoryRecord')
}

//翻译拍摄要求
const transformContentList = ref([])
const translateLoading = ref(false)

function handleR(data) {
  return data.replace(/\r/g, '\r\n')
}
function doTranslate() {
  translateLoading.value = true
  let data = []
  if (orderVideoSimpleVO.value?.shootRequired) {
    orderVideoSimpleVO.value.shootRequired.forEach((item, index) => {
      let data1 = item.content
      if (data1) data1 = data1.replace(/\r/g, '\r\n')
      data.push(data1)
    })
  }
  getTranslate({ language: 1, wordList: data })
    .then(res => {
      transformContentList.value = res.data
      translateLoading.value = false
    })
    .finally(() => (translateLoading.value = false))
}

const shootingTransLateSuggestion = ref('')
const translateLoadingV1 = ref(false)
function doTranslateV1() {
  translateLoadingV1.value = true
  getOneTranslate({ language: 1, originText: orderVideoSimpleVO.value.sellingPointProduct })
    .then(res => {
      let data = res.data.targetText
      if (data) data = data.replace(/\r/g, '\r\n')
      shootingTransLateSuggestion.value = data

      translateLoadingV1.value = false
    })
    .finally(() => (translateLoadingV1.value = false))
}

const handleDiscount = (list, type) => {
  return list.find(item => item.type == type).discountAmount
}
const handleShowDiscount = (list, type) => {
  return list && list.length > 0 ? list.some(item => item.type == type) : false
}

function handleShowAuditTime(type) {
  if (type) {
    if (
      [
        payTypeMap['银行卡'],
        payTypeMap['银行卡+余额'],
        payTypeMap['对公'],
        payTypeMap['对公+余额'],
        payTypeMap['全币种'],
        payTypeMap['全币种+余额'],
      ].includes(type)
    ) {
      return true
    }
  }
  return false
}

function handleLink(link) {
  window.open(link, '_blank')
}
</script>

<style scoped lang="scss">
.product-info {
  margin-top: 20px;
  background-color: #fff;
  //   padding: 13px;
  border-radius: 4px;
  gap: 10px;
  position: relative;
  box-shadow: var(--el-box-shadow-light);
  border: 1px solid #e9e9e9;
}
.info-head {
  padding: 13px;
  background: #f9f9f9;
  border-bottom: 1px solid #e9e9e9;

  .promotion {
    font-size: 12px;
    padding: 0 3px;
    background: var(--el-color-warning-light-3);
    color: #fff;
    border-radius: 5px;
    line-height: 20px;
    margin-left: 7px;
  }
}
.info-content {
  display: flex;
  align-items: center;
  padding: 10px 20px;
  &-right {
    flex: 1;
  }
  &-left {
    margin-right: 30px;
    .icon {
      cursor: pointer;
      object-fit: fill;
      width: 100px;
      height: 100px;
    }
  }
}
.video-content {
  padding: 10px 20px;
}
.order-content {
  padding: 10px 20px;
  .label {
    // text-align: end;
    // width: 100px !important;
    // margin-right: 20px;
  }
}
.el-col {
  align-items: baseline;
}

.el-col-8 {
  display: flex;
  margin-bottom: 10px;
  font-size: 13px;
  .label {
    flex-shrink: 0;
    width: 120px;
    color: #999;
    text-align: end;
    margin-right: 20px;
  }
  .content {
    flex-shrink: 0;
    width: calc(100% - 130px);
    word-break: break-all;
    white-space: pre-line;
  }
}
.el-col-12,
.el-col-16 {
  display: flex;
  margin-bottom: 10px;
  font-size: 13px;
  .label {
    flex-shrink: 0;
    width: 100px;
    color: #999;
    text-align: end;
    margin-right: 20px;
  }
  .content {
    flex-shrink: 0;
    max-width: calc(100% - 120px);
    word-break: break-all;
    word-break: break-all;
    white-space: pre-line;
  }
  .productLink {
    cursor: pointer;
    color: #02a7f0;
  }
}
.col-24 {
  align-items: flex-start;
  margin-bottom: 10px;
  width: 100%;
  font-size: 13px;
  .label {
    flex-shrink: 0;
    width: 120px;
    color: #999;
    text-align: end;
    margin-right: 20px;
  }
  .content {
    flex-shrink: 0;
    max-width: calc(100% - 120px);
    word-break: break-all;
    word-break: break-all;
    white-space: pre-line;
  }
  .tip {
    flex-shrink: 0;
    color: #7f7f7f;
  }
  .tip-text {
    line-break: anywhere;
    white-space: pre-line;
  }
}
</style>
