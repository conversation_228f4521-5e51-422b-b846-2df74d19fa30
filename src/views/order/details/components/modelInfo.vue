<template>
  <div class="info">
    <div class="product-info">
      <div class="info-head flex-between">
        <div>{{ type == 'shoot' ? '拍摄模特' : '意向模特' }}</div>
      </div>
      <template v-for="(item, i) in modelList" :key="item">
        <div class="info-content">
          <div class="info-content-left" @click="showViewer([item.modelPic])">
            <div v-if="type == 'shoot'" style="font-size: 14px; margin-bottom: 8px">
              {{
                i == modelList.length - 1 ? (modelList.length == 1 ? '拍摄模特' : '原拍摄模特') : '变更后模特'
              }}
              <span v-if="item.isRejectAfterSubmitModel" style="margin-left: 10px">(商家驳回)</span>
              <span v-else-if="item.rollbackId" style="margin-left: 10px">(订单回退)</span>
            </div>
            <div v-else style="font-size: 14px; margin-bottom: 8px">
              {{ i == modelList.length - 1 ? '首次选择' : `${modelList.length - i}次选择` }}
              <span v-if="item.isRejectAfterSubmitModel" style="margin-left: 10px">(商家驳回)</span>
              <span v-else-if="item.rollbackId" style="margin-left: 10px">(订单回退)</span>
            </div>

            <img class="icon" :src="$picUrl + item.modelPic" alt="" />
          </div>
          <div class="info-content-right">
            <el-row>
              <el-col :span="8">
                <div class="label">模特姓名：</div>
                <div class="content">
                  {{ item.name }}
                </div>
              </el-col>
              <el-col :span="8">
                <div class="label">国家：</div>
                <div class="content">
                  <biz-nation :value="item.nation" tag="text" />
                </div>
              </el-col>
              <el-col :span="8">
                <div class="label">性别：</div>
                <div class="content">
                  {{ item.sex == '1' ? '男' : '女' }}
                </div>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <div class="label">模特类型：</div>
                <div class="content">
                  <biz-model-type :value="item.type" tag="text" />
                </div>
              </el-col>
              <el-col :span="8">
                <div class="label">模特年龄层：</div>
                <div class="content">
                  <biz-model-ageGroup :value="item.ageGroup" tag="text" />
                </div>
              </el-col>
              <el-col :span="8">
                <div class="label">适用平台：</div>
                <div class="content">
                  <!-- <template v-for="dict in biz_model_platform" :key="dict.value"> -->
                  <span>{{ hanldePlatform(item.platform) }}</span>
                  <!-- <span v-if="dict.value == item.platform">{{ dict.label }}</span> -->
                  <!-- </template> -->
                </div>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <div class="label">模特评分：</div>
                <div class="content flex-start">
                  <span v-if="item.cooperationScore">{{ item.cooperationScore }}分</span>
                  (
                  <biz-model-cooperation :value="item.cooperation" tag="text" />
                  )
                </div>
              </el-col>
              <el-col :span="8">
                <div class="label">英文客服：</div>
                <div class="content">
                  {{ item.issueUserName }}
                </div>
              </el-col>
              <el-col :span="8" v-if="type == 'shoot'">
                <div class="label">选定时间：</div>
                <div class="content">
                  {{ item.selectedTime }}
                </div>
              </el-col>
            </el-row>
            <!-- <el-row v-if="type == 'shoot'">
              <el-col :span="8">
                <div class="label">排单类型：</div>
                <div class="content" v-if="item.scheduleType">
                  {{ item.scheduleType == '1' ? '排单' : '携带排单' }}
                </div>
              </el-col>
              <el-col :span="8" v-if="item.carryType">
                <div class="label">携带类型：</div>
                <div class="content">
                  {{ item.carryType == 1 ? '主携带' : '被携带' }}
                </div>
              </el-col>
              <el-col :span="8" v-if="item.scheduleType == '1'">
                <div class="label">佣金：</div>
                <div class="content" >
                  {{ item.commission }}
                  <template v-for="dict in bizCommissionUnit" :key="dict.value">
                    <span v-if="dict.value == item.commissionUnit">{{ dict.label }}</span>
                  </template>
                </div>
              </el-col>
            </el-row> -->
            <el-row>
              <div class="flex-start col-24" v-if="item.overstatement">
                <div class="label">超额说明：</div>
                <div class="content more-ell" style="--l: 5">
                  {{ item.overstatement }}
                </div>
              </div>
            </el-row>
            <el-row>
              <div class="flex-start col-24">
                <div class="label">擅长品类：</div>
                <div class="content more-ell" style="--l: 10">
                  {{ item.specialtyCategory }}
                </div>
              </div>
            </el-row>
            <el-row>
              <div class="flex-start col-24">
                <div class="label">模特标签：</div>
                <div class="content more-ell" style="--l: 10">
                  {{ item.modelTag }}
                </div>
              </div>
            </el-row>
          </div>
        </div>
      </template>
      <div style="text-align: center; padding: 50px 0" v-if="!modelList || modelList.length === 0">
        {{ type == 'shoot' ? '暂无拍摄模特' : '暂无意向模特' }}
      </div>
    </div>
  </div>
</template>

<script setup>
// import { picCountOptions, videoFormatOptions } from '@/views/order/list/data.js'
// import { payTypeMap } from '@/utils/dict'
import { useViewer } from '@/hooks/useViewer'
import { getTranslate } from '@/api/order/order'
const { showViewer } = useViewer()

const { proxy } = getCurrentInstance()
const { biz_model_platform } = proxy.useDict('biz_model_platform')
import { bizCommissionUnit } from '@/utils/dict'
const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
  type: {
    type: String,
    default: '',
  },
})

const orderVideoShootModelChangeVOS = computed(() => props.data.orderVideoShootModelChangeVOS)
const orderVideoIntentionModelChangeVOS = computed(() => props.data.orderVideoIntentionModelChangeVOS)
const orderVideoSimpleVO = computed(() => props.data.orderVideoSimpleVO)
const modelList = computed(() => {
  return props.type == 'shoot' ? orderVideoShootModelChangeVOS.value : orderVideoIntentionModelChangeVOS.value
})

const emits = defineEmits(['showHistoryRecord'])

const list = [1, 2]

const orderVO = computed(() => props.data.orderVO)
const orderVideoVO = computed(() => props.data.orderVideoVO)
const demandsTips = computed(() => {
  if (props.data?.orderVideoVO?.shootRequired) {
    return props.data.orderVideoVO.shootRequired.some(item => {
      return item.createTime !== item.updateTime
    })
  }
  return false
})

//触发历史变更记录
function doShowHistoryRecord() {
  emits('showHistoryRecord')
}

//使用平台
function hanldePlatform(platform) {
  const platformList = platform.split(',')
  const platformName = []
  for (let i = 0; i < platformList.length; i++) {
    const platformItem = biz_model_platform.value.find(item => item.value === platformList[i])
    if (platformItem) {
      platformName.push(platformItem.label)
    }
  }
  return platformName.join('、')
}

//翻译拍摄要求
const transformContentList = ref([])
const translateLoading = ref(false)
function doTranslate() {
  translateLoading.value = true
  let data = []
  if (orderVideoVO.value?.shootRequired) {
    orderVideoVO.value.shootRequired.forEach((item, index) => {
      data.push(item.content)
    })
  }
  getTranslate({ language: 1, wordList: data })
    .then(res => {
      transformContentList.value = res.data
      translateLoading.value = false
    })
    .finally(() => (translateLoading.value = false))
}
</script>

<style scoped lang="scss">
.product-info {
  margin-top: 20px;
  background-color: #fff;
  //   padding: 13px;
  border-radius: 4px;
  gap: 10px;
  position: relative;
  box-shadow: var(--el-box-shadow-light);
  border: 1px solid #e9e9e9;
}
.info-head {
  padding: 13px;
  background: #f9f9f9;
  border-bottom: 1px solid #e9e9e9;
}
.info-content {
  display: flex;
  padding: 10px 20px;
  &-right {
    margin-top: 30px;
    flex: 1;
  }
  &-left {
    margin-right: 30px;
    .icon {
      cursor: pointer;
      object-fit: fill;
      width: 140px;
      aspect-ratio: 3 / 4;
    }
  }
}
.video-content {
  padding: 10px 20px;
}
.order-content {
  padding: 10px 20px;
  .label {
    // text-align: end;
    // width: 100px !important;
    // margin-right: 20px;
  }
}
.el-col {
  align-items: baseline;
}

.el-col-8 {
  display: flex;
  margin-bottom: 15px;
  //   align-items: center;
  font-size: 13px;
  .label {
    flex-shrink: 0;
    width: 100px;
    color: #999;
    text-align: end;
    margin-right: 20px;
  }
  .content {
    flex-shrink: 0;
    width: calc(100% - 100px);
    word-break: break-all;
  }
}
.el-col-12 {
  display: flex;
  margin-bottom: 10px;
  font-size: 13px;
  .label {
    flex-shrink: 0;
    width: 100px;
    color: #999;
    text-align: end;
    margin-right: 20px;
  }
  .content {
    flex-shrink: 0;
    width: calc(100% - 100px);
    word-break: break-all;
  }
}
.col-24 {
  align-items: flex-start;
  margin-bottom: 10px;
  width: 100%;
  font-size: 13px;
  .label {
    flex-shrink: 0;
    width: 100px;
    color: #999;
    text-align: end;
    margin-right: 20px;
  }
  .content {
    flex-shrink: 0;
    width: calc(100% - 100px);
    word-break: break-all;
  }
  .tip {
    flex-shrink: 0;
    color: #7f7f7f;
  }
}
</style>
