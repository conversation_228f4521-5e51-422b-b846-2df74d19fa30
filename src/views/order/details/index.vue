<template>
  <div class="order-details-page">
    <OrderDetailsHeader :data="orderData" @action="handleAction" />

    <!-- 物流模块 -->
    <div
      class="order-logistics flex-between"
      v-if="orderVideoSimpleVO?.status === orderStatusMap['待完成'] && orderVideoSimpleVO?.isObject != 1"
    >
      <div class="flex-center">
        <img style="width: 42px; height: 35px; margin-right: 20px" src="@/assets/icons/svg/logistics.svg" />
        <div>
          <div>
            {{
              orderLogisticSimpleVOS && orderLogisticSimpleVOS.length > 0
                ? orderLogisticSimpleVOS[0].mainStatus
                : ''
            }}
          </div>
          <div>
            物流单号:{{
              orderLogisticSimpleVOS && orderLogisticSimpleVOS.length > 0
                ? orderLogisticSimpleVOS[0].number
                : ''
            }}
          </div>
        </div>
      </div>
      <el-button v-btn link type="primary" style="font-size: 16px" @click="handleChangeSelectVal">
        查看更多
      </el-button>
    </div>
    <VideoInfo
      :data="orderData"
      v-if="isShowVideoInfo"
      @showHistoryUploadRecord="doShowHistoryUploadRecord"
      @showHistoryClipRecord="doShowHistoryClipRecord"
    />

    <div class="order-btn">
      <el-radio-group v-model="selectVal" size="large" @change="handleSelect">
        <el-radio-button :value="1">基础信息</el-radio-button>
        <el-radio-button :value="2">拍摄模特</el-radio-button>
        <el-radio-button v-if="orderVideoSimpleVO?.isObject != 1" :value="3">发货记录</el-radio-button>
        <el-radio-button :value="4">流转记录</el-radio-button>
        <el-radio-button :value="5">退款记录</el-radio-button>
      </el-radio-group>
    </div>

    <ProductInfo
      :data="orderData"
      @showHistoryRecord="doShowHistoryRecord"
      v-if="selectVal === 1"
      v-loading="loading"
    />

    <div v-if="selectVal === 2">
      <ModelInfo :data="orderData" :type="'shoot'" />
      <ModelInfo :data="orderData" :type="'intention'" />
    </div>

    <!-- <Title style="margin: 12px 0">订单信息</Title> -->
    <!-- <div class="order-details">
      <Title style="margin: 12px 0">订单信息</Title>
      <div class="flex-center">
        <el-button v-btn link type="primary" style="font-size: 16px" @click="doShowHistoryRecord">
          历史变更记录
        </el-button>
        <div
          v-if="orderData?.orderVideoVO?.hasChangeLog"
          style="width: 10px; height: 10px; border-radius: 50%; background-color: red"
        ></div>
      </div>
    </div>

    <OrderDetailsInfo v-loading="loading" :data="orderData" /> -->

    <!-- <Title style="margin: 12px 0">寄件信息</Title> -->

    <OrderDetailsSendInfo
      v-if="selectVal === 3"
      v-loading="loading"
      :data="orderData"
      @success="getOrderDetails()"
    />

    <!-- :status="orderData?.orderVideoVO?.status"
      :isObject="orderData?.orderVideoVO?.isObject"
      :remark="orderData?.orderVideoVO?.shippingRemark" -->

    <Exchange
      :data="orderData"
      v-if="selectVal === 4"
      @showHistoryRecord="doShowHistoryRecord"
      @showFeedbackCase="doFeedbackCase"
    />

    <Record
      v-if="selectVal === 5"
      :recordList="orderData.orderVideoRefundVOS"
      :refundAmountTotal="orderData.refundAmountTotal"
    />

    <EditOrderPrice ref="EditOrderPriceRef" @success="getOrderDetails()" />
    <ApplyRefundDialog ref="ApplyRefundDialogRef" @success="getOrderDetails()" />
    <HistoryChangeOrder ref="HistoryChangeOrderRef" />
    <ConfirmDeliverGoods ref="ConfirmDeliverGoodsRef" @success="getOrderDetails()" />
    <FeedbackCase ref="FeedbackCaseRef" />
    <el-dialog
      v-model="showRollbackDialog"
      title="订单回退"
      align-center
      width="600px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="rollbackClose"
    >
      <div>
        <div style="font-weight: 600; font-size: 16px; color: #333">确认订单回退吗？</div>
        <div style="margin: 10px 0">回退后，订单将回到待匹配状态，请与商家充分沟通和确认后决定。</div>
        <el-form :model="rollbackForm" :rules="rollbackRules" ref="rollbackFormRef">
          <el-form-item label="回退原因" prop="rollbackReason">
            <el-input
              type="textarea"
              v-model="rollbackForm.rollbackReason"
              placeholder="请输入回退原因"
              maxlength="1000"
              show-word-limit
              :rows="8"
            />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="flex-center">
          <el-button type="primary" @click="doConfirmRollback">提交</el-button>
        </div>
      </template>
    </el-dialog>
    <HistoryUploadDialog ref="HistoryUploadDialogRef" />
    <HistoryClipRecord ref="HistoryClipRecordRef" />
  </div>
</template>

<script setup>
import HistoryClipRecord from '@/views/task/clip/components/historyClipRecord.vue'
import OrderDetailsHeader from '@/views/order/details/components/header.vue'
import HistoryUploadDialog from '@/views/task/clip/components/historyUploadDialog.vue'
import Title from '@/components/Public/title.vue'
import OrderDetailsInfo from '@/views/order/details/components/info.vue'
import OrderDetailsSendInfo from '@/views/order/details/components/sendInfo.vue'
import ConfirmDeliverGoods from '@/views/order/components/dialog/confirmDeliverGoods.vue'
import EditOrderPrice from '@/views/order/components/dialog/editOrderPrice.vue'
import FeedbackCase from '@/views/order/components/dialog/feedbackCase.vue'
import Record from '@/views/order/details/components/record.vue'

import VideoInfo from '@/views/order/details/components/videoInfo.vue'
import ProductInfo from '@/views/order/details/components/productInfo.vue'
import ModelInfo from '@/views/order/details/components/modelInfo.vue'
import Exchange from '@/views/order/details/components/exchange.vue'

import {
  orderDetails,
  videoOrderDetails,
  updateBaiduRate,
  rollbackOrder,
  getShippingInfo,
} from '@/api/order/order'
import { orderStatusMap } from '@/views/order/list/data.js'
import { ElMessage } from 'element-plus'
import useOrderApi from '@/hooks/useOrderApi'
import ApplyRefundDialog from '@/views/order/components/dialog/applyRefund.vue'
import HistoryChangeOrder from '@/views/order/components/dialog/historyChangeOrder.vue'
import { ref } from 'vue'

const route = useRoute()

const { handleCancelOrder, handleApplyRefund } = useOrderApi()

const EditOrderPriceRef = ref()
const orderData = ref({})
const shipInfoVO = ref()
const loading = ref(false)
const ApplyRefundDialogRef = ref(null)
const HistoryChangeOrderRef = ref(null)
const selectVal = ref(1)
const FeedbackCaseRef = ref(null)
const flag = ref(false)
const rollbackForm = ref({
  rollbackReason: '',
})
const rollbackFormRef = ref(null)
const rollbackRules = {
  rollbackReason: [
    { required: true, message: '请输入回退原因', trigger: 'change' },
    { validator: validateStartSpace, trigger: 'change' },
  ],
}
function validateStartSpace(rule, value, callback) {
  if (value.trim() == '') {
    callback(new Error('请输入回退原因'))
  } else {
    callback()
  }
}
//旧
// function getOrderDetails() {
//   if (!route.params.videoId) {
//     window.location.href = '/index'
//     return
//   }
//   loading.value = true
//   orderDetails(route.params.videoId)
//     .then(res => {
//       orderData.value = res.data
//       if (res.data.shipInfoVO) {
//         shipInfoVO.value = res.data.shipInfoVO
//       }
//     })
//     .finally(() => (loading.value = false))
// }

const orderVO = computed(() => orderData.value.orderVO)
const orderVideoVO = computed(() => orderData.value.orderVideoVO)
const orderSimpleVO = computed(() => orderData.value.orderSimpleVO)
const orderVideoSimpleVO = computed(() => orderData.value.orderVideoSimpleVO)
const orderVideoFlowNodeDiagramVOS = computed(() => orderData.value.orderVideoFlowNodeDiagramVOS)
const orderLogisticSimpleVOS = computed(() => orderData.value.orderLogisticSimpleVOS)
const isShowVideoInfo = computed(
  () =>
    (orderData.value?.orderFeedBackMaterialInfoSimpleVOS &&
      orderData.value?.orderFeedBackMaterialInfoSimpleVOS.length > 0) ||
    (orderData.value?.orderFeedBackSimpleVOS && orderData.value?.orderFeedBackSimpleVOS.length > 0)
)

const videoId = ref('')
//新
function getOrderDetails() {
  if (!route.params.videoId) {
    window.location.href = '/index'
    return
  }
  // getShippingflag()
  videoId.value = route.params.videoId
  loading.value = true
  videoOrderDetails(route.params.videoId)
    .then(res => {
      orderData.value = res.data
      if (res.data.shipInfoVO) {
        shipInfoVO.value = res.data.shipInfoVO
      }
    })
    .finally(() => (loading.value = false))
}
const showRollbackDialog = ref(false)
const ConfirmDeliverGoodsRef = ref(null)
function handleAction(btn) {
  if (btn === '取消订单') {
    // [orderData.value.orderVideoVO.id]
    handleCancelOrder({ orderNum: orderSimpleVO.value.orderNum }, () => getOrderDetails())
  } else if (btn === '修改订单费用') {
    EditOrderPriceRef.value?.open(orderVideoSimpleVO?.value.id, orderVideoSimpleVO.value)
    // orderData.value.orderVideoVO.commission,
    // orderData.value.orderVideoVO.commissionUnit
  } else if (btn === '申请退款') {
    ApplyRefundDialogRef.value?.open(
      orderVideoSimpleVO?.value.id,
      orderVideoSimpleVO?.value.picCount,
      orderVideoVO?.value?.orderVideoRefund?.refundStatus
    )
  } else if (btn === '填写物流单号') {
    ConfirmDeliverGoodsRef.value?.open(
      orderVideoSimpleVO?.value.id,
      orderVideoSimpleVO?.value.status === orderStatusMap['需发货']
    )
  } else if (btn === '订单回退') {
    showRollbackDialog.value = true
  }
}

function handleSelect(val) {
  selectVal.value = val
}
function handleQueryDetail() {
  ApplyRefundDialogRef.value?.close()
  getOrderDetails()
}
//历史订单记录
function doShowHistoryRecord() {
  HistoryChangeOrderRef.value?.open(orderVideoSimpleVO?.value.id)
}

//匹配情况反馈
function doFeedbackCase() {
  FeedbackCaseRef.value?.open(orderVideoSimpleVO?.value.id, true)
}

//关闭订单回退弹窗
function rollbackClose() {
  showRollbackDialog.value = false
  rollbackFormRef.value.resetFields()
}
//确认订单回退
function doConfirmRollback() {
  // showRollbackDialog.value = false
  // ElMessage.success('订单回退成功')
  // getOrderDetails()
  // rollbackReason = rollbackForm.value.rollbackReason
  rollbackFormRef.value.validate(valid => {
    if (valid) {
      rollbackOrder({
        cause: rollbackForm.value.rollbackReason,
        videoId: route.params.videoId,
      }).then(res => {
        showRollbackDialog.value = false
        ElMessage.success('订单回退成功')
        getOrderDetails()
      })
    }
  })
}

//查看物流信息
function handleChangeSelectVal() {
  selectVal.value = 3
}

const HistoryUploadDialogRef = ref()
function doShowHistoryUploadRecord(id) {
  HistoryUploadDialogRef.value.open(id)
}

const HistoryClipRecordRef = ref()
function doShowHistoryClipRecord() {
  HistoryClipRecordRef.value?.open(videoId.value)
}

// function getShippingflag() {
//   getShippingInfo(route.params.videoId).then(res => {
//     if (res.data && res.data.logisticFlag && res.data.logisticFlag == 1) {
//       flag.value = true
//     }
//   })
// }

getOrderDetails()
// getShippingflag()
</script>

<style scoped lang="scss">
.order-details-page {
  padding: 20px;
  min-height: calc(100vh - 84px);
  overflow-y: auto;
  .order-details {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .order-logistics {
    margin-top: 20px;
    background-color: #fff;
    padding: 13px;
    border-radius: 4px;
    gap: 10px;
    position: relative;
    box-shadow: var(--el-box-shadow-light);
  }
  .order-btn {
    .el-radio-button {
    }
    margin-top: 20px;
  }
}

:deep(.el-radio-group) {
  .el-radio-button {
    margin-right: 40px;
  }
  .el-radio-button__inner {
    box-shadow: 0 0 0 0;
    font-size: 16px;
    padding: 0;
    // margin-right: 40px;
    border: none;
  }
  .el-radio-button.is-active {
    .el-radio-button__inner {
      box-shadow: 0 0 0 0;
      border: none;
      color: var(--el-color-primary);
      background-color: #fff;
    }
  }
}
</style>
