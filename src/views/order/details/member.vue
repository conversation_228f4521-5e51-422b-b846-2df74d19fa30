<template>
  <div class="order-details-page">
    <div class="flex-start head-box">
      <div class="flex-column status-box">
        <img src="@/assets/icons/svg/money_icon.svg" />
        <span v-if="orderData?.status">{{ getVipOrderStatusText(orderData.status) }}</span>
      </div>
      <div class="content">
        <div>订单号：{{ orderData?.orderNum }}</div>

        <div style="margin-top: 8px">下单时间：{{ orderData?.orderTime }}</div>

        <div style="margin-top: 8px">会员编码：{{ orderData?.businessAccountDetailVO?.memberCode }}</div>
      </div>
      <div class="button">
        <template v-if="getVipOrderStatusText(orderData.status) === '待支付'">
          <div class="flex-end">
            <el-button v-btn v-hasPermi="['order:vip:cancel']" plain round @click="cancelOrder">
              取消订单
            </el-button>
          </div>
        </template>
      </div>
    </div>

    <Title style="margin: 12px 0">订单信息</Title>

    <div class="info-box" v-loading="loading">
      <el-row>
        <el-col :span="12">
          <div class="label">下单用户：</div>
          <div class="content">
            {{
              (orderData?.orderUserName || orderData?.orderUserNickName || '') +
              (orderData?.orderUserAccount ? `(${orderData?.orderUserAccount})` : '')
            }}
          </div>
        </el-col>
        <el-col :span="12">
          <div class="label">支付方式：</div>
          <div class="content">
            {{ orderData?.status == 1 ? '' : payTypeMap[orderData?.payType] }}
          </div>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <div class="label">套餐名称：</div>
          <div class="content">{{ getType(orderData?.packageType, 'label') }}</div>
        </el-col>
        <el-col :span="12">
          <div class="label">赠送活动：</div>
          <div class="content">
            {{ activityName }}
          </div>
        </el-col>
      </el-row>
      <!-- <el-row v-if="orderData?.seedCodeDiscount">
        <el-col :span="12">
          <div class="label">种草优惠：</div>
          <div class="content">
            {{ orderData.channelType == 7 ? '裂变' : '渠道' }}名称：{{
              orderData?.channelName + ' 会员折扣：' + orderData?.settleRage + '%'
            }}
          </div>
        </el-col>
        <el-col :span="12">
          <div class="label">优惠金额：</div>
          <div class="content">￥{{ orderData?.seedCodeDiscount }}</div>
        </el-col>
      </el-row> -->
      <el-row>
        <el-col :span="12">
          <div class="label">套餐金额：</div>
          <div class="content">
            <!-- {{ orderData?.status == 1 ? '' : '$' + getType(orderData?.packageType, '$') }} -->
            美元：${{ orderData?.packageAmount }}，百度汇率：{{ orderData?.currentExchangeRate || '-' }}
            <br />
            人民币：￥{{ orderData?.orderAmount }}
          </div>
        </el-col>
        <el-col :span="12">
          <div class="label">支付情况：</div>
          <div class="content">
            {{ orderData?.useBalance ? `钱包余额抵扣：￥${orderData.useBalance}，` : '' }}
            剩余支付：￥{{ orderData?.surplusAmount }}
            <br />
            实付金额：{{
              orderData?.auditStatus == 1
                ? orderData?.realPayAmountCurrency
                : orderData?.realPayAmountCurrency || '-'
            }}， 支付币种：{{ handleCurrency(orderData?.currency) }}
            <span v-if="orderData.payType == 7 || orderData.payType == 17">
              ，实付人民币：{{ orderData.auditStatus == 1 ? orderData?.realPayAmount : '-' }}
            </span>
          </div>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <div class="label">优惠信息：</div>
          <div class="content">
            <template v-if="orderData.orderDiscountDetailVOS && orderData.orderDiscountDetailVOS.length > 0">
              优惠类型：{{
                disountTypeList.find(item => item.value == orderData.orderDiscountDetailVOS[0].type)?.label
              }}
              <br />
              <template
                v-if="
                  orderData.orderDiscountDetailVOS[0].type == 3 ||
                  orderData.orderDiscountDetailVOS[0].type == 5
                "
              >
                {{ orderData.orderDiscountDetailVOS[0].channelType == 7 ? '裂变' : '渠道' }}名称：{{
                  orderData.orderDiscountDetailVOS[0].channelName
                }}{{ orderData.seedId ? ` (ID${orderData.seedId})` : '' }}
                <br />
              </template>
              会员折扣：{{ orderData.orderDiscountDetailVOS[0].discountRatio
              }}{{ orderData.memberDiscountType == 1 ? '元 (固定金额)' : '% (固定比例)' }}
              <br />
              优惠金额：￥{{ orderData.orderDiscountDetailVOS[0].discountAmount }}
            </template>
            <span v-else>-</span>
          </div>
        </el-col>
      </el-row>
    </div>

    <Title style="margin: 12px 0">开票信息</Title>

    <div class="info-box" v-loading="loading">
      <div class="no-data" v-if="!orderData?.orderInvoiceVO?.id">暂无开票信息</div>
      <template v-else>
        <el-row>
          <el-col :span="12">
            <div class="label">发票类型：</div>
            <div class="content">
              {{ orderData.orderInvoiceVO.invoiceType == 1 ? '增值税普通发票' : '形式发票' }}
            </div>
          </el-col>
        </el-row>
        <div v-if="orderData.orderInvoiceVO.invoiceType == 1">
          <el-row>
            <el-col :span="12">
              <div class="label">抬头类型：</div>
              <div class="content">{{ orderData.orderInvoiceVO.titleType == 1 ? '企业单位' : '' }}</div>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <div class="label">发票抬头：</div>
              <div class="content">{{ orderData.orderInvoiceVO.title }}</div>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <div class="label">企业税号：</div>
              <div class="content">{{ orderData.orderInvoiceVO.dutyParagraph }}</div>
            </el-col>
          </el-row>
        </div>
        <div v-else>
          <el-row>
            <el-col :span="12">
              <div class="label">公司名称：</div>
              <div class="content">{{ orderData.orderInvoiceVO.companyName }}</div>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <div class="label">公司地址：</div>
              <div class="content">{{ orderData.orderInvoiceVO.companyAddress }}</div>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <div class="label">联系电话：</div>
              <div class="content">{{ orderData.orderInvoiceVO.companyPhone || '无' }}</div>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <div class="label">联系人：</div>
              <div class="content">{{ orderData.orderInvoiceVO.companyContact || '无' }}</div>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <div class="label">附件：</div>
              <div class="content">
                <el-button
                  style="padding: 0"
                  v-if="orderData.orderInvoiceVO.attachmentObjectKey"
                  v-btn
                  link
                  type="primary"
                  @click="getFileUrl(orderData.orderInvoiceVO.attachmentObjectKey)"
                >
                  下载
                </el-button>
                <span v-else>无</span>
              </div>
            </el-col>
          </el-row>
        </div>
        <el-row>
          <el-col :span="12">
            <div class="label">发票内容：</div>
            <div class="content">
              <div class="content">{{ orderData.orderInvoiceVO.content }}</div>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <div class="label">发票备注：</div>
            <div class="content">{{ orderData.orderInvoiceVO.invoiceRemark || '无' }}</div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <div class="label">发票号：</div>
            <div class="content">{{ orderData.orderInvoiceVO.number || '-' }}</div>
          </el-col>
        </el-row>
      </template>
    </div>
  </div>
</template>

<script setup>
import OrderDetailsHeader from '@/views/order/details/components/header.vue'
import Title from '@/components/Public/title.vue'
import { vipListDetail, cancelMemberOrder } from '@/api/finance/vip.js'
import { getVipOrderStatusText, disountTypeList } from '@/views/order/vip/data.js'
import { payTypeMap } from '@/utils/dict.js'
import { downloadFile, downUrlFile } from '@/utils/index'
import { setMealTypeList } from '@/views/finance/data.js'
import { ElLoading, ElMessage, ElMessageBox } from 'element-plus'
import { invoiceTypeList, refundTypeList } from '@/views/finance/invoice/data.js'

const route = useRoute()

const { proxy } = getCurrentInstance()
const { sys_money_type } = proxy.useDict('sys_money_type')

const orderData = ref({})
const loading = ref(false)

function getOrderDetails() {
  if (!route.params.orderNum) {
    window.location.href = '/index'
    return
  }
  loading.value = true
  vipListDetail(route.params.orderNum)
    .then(res => {
      // console.log(res);
      orderData.value = res.data
    })
    .finally(() => (loading.value = false))
}

const activityName = computed(() => {
  if (orderData.value.presentedTime) {
    let s =
      orderData.value.presentedTimeType == 1
        ? '天'
        : orderData.value.presentedTimeType == 2
        ? '月'
        : orderData.value.presentedTimeType == 3
        ? '年'
        : '-'
    return `赠送${orderData.value.presentedTime}${s}`
  } else {
    return '-'
  }
})

function getType(val, key) {
  let type = setMealTypeList.find(item => item.value === val)
  if (type) {
    return type[key]
  }
  return ''
}

function handleCurrency(val) {
  if (val == '1') {
    return '人民币'
  }
  return sys_money_type.value.find(item => item.value == val)?.label || '-'
}

function cancelOrder() {
  ElMessageBox.confirm('确认取消订单？', '温馨提示', {
    autofocus: false,
    confirmButtonText: '确定',
    cancelButtonText: '再想想',
  })
    .then(() => {
      const el_loading = ElLoading.service({
        lock: true,
        text: '正在执行中，请稍后',
        background: 'rgba(0, 0, 0, 0.7)',
      })
      cancelMemberOrder(orderData.value.orderNum)
        .then(res => {
          ElMessage.success('取消成功')
          getOrderDetails()
        })
        .finally(() => el_loading.close())
    })
    .catch(() => {})
}

function getFileUrl(objectKey) {
  const fileSuffix = objectKey.substring(objectKey.lastIndexOf('/') + 1)
  downUrlFile(objectKey, fileSuffix)
}

getOrderDetails()
</script>

<style scoped lang="scss">
.order-details-page {
  padding: 20px;
  min-height: calc(100vh - 84px);
  overflow-y: auto;

  .head-box {
    background-color: #fff;
    padding: 13px;
    border-radius: 4px;
    gap: 10px;
    position: relative;
    box-shadow: var(--el-box-shadow-light);
    // box-shadow: 3px 3px 4px 0px #ddd;

    .status-box {
      padding: 8px 20px;
      border-radius: 4px;
      font-size: 16px;
      background-color: #f2f2f2;

      img {
        width: 40px;
        height: 40px;
      }

      span {
        color: #000;
      }
    }

    .content {
      font-size: 16px;

      &:first-child() {
        margin-bottom: 10px;
      }

      .red {
        color: var(--el-color-danger);
      }
    }

    .button {
      position: absolute;
      bottom: 13px;
      right: 13px;
    }

    .up-url {
      gap: 10px;
      color: var(--el-color-primary);

      div {
        max-width: 500px;
      }
    }
  }

  .info-box {
    padding: 20px;
    background-color: #fff;
    border-radius: 4px;
    font-size: 15px;
    box-shadow: var(--el-box-shadow-light);

    .el-col-12 {
      display: flex;
      margin-bottom: 10px;

      .label {
        flex-shrink: 0;
        width: 90px;
      }

      .content {
        flex-shrink: 0;
        width: calc(100% - 95px);
        word-break: break-all;
      }
    }
  }

  .no-data {
    height: 100px;
  }
}
</style>
