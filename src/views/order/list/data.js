/**
 * 订单状态
 */
const orderStatusList = [
  { label: '待支付', value: 1 },
  { label: '待审核', value: 2 },
  { label: '待确认', value: 3 },
  { label: '待匹配', value: 4 },
  { label: '需发货', value: 5 },
  { label: '待完成', value: 6 },
  { label: '需确认', value: 7 },
  { label: '已完成', value: 8 },
  { label: '交易关闭', value: 9 },
  // { label: '售后待审核', value: 9 },
  // { label: '已拒绝', value: 10 },
  // { label: '已取消', value: 11 },
  // { label: '售后中', value: 12 },
  // { label: '售后完成', value: 13 },
]
/**
 * 订单状态
 */
const orderStatusMap = {}
orderStatusList.forEach(item => {
  orderStatusMap[item.label] = item.value
  orderStatusMap[item.value] = item.label
})
/**
 * 订单退款状态
 */
const orderRefundStatusList = [
  { label: '退款待审核', value: 0 },
  { label: '退款中', value: 1 },
  { label: '已拒绝', value: 2 },
  { label: '已取消', value: 3 },
  { label: '退款成功', value: 4 },
]

const videoStyleList = [
  { label: 'Amazon', value: 0 },
  { label: 'TikTok', value: 1 },
  { label: 'APP/解说类', value: 2 },
]
/**
 * 订单退款状态
 */
const orderRefundStatusMap = {}
orderRefundStatusList.forEach(item => {
  orderRefundStatusMap[item.label] = item.value
  orderRefundStatusMap[item.value] = item.label
})
/**
 * 预选模特状态
 */
const preselectStatusList = [
  { label: '未对接', value: 0 },
  { label: '已对接', value: 1 },
  { label: '已选定', value: 2 },
  { label: '已淘汰（商家驳回）', value: 3 },
  { label: '已淘汰（客服淘汰）', value: 4 },
  { label: '已淘汰（MT不想要）', value: 5 },
  { label: '已淘汰（未被选中）', value: 6 },
  { label: '已淘汰（超时未选择意向）', value: 7 },
  { label: '已淘汰（订单回退）', value: 8 },
]
/**
 * 照片选配
 */
const picCountOptions = [
  { label: '2张/$10', value: 1 },
  { label: '5张/$20', value: 2 },
]
/**
 * 照片选配
 */
const picCountNumOptions = [
  { label: '2张', value: 1 },
  { label: '5张', value: 2 },
]
/**
 * 视频格式
 */
const videoFormatOptions = [
  { label: '横屏拍摄16:9', value: 1 },
  { label: '竖屏拍摄9:16', value: 2 },
]
//平台
const Platform = {
  Amazon: '0',
  Tiktok: '1',
  Other: '2',
  App: '3',
}
//退款类型
const refundTypeOptions = [
  {
    label: '补偿订单',
    value: 1,
  },
  {
    label: '取消订单',
    value: 2,
  },
  {
    label: '取消选配',
    value: 3,
  },
]
export const addressInfoMap = {
  // 美国
  7: [
    { label: 'Name', key: 'recipient', value: '' }, //（收件人姓名）
    { label: 'Street Address', key: 'detailAddress', value: '' }, //（街道地址，包括门牌号和街道名称）
    { label: 'City', key: 'city', value: '' }, //（城市）
    { label: 'State', key: 'state', value: '' }, //（州）
    { label: 'Postal code', key: 'zipcode', value: '' }, //（邮政编码）
    { label: 'Country', key: 'nation', value: '' }, //（ 国家）
  ],
  // 加拿大
  2: [
    { label: 'Name', key: 'recipient', value: '' }, //（收件人姓名）
    { label: 'Street Address', key: 'detailAddress', value: '' }, //（街道地址）
    { label: 'City', key: 'city', value: '' }, //（城市）
    { label: 'Province', key: 'state', value: '' }, //（省）
    { label: 'Postal code', key: 'zipcode', value: '' }, //（邮政编码）
    { label: 'Country', key: 'nation', value: '' }, //（ 国家）
  ],
  // 德国
  3: [
    { label: 'Empfängername', key: 'recipient', value: '' }, //（收件人姓名）
    { label: 'Straße und Hausnummer', key: 'detailAddress', value: '' }, //（街道和门牌号）
    { label: 'Ort', key: 'city', value: '' }, //（城市）
    { label: 'Bundesländer', key: 'state', value: '' }, //（省）
    { label: 'Postleitzahl', key: 'zipcode', value: '' }, //（邮政编码）
    { label: 'Land', key: 'nation', value: '' }, //（ 国家）
  ],
  // 法国
  4: [
    { label: 'Nom', key: 'recipient', value: '' }, //（收件人姓名）
    { label: 'Street Adresse', key: 'detailAddress', value: '' }, //（街道地址）
    { label: 'Ville', key: 'city', value: '' }, //（城市）
    { label: 'Département', key: 'state', value: '' }, //（省）
    { label: 'Code postal', key: 'zipcode', value: '' }, //（邮政编码）
    { label: 'Pays', key: 'nation', value: '' }, //（ 国家）
  ],
  // 意大利
  5: [
    { label: 'Nome del destinatario', key: 'recipient', value: '' }, //（收件人姓名）
    { label: 'Via e numero civico', key: 'detailAddress', value: '' }, //（街道名称和门牌号）
    { label: 'Città', key: 'city', value: '' }, //（城市）
    { label: 'Provincia', key: 'state', value: '' }, //（省）
    { label: 'CAP', key: 'zipcode', value: '' }, //（邮政编码）
    { label: 'Paese', key: 'nation', value: '' }, //（ 国家）
  ],
  // 西班牙
  6: [
    { label: 'Nombre del destinatario', key: 'recipient', value: '' }, //（收件人姓名）
    { label: 'Dirección (calle y número)', key: 'detailAddress', value: '' }, //（街道名称和门牌号）
    { label: 'Ciudad', key: 'city', value: '' }, //（城市）
    { label: 'Provincias', key: 'state', value: '' }, //（省）
    { label: 'Código postal', key: 'zipcode', value: '' }, //（邮政编码）
    { label: 'País', key: 'nation', value: '' }, //（ 国家）
  ],
  // 英国
  1: [
    { label: 'Recipient Name', key: 'recipient', value: '' }, //（收件人姓名）
    { label: 'House Number and Street Name', key: 'detailAddress', value: '' }, //（门牌号和街道名称）
    { label: 'City or Town', key: 'city', value: '' }, //（城市或镇）
    { label: 'Locality', key: 'state', value: '' }, //（局部区域）----选填
    { label: 'Postcode', key: 'zipcode', value: '' }, //（邮政编码）
    { label: 'Country', key: 'nation', value: '' }, //（ 国家）
  ],
}

export {
  orderStatusList,
  videoStyleList,
  orderStatusMap,
  orderRefundStatusList,
  orderRefundStatusMap,
  preselectStatusList,
  picCountOptions,
  picCountNumOptions,
  videoFormatOptions,
  Platform,
  refundTypeOptions,
}
