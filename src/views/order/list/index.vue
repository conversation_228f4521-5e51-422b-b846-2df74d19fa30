<template>
  <div class="order-list-page">
    <el-tabs v-model="curTab" class="tabs" @tab-change="handleTabChange">
      <el-tab-pane v-for="(tab, i) in tabList" :key="tab.value" :name="tab.value">
        <template #label>
          <div v-if="i">
            {{ tab.label }}
            {{ '(' }}
            <span style="color: red">{{ tab.number }}</span>
            {{ ')' }}
          </div>
          <div v-else>{{ tab.label }}</div>
        </template>
      </el-tab-pane>
    </el-tabs>

    <div class="search-box">
      <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="88px" @submit.prevent>
        <el-form-item label="搜索" prop="val">
          <el-input v-model="queryParams.val" clearable style="width: 300px" placeholder="请输入关键字">
            <!-- <template #prepend>
              <el-select v-model="queryParams.select" placeholder="请选择" clearable style="width: 100px">
                <el-option
                  v-for="item in searchSelectList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </template> -->
          </el-input>
        </el-form-item>
        <el-form-item label="订单运营" prop="createOrderUserName">
          <el-select
            v-model="queryParams.createOrderUserName"
            placeholder="请选择"
            filterable
            clearable
            style="width: 180px"
            :loading="operateLoading"
            @visible-change="getOperatetList"
          >
            <el-option
              v-for="item in operateList"
              :key="item.id"
              :label="item.name + '/' + item.nickName"
              :value="item.name"
            />
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="平台" prop="platform">
          <el-select
            v-model="queryParams.platform"
            placeholder="请选择"
            multiple
            collapse-tags
            collapse-tags-tooltip
            clearable
            style="width: 180px"
          >
            <el-option
              v-for="item in biz_model_platform"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item> -->
        <!-- <el-form-item label="拍摄模特" prop="shootModelId">
          <el-select
            v-model="queryParams.shootModelId"
            placeholder="请选择"
            multiple
            collapse-tags
            collapse-tags-tooltip
            clearable
            style="width: 180px"
          >
            <el-option v-for="item in shootModelList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item> -->
        <el-form-item label="中文部客服" prop="contactId">
          <el-select
            :reserve-keyword="false"
            v-model="queryParams.contactId"
            placeholder="请选择"
            multiple
            filterable
            collapse-tags
            collapse-tags-tooltip
            clearable
            style="width: 180px"
          >
            <el-option v-for="item in contactList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="英文部客服" prop="issueId" v-if="curTab != orderStatusMap['待确认']">
          <el-select
            :reserve-keyword="false"
            v-model="queryParams.issueId"
            placeholder="请选择"
            multiple
            filterable
            collapse-tags
            collapse-tags-tooltip
            clearable
            style="width: 180px"
          >
            <el-option v-for="item in issueList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="意向模特" prop="intentionModelIds" v-if="curTab == orderStatusMap['待匹配']">
          <el-select
            :reserve-keyword="false"
            v-model="queryParams.intentionModelIds"
            placeholder="请选择"
            multiple
            filterable
            collapse-tags
            collapse-tags-tooltip
            clearable
            style="width: 180px"
          >
            <el-option
              v-for="item in intentionModelList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="通品"
          label-width="50px"
          prop="isGunds"
          v-if="curTab != orderStatusMap['待确认']"
        >
          <el-select
            v-model="queryParams.isGunds"
            multiple
            collapse-tags
            collapse-tags-tooltip
            clearable
            placeholder="请选择"
            style="width: 180px"
          >
            <!-- <el-option label="全部" :value="" /> -->
            <el-option label="通品" :value="1" />
            <el-option label="非通品" :value="0" />
            <el-option label="暂未选择" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="关闭订单时状态" label-width="110px" prop="closeOrderStatus" v-if="curTab === ''">
          <el-select
            v-model="queryParams.closeOrderStatus"
            placeholder="请选择"
            clearable
            style="width: 180px"
          >
            <el-option label="需发货/待完成" :value="1" />
          </el-select>
        </el-form-item>
        <el-form-item label="订单状态" prop="status" v-if="curTab === ''">
          <el-select
            v-model="queryParams.status"
            placeholder="请选择"
            multiple
            collapse-tags
            collapse-tags-tooltip
            clearable
            style="width: 180px"
          >
            <el-option
              v-for="item in orderStatusList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <el-checkbox-group
            v-model="queryParams.newStatusDays"
            @change="checkboxLimit($event, queryParams.newStatusDays)"
            style="margin-left: 10px"
          >
            <el-checkbox-button value="0">0天</el-checkbox-button>
            <el-checkbox-button value="3">1-3天</el-checkbox-button>
            <el-checkbox-button value="4">4-7天</el-checkbox-button>
            <el-checkbox-button value="7">7天以上</el-checkbox-button>
            <el-checkbox-button value="14">14天以上</el-checkbox-button>
            <el-checkbox-button value="30">30天以上</el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="下单时间" style="width: 400px">
          <el-date-picker
            v-model="queryParams.placeOrderTime"
            format="YYYY/M/D HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="datetimerange"
            range-separator="-"
            unlink-panels
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
            :shortcuts="shortcuts"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="照片数量" v-if="curTab === ''">
          <el-select style="width: 120px" clearable v-model="queryParams.hasPicCount" placeholder="请选择">
            <el-option label="有" :value="true">有</el-option>
            <el-option label="无" :value="false">无</el-option>
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="下单运营">
          <el-select
            v-model="queryParams.orderUserId"
            placeholder="请选择"
            multiple
            filterable
            collapse-tags
            collapse-tags-tooltip
            clearable
            style="width: 180px"
          >
            <el-option v-for="item in orderUserList" :key="item.id" :label="item.nickName" :value="item.id" />
          </el-select>
        </el-form-item> -->
        <el-form-item
          label="确认提交时间"
          label-width="120px"
          style="width: 400px"
          v-if="curTab === orderStatusMap['需发货']"
        >
          <template #label>
            <div class="flex-end">
              <span>确认提交时间</span>
              <el-tooltip effect="dark" content="提交确认模特给商家的时间" placement="top">
                <el-icon color="rgb(155, 166, 186)" style="margin-left: 3px">
                  <QuestionFilled />
                </el-icon>
              </el-tooltip>
            </div>
          </template>
          <el-date-picker
            v-model="queryParams.confirmTime"
            format="YYYY/M/D HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="datetimerange"
            range-separator="-"
            unlink-panels
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
            :shortcuts="shortcuts"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="支付时间" style="width: 400px">
          <el-date-picker
            v-model="queryParams.playTime"
            format="YYYY/M/D HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="datetimerange"
            range-separator="-"
            unlink-panels
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
            :shortcuts="shortcuts"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="支付方式" v-if="curTab != orderStatusMap['待确认']">
          <el-select
            v-model="queryParams.payType"
            placeholder="请选择"
            multiple
            collapse-tags
            collapse-tags-tooltip
            clearable
            style="width: 180px"
          >
            <el-option
              v-for="item in payTypeSelectList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="物流状态"
          prop="logisticMainStatus"
          v-if="curTab === '' || curTab === orderStatusMap['待完成'] || curTab === orderStatusMap['需确认']"
        >
          <el-select
            v-model="queryParams.logisticMainStatus"
            placeholder="请选择"
            multiple
            collapse-tags
            collapse-tags-tooltip
            clearable
            style="width: 180px"
          >
            <el-option
              v-for="item in logisticsStatus"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="添加预选人"
          prop="preselectAddUserName"
          v-if="curTab === '' || curTab === orderStatusMap['待匹配']"
        >
          <el-select
            v-model="queryParams.preselectAddUserName"
            placeholder="请选择"
            multiple
            filterable
            collapse-tags
            collapse-tags-tooltip
            clearable
            style="width: 180px"
          >
            <el-option v-for="item in preselectUserList" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item
          label="预选模特"
          prop="preselectModelId"
          v-if="curTab === '' || curTab === orderStatusMap['待匹配']"
        >
          <!-- <SelectLoad
            style="width: 100%"
            v-model="queryParams.preselectModelId"
            :request="selectListPreselectModel"
            :requestCallback="res => res.data"
            multiple
            keyValue="id"
            keyLabel="name"
            keyWord="name"
          >
            <template v-slot="{ row }">
              {{ row.name + `(${row.id})` }}
            </template>
          </SelectLoad> -->
          <el-select
            v-model="queryParams.preselectModelId"
            placeholder="请选择"
            multiple
            filterable
            collapse-tags
            collapse-tags-tooltip
            clearable
            style="width: 180px"
            :loading="preselectLoading"
            @visible-change="getOperatetList"
          >
            <el-option
              v-for="item in preselectModelList"
              :key="item.id"
              :label="item.name + `(${item.account})`"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="模特类型"
          prop="preselectStatus"
          v-if="curTab == '' || curTab === orderStatusMap['待匹配']"
        >
          <el-select v-model="queryParams.modelType" placeholder="请选择" clearable style="width: 180px">
            <el-option
              v-for="item in biz_model_type"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
            <el-option label="影/素都可以" value="3" />
          </el-select>
        </el-form-item>
        <el-form-item
          label="拍摄国家"
          prop="shootingCountry"
          v-if="curTab === '' || curTab === orderStatusMap['待匹配']"
        >
          <el-select
            v-model="queryParams.shootingCountry"
            placeholder="请选择"
            multiple
            collapse-tags
            collapse-tags-tooltip
            clearable
            style="width: 180px"
          >
            <el-option v-for="item in biz_nation" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item
          label="预选状态"
          prop="preselectStatus"
          v-if="curTab === '' || curTab === orderStatusMap['待匹配']"
        >
          <el-select
            v-model="queryParams.preselectStatus"
            placeholder="请选择"
            multiple
            collapse-tags
            collapse-tags-tooltip
            clearable
            style="width: 180px"
          >
            <el-option
              v-for="item in preselectStatusList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="催单状态" prop="reminderStatus" v-if="curTab === orderStatusMap['待完成']">
          <el-select
            v-model="queryParams.reminderStatus"
            placeholder="请选择"
            multiple
            collapse-tags
            collapse-tags-tooltip
            clearable
            style="width: 180px"
          >
            <el-option label="未处理" value="1"></el-option>
            <el-option label="已处理" value="2"></el-option>
            <!-- <el-option
              v-for="item in urgeOrderList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            /> -->
          </el-select>
        </el-form-item>
        <el-form-item
          label="匹配情况反馈"
          label-width="100px"
          v-if="curTab === '' || curTab === orderStatusMap['待匹配']"
        >
          <el-select
            v-model="queryParams.replyContent"
            placeholder="请选择"
            multiple
            collapse-tags
            collapse-tags-tooltip
            clearable
            style="width: 180px"
          >
            <el-option label="商家同意" value="1"></el-option>
            <el-option label="商家不同意" value="2"></el-option>
            <el-option label="待商家反馈" value="0"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="匹配状态"
          label-width="100px"
          v-if="curTab === '' || curTab === orderStatusMap['待匹配']"
        >
          <el-select v-model="queryParams.matchStatus" placeholder="请选择" clearable style="width: 180px">
            <el-option label="暂停匹配" :value="2"></el-option>
            <el-option label="匹配中" :value="1"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="添加预选时间" label-width="100px" v-if="curTab === orderStatusMap['待匹配']">
          <el-checkbox-group
            v-model="queryParams.addPreselectTime"
            @change="checkboxLimit($event, queryParams.addPreselectTime)"
            style="margin-left: 10px"
          >
            <el-checkbox-button v-for="item in timeOptions" :value="item.value">
              {{ item.label }}
            </el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="开始匹配时间" label-width="100px" v-if="curTab === orderStatusMap['待匹配']">
          <el-checkbox-group
            v-model="queryParams.matchStartTimes"
            style="margin-left: 10px"
            @change="handleChangeMathTimes"
          >
            <el-checkbox-button v-for="item in timeOptions" :value="item.value">
              {{ item.label }}
            </el-checkbox-button>
            <el-checkbox-button :value="99">自定义</el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item v-if="curTab === orderStatusMap['待匹配'] && handleMatchTime">
          <el-date-picker
            v-model="queryParams.beforeStatusTime"
            format="YYYY/M/D"
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            unlink-panels
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 250px"
          />
        </el-form-item>
        <el-form-item
          label="下载视频"
          prop="downloadStatus"
          v-if="curTab === '' || curTab === orderStatusMap['待完成'] || curTab === orderStatusMap['需确认']"
        >
          <el-select v-model="queryParams.downloadStatus" placeholder="请选择" clearable style="width: 120px">
            <el-option label="未下载" value="0"></el-option>
            <el-option label="已下载" value="1"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="确认收货"
          prop="receipt"
          v-if="curTab === '' || curTab === orderStatusMap['待完成'] || curTab === orderStatusMap['需确认']"
        >
          <el-select v-model="queryParams.receipt" placeholder="请选择" clearable style="width: 120px">
            <el-option label="未收货" value="0"></el-option>
            <el-option label="已收货" value="1"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="上传需求提交日期"
          prop="uploadLinkTime"
          label-width="150px"
          v-if="curTab === '' || curTab === orderStatusMap['已完成']"
          style="width: 450px"
        >
          <el-date-picker
            style="width: 350px"
            v-model="queryParams.uploadLinkTime"
            format="YYYY/M/D HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="datetimerange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
            :shortcuts="shortcuts"
          ></el-date-picker>
        </el-form-item>
        <el-form-item
          label="拍摄模特"
          prop="receipt"
          style="align-items: center"
          v-if="
            curTab === orderStatusMap['待完成'] ||
            curTab === orderStatusMap['需确认'] ||
            curTab === orderStatusMap['需发货'] ||
            curTab === ''
          "
        >
          <el-select
            filterable
            placeholder="请选择"
            v-model="queryParams.shootModelId"
            clearable
            style="width: 150px"
          >
            <el-option v-for="item in shootModelList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
          <div style="margin-left: 10px" v-if="curTab != orderStatusMap['需发货']">
            <el-checkbox
              true-value="1"
              false-value="0"
              size="large"
              v-model="queryParams.includeFamily"
              label="包含家庭成员"
            />
          </div>
          <div style="margin-left: 10px" v-if="curTab === ''">
            <el-checkbox
              true-value="true"
              false-value="false"
              size="large"
              v-model="queryParams.modelWaitOrder"
              label="模特待完成订单"
            />
          </div>
        </el-form-item>
        <el-form-item label="确认签收时间" label-width="100px" v-if="curTab === orderStatusMap['待完成']">
          <el-checkbox-group
            v-model="queryParams.confirmReceiptTimes"
            style="margin-left: 10px"
            @change="handleChangeReceiptTimes"
          >
            <el-checkbox-button v-for="item in timeOptions" :value="item.value">
              {{ item.label }}
            </el-checkbox-button>
            <el-checkbox-button :value="99">自定义</el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item v-if="curTab === orderStatusMap['待完成'] && handleMatchTimeV1">
          <el-date-picker
            v-model="queryParams.beforeConfirmReceiptTime"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="此日期前确认签收"
          />
        </el-form-item>
        <el-form-item
          label="照顾单"
          prop="care"
          v-if="curTab === '' || curTab === orderStatusMap['待匹配'] || curTab === orderStatusMap['待完成']"
        >
          <el-select v-model="queryParams.care" placeholder="请选择" clearable style="width: 180px">
            <el-option :key="1" label="是" :value="1">是</el-option>
            <el-option :key="0" label="否" :value="0">否</el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="完成时间" style="width: 400px" v-if="curTab === orderStatusMap['已完成']">
          <el-date-picker
            v-model="queryParams.confirmTime"
            format="YYYY/M/D HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="datetimerange"
            range-separator="-"
            unlink-panels
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
            :shortcuts="shortcuts"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button v-btn type="primary" icon="Search" native-type="submit" @click="onQuery">搜索</el-button>
          <el-button v-btn icon="Refresh" plain @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- <div
      class="flex-end"
      style="margin-bottom: 10px"
      v-if="curTab == '' || curTab == orderStatusMap['待匹配']"
    >
      <el-button
        v-btn
        style="margin-left: 3px"
        plain
        icon="Edit"
        :disabled="!multipleSelectionComputed"
        @click="handleBatchEdit"
        v-hasPermi="['order:manage:batch-submit']"
      >
        批量提交预选模特
      </el-button>
    </div> -->
    <div class="flex-between" style="margin-bottom: 10px">
      <div class="flex-start">
        <SortButton
          v-if="curTab !== '' && curTab !== orderStatusMap['已完成']"
          v-model="queryParams.statusTimeSort"
          style="margin-right: 10px"
          :loading="loading"
          @change="onQuery"
        >
          状态天数
        </SortButton>
        <!-- <DownloadBtn
          v-if="checkPermi(['order:manage:export'])"
          type="success"
          plain
          icon="Download"
          url="/order/order/export"
          :params="handleParams()"
          fileName="订单.xlsx"
        /> -->
        <!-- <template
          v-if="
            checkPermi(['order:manage:create-work-order']) &&
            (curTab === orderStatusMap['待完成'] || curTab === orderStatusMap['需确认'])
          "
        >
          <el-button v-btn plain type="primary" @click="openCreateWork">创建工单</el-button>
        </template> -->
        <!-- <template v-if="curTab1">
          <el-button
            v-btn
            plain
            type="primary"
            @click="openCreateWork"
            v-hasPermi="['order:manage:create-work-order']"
          >
            创建工单
          </el-button>
        </template> -->
        <div class="video-statistics">视频数量：{{ videoCount }}条</div>

        <div style="margin-left: 50px" v-if="curTab != ''">
          <el-checkbox size="large" v-model="queryParams.aboutMe" @change="onQuery" label="与我相关的订单" />
        </div>
      </div>
      <!-- <el-pagination
        background
        @size-change="pageChange({ pageNum: 1, pageSize: $event })"
        @current-change="pageChange({ pageNum: $event, pageSize })"
        :current-page="pageNum"
        :page-size="pageSize"
        :page-sizes="pageSizes"
        layout="total, prev, pager, next, jumper"
        :total="total"
      /> -->
    </div>
    <!-- </div> -->

    <div v-loading="loading">
      <el-empty v-if="!tableData.length" description="暂无数据" :image-size="80"></el-empty>

      <template v-for="(item, i) in tableData">
        <OrderListItem
          v-if="defer(i)"
          :key="item.orderNum"
          :data="item"
          @action="handleButtonAction"
          @selection-change="handleSelectionChange($event, i)"
          @hover="handleModelHover"
        />
      </template>
    </div>
    <div class="flex-end" style="margin: 10px 0 60px">
      <PaginationFloatBar
        :current-page="pageNum"
        :page-size="pageSize"
        @update:current-page="handlePageChange"
        :total="total"
      >
        <DownloadBtn
          v-if="checkPermi(['order:manage:export'])"
          type="success"
          plain
          icon="Download"
          url="/order/order/export"
          :params="handleParams()"
          fileName="订单.xlsx"
        />
      </PaginationFloatBar>
      <!-- <el-pagination
        background
        @size-change="pageChange({ pageNum: 1, pageSize: $event })"
        @current-change="pageChange({ pageNum: $event, pageSize })"
        :current-page="pageNum"
        :page-size="pageSize"
        :page-sizes="pageSizes"
        layout="total, prev, pager, next, jumper"
        :total="total"
      /> -->
    </div>

    <!-- <el-backtop style="z-index: 1000" :bottom="100" target=".main-container">
      <div
        class="flex-center"
        style="
          height: 100%;
          width: 100%;
          border-radius: 50%;
          background-color: var(--el-bg-color-overlay);
          box-shadow: var(--el-box-shadow-lighter);
          text-align: center;
          line-height: 40px;
          color: #1989fa;
        "
      >
        <el-icon><Top /></el-icon>
      </div>
    </el-backtop> -->

    <EditOrderDialog ref="EditOrderDialogRef" @success="handleQuery" />
    <AuditOrderDrawer
      ref="AuditOrderDrawerRef"
      @success="handleQuery"
      @viewRejectRecord="id => RejectRecordRef?.open(id)"
    />
    <CropperDialog
      ref="CropperDialogRef"
      :img="imgFile"
      :fixed-number-arr="[[2, 2]]"
      :previews-width="180"
      :is-show-action="false"
      @confirm="onConfirmCropper"
    />
    <ApplyRefundDialog ref="ApplyRefundDialogRef" @success="handleQuery" />
    <AddPreselectionModel ref="AddPreselectionModelRef" @change="handleQuery" />
    <FeedbackCase ref="FeedbackCaseRef" />
    <MarkersOrder ref="MarkersOrderRef" @previews="openPreviews" @success="handleQuery" />
    <ConfirmDeliverGoods ref="ConfirmDeliverGoodsRef" @success="handleQuery" />
    <FeedbackLinkList ref="FeedbackLinkListRef" @success="handleQuery" />
    <FeedbackLinkToMerchant ref="FeedbackLinkToMerchantRef" @success="handleQuery" />
    <CreateWorkOrder ref="CreateWorkOrderRef" @success="handleQuery" />
    <OrderRemark ref="OrderRemarkRef" @success="handleQuery" />

    <ModelInfoPopover ref="ModelInfoPopoverRef" />
    <ProductMoreInfo ref="ProductMoreInfoRef" />
    <PauseMatch ref="PauseMatchRef" @success="handleQuery" />
    <UploadMaterial ref="UploadMaterialRef" @success="handleQuery" />
    <UploadVideo ref="UoloadVideoRef" @success="handleQuery" />
    <ChangeRate ref="ChangeRateRef" @success="handleQuery" :requestApi="updateBaiduRate" />

    <PreselectionDialog ref="PreselectionDialogRef" @success="handleQuery" :showMoney="false" />

    <DragUploadDialog
      ref="DragUploadDialogRef"
      title="上传产品图"
      :limit="1"
      :multiple="false"
      :auto-upload="false"
      @success="uploadSuccess"
      bucketType="order"
    />

    <CancelOrderHint ref="CancelOrderHintRef" />
    <CancelOrderDialog ref="CancelOrderDialogRef" @success="handleQuery" />

    <RejectRecord ref="RejectRecordRef" />
  </div>
</template>

<script setup>
// import SelectLoad from '@/components/Select/SelectLoad'
import DownloadBtn from '@/components/Button/DownloadBtn'
import SortButton from '@/components/Button/SortButton.vue'
import OrderListItem from '@/views/order/list/orderListItem.vue'
import EditOrderDialog from '@/views/order/components/dialog/editOrder.vue'
import AuditOrderDrawer from '@/views/order/components/dialog/auditOrder.vue'
import RejectRecord from '@/views/order/components/dialog/rejectRecord.vue'
import ApplyRefundDialog from '@/views/order/components/dialog/applyRefund.vue'
import AddPreselectionModel from '@/views/order/components/dialog/addPreselectionModel.vue'
import FeedbackCase from '@/views/order/components/dialog/feedbackCase.vue'
import MarkersOrder from '@/views/order/components/dialog/markersOrder.vue'
import ConfirmDeliverGoods from '@/views/order/components/dialog/confirmDeliverGoods.vue'
import FeedbackLinkList from '@/views/order/components/dialog/feedbackLinkList.vue'
import FeedbackLinkToMerchant from '@/views/order/components/dialog/feedbackLinkToMerchant.vue'
import CreateWorkOrder from '@/views/order/components/dialog/createWorkOrder.vue'
import OrderRemark from '@/views/order/components/dialog/orderRemark.vue'
import ProductMoreInfo from '@/views/order/components/dialog/productMoreInfo.vue'
import PauseMatch from '@/views/order/components/dialog/pauseMatch.vue'
import CropperDialog from '@/components/Cropper/cropperDialog'
import ModelInfoPopover from '@/views/model/ModelInfoPopover.vue'
import UploadMaterial from '@/views/order/components/dialog/uploadMaterial.vue'
import UploadVideo from '@/views/order/components/dialog/uploadVideo.vue'
import ChangeRate from '@/views/order/components/dialog/changeRate.vue'
import PreselectionDialog from '@/views/task/preselection/components/preselectionDialog.vue'
import CancelOrderHint from '@/views/order/components/dialog/cancelOrderHint.vue'
import CancelOrderDialog from '@/views/order/components/dialog/cancelOrderDialog.vue'
import {
  selectListContact,
  selectListIssue,
  selectOrderUser,
  selectListPreselectModel,
  selectListPreselectUser,
  selectListShootModel,
  selectListOrderUser,
  selectIntentionModelSelect,
} from '@/api/order/select'
import {
  getOrderList,
  uploadProductImg,
  getTabsCount,
  getVideoCount,
  checkPreselectModel,
  orderReminder,
  confirmReceipt,
  updateBaiduRate,
  cancelOrder,
  checkOrderMerge,
} from '@/api/order/order'
import { timeOptions } from '@/views/task/preselection/index.js'
import { continueMatch } from '@/api/order/preselection'
import { uploadCloudFile } from '@/api/index'
import { orderStatusList, orderStatusMap, preselectStatusList } from '@/views/order/list/data.js'
import { payTypeSelectList, logisticsStatus } from '@/utils/dict'
import { useDefer } from '@/hooks/useDefer'
import useOrderApi from '@/hooks/useOrderApi'
import { ElMessageBox, ElMessage } from 'element-plus'
import { useViewer } from '@/hooks/useViewer'
import { checkPermi } from '@/utils/permission'
import useUserStore from '@/store/modules/user'

const { showViewer } = useViewer()
// const urgeOrderList = [
//   { label: '未处理', value: 1 },
//   { label: '已确认', value: 2 },
//   { label: '已完成', value: 3 },
// ]

const ChangeRateRef = ref(null)

const { defer, updateDefer } = useDefer()

const { handleCancelOrder, handleConfirmSubmit, handleUploadVideo } = useOrderApi()

const router = useRouter()
const route = useRoute()
const store = useUserStore()

const { proxy } = getCurrentInstance()

const { biz_model_type, biz_nation } = proxy.useDict('biz_model_type', 'biz_nation')

const EditOrderDialogRef = ref()
const AuditOrderDrawerRef = ref()

const CropperDialogRef = ref()
const ApplyRefundDialogRef = ref()
const AddPreselectionModelRef = ref()
const FeedbackCaseRef = ref()
const MarkersOrderRef = ref()
const ConfirmDeliverGoodsRef = ref()
const FeedbackLinkListRef = ref()
const FeedbackLinkToMerchantRef = ref()
const CreateWorkOrderRef = ref()
const OrderRemarkRef = ref()
const ProductMoreInfoRef = ref()
const ModelInfoPopoverRef = ref()
const PauseMatchRef = ref()
const UploadMaterialRef = ref()
const UoloadVideoRef = ref()
const PreselectionDialogRef = ref()
const DragUploadDialogRef = ref()
const CancelOrderHintRef = ref()
const CancelOrderDialogRef = ref()
const RejectRecordRef = ref()

const shortcuts = [
  {
    text: '今天',
    value: () => {
      const start = new Date()
      start.setHours(0, 0, 0, 0)
      const end = new Date()
      end.setHours(23, 59, 59, 59)
      return [start, end]
    },
  },
]

const curTab = ref('')
const tabList = ref([
  { label: '所有订单', value: '' },
  { label: '待确认', value: orderStatusMap['待确认'], number: 0 },
  { label: '待匹配', value: orderStatusMap['待匹配'], number: 0 },
  { label: '需发货', value: orderStatusMap['需发货'], number: 0 },
  { label: '待完成', value: orderStatusMap['待完成'], number: 0 },
  { label: '需确认', value: orderStatusMap['需确认'], number: 0 },
  { label: '已完成', value: orderStatusMap['已完成'], number: 0 },
  // { label: '待上传', value: 14, number: 0 },
])
// const searchSelectList = [
//   { label: '订单号', value: 'orderNum' },
//   { label: '商家名称', value: 'merchantName' },
//   { label: '视频编码', value: 'videoCode' },
//   { label: '产品名称', value: 'productName' },
//   { label: '产品链接', value: 'productLink' },
// ]
const shootModelList = ref([])
const contactList = ref([])
const issueList = ref([])
const preselectModelList = ref([])
const preselectUserList = ref([])
const orderUserList = ref([])
const intentionModelList = ref([])

const queryParams = ref({
  val: '',
  care: '',
  select: 'videoCode',
  aboutMe: false,
  platform: [],
  shootModelId: '',
  contactId: [],
  issueId: [],
  status: [],
  closeOrderStatus: '',
  isGunds: [],
  statusDays: [],
  newStatusDays: [],
  statusTimeSort: '',
  downloadStatus: '',
  receipt: '',
  modelType: '',
  shootingCountry: [],
  placeOrderTime: [],
  orderUserId: [],
  confirmTime: [],
  playTime: [],
  uploadLinkTime: [],
  payType: [],
  logisticMainStatus: [],
  preselectAddUserName: [],
  preselectModelId: [],
  preselectStatus: [],
  reminderStatus: [],
  replyContent: [],
  matchStatus: '',
  addPreselectTime: [],
  matchStartTimes: [],
  confirmReceiptTimes: [],
  beforeStatusTime: [],
  intentionModelIds: [],
  beforeConfirmReceiptTime: '',
  includeFamily: '0',
  hasPicCount: '',
  modelWaitOrder: '',
  createOrderUserName: '',
})

const imgFile = ref({})

const multipleSelection = ref([])
const tableData = ref([])
const loading = ref(false)
const pageNum = ref(1)
const pageSize = ref(50)
const pageSizes = [10, 20, 30, 50]
const total = ref(0)
const videoCount = ref(0)
let randomStr = ''

// const multipleSelectionComputed = computed(() => {
//   return multipleSelection.value.flat().length > 0
// })

function init() {
  // 拍摄模特
  selectListShootModel().then(res => {
    if (res.data) {
      shootModelList.value = res.data.map(item => ({
        ...item,
        name: item.name + `(${item.account})`,
      }))
    }
  })
  // 对接人
  selectListContact().then(res => {
    if (res.data) {
      contactList.value = res.data.map(item => ({
        ...item,
        name: item.name + `(${item.id})`,
      }))
    }
  })
  // 出单人
  selectListIssue().then(res => {
    if (res.data) {
      issueList.value = res.data.map(item => ({
        ...item,
        name: item.name + `(${item.id})`,
      }))
    }
  })
  // 下单用户（运营）
  selectOrderUser().then(res => {
    if (res.data) {
      orderUserList.value = res.data
    }
  })

  // 预选添加人
  selectListPreselectUser().then(res => {
    if (res.data) {
      preselectUserList.value = res.data
    }
  })

  //意向模特
  selectIntentionModelSelect().then(res => {
    if (res.data) {
      intentionModelList.value = res.data
    }
  })

  // 预选模特
  getPreselectList(true)

  // 跳转搜索
  if (route.query.keyword) {
    queryParams.value.val = route.query.keyword
  }
  if (route.query.tab) {
    curTab.value = +route.query.tab || ''
  } else if (route.query.tabs) {
    let tabs = route.query.tabs.split(',')
    Array.from(new Set(tabs)).forEach(item => {
      queryParams.value.status.push(+item)
    })
  }
  // 匹配状态
  if (route.query.match_s) {
    queryParams.value.matchStatus = Number(route.query.match_s) || ''
  }
  // 中文客服
  if (route.query.contact) {
    queryParams.value.contactId = [store.id]
  }
  // 英文客服
  if (route.query.issue) {
    queryParams.value.issueId = [store.id]
  }
  // 关闭订单时状态
  if (route.query.close_os) {
    queryParams.value.closeOrderStatus = Number(route.query.close_os) || ''
  }
  // 物流状态
  if (route.query.logistic_s) {
    let ls = atob(route.query.logistic_s).split(',')
    let lsArr = logisticsStatus.map(item => item.value)
    ls.forEach(item => {
      let val = Number(item)
      if (val && lsArr.includes(val)) {
        queryParams.value.logisticMainStatus.push(val)
      }
    })
  }
  if (route.query.abm && route.query.abm == 'true') {
    queryParams.value.aboutMe = true
  } else if (route.query.abm && route.query.abm == 'false') {
    queryParams.value.aboutMe = false
  } else {
    queryParams.value.aboutMe = curTab.value == '' ? false : true
  }

  onQuery()
}

let preselectParams = '0'
const preselectLoading = ref(false)
// 预选模特
function getPreselectList(visible) {
  let params = queryParams.value.preselectAddUserName.join(',')
  if (visible && params != preselectParams) {
    preselectParams = params
    preselectLoading.value = true
    preselectModelList.value.length = 0
    selectListPreselectModel({ backUserIds: params }).then(res => {
      if (res.data && params === preselectParams) {
        preselectModelList.value = res.data
        preselectLoading.value = false
      }
    })
  }
}

const operateLoading = ref(false)
const operateList = ref([])
function getOperatetList(visible) {
  if (visible) {
    operateLoading.value = true
    selectListOrderUser()
      .then(res => {
        operateList.value = res.data || []
      })
      .finally(() => {
        operateLoading.value = false
      })
  }
}

function resetQuery() {
  router.replace({ query: {} })
  queryParams.value = {
    val: '',
    care: '',
    select: 'videoCode',
    aboutMe: curTab.value == '' ? false : true,
    platform: [],
    shootModelId: '',
    contactId: [],
    issueId: [],
    status: [],
    closeOrderStatus: '',
    isGunds: [],
    statusDays: [],
    newStatusDays: [],
    statusTimeSort: '',
    downloadStatus: '',
    receipt: '',
    modelType: '',
    shootingCountry: [],
    placeOrderTime: [],
    orderUserId: [],
    confirmTime: [],
    playTime: [],
    uploadLinkTime: [],
    payType: [],
    logisticMainStatus: [],
    preselectAddUserName: [],
    preselectModelId: [],
    preselectStatus: [],
    reminderStatus: [],
    replyContent: [],
    matchStatus: '',
    addPreselectTime: [],
    matchStartTimes: [],
    confirmReceiptTimes: [],
    beforeStatusTime: [],
    intentionModelIds: [],
    beforeConfirmReceiptTime: '',
    includeFamily: '0',
    hasPicCount: '',
    modelWaitOrder: '',
    createOrderUserName: '',
  }
  onQuery()
}

function handleParams() {
  let {
    val,
    select,
    uploadLinkTime,
    confirmTime,
    playTime,
    placeOrderTime,
    statusDays,
    addPreselectTime,
    newStatusDays,
    matchStartTimes,
    confirmReceiptTimes,
    beforeStatusTime,
    ...params
  } = queryParams.value
  // if (select) {
  //   params[select] = val
  // }
  params.keyword = val
  if (curTab.value != '') {
    params.status = curTab.value
  }
  if (curTab.value == '' || curTab.value == orderStatusMap['已完成']) {
    params.statusTimeSort = ''
  }
  if (uploadLinkTime && uploadLinkTime.length > 0) {
    params.uploadLinkTimeBegin = uploadLinkTime[0]
    params.uploadLinkTimeEnd = uploadLinkTime[1]
  }
  if (
    (curTab.value == orderStatusMap['需发货'] || curTab.value == orderStatusMap['已完成']) &&
    confirmTime?.length > 0
  ) {
    params.statusTimeBegin = confirmTime[0]
    params.statusTimeEnd = confirmTime[1]
  }
  if (playTime && playTime.length > 0) {
    params.payTimeBegin = playTime[0]
    params.payTimeEnd = playTime[1]
  }
  if (placeOrderTime && placeOrderTime.length > 0) {
    params.orderTimeBegin = placeOrderTime[0]
    params.orderTimeEnd = placeOrderTime[1]
  }
  if (statusDays && statusDays.length > 0) {
    params.statusDays = statusDays[0]
  }
  if (newStatusDays && newStatusDays.length > 0) {
    params.newStatusDays = newStatusDays[0]
  }
  if (addPreselectTime && addPreselectTime.length > 0) {
    params.addPreselectTime = addPreselectTime[0]
  }
  if (handleMatchTime.value) {
    if (beforeStatusTime && beforeStatusTime.length > 0) {
      params.beforeStatusTimeStart = beforeStatusTime[0]
      params.beforeStatusTimeEnd = beforeStatusTime[1]
    }
  } else {
    if (matchStartTimes && matchStartTimes.length > 0) {
      params.matchStartTimes = matchStartTimes.filter(item => item != 99)
    }
  }
  if (confirmReceiptTimes && confirmReceiptTimes.length > 0) {
    params.confirmReceiptTimes = confirmReceiptTimes.filter(item => item != 99)
  }

  Object.keys(params).forEach(key => {
    if (Array.isArray(params[key])) {
      params[key] = params[key].join(',')
    }
  })
  return params
}

function onQuery() {
  pageNum.value = 1
  handleQuery(true)
}
function handleQuery(isUpdateDefer) {
  let params = handleParams()
  getList(params, isUpdateDefer)
  getVideoCount(params).then(res => {
    if (res.code === 200) {
      videoCount.value = res.data
    }
  })
  getTabsCount().then(res => {
    if (res.data) {
      tabList.value.forEach(item => {
        if (item.label == '待确认') {
          item.number = res.data.unConfirmCount
        } else if (item.label == '待匹配') {
          item.number = res.data.unMatchCount
        } else if (item.label == '需发货') {
          item.number = res.data.unFilledCount
        } else if (item.label == '待完成') {
          item.number = res.data.unFinishedCount
        } else if (item.label == '需确认') {
          item.number = res.data.needConfirmCount
        } else if (item.label == '已完成') {
          item.number = res.data.finishedCount
        } else if (item.label == '待上传') {
          item.number = res.data.unUploadCount
        }
      })
    }
  })
}

function getList(params = {}, isUpdateDefer) {
  params.pageNum = pageNum.value
  params.pageSize = pageSize.value
  loading.value = true
  // 只加载最近一次请求
  randomStr = Math.random().toString(36).substring(2)
  let checkStr = randomStr
  // tableData.value = []
  getOrderList(params)
    .then(res => {
      if (checkStr === randomStr) {
        tableData.value = res.data.rows
        total.value = res.data.total
        if (isUpdateDefer) updateDefer()
      }
    })
    .finally(() => {
      if (checkStr === randomStr) loading.value = false
    })
  multipleSelection.value = []
}

// 模特浮窗
function handleModelHover(el, id, show) {
  if (show) {
    ModelInfoPopoverRef.value?.open(el, id)
  } else {
    ModelInfoPopoverRef.value?.close()
  }
}

// tab切换
function handleTabChange(name) {
  curTab.value = name
  resetQuery()
}
// 分页跳转
function pageChange(page) {
  pageNum.value = page.pageNum
  pageSize.value = page.pageSize
  handleQuery(true)
}
function handlePageChange(num) {
  pageNum.value = num
  // pageSize.value = page.pageSize
  handleQuery(true)
}

function checkboxLimit(val, form) {
  if (val.length > 1) {
    form.shift()
  }
}

// 批量提交预选模特
function handleBatchEdit() {
  // console.log(multipleSelection.value);
  // console.log(multipleSelection.value.flat());
  handleConfirmSubmit(
    multipleSelection.value.flat(),
    () => {},
    () => handleQuery()
  )
}
const changeRateOrderNum = ref('')
let uploadRowData = {}
function handleButtonAction(btn, row) {
  // console.log(btn)
  switch (btn) {
    case '上传产品图':
      DragUploadDialogRef.value.open()
      uploadRowData = row
      break
    case '取消订单':
      checkOrderMerge({ orderNum: row.orderNum }).then(res => {
        if (res.data.errorMessage && res.data.errorMessage == '订单已合并') {
          if (!res.data.orderVOS) {
            CancelOrderHintRef.value?.open()
          } else {
            CancelOrderDialogRef.value?.open(res.data.orderVOS, row.orderNum)
          }
        } else {
          handleCancelOrder({ orderNum: row.orderNum }, () => handleQuery())
        }
      })
      // if (row.isMergeOrder) {
      //   if (row.status === orderStatusMap['待支付']) CancelOrderHintRef.value?.open()
      //   if (row.status === orderStatusMap['待审核']) CancelOrderDialogRef.value?.open()
      // } else {
      //   if (row.status === orderStatusMap['待审核'] || row.status === orderStatusMap['待支付']) {
      //     // let ids = []
      //     // let data = tableData.value.find(item => item.orderNum === row.orderNum)
      //     // if (data) {
      //     //   ids = data.orderVideoVOS.map(item => item.id)
      //     // handleCancelOrder({ orderNum: row.orderNum }, () => handleQuery())
      //     // }
      //   }
      // }
      break
    case '去审核':
      router.push('/finance/receivableApprove?orderNum=' + row.orderNum)
      break
    case '已选参考图':
      if (row.referencePic && row.referencePic.length) {
        showViewer(row.referencePic.map(item => item))
      }
      break
    case '编辑订单':
      if (checkIsContact(row)) {
        // EditOrderDialogRef.value?.open(row.id, 'audit', row.createOrderBizUserId)
        AuditOrderDrawerRef.value?.open(row.id, 'audit', row.createOrderBizUserId)
      }
      break
    case '申请退款':
      if (checkIsContact(row)) {
        ApplyRefundDialogRef.value?.open(row.id, row.picCount)
      }
      break
    case '暂停模特匹配':
      PauseMatchRef.value?.open(row.id)
      break
    case '继续模特匹配':
      proxy.$modal
        .confirm('是否确认继续该订单匹配？', '继续匹配', {
          confirmButtonText: '确定',
        })
        .then(() => {
          proxy.$modal.loading('执行中...')
          continueMatch({ videoId: row.id })
            .then(res => {
              proxy.$modal.msgSuccess('已重新开始模特匹配，请等待英文部客服添加模特')
              handleQuery()
            })
            .finally(() => {
              proxy.$modal.closeLoading()
            })
        })
        .catch(() => {})
      break
    case '查看预选':
      PreselectionDialogRef.value?.show(row.id, true)
      break
    case '修改订单信息':
      // EditOrderDialogRef.value?.open(row.id, 'edit', row.createOrderBizUserId)
      AuditOrderDrawerRef.value?.open(row.id, 'edit', row.createOrderBizUserId)
      break
    case '预选管理':
      openAddPreselectionModel(row)
      break
    case '预选管理-in':
      openAddPreselectionModel(row, true)
      break
    case '匹配情况反馈':
      FeedbackCaseRef.value?.open(row.id)
      break
    case '标记订单':
      MarkersOrderRef.value?.open(row.id, row.orderFlag)
      break
    case '确认提交':
      checkPreselectModel([row.id]).then(res => {
        if (res.data) {
          handleConfirmSubmit(
            row,
            () => {
              AddPreselectionModelRef.value?.open(row.id)
            },
            () => handleQuery()
          )
        } else {
          proxy.$modal
            .alert('当前没有选定的模特无法提交', '温馨提示', {
              confirmButtonText: '去选定',
            })
            .then(() => {
              openAddPreselectionModel(row)
            })
            .catch(() => {})
        }
      })
      break
    case '填写物流单号':
      ConfirmDeliverGoodsRef.value?.open(row.id, row.status === orderStatusMap['需发货'])
      break
    case '反馈素材':
      let s = null
      let picCount = null
      // row.status == '8' ||
      // if (row.orderVideoRefund?.refundType == '3') {
      //   s = '1'
      // }
      if (row.orderVideoRefundList && row.orderVideoRefundList.length > 0) {
        row.orderVideoRefundList.find(item => {
          if (item.refundType == '3') {
            s = '1'
          }
        })
      }
      if (row.picCount) {
        picCount = row.surplusPicCount
      }
      FeedbackLinkToMerchantRef.value?.open(row.id, picCount, null, s)
      break
    case '模特反馈素材':
      FeedbackLinkListRef.value?.open(row.id)
      break
    case '创建工单':
      CreateWorkOrderRef.value?.open(row)
      break
    case '汇率异常调整':
      ChangeRateRef.value.open(row.orderNum)
      break
    case '上传视频':
      if (row.isUpload == 1) {
        UoloadVideoRef.value.open(row.id, row.productLink)
      } else {
        ElMessageBox.alert(`商家暂无上传视频需求，无需上传`, '上传视频', {
          autofocus: false,
          confirmButtonText: '知道了',
        })
      }
      // handleUploadVideo(row.id, row.isUpload, () => handleQuery())
      break
    case '备注':
      OrderRemarkRef.value?.open(row.id)
      break
    case '查看详情':
      router.push(`/order/details/${row.id}`)
      break
    case '已催单':
      doUrgeOrder(row)
      break
    case '更多':
      ProductMoreInfoRef.value?.open(row.id)
      break
    case '上传素材':
      UploadMaterialRef.value?.open(row.id, row.productLink)
      break
    case '确认收货':
      proxy.$modal
        .confirm('确认模特已经收到货了吗？', '确认收货', {
          cancelButtonText: '再去确认下',
          confirmButtonText: '是的，收到了',
          center: true,
        })
        .then(() => {
          doConfirmReceipt(row.logisticInfo?.logisticId)
        })
        .catch(() => {})
      break

    default:
      break
  }
}

function handleChangeMathTimes(value) {
  if (value && value.length > 0) {
    if (value[value.length - 1] == 99) {
      queryParams.value.matchStartTimes = [99]
    } else {
      queryParams.value.matchStartTimes = value.filter(item => item != 99)
    }
  }
}
function handleChangeReceiptTimes(value) {
  if (value && value.length > 0) {
    if (value[value.length - 1] == 99) {
      queryParams.value.confirmReceiptTimes = [99]
    } else {
      queryParams.value.confirmReceiptTimes = value.filter(item => item != 99)
    }
  }
}
//催一催
function doUrgeOrder(row) {
  ElMessageBox.confirm('已完成对该订单的催单，将会取消催单标记', '已催单', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    showClose: false,
    roundButton: true,
  }).then(() => {
    orderReminder({ videoId: row.id }).then(res => {
      handleQuery()
    })
  })
}
function doConfirmReceipt(logisticId) {
  confirmReceipt(logisticId).then(res => {
    ElMessage.success('确认收货成功')
    handleQuery()
  })
}
// 是否有对接人
function checkIsContact(row) {
  if (row.contact?.id) {
    return true
  }
  proxy.$modal.alert('该商家还没有关联对接人，请先去关联~', '无法编辑订单', {
    center: true,
    confirmButtonText: '我知道了',
  })
  return false
}

// 上传按钮事件
function handleUploadAction(file, btn, row) {
  imgFile.value = file
  uploadRowData = row
  CropperDialogRef.value.open()
}
function uploadSuccess(files) {
  if (files.length) {
    imgFile.value = {
      ...files[0],
      url: URL.createObjectURL(files[0].raw),
    }
    CropperDialogRef.value.open()
  }
}

function handleSelectionChange(val, index) {
  let i = (index + 1) * pageNum.value
  multipleSelection.value[i] = val
  // console.log(multipleSelection.value);
  // console.log(multipleSelection.value.flat());
}

function openAddPreselectionModel(row, isIn = false) {
  AddPreselectionModelRef.value?.open(
    row.id,
    {
      nation: row.shootingCountry,
      platform: row.platform,
      type: row.modelType == '3' ? '0,1' : row.modelType,
    },
    row.unMatchFlag == 1,
    isIn
  )
}

function openPreviews(fileList, index) {
  const list = fileList.map(item => {
    return item.url
  })
  showViewer(list, { index })
}

// 上传裁剪产品图
async function onConfirmCropper(img) {
  // console.log(img, uploadRowData);
  proxy.$modal.loading('正在上传中...')
  try {
    const formData = new FormData()
    formData.append('file', img.raw)
    let upres = await uploadCloudFile(formData, 'order')
    let res = await uploadProductImg({
      resourceId: upres.data.id,
      productPic: upres.data.picUrl,
      videoId: uploadRowData.id,
    })
    proxy.$modal.closeLoading()
    proxy.$modal.msgSuccess('上传成功！')
    handleQuery()
  } catch (e) {
    proxy.$modal.closeLoading()
  }
}

const handleMatchTime = computed(() => {
  let isShow = false
  if (queryParams.value.matchStartTimes && queryParams.value.matchStartTimes.length > 0) {
    isShow = queryParams.value.matchStartTimes.includes(99)
  }
  if (!isShow) queryParams.value.beforeStatusTime = []
  return isShow
})
const handleMatchTimeV1 = computed(() => {
  let isShow = false
  if (queryParams.value.confirmReceiptTimes && queryParams.value.confirmReceiptTimes.length > 0) {
    isShow = queryParams.value.confirmReceiptTimes.includes(99)
  }
  if (!isShow) queryParams.value.beforeConfirmReceiptTime = ''
  return isShow
})

init()
</script>

<style scoped lang="scss">
.order-list-page {
  padding: 20px 20px 0 20px;

  .search-box {
    .el-form--inline .el-form-item {
      margin-right: 20px;
    }
  }

  .video-statistics {
    margin-left: 10px;
    font-size: 14px;
    font-weight: 400;
    color: var(--el-text-color-regular);
  }
}
</style>
