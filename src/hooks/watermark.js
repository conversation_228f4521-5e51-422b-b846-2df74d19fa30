import { Watermark } from '@pansy/watermark'

const watermarkConfig = {
  mode: 'repeat',
  monitor: false,
  text: '蜗牛海拍',
  image: undefined,
  opacity: 0.1,
  width: 240,
  height: 100,
  offsetLeft: 0,
  offsetTop: 0,
  gapX: 90,
  gapY: 90,
  zIndex: 9999,
  rotate: -22,
  fontSize: 16,
  textAlign: 'center',
  fontStyle: 'normal',
  fontColor: '#000',
  fontFamily: 'sans-serif',
  fontWeight: '300',
  blindText: '蜗牛海拍',
  blindOpacity: 0.005,
}
const watermark = new Watermark(watermarkConfig);

export default {
  // 更新文字
  setText(text = '') {
    text = text ? `蜗牛海拍-${text}` : '蜗牛海拍'
    watermark.update({
      ...watermarkConfig,
      text,
      blindText: text
    });
  },

  // 如果需要修改水印参数，请调用
  update(config) {
    watermark.update({
      ...watermarkConfig,
      ...config
    });
  },

  // 隐藏水印
  hide() {
    watermark.hide();
  },

  // 显示水印
  show() {
    watermark.show();
  },

  // 销毁水印
  destroy() {
    watermark.destroy();
  },
}
