import { onUnmounted, ref } from 'vue';
/**
 * 逐帧渲染
 * @param maxCount 最大帧率
 * @returns Boolean
 */
export function useDefer(maxCount = 100) {
  const frameCount = ref(0);
  let rafId;
  function updateFrameCount() {
    rafId = requestAnimationFrame(() => {
      frameCount.value++;
      if (frameCount.value >= maxCount) {
          return;
      }
      updateFrameCount();
    });
  }
  updateFrameCount();

  onUnmounted(() => {
    cancelAnimationFrame(rafId);
  });

  function defer(n) {
    return frameCount.value >= n;
  };

  function updateDefer() {
    frameCount.value = 0;
    updateFrameCount();
  }

  return {
    defer,
    updateDefer,
  }
}
