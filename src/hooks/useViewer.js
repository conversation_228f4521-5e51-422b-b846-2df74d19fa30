import { ref } from 'vue'
import { http_reg } from '@/utils/RegExp'

/**
 * 图片比例
 * 1x1、3x2、2x3、3x4、4x3、16x9、9x16、compress：压缩图、fullSize：全尺寸、raw：原图、square：正方形
 */
const scaleMap = ['1x1', '3x2', '2x3', '3x4', '4x3', '16x9', '9x16', 'compress', 'fullSize', 'raw', 'square']
const scaleMapCompress = scaleMap.map(item => {
  if (item != 'compress') {
    return item + 'compress'
  }
  return item
})
const imgViewerVisible = ref(false)
const imgViewerUrl = ref([])
const imgViewerIndex = ref(0)
const http = import.meta.env.VITE_APP_FILE_HTTP_PATH
/**
 * 打开图片预览
 * @param list 图片
 * @param options 配置
 */
const showViewer = (list, options) => {
  if (list && list.length == 1 && list[0] == null) {
    return
  }
  let scale = options?.scale || 'fullSize'
  imgViewerUrl.value = list.map(url => {
    if (!http_reg.test(url)) {
      url = http + url
    }
    const urlType = url.substring(url.lastIndexOf('.') + 1)
    const suffix = url.substring(url.lastIndexOf('!') + 1)
    //  || urlType === 'gif'  腾讯云gif图暂不支持
    if (options?.raw) {
      return url
    }
    if (scaleMap.includes(suffix) || scaleMapCompress.includes(suffix)) {
      return url.indexOf('compress') === -1 ? url + 'compress' : url
    }
    return url + '!' + (scale.indexOf('compress') === -1 ? scale + 'compress' : scale)
  })
  if (options?.index && options.index > 0 && options.index < list.length) {
    imgViewerIndex.value = options.index
  } else {
    imgViewerIndex.value = 0
  }
  imgViewerVisible.value = true
}
/**
 * 关闭图片预览
 */
const closeViewer = () => {
  imgViewerVisible.value = false
  imgViewerUrl.value = []
}

export function useViewer() {
  return {
    imgViewerVisible,
    imgViewerUrl,
    imgViewerIndex,
    showViewer,
    closeViewer,
  }
}
