import { ref } from 'vue'

const tooltipKey = ref(0)
const tooltipVisible = ref(false)
const tooltipContent = ref('')
const tooltipsTriggerRef = ref(null)

/**
 * 显示tooltips
 * @param event 元素
 * @param content text or html
 * @returns 
 */
function showTooltips(event, content) {
  tooltipsTriggerRef.value = event.target
  tooltipContent.value = content
  if (tooltipVisible.value) {
    setTimeout(() => {
      tooltipVisible.value = true
    })
    return
  }
  tooltipVisible.value = true
}


export function useTooltips() {
  return {
    tooltipKey,
    tooltipVisible,
    tooltipContent,
    tooltipsTriggerRef,
    showTooltips,
  }
}
