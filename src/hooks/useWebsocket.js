import { ref } from 'vue'
import { fingerprint } from '@/utils/fingerprintjs2'
import { getToken } from '@/utils/auth'

let socket = null
let retries = 0 // 重试计数器
let retryTimer = null // 重试定时器
const MAX_RETRIES = 3 // 最大重试次数
const BASE_DELAY = 3000 // 基础重试间隔（3秒）
const SILENT_PERIOD = 30000 // 静默时间（30秒）

const remindVisible = ref(false)

//webSocketUrl websocket地址动态设置
const initWebSocket = webSocketUrl => {
  const wsUrlRequest = constructWsUrl(webSocketUrl)
  socket = new WebSocket(wsUrlRequest)
  socket.onopen = () => {
    console.log('WebSocket connected')
    retries = 0 // 连接成功时重置计数器
  }

  // type=1 模特选单
  socket.onmessage = event => {
    /* 处理消息逻辑 */
    if (event.data) {
      const data = JSON.parse(event.data)
      if (data.result === 'HB_ACK') {
        console.log('Received PONG')
      }
      if (data.type && data.type == '1') {
        remindVisible.value = true
      }
    }
  }

  socket.onerror = error => {
    console.error('WebSocket error:', error)
    handleReconnect()
  }

  socket.onclose = event => {
    if (!event.wasClean) {
      handleReconnect()
    }
  }

  setInterval(() => {
    if (socket.readyState === WebSocket.OPEN) {
      socket.send(JSON.stringify({ type: 'HEARTBEAT' }))
    }
  }, 15000)
}

// 处理重连逻辑
const handleReconnect = () => {
  if (retries >= MAX_RETRIES) {
    // 超过最大重试次数，进入静默期
    console.log('Reached max retries, entering silent period')
    retryTimer = setTimeout(() => {
      retries = 0 // 静默期结束后重置计数器
      initWebSocket() // 重新尝试连接
    }, SILENT_PERIOD)
  } else {
    // 指数退避：延迟时间 = 基础间隔 * 2^重试次数
    const delay = BASE_DELAY * Math.pow(2, retries)
    retryTimer = setTimeout(() => {
      retries++
      initWebSocket()
    }, delay)
  }
}

//关闭连接
const closeConnect = () => {
  socket?.close()
  retryTimer ? clearTimeout(retryTimer) : ''
}

// 关闭提醒弹窗
const closeRemindVisible = () => {
  remindVisible.value = false
}

const constructWsUrl = constructWsUrl => {
  let token = getToken()
  return `${constructWsUrl}?authorization=${token}&fp=${fingerprint.value}` // 通过 Query 传参
}

export function useWebsocket() {
  return {
    initWebSocket,
    handleReconnect,
    closeConnect,
    closeRemindVisible,
    remindVisible,
    socket
  }
}
