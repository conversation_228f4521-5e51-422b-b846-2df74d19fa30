import { ElMessageBox, ElMessage, ElLoading } from 'element-plus'
import { http_reg } from '@/utils/RegExp'
import {
  cancelOrder,
  applyRefund,
  submitPreselectModel,
  uploadLink,
  checkOrderMerge,
} from '@/api/order/order'

export default function useOrderApi() {
  /**
   * 取消订单
   * @param { any } params 订单ids
   * @param { (res: any) => void } callback 成功回调
   * @param { (err: any) => void } error 失败回调
   */
  function handleCancelOrder(params, callback, error) {
    ElMessageBox.confirm('确认取消订单？', '温馨提示', {
      autofocus: false,
      confirmButtonText: '确定',
      cancelButtonText: '再想想',
    })
      .then(() => {
        const el_loading = ElLoading.service({
          lock: true,
          text: '正在取消中，请稍后',
          background: 'rgba(0, 0, 0, 0.7)',
        })
        cancelOrder(params)
          .then(res => {
            ElMessage.success('取消成功')
            if (callback) callback(res)
          })
          .catch(e => {
            if (error) error(e)
          })
          .finally(() => el_loading.close())
      })
      .catch(() => {})
  }

  /**
   * 申请退款
   * @param { any } data
   * @param { (res: any) => void } callback 成功回调
   * @param { (err: any) => void } error 失败回调
   */
  function handleApplyRefund(data, callback, error) {
    ElMessageBox.confirm('确认申请退款？', '温馨提示', {
      autofocus: false,
      confirmButtonText: '确定',
      cancelButtonText: '再想想',
    })
      .then(() => {
        const el_loading = ElLoading.service({
          lock: true,
          text: '正在执行中，请稍后',
          background: 'rgba(0, 0, 0, 0.7)',
        })
        applyRefund(data)
          .then(res => {
            ElMessage.success('操作成功')
            if (callback) callback(res)
          })
          .catch(e => {
            if (error) error(e)
          })
          .finally(() => el_loading.close())
      })
      .catch(() => {})
  }
  /**
   * 确认提交预选模特（单个/批量）
   * @param { any | any[] } row
   * @param { () => void } intercept 中止回调
   * @param { (res: any) => void } callback 成功回调
   * @param { (err: any) => void } error 失败回调
   */
  function handleConfirmSubmit(row, intercept, callback, error) {
    let vIds = []
    if (row instanceof Array) {
      vIds = row.map(item => item.id)
    } else {
      vIds.push(row.id)
    }
    ElMessageBox.confirm(
      `
        <div style="margin-bottom: 15px">
          <h3>确认提交给商家？</h3>
          <p>已选定 <span style="color: red;">${vIds.length}</span> 位模特</p>
        </div>
      `,
      '',
      {
        center: true,
        autofocus: false,
        dangerouslyUseHTMLString: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
      }
    )
      .then(() => {
        const el_loading = ElLoading.service({
          lock: true,
          text: '正在提交中，请稍后',
          background: 'rgba(0, 0, 0, 0.7)',
        })
        submitPreselectModel(vIds)
          .then(res => {
            // console.log(res);
            if (res.data) {
              ElMessageBox.alert('当前有未选定的模特，无法提交！', '提示', {
                autofocus: false,
                confirmButtonText: '去选定',
              }).then(() => {
                if (intercept) intercept()
              })
            } else {
              ElMessage.success('提交成功')
              if (callback) callback(res)
            }
          })
          .catch(e => {
            if (error) error(e)
          })
          .finally(() => el_loading.close())
      })
      .catch(() => {})
  }
  /**
   * 驳回原因
   * @param {string} title
   * @param {string} remark
   */
  function openRejectRemark(title, remark) {
    ElMessageBox.alert(
      `
        <div style="max-height: ">
          <div style="font-weight: bold;font-size: 15px;">
            驳回标题：
            <span style="font-weight: 300;">${title}</span>
          </div>
          <div style="font-weight: bold;font-size: 15px;margin: 10px 0;">
            驳回原因：
          </div>
          ${remark}
        </div>
      `,
      '驳回原因',
      {
        dangerouslyUseHTMLString: true,
        customStyle: {
          maxWidth: '450px',
        },
        autofocus: false,
        confirmButtonText: '知道了',
        showConfirmButton: false,
      }
    )
  }
  /**
   * 上传视频
   * @param {number} videoId
   * @param {number | null} allowUpload
   * @param { (res: any) => void } callback 成功回调
   * @param { (err: any) => void } error 失败回调
   */
  function handleUploadVideo(videoId, isUpload, callback, error) {
    if (isUpload == 1) {
      ElMessageBox
        // .prompt(
        //   ``,
        //   '确认上传',
        //   {
        //     confirmButtonText: '提交',
        //     cancelButtonText: '取消',
        //     // inputPlaceholder: '请输入商家可以查看到视频正确的地址',
        //     // inputValidator: val => {
        //     //   return http_reg.test(val)
        //     // },
        //     // inputErrorMessage: '请输入商家可以查看到视频正确的地址',
        //   }
        // )
        .confirm('确认素材上传至商家指定位置', {
          confirmButtonText: '确认提交',
          cancelButtonText: '取消',
        })
        .then(res => {
          const el_loading = ElLoading.service({
            lock: true,
            text: '正在上传中，请稍后',
            background: 'rgba(0, 0, 0, 0.7)',
          })
          uploadLink({
            id: videoId,
          })
            .then(res => {
              ElMessage.success('上传成功')
              if (callback) callback(res)
            })
            .catch(e => {
              if (error) error(e)
            })
            .finally(() => el_loading.close())
        })
        .catch(() => {})
    } else {
      ElMessageBox.alert(`商家暂无上传视频需求，无需上传`, '上传视频', {
        autofocus: false,
        confirmButtonText: '知道了',
      })
    }
  }

  return {
    handleCancelOrder,
    handleApplyRefund,
    handleConfirmSubmit,
    openRejectRemark,
    handleUploadVideo,
  }
}
// proxy.$modal
//   .prompt(
//     `
//     <div>物流单号：</div>
//     <div
//       style="
//         color: red;
//         font-weight: 100;
//         font-size: 12px;
//         height: 0;
//         text-align: center;
//         position: relative;
//         top: 70px;
//       "
//     >
//       请注意：一经确认发货，订单将流转至待完成，请确保物流单号的准确性。
//     </div>
//   `,
//     '填写物流单号',
//     {
//       dangerouslyUseHTMLString: true,
//       customStyle: {
//         width: '450px',
//         'max-width': '450px',
//       },
//       confirmButtonText: '确定发货',
//       cancelButtonText: '取消',
//       inputPlaceholder: '请输入物流单号',
//       inputValidator: val => {
//         return val ? true : false
//       },
//       inputErrorMessage: '请输入物流单号',
//     }
//   )
//   .then(({ value }) => {
//     // console.log(value);
//   })
