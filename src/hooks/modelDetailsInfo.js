import { ref } from 'vue'
import { modelMerchantDetails } from '@/api/model/model'

const modelInfoMap = new Map()
const modelInfo = ref({
  amazonVideo: [],
  name: '',
  platform: '',
  specialtyCategory: [],
  tags: [],
  tiktokVideo: [],
  type: '',
  cooperation: ''
})
const loading = ref(false)

const getModelInfo = (id) => {
  if(!id) {
    return
  }
  loading.value = true
  if(modelInfoMap.has(id)) {
    modelInfo.value = modelInfoMap.get(id)
    loading.value = false
  } else {
    modelMerchantDetails(id).then(res => {
      modelInfo.value = res.data
      modelInfoMap.set(id, res.data)
    }).finally(() => loading.value = false)
  }
}
const resetModelInfo = () => {
  modelInfo.value = {
    amazonVideo: [],
    name: '',
    platform: '',
    specialtyCategory: [],
    tags: [],
    tiktokVideo: [],
    type: '',
    cooperation: ''
  }
  loading.value = true
}

export function useModelMap() {
  return {
    modelInfo,
    loading,
    getModelInfo,
    resetModelInfo
  }
}