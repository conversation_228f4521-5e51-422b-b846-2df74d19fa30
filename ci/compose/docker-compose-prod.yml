version: "3.8"
services:
  wnkx-front:
    image: $IMAGE_TAG/wnkx-front:latest-prod
    restart: always
    ports:
      - "25680:80"
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: '1'
          memory: 512M
    logging:
      driver: loki
      options:
        loki-url: "http://10.1.20.10:3100/loki/api/v1/push"
        max-size: 50m
        max-file: "10"
    networks:
      - wnkx-network
networks:
  wnkx-network:
    external: true
