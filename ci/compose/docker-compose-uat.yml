version: "3.7"
services:
  wnkx-front-uat:
    container_name: wnkx-front-uat
    image: $IMAGE_TAG/wnkx-front:latest-uat
    restart: always
    ports:
      - "25580:80"
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
    logging:
      driver: loki
      options:
        loki-url: "http://**********:3100/loki/api/v1/push"
        max-size: 50m
        max-file: "10"
    networks:
      wnkx-network-test:
        ipv4_address: ***********
networks:
  wnkx-network-test:
    external: true
