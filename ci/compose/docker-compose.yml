version: "3.7"
services:
  wnkx-front:
    container_name: wnkx-front
    image: $IMAGE_TAG/wnkx-front:latest
    restart: always
    ports:
      - "25380:80"
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
    logging:
      driver: loki
      options:
        loki-url: "http://**********:3100/loki/api/v1/push"
        max-size: 50m
        max-file: "10"
    networks:
      wnkx-network:
        ipv4_address: ***********
networks:
  wnkx-network:
    external: true
