version: "3.7"
services:
  wnkx-front-test:
    container_name: wnkx-front-test
    image: $IMAGE_TAG/wnkx-front:latest-test
    restart: always
    ports:
      - "25480:80"
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
    logging:
      driver: loki
      options:
        loki-url: "http://**********:3100/loki/api/v1/push"
        max-size: 50m
        max-file: "10"
    networks:
      wnkx-network-uat:
        ipv4_address: ***********
networks:
  wnkx-network-uat:
    external: true
