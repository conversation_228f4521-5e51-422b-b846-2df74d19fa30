version: "3.7"
services:
  wnkx-front-daily:
    container_name: wnkx-front-daily
    image: $IMAGE_TAG/wnkx-front:latest-daily
    restart: always
    ports:
      - "25780:80"
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
    logging:
      driver: loki
      options:
        loki-url: "http://**********:3100/loki/api/v1/push"
        max-size: 50m
        max-file: "10"
    networks:
      wnkx-network-daily:
        ipv4_address: ***********
networks:
  wnkx-network-daily:
    external: true
