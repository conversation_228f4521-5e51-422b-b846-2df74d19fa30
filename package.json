{"name": "woniu", "version": "3.6.4", "description": "蜗牛海拍", "author": "wnkx", "license": "MIT", "type": "module", "scripts": {"dev": "vite", "test": "vite --mode test", "build:prod": "vite build", "build:stage": "vite build --mode staging", "build:uat": "vite build --mode uat", "build:test": "vite build --mode test", "build:daily": "vite build --mode daily", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "2.3.1", "@pansy/watermark": "^2.3.0", "@vueup/vue-quill": "1.2.0", "@vueuse/core": "10.6.1", "@wangeditor/editor": "^5.1.12", "@wangeditor/editor-for-vue": "^5.1.12", "aegis-web-sdk": "1.39.2", "axios": "0.27.2", "currency.js": "^2.0.4", "echarts": "^5.4.3", "element-plus": "2.9.11", "file-saver": "^2.0.5", "fingerprintjs2": "^2.1.4", "fuse.js": "6.6.2", "html2canvas": "^1.4.1", "js-cookie": "3.0.5", "jsencrypt": "3.3.2", "nprogress": "0.2.0", "ofd.js": "^1.4.9", "pinia": "2.1.7", "qrcode": "^1.5.3", "sortablejs": "^1.15.3", "vant": "^4.9.4", "vue": "3.3.9", "vue-cropper": "^1.1.1", "vue-router": "4.2.5", "vuedraggable": "^4.1.0"}, "devDependencies": {"@plugin-web-update-notification/vite": "^2.0.0", "@vitejs/plugin-vue": "4.5.0", "@vue/compiler-sfc": "3.3.9", "mathjs": "^14.2.1", "sass": "1.69.5", "unplugin-auto-import": "0.17.1", "unplugin-vue-setup-extend-plus": "1.0.0", "vite": "5.0.4", "vite-plugin-compression": "0.5.1", "vite-plugin-svg-icons": "2.0.1"}}