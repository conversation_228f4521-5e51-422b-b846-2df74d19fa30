import vue from '@vitejs/plugin-vue'

import createAutoImport from './auto-import'
import createSvgIcon from './svg-icon'
import createCompression from './compression'
import createSetupExtend from './setup-extend'
import { webUpdateNotice } from '@plugin-web-update-notification/vite'

export default function createVitePlugins(viteEnv, isBuild = false) {
    const vitePlugins = [vue()]
    vitePlugins.push(createAutoImport())
	vitePlugins.push(createSetupExtend())
    vitePlugins.push(createSvgIcon(isBuild))
    vitePlugins.push(webUpdateNotice({
      notificationProps: {
        title: '温馨提示',
        description: '我们更新了网站,请刷新页面后再进行操作',
        buttonText: '刷新',
        dismissButtonText: '忽略'
      },
    }))
	isBuild && vitePlugins.push(...createCompression(viteEnv))
    return vitePlugins
}
