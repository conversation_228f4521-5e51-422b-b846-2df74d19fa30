<h1 align="center" style="margin: 30px 0 30px; font-weight: bold;">蜗牛开箱系统</h1>
<h4 align="center">基于 RuoYi v3.6.4</h4>

## 平台简介

* 本仓库为前端技术栈 [Vue3](https://v3.cn.vuejs.org) + [Element Plus](https://element-plus.org/zh-CN) + [Vite](https://cn.vitejs.dev) 版本。
* 前端技术栈（[Vue2](https://cn.vuejs.org) + [Element](https://github.com/ElemeFE/element) + [Vue CLI](https://cli.vuejs.org/zh)），请移步[RuoYi-Cloud](https://gitee.com/y_project/RuoYi-Cloud/tree/master/ruoyi-ui)。

## 前端运行

```bash
# 进入项目目录
cd RuoYi-Cloud-Vue3

# 安装依赖
yarn --registry=http://mirrors.tencentyun.com/

# 启动服务
yarn dev

# 构建测试环境 yarn build:stage
# 构建生产环境 yarn build:prod
# 前端访问地址 http://localhost:80
```
