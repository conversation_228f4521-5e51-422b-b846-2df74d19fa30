# 页面标题
VITE_APP_TITLE = 运营系统-日更

# 生产环境配置
VITE_APP_ENV = 'daily'

# 蜗牛开箱/生产环境
VITE_APP_BASE_API = '/api'

# 是否在打包时开启压缩，支持 gzip 和 brotli
VITE_BUILD_COMPRESS = gzip

# 测试环境子账号邀请地址
VITE_APP_INVITE_API = 'https://wxg.woniu.video/daily'

# 商家端地址
VITE_APP_CUSTOMER_WONIU_VIDEO_PATH = 'https://customer.lc3oaioj.daily.woniu.video'

# 图片文件路径
VITE_APP_FILE_HTTP_PATH = 'https://pstatic.woniu.video/'

# websocket地址
VITE_APP_WS_URL = 'ws://**********:19039/ws-endpoint'
